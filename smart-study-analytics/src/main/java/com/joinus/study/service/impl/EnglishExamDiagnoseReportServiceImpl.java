package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishExamDiagnoseReportMapper;
import com.joinus.study.model.entity.EnglishExamDiagnoseReport;
import com.joinus.study.service.EnglishExamDiagnoseReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/3 14:12
 */
@Service
@Slf4j
public class EnglishExamDiagnoseReportServiceImpl extends ServiceImpl<EnglishExamDiagnoseReportMapper, EnglishExamDiagnoseReport> implements EnglishExamDiagnoseReportService {

    @Override
    public List<EnglishExamDiagnoseReport> listByRecordId(Long recordId) {
        return lambdaQuery().eq(EnglishExamDiagnoseReport::getRecordId, recordId)
                .orderByDesc(EnglishExamDiagnoseReport::getId)
                .list();
    }
}
