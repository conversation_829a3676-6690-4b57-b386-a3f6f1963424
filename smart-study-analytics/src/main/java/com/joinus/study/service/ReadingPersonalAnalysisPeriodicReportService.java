package com.joinus.study.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingPersonalAnalysisPeriodicReport;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.vo.ReadingPeriodicReportDetailVo;
import com.joinus.study.model.vo.ReadingPeriodicReportViewDetailVo;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 针对表【reading_personal_analysis_report】的数据库操作service
 * @createDate 2025-03-28 09:47:33
 * @Entity com.joinus.study.model.entity.ReadingPersonalAnalysisReport
 */
public interface ReadingPersonalAnalysisPeriodicReportService extends IService<ReadingPersonalAnalysisPeriodicReport> {

    /**
     * 定时执行生成周报或月报
     */
    void autoGeneratePeriodicReports();

    /**
     * 生成周报或月报
     *
     * @param reportType 报告类型：2周报，3月报
     * @return
     */
    void generatePeriodicReport(Integer reportType);

    ReadingPeriodicReportViewDetailVo periodicReportDetail(ReadingPeriodicReportDetailParam param);

    Boolean checkPeriodicReportIsAnalysisCompleted(Long reportId);

    ReadingPeriodicReportDetailVo queryPeriodicReportDetail(Long studentId, LocalDate thisWeekStart, Integer reportType);

    Boolean checkPersonalPassageIsExistReport(Long personalPassageId);

    void againGeneratePeriodicReport();
}
