package com.joinus.study.service.impl;

import com.joinus.study.mapper.GradeLeaveMapper;
import com.joinus.study.model.dto.GradeInfo;
import com.joinus.study.service.GradeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class GradeMapperImpl implements GradeService {

    @Resource
    private GradeLeaveMapper gradeLeaveMapper;

    @Override
    public GradeInfo getGradeByStudentId(Long studentId) {
        return gradeLeaveMapper.getGradeByStudentId(studentId);
    }
}
