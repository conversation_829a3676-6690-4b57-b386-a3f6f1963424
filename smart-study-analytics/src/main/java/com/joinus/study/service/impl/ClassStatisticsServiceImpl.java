package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.study.mapper.ActiveStudentsMapper;
import com.joinus.study.mapper.ClassExamQuestionStatisticsMapper;
import com.joinus.study.mapper.ClassExamStatisticsMapper;
import com.joinus.study.mapper.ClassKnowledgePointStatisticsMapper;
import com.joinus.study.mapper.ExamAnalyzeResultMapper;
import com.joinus.study.mapper.PersonalExamQuestionMapper;
import com.joinus.study.mapper.QuestionKnowledgePointMapper;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.PersonalExamQuestionResultEnum;
import com.joinus.study.model.vo.ActiveStudentVo;
import com.joinus.study.model.vo.KnowledgePointStatisticsVo;
import com.joinus.study.service.ClassStatisticsService;

import com.joinus.study.service.QuestionKnowledgePointService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 班级统计服务实现
 * @Author: anpy
 * @date: 2025/4/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClassStatisticsServiceImpl implements ClassStatisticsService {

    private final ActiveStudentsMapper activeStudentsMapper;
    private final ClassExamStatisticsMapper classExamStatisticsMapper;
    private final ClassExamQuestionStatisticsMapper classExamQuestionStatisticsMapper;
    private final ClassKnowledgePointStatisticsMapper classKnowledgePointStatisticsMapper;
    private final QuestionKnowledgePointMapper questionKnowledgePointMapper;
    private final ExamAnalyzeResultMapper examAnalyzeResultMapper;
    private final PersonalExamQuestionMapper personalExamQuestionMapper;
    @Resource
    private QuestionKnowledgePointService knowledgePointService;

    @Override
    public ActiveStudentVo getClassIdByStudentId(Long studentId) {
        return activeStudentsMapper.getClassIdByStudentId(studentId);
    }

    @Override
    @Async("taskExecutor")
    public void updateClassExamStatistics(Long classId, Long gradeId, UUID examId) {
        log.info("异步执行班级考试统计数据更新: classId={}, gradeId={}, examId={}", classId, gradeId, examId);
        try {
            // 获取班级考试统计数据和学生最新的考试分析结果
            if (classId == null) {
                List<ActiveStudentsEntity> activeStudentsEntity = activeStudentsMapper.getClassIdByGradeId(gradeId);
                if (CollUtil.isNotEmpty(activeStudentsEntity)) {
                    List<Long> classIds = activeStudentsMapper.getExamClassIds(examId, gradeId);
                    Long finalGradeId = gradeId;
                    classIds.forEach(item -> {
                        updateStatistics(item, finalGradeId, examId);
                    });
                }
            } else {
                if (gradeId == null) {
                    ActiveStudentsEntity activeStudentsEntity = activeStudentsMapper.selectOne(
                            new LambdaQueryWrapper<ActiveStudentsEntity>()
                                    .eq(ActiveStudentsEntity::getClassId, classId)
                                    .last("LIMIT 1"));
                    gradeId = activeStudentsEntity.getGradeId();
                }
                updateStatistics(classId, gradeId, examId);
            }
            log.info("异步班级考试统计数据更新完成: classId={}, gradeId={}, examId={}", classId, gradeId, examId);
        } catch (Exception e) {
            log.error("异步班级考试统计数据更新失败: classId={}, gradeId={}, examId={}, 错误信息: {}",
                    classId, gradeId, examId, e.getMessage(), e);
        }
    }

    private void updateStatistics(Long classId, Long gradeId, UUID examId) {
        ExamStatisticsData statsData = getExamStatisticsData(classId, gradeId, examId);
        List<ExamAnalyzeResult> classAnalyzeResults = statsData.getClassAnalyzeResultList();
        List<ExamAnalyzeResult> gradeAnalyzeResults = statsData.getGradeAnalyzeResultList();
        if (CollUtil.isEmpty(classAnalyzeResults)) {
            return;
        }
        ClassExamStatistics classExamStatistics = statsData.getClassExamStatistics();

        //计算班级、年级每道题目正确率
        calculateQuestionCorrectRate(examId, classAnalyzeResults, classExamStatistics, "class");
        calculateQuestionCorrectRate(examId, gradeAnalyzeResults, classExamStatistics, "grade");

        // 更新班级知识点统计
        updateClassKnowledgePointStatistics(statsData, classAnalyzeResults, classId, gradeId, examId, "class");
        updateClassKnowledgePointStatistics(statsData, gradeAnalyzeResults, classId, gradeId, examId, "grade");
    }

    /**
     * 获取学生题目答案映射
     */
    private Map<Long, List<PersonalExamQuestion>> getStudentQuestionMap(List<ExamAnalyzeResult> latestResults) {
        // 获取所有学生的个人考试ID列表
        List<Long> personalExamIds = getClassExamPersonalExamIds(latestResults);

        // 批量获取所有学生的所有题目答题记录
        return personalExamQuestionMapper.selectList(
                        new LambdaQueryWrapper<PersonalExamQuestion>()
                                .in(PersonalExamQuestion::getPersonalExamId, personalExamIds)
                                .isNull(PersonalExamQuestion::getDeletedAt))
                .stream()
                .collect(Collectors.groupingBy(PersonalExamQuestion::getPersonalExamId));
    }

    /**
     * 获取知识点相关题目ID集合
     */
    private Set<UUID> getQuestionIdsForKnowledgePoint(List<QuestionKnowledgePoint> pointQuestions) {
        return pointQuestions.stream()
                .map(QuestionKnowledgePoint::getQuestionId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取知识点统计
     */
    private ClassKnowledgePointStatistics getKnowledgePointStatistics(Long classId, UUID examId, UUID knowledgePointId) {
        return classKnowledgePointStatisticsMapper.selectOne(
                new LambdaQueryWrapper<ClassKnowledgePointStatistics>()
                        .eq(ClassKnowledgePointStatistics::getClassId, classId)
                        .eq(ClassKnowledgePointStatistics::getExamId, examId)
                        .eq(ClassKnowledgePointStatistics::getKnowledgePointId, knowledgePointId)
                        .isNull(ClassKnowledgePointStatistics::getDeletedAt));
    }

    /**
     * 计算掌握知识点的学生数量
     */
    private int calculateMasteredStudentCount(List<ExamAnalyzeResult> latestResults, Map<Long, List<PersonalExamQuestion>> studentQuestionMap,
                                              Set<UUID> pointQuestionIds, double masteryThreshold) {
        int masteredCount = 0;

        // 遍历每个学生，判断是否掌握该知识点
        for (ExamAnalyzeResult examAnalyzeResult : latestResults) {
            Long personalExamId = examAnalyzeResult.getPersonalExamId();
            if (personalExamId == null) {
                continue;
            }

            // 获取该学生的答题记录
            List<PersonalExamQuestion> studentAnswers = studentQuestionMap.getOrDefault(personalExamId, Collections.emptyList());

            // 计算该学生在该知识点相关题目的答题情况
            List<PersonalExamQuestion> relevantQuestions = new ArrayList<>();
            for (UUID questionId : pointQuestionIds) {
                studentAnswers.stream()
                        .filter(q -> q.getQuestionId().equals(questionId))
                        .findFirst().ifPresent(relevantQuestions::add);
            }

            // 计算该学生在该知识点的正确率
            if (!relevantQuestions.isEmpty()) {
                long correctCount = relevantQuestions.stream()
                        .filter(q -> PersonalExamQuestionResultEnum.correct.equals(q.getResult()))
                        .count();

                double correctRate = (double) correctCount / relevantQuestions.size();

                // 如果正确率超过阈值，认为学生掌握了该知识点
                if (correctRate >= masteryThreshold) {
                    masteredCount++;
                }
            }
        }

        return masteredCount;
    }

    /**
     * 更新知识点统计
     */
    private void updateKnowledgePointStatistics(Long classId, Long gradeId, UUID examId, ClassExamStatistics classExamStatistics,
                                                UUID knowledgePointId, int masteredCount,
                                                BigDecimal correctRate) {
        // 查询该知识点在该班级的统计记录
        ClassKnowledgePointStatistics pointStatistics = getKnowledgePointStatistics(classId, examId, knowledgePointId);
        // 创建或更新知识点统计
        if (pointStatistics == null) {
            pointStatistics = new ClassKnowledgePointStatistics();
            pointStatistics.setClassId(classId);
            pointStatistics.setGradeId(gradeId);
            pointStatistics.setExamId(examId);
            pointStatistics.setKnowledgePointId(knowledgePointId);
            pointStatistics.setClassExamId(classExamStatistics.getId());
            pointStatistics.setCorrectCount(masteredCount);
            pointStatistics.setCorrectRate(correctRate);
            pointStatistics.setCreatedAt(new Date());
            pointStatistics.setUpdatedAt(new Date());

            classKnowledgePointStatisticsMapper.insert(pointStatistics);
        } else {
            // 更新现有的知识点统计
            pointStatistics.setCorrectCount(masteredCount);
            pointStatistics.setCorrectRate(correctRate);
            pointStatistics.setUpdatedAt(new Date());
            classKnowledgePointStatisticsMapper.updateById(pointStatistics);
        }
    }

    /**
     * 获取考试统计数据，包括班级学生列表、最新的考试分析结果和班级考试统计
     *
     * @param classId 班级ID
     * @param gradeId 年级ID
     * @param examId  考试ID
     * @return 考试统计数据
     */
    private ExamStatisticsData getExamStatisticsData(Long classId, Long gradeId, UUID examId) {
        // 1. 获取班级所有学生ID
        List<Long> classStudentIds = activeStudentsMapper.getStudentIdsByClassId(classId);
        // 2. 按学生ID分组，获取参加考试的学生，每个学生只保留最新的一条记录
        List<ExamAnalyzeResult> classResultMap = studentLastResult(examId, classStudentIds);

        // 4. 获取年级下所有班级
        List<ActiveStudentsEntity> classesInGrade = activeStudentsMapper.getClassIdByGradeId(gradeId);

        // 5. 将年级下所有班级的学生ID分组
        Map<Long, List<Long>> studentsByClass = new HashMap<>();
        for (ActiveStudentsEntity classInfo : classesInGrade) {
            Long currentClassId = classInfo.getClassId();
            if (!studentsByClass.containsKey(currentClassId)) {
                List<Long> studentsInClass = activeStudentsMapper.getStudentIdsByClassId(currentClassId);
                studentsByClass.put(currentClassId, studentsInClass);
            }
        }

        // 6. 获取年级下所有班级的最新考试分析结果
        List<ExamAnalyzeResult> gradeResults = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> entry : studentsByClass.entrySet()) {
            List<ExamAnalyzeResult> results = studentLastResult(examId, entry.getValue());
            if (results.size() >= 10) {
                gradeResults.addAll(results);
            }

        }

        // 7. 获取或创建班级考试统计记录
        ClassExamStatistics classExamStatistics = classExamStatisticsMapper.selectByClassIdAndExamId(classId, examId);
        if (classExamStatistics == null) {
            classExamStatistics = new ClassExamStatistics();
            classExamStatistics.setClassId(classId);
            classExamStatistics.setGradeId(gradeId);
            classExamStatistics.setExamId(examId);
            classExamStatistics.setCreatedAt(new Date());
            classExamStatistics.setUpdatedAt(new Date());

            classExamStatisticsMapper.insert(classExamStatistics);
        }

        return new ExamStatisticsData(classResultMap, gradeResults, classExamStatistics);
    }

    /**
     * 按学生ID分组，每个学生只保留最新的一条记录
     *
     * @return 最新的一条记录
     */
    private List<ExamAnalyzeResult> studentLastResult(UUID examId, List<Long> classStudentIds) {
        //从exam_analyze_result表中获取最新的分析结果
        List<ExamAnalyzeResult> allResults = examAnalyzeResultMapper.selectList(new LambdaQueryWrapper<ExamAnalyzeResult>()
                .eq(ExamAnalyzeResult::getExamId, examId)
                .in(ExamAnalyzeResult::getStudentId, classStudentIds)
                .eq(ExamAnalyzeResult::getResult, ExamAnalyzeResultEnum.FINISHED)
                .isNull(ExamAnalyzeResult::getDeletedAt)
                .orderByDesc(ExamAnalyzeResult::getUpdatedAt));
        //（每个学生只取最新的一条记录）
        Map<Long, ExamAnalyzeResult> latestResultMap = allResults.stream()
                .collect(Collectors.toMap(
                        ExamAnalyzeResult::getStudentId,
                        result -> result,
                        (existing, replacement) ->
                                replacement.getUpdatedAt().after(existing.getUpdatedAt()) ? replacement : existing
                ));
        return new ArrayList<>(latestResultMap.values());
    }

    /**
     * 计算班级平均正确率
     *
     * @param results 分析结果列表
     * @return 平均正确率
     */
    private BigDecimal calculateAverageCorrectRate(List<ExamAnalyzeResult> results) {
        BigDecimal totalCorrectRate = BigDecimal.ZERO;

        for (ExamAnalyzeResult result : results) {
            String correctRateJson = result.getCorrectRate();
            try {
                JsonNode jsonNode = new ObjectMapper().readTree(correctRateJson);
                String percentileValue = jsonNode.get("percentile").asText();
                if (percentileValue.endsWith("%")) {
                    percentileValue = percentileValue.substring(0, percentileValue.length() - 1);
                }
                BigDecimal studentCorrectRate = new BigDecimal(percentileValue);
                totalCorrectRate = totalCorrectRate.add(studentCorrectRate);
            } catch (Exception e) {
                log.error("Error processing correct rate for student {}: {}", result.getStudentId(), e.getMessage(), e);
            }
        }

        return totalCorrectRate.divide(BigDecimal.valueOf(results.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算每道题目和每个知识点班级平均正确率
     */
    private BigDecimal calculateQuestionAndKnowledgePointClassCorrectRate(long correctCount, long totalCount) {
        if (totalCount <= 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(correctCount)
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    @Override
    public ClassExamStatistics getClassExamStatistics(Long classId, UUID examId) {
        return classExamStatisticsMapper.selectByClassIdAndExamId(classId, examId);
    }

    @Override
    public ClassExamQuestionStatistics getClassExamQuestionStatistics(Long classId, UUID examId, UUID questionId) {
        return classExamQuestionStatisticsMapper.selectByClassIdExamIdAndQuestionId(classId, examId, questionId);
    }

    @Override
    public ClassKnowledgePointStatistics getClassKnowledgePointStatistics(Long classId, UUID examId, UUID knowledgePointId) {
        return classKnowledgePointStatisticsMapper.selectByClassIdExamIdAndKnowledgePointId(classId, examId, knowledgePointId);
    }

    @Override
    public String generateExamSession(UUID examId) {
        // 使用试卷ID和当前时间戳生成唯一的考试场次ID
        return examId.toString().substring(0, 8) + "-" + Instant.now().getEpochSecond();
    }

    /**
     * 计算题目正确率（支持班级和年级）
     *
     * @param examId              考试ID
     * @param analyzeResults      考试分析结果列表
     * @param classExamStatistics 班级考试统计
     * @param type                类型："class" 或 "grade"
     */
    private void calculateQuestionCorrectRate(UUID examId,
                                              List<ExamAnalyzeResult> analyzeResults,
                                              ClassExamStatistics classExamStatistics,
                                              String type) {
        if (CollUtil.isEmpty(analyzeResults)) {
            return;
        }

        Long classId = classExamStatistics.getClassId();
        Long gradeId = classExamStatistics.getGradeId();

        // 1. 根据考试分析报告获取班级里所有学生的个人考试ID列表
        List<Long> personalExamIds = getClassExamPersonalExamIds(analyzeResults);
        // 2. 根据学生考试id批量获取所有学生的所有题目答题记录
        List<PersonalExamQuestion> allQuestions = getClassExamQuestionList(personalExamIds);

        if (CollUtil.isEmpty(allQuestions)) {
            log.warn("No question records found for {} in exam {}", type, examId);
            return;
        }

        // 3. 按题目ID分组
        Map<UUID, List<PersonalExamQuestion>> questionGroups = allQuestions.stream()
                .collect(Collectors.groupingBy(PersonalExamQuestion::getQuestionId));

        // 存储所有题目的正确率，用于计算整体平均正确率
        List<BigDecimal> allQuestionCorrectRates = new ArrayList<>();

        // 5. 处理每道题目，计算正确率
        for (Map.Entry<UUID, List<PersonalExamQuestion>> entry : questionGroups.entrySet()) {
            UUID questionId = entry.getKey();
            List<PersonalExamQuestion> questionRecords = entry.getValue();
            PersonalExamQuestion personalExamQuestion = questionRecords.get(0);
            // 计算该题目的正确率
            long totalAnswers = questionRecords.size();
            long correctAnswers = questionRecords.stream()
                    .filter(q -> PersonalExamQuestionResultEnum.correct.equals(q.getResult()))
                    .count();

            BigDecimal correctRate = calculateQuestionAndKnowledgePointClassCorrectRate(correctAnswers, totalAnswers);

            if ("class".equals(type)) {
                // 添加到题目正确率列表
                allQuestionCorrectRates.add(correctRate);
                updateClassStatistics(examId, classExamStatistics, classId, questionId, gradeId, (int) correctAnswers, correctRate, personalExamQuestion.getSortNo());
            } else if ("grade".equals(type)) {
                ClassExamStatistics classExamStatisticsData = classExamStatisticsMapper.selectByClassIdAndExamId(classId, examId);
                if (classExamStatisticsData.getStudentCount() >= 10) {
                    // 年级正确率只计算超过十个人做的班级，添加到题目正确率列表
                    allQuestionCorrectRates.add(correctRate);
                    updateGradeStatistics(examId, gradeId, questionId, correctRate);
                }
            }
        }

        // 计算整体正确率（基于题目的平均值）
        if (!allQuestionCorrectRates.isEmpty()) {
            BigDecimal overallCorrectRate = calculateOverallCorrectRateByQuestions(allQuestionCorrectRates);
            if ("class".equals(type)) {
                // 更新班级整体正确率
                classExamStatistics.setCorrectRate(overallCorrectRate);
                classExamStatistics.setStudentCount(analyzeResults.size());
                classExamStatisticsMapper.updateById(classExamStatistics);
            } else if ("grade".equals(type)) {
                // 更新年级整体正确率
                ClassExamStatistics classExamStatisticsData = classExamStatisticsMapper.selectByClassIdAndExamId(classId, examId);
                if (classExamStatisticsData.getStudentCount() >= 10) {
                    updateExamGradeStatistics(examId, gradeId, overallCorrectRate);
                }
            }
        }
    }

    /**
     * 根据学生考试id批量获取所有学生的所有题目答题记录
     */
    private List<PersonalExamQuestion> getClassExamQuestionList(List<Long> personalExamIds) {
        return personalExamQuestionMapper.selectList(
                Wrappers.lambdaQuery(PersonalExamQuestion.class)
                        .in(PersonalExamQuestion::getPersonalExamId, personalExamIds)
                        .isNull(PersonalExamQuestion::getDeletedAt));
    }

    /**
     * 根据考试分析报告获取班级里所有学生的个人考试ID列表
     */
    private static List<Long> getClassExamPersonalExamIds(List<ExamAnalyzeResult> analyzeResults) {
        return analyzeResults.stream()
                .map(ExamAnalyzeResult::getPersonalExamId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 基于题目正确率计算整体正确率
     * 计算方法：所有题目正确率之和 / 题目总数
     *
     * @param questionCorrectRates 所有题目的正确率列表
     * @return 整体正确率
     */
    private BigDecimal calculateOverallCorrectRateByQuestions(List<BigDecimal> questionCorrectRates) {
        if (questionCorrectRates == null || questionCorrectRates.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal sum = questionCorrectRates.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return sum.divide(BigDecimal.valueOf(questionCorrectRates.size()), 2, RoundingMode.HALF_UP);
    }

    private void updateGradeStatistics(UUID examId, Long gradeId, UUID questionId, BigDecimal correctRate) {
        // 更新该年级下所有班级的题目统计中的年级正确率
        List<ClassExamQuestionStatistics> allClassQuestionStats = classExamQuestionStatisticsMapper.selectList(
                Wrappers.lambdaQuery(ClassExamQuestionStatistics.class)
                        .eq(ClassExamQuestionStatistics::getGradeId, gradeId)
                        .eq(ClassExamQuestionStatistics::getExamId, examId)
                        .eq(ClassExamQuestionStatistics::getQuestionId, questionId)
                        .isNull(ClassExamQuestionStatistics::getDeletedAt));

        for (ClassExamQuestionStatistics stats : allClassQuestionStats) {
            stats.setGradeCorrectRate(correctRate);
            stats.setUpdatedAt(new Date());
            classExamQuestionStatisticsMapper.updateById(stats);
        }
    }

    private void updateExamGradeStatistics(UUID examId, Long gradeId, BigDecimal correctRate) {
        // 更新该年级下所有班级的题目统计中的年级正确率
        List<ClassExamStatistics> allClassStats = classExamStatisticsMapper.selectList(
                Wrappers.lambdaQuery(ClassExamStatistics.class)
                        .eq(ClassExamStatistics::getGradeId, gradeId)
                        .eq(ClassExamStatistics::getExamId, examId)
                        .isNull(ClassExamStatistics::getDeletedAt));

        for (ClassExamStatistics stats : allClassStats) {
            stats.setGradeCorrectRate(correctRate);
            stats.setUpdatedAt(new Date());
            classExamStatisticsMapper.updateById(stats);
        }
    }

    private void updateClassStatistics(UUID examId, ClassExamStatistics classExamStatistics,
                                       Long classId, UUID questionId, Long gradeId, int correctAnswers,
                                       BigDecimal correctRate, Integer sortNo) {
        // 6. 查询或创建题目统计
        ClassExamQuestionStatistics questionStatistics = classExamQuestionStatisticsMapper.selectOne(
                Wrappers.lambdaQuery(ClassExamQuestionStatistics.class)
                        .eq(ClassExamQuestionStatistics::getClassId, classId)
                        .eq(ClassExamQuestionStatistics::getExamId, examId)
                        .eq(ClassExamQuestionStatistics::getQuestionId, questionId)
                        .isNull(ClassExamQuestionStatistics::getDeletedAt));

        if (questionStatistics == null) {
            // 创建题目统计
            questionStatistics = new ClassExamQuestionStatistics();
            questionStatistics.setClassId(classId);
            questionStatistics.setGradeId(gradeId);
            questionStatistics.setExamId(examId);
            questionStatistics.setSortNo(sortNo);
            questionStatistics.setQuestionId(questionId);
            questionStatistics.setClassExamId(classExamStatistics.getId());
            questionStatistics.setCorrectCount(correctAnswers);
            questionStatistics.setCorrectRate(correctRate);
            questionStatistics.setCreatedAt(new Date());
            questionStatistics.setUpdatedAt(new Date());

            classExamQuestionStatisticsMapper.insert(questionStatistics);
        } else {
            // 更新现有的题目统计
            questionStatistics.setCorrectCount(correctAnswers);
            questionStatistics.setCorrectRate(correctRate);
            questionStatistics.setUpdatedAt(new Date());
            classExamQuestionStatisticsMapper.updateById(questionStatistics);
        }
    }

    /**
     * 考试统计数据类，用于封装获取的数据
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class ExamStatisticsData {
        private List<ExamAnalyzeResult> classAnalyzeResultList;
        private List<ExamAnalyzeResult> gradeAnalyzeResultList;
        private ClassExamStatistics classExamStatistics;
    }

    @Override
    public void updateClassKnowledgePointStatistics(ExamStatisticsData statsData,
                                                    List<ExamAnalyzeResult> analyzeResults,
                                                    Long classId, Long gradeId,
                                                    UUID examId, String type) {
        ClassExamStatistics classExamStatistics = statsData.getClassExamStatistics();

        // 获取本场考试所有知识点
        List<QuestionKnowledgePoint> allKnowledgePoints = questionKnowledgePointMapper.selectList(new LambdaQueryWrapper<QuestionKnowledgePoint>()
                .eq(QuestionKnowledgePoint::getExamId, examId)
                .isNull(QuestionKnowledgePoint::getDeletedAt));

        if (CollUtil.isEmpty(allKnowledgePoints)) {
            return;
        }
        List<Long> studentIds = analyzeResults.stream().map(ExamAnalyzeResult::getStudentId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        List<KnowledgePointStatisticsVo> classKnowledgePointStatics = knowledgePointService.getClassKnowledgePointStatics(studentIds, null, examId);
        // 按知识点ID分组
        Map<UUID, List<QuestionKnowledgePoint>> knowledgePointMap = allKnowledgePoints.stream()
                .collect(Collectors.groupingBy(QuestionKnowledgePoint::getKnowledgePointId));

        // 获取学生答题记录
        Map<Long, List<PersonalExamQuestion>> studentQuestionMap = getStudentQuestionMap(analyzeResults);

        // 知识点掌握率阈值（可以考虑将此值配置化）
        final double MASTERY_THRESHOLD = 0.7;
        int totalStudents = analyzeResults.size();

        // 遍历每个知识点，计算班级掌握情况
        for (Map.Entry<UUID, List<QuestionKnowledgePoint>> entry : knowledgePointMap.entrySet()) {
            UUID knowledgePointId = entry.getKey();
            List<QuestionKnowledgePoint> pointQuestions = entry.getValue();

            // 获取该知识点涉及的所有题目ID
            Set<UUID> pointQuestionIds = getQuestionIdsForKnowledgePoint(pointQuestions);
            if (pointQuestionIds.isEmpty()) {
                continue;
            }
            KnowledgePointStatisticsVo classTotal = classKnowledgePointStatics.stream().filter(vo ->
                    vo.getKnowledgePointId().equals(knowledgePointId)).findFirst().orElse(null);
            Integer classMasteredCount = classTotal.getTotalCorrectCount();
            Integer classTotalKnowledgePoints = classTotal.getTotalQuestionCount();
            classMasteredCount = classMasteredCount == null ? 0 : classMasteredCount;
            classTotalKnowledgePoints = classTotalKnowledgePoints == null ? 0 : classTotalKnowledgePoints;
            BigDecimal classMasteredCountPercent;
            if (classTotalKnowledgePoints == 0 || classMasteredCount == 0) {
                classMasteredCountPercent = BigDecimal.ZERO;
            } else {
                classMasteredCountPercent = BigDecimal.valueOf(Math.round((double) classMasteredCount / classTotalKnowledgePoints * 100));
            }

            // 计算掌握该知识点的学生数量
            int masteredCount = calculateMasteredStudentCount(analyzeResults, studentQuestionMap,
                    pointQuestionIds, MASTERY_THRESHOLD);

            // 计算知识点掌握率
            BigDecimal correctRate = calculateQuestionAndKnowledgePointClassCorrectRate(masteredCount, totalStudents);
            if ("class".equals(type)) {
                // 计算知识点掌握率并更新统计数据
                updateKnowledgePointStatistics(classId, gradeId, examId, classExamStatistics,
                        knowledgePointId, classMasteredCount, classMasteredCountPercent);
            } else if ("grade".equals(type)) {
                ClassExamStatistics classExamStatisticsData = classExamStatisticsMapper.selectByClassIdAndExamId(classId, examId);
                if (classExamStatisticsData.getStudentCount() >= 10) {
                    updateGradeKnowledgePointStatistics(examId, gradeId, knowledgePointId, classMasteredCountPercent);
                }
            }
        }
    }

    private void updateGradeKnowledgePointStatistics(UUID examId, Long gradeId, UUID questionId, BigDecimal correctRate) {
        // 更新该年级下所有班级的题目统计中的年级正确率
        List<ClassKnowledgePointStatistics> allClassQuestionStats = classKnowledgePointStatisticsMapper.selectList(
                Wrappers.lambdaQuery(ClassKnowledgePointStatistics.class)
                        .eq(ClassKnowledgePointStatistics::getGradeId, gradeId)
                        .eq(ClassKnowledgePointStatistics::getExamId, examId)
                        .eq(ClassKnowledgePointStatistics::getKnowledgePointId, questionId)
                        .isNull(ClassKnowledgePointStatistics::getDeletedAt));

        for (ClassKnowledgePointStatistics stats : allClassQuestionStats) {
            stats.setGradeCorrectRate(correctRate);
            stats.setUpdatedAt(new Date());
            classKnowledgePointStatisticsMapper.updateById(stats);
        }
    }
}
