package com.joinus.study.service;

import com.joinus.study.model.dto.BusinessSubscriptionDto;
import com.joinus.study.model.result.MathH5TokenResult;
import com.joinus.study.model.param.SmsLoginParam;
import com.joinus.study.model.param.SmsParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface QylService {

    String sendSms(SmsParam smsParam, HttpServletRequest  request);


    MathH5TokenResult login(SmsLoginParam smsLoginParam, HttpServletResponse response);

    String  logout(HttpServletRequest request);

    boolean queryStudentMathMembership(Long studentId, Long parentId);
    // 学生开通数学会员
    boolean studentOpenProduct(Long studentId, String telNum, Integer businessDays);
    // 查询学生开通的产品信息
    BusinessSubscriptionDto getStudentOpenProductInfo(Integer subjectType, Long studentId, Long parentId);
}
