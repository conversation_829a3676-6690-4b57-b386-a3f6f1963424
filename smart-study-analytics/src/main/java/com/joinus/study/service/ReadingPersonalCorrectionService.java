package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.param.ReadingErrorCorrectingParam;
import com.joinus.study.model.param.ReadingPersonalCorrectionPageParam;
import com.joinus.study.model.vo.ReadingPersonalCorrectionVO;

public interface ReadingPersonalCorrectionService {
    /**
     * 新增-APP
     *
     * @param param
     * @return
     */
    void create(ReadingErrorCorrectingParam param);

    /**
     * 分页-管理后台
     *
     * @param pageParam
     * @return
     */
    Page<ReadingPersonalCorrectionVO> pages(ReadingPersonalCorrectionPageParam pageParam);
}
