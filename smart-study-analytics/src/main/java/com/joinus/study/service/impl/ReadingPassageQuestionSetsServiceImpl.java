package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPassageQuestionSetsMapper;
import com.joinus.study.mapper.ReadingPassagesMapper;
import com.joinus.study.model.entity.ReadingPassageQuestionSets;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.enums.ReadingDimension;
import com.joinus.study.model.enums.ReadingQuestionTypeEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.ReadingPassageQuestionSetsService;
import com.joinus.study.utils.DataUtil;
import com.joinus.study.utils.HtmlToPdfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;

@Service
@Slf4j
public class ReadingPassageQuestionSetsServiceImpl extends ServiceImpl<ReadingPassageQuestionSetsMapper, ReadingPassageQuestionSets>
        implements ReadingPassageQuestionSetsService {

    @Value("${reading-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net/api/edu-knowledge-hub}")
    private String readingHubHostUrl;
    @Value("${question.set.html.host.url:https://pdf-generator-server.uat.static.ijx.ink}")
    private String QuestionSetHtmlHostUrl;
    @Value("${question.set.html.Api:/pdf/generate/chinese-paper-html-preview?all_questions=true&id=***}")
    private String QuestionSetHtmlApi;

    @Resource
    private ReadingPassagesMapper readingPassagesMapper;

    @Override
    public Page<ReadingPassageQuestionSetsVo> queryQuestionSetsList(ReadingPassageQuestionSetsParam param) {
        CommonResponse.ERROR.assertNotNull(param.getPassageId(), "文章id不能为空");
        Page<ReadingPassageQuestionSetsVo> page = new Page<>(param.getCurrent(), param.getSize());
        List<ReadingPassageQuestionSetsVo> questionSetsList = baseMapper.queryQuestionSetsList(page, param.getPassageId());
        for (ReadingPassageQuestionSetsVo questionSetsVo : questionSetsList){
            // 转换题型名称
            String[] types = questionSetsVo.getQuestionTypes().split("/");
            List<String> typeNames = new ArrayList<>();
            for (String type : types) {
                String questionType = ReadingQuestionTypeEnum.getByName(type.trim());
                if (questionType != null) {
                    typeNames.add(questionType);
                }
            }
            questionSetsVo.setQuestionTypes(String.join("/", typeNames)); // 重新设置拼接后的值
        }
        page.setRecords(questionSetsList);
        return page;
    }

    @Override
    public Boolean questionSetsEdit(ReadingQuestionSetEditParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getQuestionList(), "题目列表不能为空");
        //恢复答案和题干为库中json字符串格式
        for (ReadingQuestionSetItemEditParam question : param.getQuestionList()){
            question.setContent(DataUtil.formatQuestionContent(question.getContent(), question.getOptions()));
            question.setAnswer(DataUtil.formatAnswer(question.getAnswer(),question.getPrettyAnswer()));
        }
        return questionSetsEditRequest(param);
    }

    @Override
    public Boolean operateEnabled(ReadingPassageQuestionSetsEnabledParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        List<UUID> paramList = new ArrayList<>();
        paramList.add(param.getQuestionSetId());
        if(param.getIsEnabled() == 1){
            return operateEnabledRequest(paramList);
        }
        return operateDisableRequest(paramList);
    }

    @Override
    public Boolean operateAudit(ReadingPassageQuestionSetsAuditParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        List<UUID> paramList = new ArrayList<>();
        paramList.add(param.getQuestionSetId());
        if(param.getIsAudit() == 1){
            return operatePassRequest(paramList);
        }
        return operateNoPassRequest(paramList);
    }

    @Override
    public Boolean questionSetsDelete(ReadingPassageQuestionSetsParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        List<UUID> paramList = new ArrayList<>();
        paramList.add(param.getQuestionSetId());
        return questionSetsDeleteRequest(paramList);
    }

    @Override
    public ReadingQuestionSetsViewVo questionSetsView(ReadingPassageQuestionSetsParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        ReadingPassageQuestionSets questionSets = baseMapper.selectById(param.getQuestionSetId());
        log.info("questionSetsView questionSets：{}", questionSets);
        CommonResponse.ERROR.assertNotNull(questionSets, "参数套题id异常，找不到套题信息");
        ReadingQuestionSetsViewVo passages = readingPassagesMapper.getPassageInfo(questionSets.getPassageId());
        log.info("questionSetsView passages：{}", passages);
        CommonResponse.ERROR.assertNotNull(passages, "套题信息异常，找不到对应文章信息");
        ReadingQuestionSetsViewVo viewVo = new ReadingQuestionSetsViewVo();
        viewVo.setId(questionSets.getId());
        viewVo.setName(questionSets.getName());
        viewVo.setGenre(GenreEnum.getByName(passages.getGenre()));
        viewVo.setTitle(passages.getTitle());
        viewVo.setContent(passages.getContent());
        viewVo.setPassageId(questionSets.getPassageId());
        viewVo.setPassageIsAudit(passages.getPassageIsAudit());
        viewVo.setGradeName(passages.getGradeName());
        viewVo.setUnitName(passages.getUnitName());
        viewVo.setSemester(passages.getSemester());
        viewVo.setSource(passages.getSource());
        viewVo.setCreatedAt(passages.getCreatedAt());
        viewVo.setIsAudit(questionSets.getIsAudit());
        viewVo.setIsEnabled(questionSets.getIsEnabled());
        viewVo.setTextbook(passages.getTextbook());
        viewVo.setGrade(passages.getGrade());
        if (ObjectUtil.isNotEmpty(passages.getGrade())){
            viewVo.setPeriod(this.getPeriodByGrade(passages.getGrade()));
        }
        List<ReadingQuestionSetsItemViewVo> questionList = baseMapper.getQuestionSetsItemInfo(param.getQuestionSetId());
        //用流查找审核状态是2的题目数量
        boolean isAudit = questionList.stream().anyMatch(item -> item.getIsAudit() == 2);
        for (ReadingQuestionSetsItemViewVo item : questionList){
            item.setQuestionType(ReadingQuestionTypeEnum.getByName(item.getQuestionType()));
            //封装题目选项和答案
            Map<String, Object> question = DataUtil.processQuestionContent(item.getContent());
            item.setContent(question.get("questionContent").toString());
            item.setOptions((JSONArray)question.get("options"));
            JSONObject answerJson = JSONUtil.parseObj(item.getAnswer());
            item.setPrettyAnswer(answerJson.getStr("prettyAnswer", ""));
            item.setAnswer(DataUtil.parseAnswer(item.getAnswer()));
            //封装知识点
            List<Map<String, Object>> knowledgePoints = baseMapper.getKnowledgePoints(item.getId());
            item.setKnowledgePoints(knowledgePoints);
            List<QuestionAuditResultVo> auditResult = getQuestionAuditResult(item.getId());
            item.setAuditResult(auditResult);
        }
        viewVo.setQuestionList(questionList);
        return viewVo;
    }

    public boolean operateEnabledRequest(List<UUID> param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets/enable";
        log.info("operateEnabledRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.put(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("operateEnabledRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("operateEnabledRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateEnabledRequest-异常：{}"+ e.getMessage());
        }
    }

    public boolean operateDisableRequest(List<UUID> param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets/disable";
        log.info("operateUnEnabledRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.put(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("operateUnEnabledRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("operateUnEnabledRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateUnEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateUnEnabledRequest-异常：{}"+ e.getMessage());
        }
    }

    public boolean operatePassRequest(List<UUID> param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets/audit/pass";
        log.info("operateAuditRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.put(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("operateAuditRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("operateAuditRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateEnabledRequest-异常：{}"+ e.getMessage());
        }
    }

    public boolean operateNoPassRequest(List<UUID> param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets/audit/no-pass";
        log.info("operateUnAuditRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.put(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("operateUnAuditRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("operateUnAuditRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateUnAuditRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateUnAuditRequest-异常：{}"+ e.getMessage());
        }
    }

    public boolean questionSetsDeleteRequest(List<UUID> param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets";
        log.info("questionSetsDeleteRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.delete(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute()// 执行请求
                    .body(); // 获取响应体
            log.info("questionSetsDeleteRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("questionSetsDeleteRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateEnabledRequest-异常：{}"+ e.getMessage());
        }
    }

    public boolean questionSetsEditRequest(ReadingQuestionSetEditParam param) {
        String url = readingHubHostUrl+"/reading-passages-question-sets/update";
        log.info("questionSetsEditRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.put(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .execute()// 执行请求
                    .body(); // 获取响应体
            log.info("questionSetsEditRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("questionSetsEditRequest 请求成功：{}", result);
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            log.info("operateEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateEnabledRequest-异常：{}"+ e.getMessage());
        }
    }

    public Integer getQuestionSetsCount(UUID passageId) {
        CommonResponse.ERROR.assertNotNull(passageId, "文章id不能为空");

        Long resCount = count(Wrappers.<ReadingPassageQuestionSets>lambdaQuery()
                .eq(ReadingPassageQuestionSets::getPassageId, passageId)
                .isNull(ReadingPassageQuestionSets::getDeletedAt));

        return ObjectUtil.isNotEmpty(resCount) ? resCount.intValue() : 0;
    }

    @Override
    public String generateQuestionSetHtml(ReadingPassageQuestionSetsParam param,Integer printRange) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionSetId(), "套题id不能为空");
        ReadingQuestionSetsViewVo setsViewVo = this.questionSetsView(param);
        try {
            // 获取html模板
            String htmlResourcePath = "/static/reading-comprehension.html";
            InputStream htmlStream = HtmlToPdfUtil.class.getResourceAsStream(htmlResourcePath);
            if (htmlStream == null) {
                throw new BaseException("获取html模板错误" + htmlResourcePath);
            }
            String html = new String(htmlStream.readAllBytes(), "UTF-8");
            html = html.replace("PASSAGES_TITLE", setsViewVo.getTitle());
            if (printRange < 2) { // 文章题目 0 1
                String content = "";
                if(setsViewVo.getContent().contains("\n\n")){
                    String[] split = setsViewVo.getContent().split("\n\n");
                    for(String str : split){
                        String spanStr = "<p>" + str + "</p>";
                        content = content + spanStr;
                    }
                }else{
                    content = "<p>" + setsViewVo.getContent() + "</p>";
                }
                html = html.replace("PASSAGES_CONTENT", content);
            } else {
                html = html.replace("PASSAGES_CONTENT", "");
            }
            // 获取题目及答案解析
            List<ReadingQuestionSetsItemViewVo> questionList = setsViewVo.getQuestionList();
            StringBuffer questionHtml = new StringBuffer(""); // 题目
            StringBuffer answerHtml = new StringBuffer(""); // 答案
            int i = 1; // 题号
            for (ReadingQuestionSetsItemViewVo question : questionList) {
//                this.doSetOptions(question); // 格式化设置选项
                if(printRange < 2){ // 设置题目
                    questionHtml.append(String.format("<div class=\"question\">%d : %s</div>", i, question.getContent()));
                    if (CollectionUtils.isEmpty(question.getOptions())) {
                        questionHtml.append("<div class=\"question-answer\"></div>");
                    } else {
                        if(question.getQuestionType().equals(ReadingQuestionTypeEnum.CHOICE.getQuestionTypeValue())){
                            for(int o=0;o<question.getOptions().size();o++){
                                cn.hutool.json.JSONObject object = question.getOptions().getJSONObject(o);
                                questionHtml.append("<li class=\"option-item\">"+String.valueOf(object.get("key")) + ":" + String.valueOf(object.get("value")) +"</li>");
                            }
                        }
                    }
                }
                if (printRange !=1 ) {
                    String answerStr = "<div class=\"answer\">%d： %s </div>\n";
                    String analysisStr = "<div class=\"answer\"> %d 解析： %s </div>";
                    if (printRange == 0 || printRange == 2) {
                        if(question.getAnswer().indexOf("✓")>-1){
                            question.setAnswer(question.getAnswer().replace("✓", "√"));
                        }
                        answerHtml.append(String.format(answerStr, i, question.getAnswer()));
                    }
                    if (printRange == 0 || printRange == 3) {
                        answerHtml.append(String.format(analysisStr, i, question.getAnswerContent()));
                    }
                }
                i++;
            }
            html = html.replace("QUESTION_CONTENT", questionHtml);
            if (StringUtils.isNotBlank(answerHtml.toString())) {
                html = html.replace("ANSWER_CONTENT", answerHtml);
            } else {
                html = html.replace("ANSWER_CONTENT", "");
            }
//            return html.replaceAll("cloze:", "______");// html 内容
            return html.replaceAll("<cloze:", "______");// html 内容
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取html内容失败", e.getMessage());
            throw new BaseException("获取html内容失败" + e.getMessage());
        }
    }

    @Override
    public String generateQuestionSetHtmlNew(UUID questionSetId) {
        CommonResponse.ERROR.assertNotNull(questionSetId, "套题id不能为空");
        String url = QuestionSetHtmlHostUrl+QuestionSetHtmlApi;
        //把url 中***替换为questionSetId
        url = url.replace("***", questionSetId.toString());
        log.info("questionSetHtmlNew-url:{}- 参数：{}", url, questionSetId);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.get(url) // 使用put方法创建HttpRequest对象//请求参数
                    .execute()// 执行请求
                    .body(); // 获取响应体
            log.info("questionSetHtmlNew 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)&&result.contains("<!DOCTYPE html>")) {
                return result;
            }
        }catch (Exception e){
            log.info("operateEnabledRequest-异常：{}", e.getMessage());
            throw new RuntimeException("operateEnabledRequest-异常：{}"+ e.getMessage());
        }
        return  "";
    }

    /**
     * @description: 根据套题ID获取文章和题目
     * @author: lifengxu
     * @date: 2025/6/19 15:01
     */
    @Override
    public ReadingPersonalPassagesVo getQuestionsBySetsId(UUID setId) throws BaseException {
        // 根据套题ID, 获取文章信息
        ReadingPersonalPassagesVo result = baseMapper.getPassagesBySetsId(setId);
        if (result == null) {
            return null;
        }

        // 根据套题ID, 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questions = baseMapper.getQuestionsByParam(setId, null);
        if (CollUtil.isEmpty(questions)) {
            return null;
        }

        // 题目类型名称转换
        questions.forEach(item -> {
            item.setQuestionType(ReadingQuestionTypeEnum.getByName(item.getQuestionType()));
            if (StrUtil.isNotBlank(item.getOptionStr())) {
                item.setOptions(JSONUtil.parseArray(item.getOptionStr()));
            }
        });

        result.setQuestions(questions);
        result.setGenreName(GenreEnum.getByName(result.getGenre()));
        return result;
    }
    private String getPeriodByGrade(Integer grade){
        if(grade>6&&grade<10){
            return "初中";
        }
        if(grade>9){
            return "高中";
        }
        return "小学";
    }
    public List<QuestionAuditResultVo> getQuestionAuditResult(UUID questionId) {

        String url = readingHubHostUrl+"/reading/question/audit/ai/result?questionId="+questionId;
        log.info("questionSetsEditRequest-url:{}- 参数：{}", url, questionId);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.get(url) // 使用put方法创建HttpRequest对
                    .execute()// 执行请求
                    .body(); // 获取响应体
            log.info("questionAuditResult 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200&& ObjectUtil.isNotEmpty(jsonObject.get("data"))){
                    log.info("questionAuditResult 请求成功：{}", result);
                    // 返回结果处理

                    ObjectMapper mapper = new ObjectMapper();
                    mapper.registerModule(new JavaTimeModule()); // 注册Java时间模块处理LocalDateTime
                    try {
                        ChineseQuestionDimensionsVo questionDimensions = mapper.readValue(jsonObject.get("data").toString(), ChineseQuestionDimensionsVo.class);
                        //
                        List<QuestionAuditResultVo> auditResultVos = new ArrayList<>();
                        if (questionDimensions != null){
                            if ("completed".equals(questionDimensions.getLexicalDensityDifficultyStatus())){
                              QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                              questionAuditResultVo.setName(ReadingDimension.LEXICAL_DENSITY_DIFFICULTY.getDescription());
                              questionAuditResultVo.setResult(questionDimensions.getLexicalDensityDifficultyResult());
                              questionAuditResultVo.setReason(questionDimensions.getLexicalDensityDifficultyReason());
                              auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getSentenceComplexityStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.SENTENCE_COMPLEXITY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getSentenceComplexityResult());
                                questionAuditResultVo.setReason(questionDimensions.getSentenceComplexityReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getLogicalStructureHierarchyStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.LOGICAL_STRUCTURE_HIERARCHY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getLogicalStructureHierarchyResult());
                                questionAuditResultVo.setReason(questionDimensions.getLogicalStructureHierarchyReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getFactualAccuracyAuthenticityStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.FACTUAL_ACCURACY_AUTHENTICITY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getFactualAccuracyAuthenticityResult());
                                questionAuditResultVo.setReason(questionDimensions.getFactualAccuracyAuthenticityReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getParagraphingFormattingStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.PARAGRAPHING_FORMATTING.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getParagraphingFormattingResult());
                                questionAuditResultVo.setReason(questionDimensions.getParagraphingFormattingReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getRhetoricalDevicesStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.RHETORICAL_DEVICES.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getRhetoricalDevicesResult());
                                questionAuditResultVo.setReason(questionDimensions.getRhetoricalDevicesReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getContentTopicAppealStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.CONTENT_TOPIC_APPEAL.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getContentTopicAppealResult());
                                questionAuditResultVo.setReason(questionDimensions.getContentTopicAppealReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getBiasStereotypeAvoidanceStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.BIAS_STEREOTYPE_AVOIDANCE.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getBiasStereotypeAvoidanceResult());
                                questionAuditResultVo.setReason(questionDimensions.getBiasStereotypeAvoidanceReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getCulturalRelevanceSensitivityStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.CULTURAL_RELEVANCE_SENSITIVITY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getCulturalRelevanceSensitivityResult());
                                questionAuditResultVo.setReason(questionDimensions.getCulturalRelevanceSensitivityReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getValueOrientationStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.VALUE_ORIENTATION.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getValueOrientationResult());
                                questionAuditResultVo.setReason(questionDimensions.getValueOrientationReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getRelevanceTextualSupportStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.RELEVANCE_TEXTUAL_SUPPORT.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getRelevanceTextualSupportResult());
                                questionAuditResultVo.setReason(questionDimensions.getRelevanceTextualSupportReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getClarityAccuracyUnambiguityStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.CLARITY_ACCURACY_UNAMBIGUITY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getClarityAccuracyUnambiguityResult());
                                questionAuditResultVo.setReason(questionDimensions.getClarityAccuracyUnambiguityReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getCognitiveFitQuestionTypeStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.COGNITIVE_FIT_QUESTION_TYPE.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getCognitiveFitQuestionTypeResult());
                                questionAuditResultVo.setReason(questionDimensions.getCognitiveFitQuestionTypeReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getErrorFreeStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.ERROR_FREE.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getErrorFreeResult());
                                questionAuditResultVo.setReason(questionDimensions.getErrorFreeReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getNoDirectCluesEasyRetrievalStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.NO_DIRECT_CLUES_EASY_RETRIEVAL.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getNoDirectCluesEasyRetrievalResult());
                                questionAuditResultVo.setReason(questionDimensions.getNoDirectCluesEasyRetrievalReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getAnswerAccuracyUniquenessClarityStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.ANSWER_ACCURACY_UNIQUENESS_CLARITY.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getAnswerAccuracyUniquenessClarityResult());
                                questionAuditResultVo.setReason(questionDimensions.getAnswerAccuracyUniquenessClarityReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getExplanationPedagogicalValueStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.EXPLANATION_DEPT_ACCURACY_PEDAGOGICAL_VALUE.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getExplanationPedagogicalValueResult());
                                questionAuditResultVo.setReason(questionDimensions.getExplanationPedagogicalValueReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getTitleAppropriatenessStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.TITLE_APPROPRIATENESS.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getTitleAppropriatenessResult());
                                questionAuditResultVo.setReason(questionDimensions.getTitleAppropriatenessReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getComponentCompletenessStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.COMPONENT_COMPLETENESS.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getComponentCompletenessResult());
                                questionAuditResultVo.setReason(questionDimensions.getComponentCompletenessReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getFormatCorrectnessQuestionTypesStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.FORMAT_CORRECTNESS_QUESTION_TYPES.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getFormatCorrectnessQuestionTypesResult());
                                questionAuditResultVo.setReason(questionDimensions.getFormatCorrectnessQuestionTypesReason());
                                auditResultVos.add(questionAuditResultVo);
                            }
                            if ("completed".equals(questionDimensions.getNoAnswerLeakageStatus())){
                                QuestionAuditResultVo questionAuditResultVo = new QuestionAuditResultVo();
                                questionAuditResultVo.setName(ReadingDimension.NO_ANSWER_LEAKAGE.getDescription());
                                questionAuditResultVo.setResult(questionDimensions.getNoAnswerLeakageResult());
                                questionAuditResultVo.setReason(questionDimensions.getNoAnswerLeakageReason());
                                auditResultVos.add(questionAuditResultVo);
                            }

                        }
                        return auditResultVos;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            return null;
        }catch (Exception e){
            log.info("getQuestionAuditResult-异常：{}", e.getMessage());
            throw new RuntimeException("getQuestionAuditResult-异常：{}"+ e.getMessage());
        }

    }
}
