package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.annotation.NoRepeatSubmit;
import com.joinus.study.mapper.ActiveStudentsMapper;
import com.joinus.study.mapper.MathActivityStudentMapper;
import com.joinus.study.mapper.MathActivityWeekUnitStudentMapper;
import com.joinus.study.mapper.ActiveStudentsMapper;
import com.joinus.study.mapper.MathActivityStudentMapper;
import com.joinus.study.mapper.MathActivityWeekUnitStudentMapper;
import com.joinus.study.mapper.*;
import com.joinus.study.model.bo.QueryElectronicTextbookBo;
import com.joinus.study.model.dto.ActivityRefundHistory;
import com.joinus.study.model.dto.ExamSummerPlanListDto;
import com.joinus.study.model.dto.ParentDTO;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.entity.MathActivityWeekUnitStudent;
import com.joinus.study.model.entity.MathInvitation;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.entity.MathActivityWeekUnitStudent;
import com.joinus.study.model.dto.QuerySectionVideoDto;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.MathActivityFinishEnum;
import com.joinus.study.model.enums.MathActivityStudentFinishEnum;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.RedisUtil;
import com.joinus.study.service.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MathActivityStudentServiceImpl extends BaseServiceImpl<MathActivityStudentMapper, MathActivityStudent>
        implements MathActivityStudentService {
    @Resource
    private PayService payService;
    @Resource
    private MathActivityService mathActivityService;
    @Resource
    private MathActivityWeekUnitStudentService mathActivityWeekUnitStudentService;
    @Autowired
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;
    @Resource
    private MembershipService membershipService;
    @Resource
    private ActiveStudentsMapper activeStudentsMapper;
    @Resource
    private BasicBusinessService basicBusinessService;
    @Resource
    private RedisUtil redisUtil;
    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private MathInvitationService mathInvitationService;
    @Override
    public String activityPay(IpayPayRequest param) throws BaseException {
        log.info("mathActivityServiceImpl.activityPay param:{}", param);
        CommonResponse.ERROR.assertNotNull(param.getUniqueBusinessId(), "活动id不可为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不可为空");
        CommonResponse.ERROR.assertNotNull(param.getPayMethod(), "支付方式不可为空");
        CommonResponse.ERROR.assertNotNull(param.getMoney(), "支付金额不可为空");
        CommonResponse.ERROR.assertNotNull(param.getNotifyUrl(), "回调地址不可为空");
        if (param.getPayMethod().equals("304")) {
           CommonResponse.ERROR.assertNotNull(param.getPlatformType(), "设备类型不可为空");
        }
        MathActivityStudent activityStudent = baseMapper.selectById(Long.parseLong(param.getUniqueBusinessId()));
        CommonResponse.ERROR.assertNotNull(activityStudent, "学生选择活动错误");
        MathActivity activity = mathActivityService.getById(activityStudent.getActivityId());
        CommonResponse.ERROR.assertNotNull(activity, "活动不存在");
        CommonResponse.ERROR.assertIsTrue(param.getMoney().multiply(BigDecimal.valueOf(100)).equals(activity.getPrice()), "价格校验失败");
        String extraParam = param.getUniqueBusinessId()+"#"+param.getStudentId();
        param.setExtraParam(extraParam);
        param.setUniqueBusinessId("M"+param.getUniqueBusinessId()); //区分其他项目订单
        String invokePay = payService.invokePay(param);
        log.info("invokePay:{}", invokePay);
        activityStudent.setUpdatedAt(new Date());
        activityStudent.setPayMethod(param.getPayMethod());
        baseMapper.updateById(activityStudent);
        return invokePay;
    }


    @Override
    public String activityPayBack(IpayPayBackParam param) {
        Integer status = param.getStatus();
        if (status == 2) {
            String extraParam = param.getExtraParam();
            String orderNo = param.getOrderNo();
            String[] split = extraParam.split("#");
            long activityStudentId = Long.parseLong(split[0]);
            MathActivityStudent activityStudent = baseMapper.selectById(activityStudentId);
            activityStudent.setOrderNo(orderNo);
            activityStudent.setPaidAt(new Date());
            activityStudent.setFinishResult(MathActivityFinishEnum.NOT_FINISHED);
            baseMapper.updateById(activityStudent);
        } else {
            log.error("支付回调失败：{}", JSONObject.toJSONString(param));
        }
        return "success";
    }
    @Override
    public Boolean activityRefund(ActivityRefundParam activityRefundParam) {
        MathActivityStudent activityStudent = baseMapper.selectById(activityRefundParam.getActivitystudentId());
        CommonResponse.ERROR.assertNotNull(activityStudent, "该活动不存在！");
        String refundHistory = activityStudent.getRefundHistory();
        MathActivity activity = mathActivityService.getById(activityStudent.getActivityId());
        CommonResponse.ERROR.assertNotNull(activity, "该活动无效！");
        ActivityRefundHistory activityRefundHistory = new ActivityRefundHistory();
        String refundOrderNo = String.format("%s_%s", activityRefundParam.getType(), activityStudent.getOrderNo());
        if (refundHistory != null) {
            activityRefundHistory = JSONObject.parseObject(refundHistory, ActivityRefundHistory.class);
            if (activityRefundParam.getType().equals("REVIEW")) {
                if (activityRefundHistory.getReviewRefundOrderNo() != null) {
                    return true;
                }
                activityRefundHistory.setReviewRefundAt(new Date());
                activityRefundHistory.setReviewRefundOrderNo(refundOrderNo);
                activityRefundHistory.setReviewAmount(activity.getPrice());
            }
            if (activityRefundParam.getType().equals("PREVIEW")) {
                if (activityRefundHistory.getPreviewRefundOrderNo() != null) {
                    return true;
                }
                activityRefundHistory.setPreviewRefundAt(new Date());
                activityRefundHistory.setPreviewRefundOrderNo(refundOrderNo);
                activityRefundHistory.setPreviewAmount(activity.getPrice());
            }
        }
        IpayRefundParam refundParam = new IpayRefundParam();
        refundParam.setAmount(new BigDecimal(activity.getPrice()).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
        refundParam.setOrderNo(activityStudent.getOrderNo());
        refundParam.setRefundOrderNo(String.format("%s_%s", activityRefundParam.getType(), activityStudent.getOrderNo()));
        String refund = payService.invokeRefund(refundParam);
        if (refund != null) {
            JSONObject refundBack = JSONObject.parseObject(refund);
            Integer status = refundBack.getInteger("status");
            if (status == 2) {
                activityStudent.setRefundHistory(JSONObject.toJSONString(activityRefundHistory));
                baseMapper.updateById(activityStudent);
                return true;
            } else if (status == 1) {  // 退款中
                String query = payService.refundQuery(refundParam);
                JSONObject queryBack = JSONObject.parseObject(query);
                if(queryBack.getInteger("status") == 2){
                    activityStudent.setRefundHistory(JSONObject.toJSONString(activityRefundHistory));
                    baseMapper.updateById(activityStudent);
                    return true;
                }
            }
            log.error("退款失败:{}", refundBack);
        }
        return false;
    }

    @Override
    public MathActivityStudent getStudentCurrentActivity(Long studentId) {
        MathActivity activity = mathActivityService.getMathSummerHolidayActivity();
        CommonResponse.ERROR.assertNotNull(activity, "活动不存在");
        QueryWrapper<MathActivityStudent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activity.getId());
        queryWrapper.eq("student_id", studentId);
        queryWrapper.isNull("deleted_at");
        return baseMapper.selectOne(queryWrapper);
    }
    @Override
    public List<MathActivityStudentVo> getMathActivityStudentVo(MathActivityStudent mathActivityStudent) {
        return baseMapper.selectMathActivityStudentVo(mathActivityStudent);
    }

    @Override
    @SneakyThrows
    public MathActivityLearningPlanerToastVo getPersonalPlanerToast(Long studentId) {
        List<MathActivityStudent> activityStudents = lambdaQuery()
                .eq(MathActivityStudent::getStudentId, studentId)
                .isNull(MathActivityStudent::getDeletedAt)
                .list();
        if (CollUtil.isEmpty(activityStudents)) {
            throw new BaseException("该学生没有加入活动");
        }
        if (activityStudents.size() > 1) {
            throw new BaseException("该学生加入多个活动");
        }

        MathActivityLearningPlanerToastVo toastVo = MathActivityLearningPlanerToastVo.init();

        MathActivityLearningPlanerToastBo dbToastBo = MathActivityLearningPlanerToastBo.init();

        MathActivityStudent activityStudent = activityStudents.get(0);
        String toastHistory = activityStudent.getToastHistory();
        if (StrUtil.isNotBlank(toastHistory)) {
            dbToastBo = JSONUtil.toBean(toastHistory, MathActivityLearningPlanerToastBo.class);
        }
        if (!dbToastBo.isFirstIncomeToasted() && !activityStudent.getGrade().equals(7)) {
            toastVo.setFirstIncomeNeedToast(true);
        }
        if (!dbToastBo.isHalfActivityToasted() && CollUtil.toList(MathActivityFinishEnum.REVIEW_FINISHIED, MathActivityFinishEnum.PREVIEW_FINISHIED).contains(activityStudent.getFinishResult())) {
            toastVo.setHalfActivityNeedToast(true);
        }
        if (!dbToastBo.isAllActivityToasted() && MathActivityFinishEnum.ALL_FINISHIED == activityStudent.getFinishResult()) {
            toastVo.setAllActivityNeedToast(true);
        }
        return toastVo;
    }

    @Override
    public MathActivityLearningPlanerToastVo updatePersonalPlanerToast(Long studentId, MathActivityLearningPlanerToastParam param) {
        List<MathActivityStudent> activityStudents = lambdaQuery().eq(MathActivityStudent::getStudentId, studentId)
                //.isNotNull(MathActivityStudent::getOrderNo)
                .list();
        if (CollUtil.isEmpty(activityStudents)) {
            throw new BaseException("该学生没有加入活动");
        }
        if (activityStudents.size() > 1) {
            throw new BaseException("该学生加入多个活动");
        }

        MathActivityStudent activityStudent = activityStudents.get(0);
        String toastHistory = activityStudent.getToastHistory();
        MathActivityLearningPlanerToastBo dbToastBo = MathActivityLearningPlanerToastBo.init();
        if (StrUtil.isNotBlank(toastHistory)) {
            dbToastBo = JSONUtil.toBean(toastHistory, MathActivityLearningPlanerToastBo.class);
        }
        if (null != param.getFirstIncomeToasted()) {
            dbToastBo.setFirstIncomeToasted(param.getFirstIncomeToasted());
        }
        if (null != param.getHalfActivityToasted()) {
            dbToastBo.setHalfActivityToasted(param.getHalfActivityToasted());
        }
        if (null != param.getAllActivityToasted()) {
            dbToastBo.setAllActivityToasted(param.getAllActivityToasted());
        }
        MathActivityStudent updateActivityStudent = new MathActivityStudent();
        updateActivityStudent.setId(activityStudent.getId());
        updateActivityStudent.setToastHistory(JSONObject.toJSONString(dbToastBo));
        updateById(updateActivityStudent);
        return getPersonalPlanerToast(studentId);
    }


    @Override
    @NoRepeatSubmit(
            lockParams = {"mathActivityJoinusParam.studentId"},  // 锁定学生ID参数
            lockTime = 60  // 锁定60秒
    )
    public Long studentJoinActivity(MathActivityJoinusParam mathActivityJoinusParam) throws BaseException {
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getStudentId(), "学生ID不可为空");
        log.info("学生加入活动:{}", mathActivityJoinusParam);
        MathActivityStudent activityStudent = this.getStudentCurrentActivity(mathActivityJoinusParam.getStudentId());
        CommonResponse.ERROR.assertIsTrue(activityStudent == null,"请勿重复参加");
        activityStudent = new MathActivityStudent();
        activityStudent.setActivityId(mathActivityJoinusParam.getActivityId());
        activityStudent.setStudentId(mathActivityJoinusParam.getStudentId());
        activityStudent.setParentId(mathActivityJoinusParam.getParentId());
        activityStudent.setPublisher(mathActivityJoinusParam.getPublisher());
        if (activityStudent.getParentId() !=null){ // 青于蓝用户加入
            activityStudent.setParentId(mathActivityJoinusParam.getParentId());
            ParentDTO parent = basicBusinessService.getParentByParentId(activityStudent.getParentId());
            activityStudent.setTelephoneNumber(parent.getTelNum());
        }
        if(mathActivityJoinusParam.getParentPhone() != null){  // H5游客加入
            activityStudent.setTelephoneNumber(mathActivityJoinusParam.getParentPhone());
        }
        activityStudent.setGrade(mathActivityJoinusParam.getGrade());
        activityStudent.setCreatedAt(new Date());
        activityStudent.setFinishResult(MathActivityFinishEnum.NOT_FINISHED);
        boolean member = membershipService.isMathHolidayActivityMember(mathActivityJoinusParam.getStudentId(), mathActivityJoinusParam.getParentId());
        if (member) {
            activityStudent.setMembershipLevel(MathMemberLevelEnum.PAID);
        } else {
            activityStudent.setMembershipLevel(MathMemberLevelEnum.GIFT); // 默认赠送会员
        }
        //保存邀请记录
        if(mathActivityJoinusParam.getInviterId() !=null){
            MathInvitation param = new MathInvitation();
            param.setInviteeStudentId(activityStudent.getStudentId());
            param.setInviterStudentId(mathActivityJoinusParam.getInviterId());
            param.setInviteePhone(mathActivityJoinusParam.getParentPhone());
            param.setInviterPhone(mathActivityJoinusParam.getInviterPhone());
            mathInvitationService.mathInvitationAdd(param);
        }
        baseMapper.insert(activityStudent);
        // 保存学生待学习记录
        mathActivityWeekUnitStudentService.saveStudentWeekUnit(activityStudent);
        log.info("学生加入活动成功 :{}", activityStudent.getId());
        // 初始化后更新学生 Summer  planer 状态
        this.updateStudentSummerPlanStatus(mathActivityJoinusParam.getStudentId(), mathActivityJoinusParam.getActivityId());
        return activityStudent.getId();
    }

    @Override
    public MathActivityStudentStudyRecord getActivityStudentStudyRecord(Long activityStudentId) {
        MathActivityStudent mathActivityStudent = new MathActivityStudent();
        mathActivityStudent.setId(activityStudentId);
        final List<MathActivityStudentVo> activityStudentVos = this.getMathActivityStudentVo(mathActivityStudent);
        CommonResponse.ERROR.assertIsTrue(!CollectionUtil.isEmpty(activityStudentVos), "该学生未加入活动");
        MathActivityStudentVo activityStudent = activityStudentVos.get(0);
        MathActivityStudentStudyRecord studentStudyRecord = mathActivityWeekUnitStudentService
                .getMathActivityStudentStudyRecord(activityStudent.getActivityId(), activityStudent.getStudentId());
        studentStudyRecord.setActivityStudentId(activityStudentId);
        studentStudyRecord.setCreatedAt(activityStudent.getCreatedAt());
        studentStudyRecord.setStartTime(activityStudent.getStartTime());
        studentStudyRecord.setEndTime(activityStudent.getEndTime());
        studentStudyRecord.setTotalDays((int) DateUtil.betweenDay(activityStudent.getCreatedAt(), new Date(), true) + 1);
        ActiveStudentVo studentVo = activeStudentsMapper.getClassIdByStudentId(activityStudent.getStudentId());
        if (studentVo != null) {
            studentStudyRecord.setStudentId(studentVo.getStudentId());
            studentStudyRecord.setStudentName(studentVo.getStudentName());
        }
        return studentStudyRecord;
    }

    @Override
    public List<Map<String, Object>> selectSummerPlansStatistics(Long studentId,Long activityId) {
        List<Map<String, Object>> summerPlansStatistics = mathActivityWeekUnitStudentMapper.selectSummerPlansStatistics(studentId, activityId);
        return summerPlansStatistics;
    }

    @Override
    public List<ExamSummerPlanListDto> selectStudentSummerPlans(Long studentId, Integer weekSort,Long activityId) {
        List<ExamSummerPlanListDto> summerPlans = mathActivityWeekUnitStudentMapper.selectStudentSummerPlans(studentId, weekSort,activityId);
        /**
         * 已测试的单元，需查询单元的掌握度返回
         * 章节复习和测试也需要查询掌握度
         */
        if (summerPlans != null && summerPlans.size() > 0) {
            List<UUID> sectionIdList = new ArrayList<UUID>();
             summerPlans.stream().forEach(summerPlan -> {
                 if (CollUtil.isNotEmpty(summerPlan.getSectionList())) {
                     summerPlan.getSectionList().stream()
                             .filter(section -> section.getSectionId() != null)
                             .forEach(section -> {
                         sectionIdList.add(section.getSectionId());
                     });
                 }
             });

            Map<UUID, List<QuerySectionVideoDto>> sectionVideoMap = new HashMap<>();
            if (CollUtil.isNotEmpty(sectionIdList)) {
                List<QuerySectionVideoDto> sectionVideos = eduKnowledgeHubService.querySectionVideos(sectionIdList);
                sectionVideoMap = sectionVideos.stream()
                        .collect(Collectors.groupingBy(QuerySectionVideoDto::getSectionId,
                                Collectors.collectingAndThen(Collectors.toList(), list -> {
                                            list.sort(Comparator.comparing(QuerySectionVideoDto::getSortNo));
                                            return list;
                                        }
                                )));
            }

            Map<UUID, List<QuerySectionVideoDto>> finalSectionVideoMap = sectionVideoMap;
            summerPlans.forEach(summerPlan -> {
                summerPlan.setName(summerPlan.getName().replaceFirst("^(预习-|复习-)", "").replaceAll("-", "·"));
                summerPlan.getSectionList().forEach(section -> {
                    String[] split = summerPlan.getName().split("·");
                    section.setExamPrefixName("【暑假衔接】"+split[2]+split[0]+split[1]);
                    if (MathActivityStudentFinishEnum.FINISHIED.getValue().equals(section.getFinishResult())) {
                        if (section.getSectionId() != null) {
                            section.setSectionMasteryDegree(mathActivityWeekUnitStudentMapper.selectSectionMasteryDegree(section.getSectionId().toString(), studentId,null));
                        }
                        if (section.getSectionId() == null && section.getExamAnalyzeResultId() != null) {
                            section.setSectionMasteryDegree(mathActivityWeekUnitStudentMapper.selectSectionMasteryDegree(null, studentId, section.getExamAnalyzeResultId()));
                        }
                    }
                    if (MathActivityWeekUnitTypeEnum.COMPREHENSIVE_TEST.getValue().equals(section.getWeekUnitType())) {
                        //复习-七年级-下册-北师大版
                        //八年级上册综合测评
                        section.setComprehensiveName(split[0]+split[1]+"综合练习");
                        List<Map<String, Object>> maps = mathActivityWeekUnitStudentMapper.selectComprehensiveTestStatus(summerPlan.getWeekType(), studentId);
                        section.setCanExam(false);
                        if (CollectionUtil.isEmpty(maps)) {
                            section.setCanExam(true);
                        }
                    }
                    if (CollUtil.isNotEmpty(finalSectionVideoMap.get(section.getSectionId()))) {
                        section.setVideos(finalSectionVideoMap.getOrDefault(section.getSectionId(), List.of()));
                    }
                });
            });
        }
        return summerPlans;
    }

    @Override
    public void updateStudentSummerPlanStatus(Long studentId, Long activityId) {
        List<ExamSummerPlanListDto> summerPlans = mathActivityWeekUnitStudentMapper.selectStudentSummerPlans(studentId, null ,activityId);
        if (summerPlans != null && summerPlans.size() > 0) {
            summerPlans.forEach(summerPlan -> {
                summerPlan.getSectionList().forEach(section -> {
                    //未完成状态去查询完成度来更新状态
                    if (MathActivityStudentFinishEnum.NOT_FINISHED.getValue().equals(section.getFinishResult())) {
                        //单元完成状态
                        if (section.getSectionId() != null) {
                            Double aLong = mathActivityWeekUnitStudentMapper.selectSectionMasteryDegree(section.getSectionId().toString(), studentId, null);
                            if (aLong != null) {
                                MathActivityWeekUnitStudent mathActivityWeekUnitStudent = new MathActivityWeekUnitStudent();
                                mathActivityWeekUnitStudent.setFinishResult(MathActivityStudentFinishEnum.FINISHIED);
                                mathActivityWeekUnitStudent.setUpdatedAt(new Date());
                                mathActivityWeekUnitStudent.setId(section.getWeekUnitStudentId());
                                mathActivityWeekUnitStudentMapper.updateById(mathActivityWeekUnitStudent);
                            }
                        }
                    }
                });
            });
        }
    }


    // 更新会员状态
    @Override
    public void updateMembershipLevel(Long studentId, MathMemberLevelEnum membershipLevel) {
        MathActivityStudent currentActivity = this.getStudentCurrentActivity(studentId);
        if (null == currentActivity) {
            return;
        }
        if (membershipLevel.equals(currentActivity.getMembershipLevel())){
            return;
        }
        currentActivity.setMembershipLevel(membershipLevel);
        currentActivity.setUpdatedAt(new Date());
        baseMapper.updateById(currentActivity);
        ActiveStudentVo studentVo = activeStudentsMapper.getClassIdByStudentId(studentId);
        CommonResponse.ERROR.assertNotNull(studentVo, "查询不到学生信息");
        MathActivityWeekUnitStudent weekUnitStudent = new MathActivityWeekUnitStudent();
        weekUnitStudent.setUpdatedAt(new Date());
        weekUnitStudent.setUnlocked(true);
        if (MathMemberLevelEnum.PAID.equals(membershipLevel)
                || mathActivityWeekUnitStudentService.isGiftAll(studentVo.getSchoolId(), currentActivity.getGrade())) {
            mathActivityWeekUnitStudentMapper.update(weekUnitStudent,
                    new QueryWrapper<MathActivityWeekUnitStudent>().eq("activity_student_id", currentActivity.getId()));
        } else {
            weekUnitStudent.setUnlocked(false);
            mathActivityWeekUnitStudentMapper.update(weekUnitStudent,
                    new QueryWrapper<MathActivityWeekUnitStudent>().eq("activity_student_id", currentActivity.getId()));
            if (MathMemberLevelEnum.GIFT.equals(membershipLevel)) {
                List<Long> giftWeekUnitIds = mathActivityWeekUnitStudentService.getGiftWeekUnitIds(currentActivity.getActivityId(), currentActivity.getPublisher(),
                        currentActivity.getGrade(), studentVo.getSchoolId());

                if (CollectionUtil.isNotEmpty(giftWeekUnitIds)) {
                    weekUnitStudent.setUnlocked(true);
                    mathActivityWeekUnitStudentMapper.update(weekUnitStudent,
                            new QueryWrapper<MathActivityWeekUnitStudent>().eq("activity_student_id",
                                    currentActivity.getId()).in("week_unit_id", giftWeekUnitIds));
                }
            }
        }

    }

    @Override
    public Map<String, Object> studentCurrentWeekInfo(Long studentId) {
        MathActivityStudent currentActivity = this.getStudentCurrentActivity(studentId);
        CommonResponse.ERROR.assertNotNull(currentActivity, "当前学生未加入数学暑期活动");
        if(! MathActivityFinishEnum.NOT_FINISHED.equals(currentActivity.getFinishResult())){
            QueryWrapper<MathActivityWeekUnitStudent> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("activity_student_id",currentActivity.getId());
            queryWrapper.isNull("deleted_at");
            Map<String, Object> result = new HashMap<>();
            result.put("sortNo",0);
            result.put("sectionCount",mathActivityWeekUnitStudentMapper.selectCount(queryWrapper));
            return result;
        }
        return mathActivityWeekUnitStudentMapper.getStudentCurrentWeekInfo(currentActivity.getActivityId(), studentId);
    }

    @Override
    public void autoCancleMathMemberRightsTask() {
        //查询数学暑期活动
        MathActivity activity = mathActivityService.getMathSummerHolidayActivity();
        //查询所有已开会员的学生
        List<MathActivityStudent> studentList = baseMapper.selectList(new QueryWrapper<MathActivityStudent>()
                .eq("activity_id", activity.getId())
                .in("membership_level", CollUtil.toList(MathMemberLevelEnum.PAID, MathMemberLevelEnum.GIFT)));
        //轮询调用青于蓝查询会员状态接口
        if (CollUtil.isNotEmpty(studentList)) {
            List<Long> studentIdList = studentList.stream().map(MathActivityStudent::getStudentId).distinct().collect(Collectors.toList());
            studentIdList.stream().forEach(studentId -> {
                try {
                    boolean isMember = membershipService.queryStudentMathMembership(studentId, null);
                    if (!isMember) {
                        //会员已取消，则取消其暑期活动会员权益
//                    updateStudentSummerPlanStatus(studentId, activity.getId());
                        log.info("会员已取消，取消其暑期活动会员权益 {}", studentId);
                    }
                } catch (Exception e) {
                    log.error("查询青于蓝会员状态异常 {}", studentId, e);
                }
            });
        }
    }


    @Override
    public Long deleteActivityStudent(Long studentId) {
        MathActivityStudent activityStudent = this.getStudentCurrentActivity(studentId);
        if (activityStudent != null){ // 删除当前活动
            activityStudent.setDeletedAt(new Date());
            baseMapper.updateById(activityStudent);
            MathActivityWeekUnitStudent mathActivityWeekUnitStudent= new MathActivityWeekUnitStudent();
            mathActivityWeekUnitStudent.setDeletedAt(new Date());
            mathActivityWeekUnitStudentMapper.update(mathActivityWeekUnitStudent,
                    new QueryWrapper<MathActivityWeekUnitStudent>().eq("activity_student_id", activityStudent.getId()));
            return activityStudent.getId();
        }
        return null;
    }
    @Override
    public Long changeJoinActivity(MathActivityJoinusParam mathActivityJoinusParam) {
        this.deleteActivityStudent(mathActivityJoinusParam.getStudentId());
        return this.studentJoinActivity(mathActivityJoinusParam);
    }

    @Override
    public MathElecttronicTextbookVo getElectronicTextbook(Long studentId, MathActivityWeekTypeEnum weekType) {
        MathActivityStudent currentActivity = this.getStudentCurrentActivity(studentId);
        QueryElectronicTextbookBo bo = QueryElectronicTextbookBo.builder()
                .grade(MathActivityWeekTypeEnum.REVIEW == weekType ? currentActivity.getGrade() - 1 : currentActivity.getGrade())
                .publisher(currentActivity.getPublisher())
                .semester(MathActivityWeekTypeEnum.REVIEW == weekType ? 2 : 1)
                .build();
        return eduKnowledgeHubService.getElectronicTextbook(bo);
    }

    @Override
    public Boolean checkAndGiftMember(Long studentId, Long parentId,Integer daysBetween) {
        MathActivityStudent currentActivity = this.getStudentCurrentActivity(studentId);
        // 修改添加历史参与学生无舰长id情况活动
        if(currentActivity != null && currentActivity.getParentId() == null){
            MathActivityStudent mathActivityStudent = MathActivityStudent.builder().parentId(parentId).build();
            mathActivityStudent.setId(currentActivity.getId());
            baseMapper.updateById(mathActivityStudent);
        }
        boolean member = membershipService.isMathHolidayActivityMember(studentId, parentId);
        if(member){ // 已是会员
            return true;
        } else {
            return membershipService.giftMathMember(studentId, parentId, daysBetween);
        }
    }
}
