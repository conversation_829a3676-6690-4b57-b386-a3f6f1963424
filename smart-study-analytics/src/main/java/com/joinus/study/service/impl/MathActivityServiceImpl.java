package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.mapper.MathActivityMapper;
import com.joinus.study.mapper.MathActivityWeekMapper;
import com.joinus.study.mapper.MathActivityWeekUnitMapper;
import com.joinus.study.model.dto.MathActivityWeekUnitDto;
import com.joinus.study.model.dto.MathChapterDto;
import com.joinus.study.model.dto.MathSectionDto;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityWeek;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.enums.MathActivityWeekTypeEnum;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.ActivityAutoGenerateParam;
import com.joinus.study.model.vo.ActivityChapterVo;
import com.joinus.study.model.vo.MathActivityVo;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.MathActivityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
public class MathActivityServiceImpl extends BaseServiceImpl<MathActivityMapper, MathActivity>
        implements MathActivityService {

    @Autowired
    private MathActivityWeekMapper mathActivityWeekMapper;
    @Autowired
    private MathActivityWeekUnitMapper mathActivityWeekUnitMapper; // 用于保存活动周单元
    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Value("${math.summer-holiday.name:数学暑期训练}")
    private String mathSummerHolidayName;


    @Override
    public MathActivity currentActivity(Long schoolId) {
        QueryWrapper<MathActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at");
        List<MathActivity> list = list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generateAuto(ActivityAutoGenerateParam param) {
        //创建活动
        List<MathActivity> existActivityList = lambdaQuery()
                .eq(MathActivity::getName, param.getName())
                .list();
        MathActivity mathActivity = null;
        if (CollUtil.isEmpty(existActivityList)) {
             mathActivity = BeanUtil.copyProperties(param, MathActivity.class);
            save(mathActivity);
        } else {
            mathActivity = existActivityList.get(0);
        }

        //创建活动周
        LambdaQueryWrapper<MathActivityWeek> activityWeedWrapper = Wrappers.lambdaQuery(MathActivityWeek.class)
                .eq(MathActivityWeek::getActivityId, mathActivity.getId())
                .eq(MathActivityWeek::getGrade, param.getGrade())
                .eq(MathActivityWeek::getPublisher, param.getPublisher());
        List<MathActivityWeek> existActivityWeekList = mathActivityWeekMapper.selectList(activityWeedWrapper);


        if (CollUtil.isEmpty(existActivityWeekList)) {
            MathActivity finalMathActivity = mathActivity;
            List<MathActivityWeek> addActivityWeeks = new ArrayList<>();
            IntStream.range(0, 8)
                    .forEach(i ->{
                        MathActivityWeekTypeEnum type = MathActivityWeekTypeEnum.REVIEW;
                        if (i > 1 || param.getGrade() == 7) {
                            type = MathActivityWeekTypeEnum.PREVIEW;
                        }
                        Integer grade = type == MathActivityWeekTypeEnum.REVIEW ? param.getGrade() - 1 : param.getGrade();
                        Integer semester = type == MathActivityWeekTypeEnum.REVIEW ? 2 : 1;
                        MathActivityWeek activityWeek = MathActivityWeek.builder()
                                .activityId(finalMathActivity.getId())
                                .name(StrUtil.format("{}-{}-{}-{}", type.getDesc(), getGradeText(grade), getSemesterText(semester), param.getPublisher().getDescription()))
                                .sortNo(i)
                                .publisher(param.getPublisher())
                                .grade(param.getGrade())
                                .type(type)
                                .build();
                        addActivityWeeks.add(activityWeek);
                    });
            addActivityWeeks.stream().forEach(activityWeek -> {
                mathActivityWeekMapper.insert(activityWeek);
            });
            existActivityWeekList = addActivityWeeks;
        }

        LambdaQueryWrapper<MathActivityWeekUnit> weekUnitWrapper = Wrappers.lambdaQuery(MathActivityWeekUnit.class)
                .in(MathActivityWeekUnit::getWeekId, existActivityWeekList.stream().map(MathActivityWeek::getId).collect(Collectors.toList()));
        List<MathActivityWeekUnit> mathActivityWeekUnits = mathActivityWeekUnitMapper.selectList(weekUnitWrapper);
        if (CollUtil.isNotEmpty(mathActivityWeekUnits)) {
            throw new BaseException("活动周单元已存在");
        }

        //创建活动周单元
        List<MathSectionDto> reviewSections = eduKnowledgeHubService.listMathSections(param.getGrade() - 1, 2, param.getPublisher());
        List<MathSectionDto> previewSections = eduKnowledgeHubService.listMathSections(param.getGrade(), 1, param.getPublisher());
        List<MathActivityWeekUnit> reviewActivityWeekUnits = new ArrayList<>();
        List<MathActivityWeekUnit> previewActivityWeekUnits = new ArrayList<>();
        if (param.getGrade() > 7) {
            reviewActivityWeekUnits = divideReviewSections(existActivityWeekList, reviewSections);
            previewActivityWeekUnits = dividePeviewSections(existActivityWeekList, previewSections, 6);
        } else {
            previewActivityWeekUnits = dividePeviewSections(existActivityWeekList, previewSections, 8);
        }

        List<MathActivityWeekUnit> addActivityWeekUnits = new ArrayList<>();
        addActivityWeekUnits.addAll(reviewActivityWeekUnits);
        addActivityWeekUnits.addAll(previewActivityWeekUnits);
        if (CollUtil.isNotEmpty(addActivityWeekUnits)) {
            addActivityWeekUnits.stream().forEach(activityWeekUnit -> {
                mathActivityWeekUnitMapper.insert(activityWeekUnit);
            });
        }
    }

    @Override
    public List<ActivityChapterVo> listChapters(Integer grade) {
        List<MathChapterDto> mathChapters =eduKnowledgeHubService.listMathChapters(null, null, null);

        List<ActivityChapterVo> results = new ArrayList<>();
        if (CollUtil.isEmpty(mathChapters)) {
            return results;
        }
        if (grade == 7) {
            List<MathChapterDto> collect = mathChapters.stream()
                    .filter(mathChapterDto -> null != mathChapterDto.getGrade() && null != mathChapterDto.getSemester())
                    .filter(mathChapterDto -> {
                return mathChapterDto.getGrade().equals(grade) && mathChapterDto.getSemester().equals(1);
            }).collect(Collectors.toList());
            Map<PublisherEnum, List<MathChapterDto>> publisherMap = collect.stream().collect(Collectors.groupingBy(MathChapterDto::getPublisher));
            publisherMap.forEach((publisher, chapterDtos) -> {
                ActivityChapterVo activityChapterVo = ActivityChapterVo.builder()
                        .publisher(publisher)
                        .build();
                List<ActivityChapterVo.ChapterVo> chapterVos = chapterDtos.stream().map(chapterDto -> {
                            ActivityChapterVo.ChapterVo chapterVo = ActivityChapterVo.ChapterVo.builder()
                                    .name(chapterDto.getName())
                                    .type(MathActivityWeekTypeEnum.PREVIEW)
                                    .sortNo(chapterDto.getChapterSortNo())
                                    .build();
                            return chapterVo;
                        }).sorted(Comparator.comparing(ActivityChapterVo.ChapterVo::getSortNo))
                        .collect(Collectors.toList());
                activityChapterVo.setChapters(chapterVos);
                results.add(activityChapterVo);
            });
        } else {
            List<MathChapterDto> collect = mathChapters.stream()
                    .filter(mathChapterDto -> null != mathChapterDto.getGrade() && null != mathChapterDto.getSemester())
                    .filter(mathChapterDto -> {
                return mathChapterDto.getGrade().equals(grade) && mathChapterDto.getSemester().equals(1)
                || mathChapterDto.getGrade().equals(grade - 1) && mathChapterDto.getSemester().equals(2);
            }).collect(Collectors.toList());
            Map<PublisherEnum, List<MathChapterDto>> publisherMap = collect.stream().collect(Collectors.groupingBy(MathChapterDto::getPublisher));
            publisherMap.forEach((publisher, chapterDtos) -> {
                ActivityChapterVo activityChapterVo = ActivityChapterVo.builder()
                        .publisher(publisher)
                        .build();
                List<ActivityChapterVo.ChapterVo> chapterVos = chapterDtos.stream().map(chapterDto -> {
                            ActivityChapterVo.ChapterVo chapterVo = ActivityChapterVo.ChapterVo.builder()
                                    .name(chapterDto.getName())
                                    .type(chapterDto.getSemester().equals(2) ? MathActivityWeekTypeEnum.REVIEW : MathActivityWeekTypeEnum.PREVIEW)
                                    .sortNo(chapterDto.getChapterSortNo())
                                    .build();
                            return chapterVo;
                        }).sorted(Comparator.comparing(ActivityChapterVo.ChapterVo::getType).thenComparing(ActivityChapterVo.ChapterVo::getSortNo))
                        .collect(Collectors.toList());
                activityChapterVo.setChapters(chapterVos);
                results.add(activityChapterVo);
            });

        }
        return results.stream().sorted(Comparator.comparing(activityChapterVo ->
                activityChapterVo.getPublisher().getSortNo()
        )).collect(Collectors.toList());
    }

    @Override
    public MathActivity getMathSummerHolidayActivity() {
        return lambdaQuery().eq(MathActivity::getName, "数学暑期训练").one();
    }


    /**
     * 分割复习单元
     * 复习，按 2 周平均划分复习教材章节学习计划；标题显示 复习+年级+上/下册+版本
     * eg：七年级下册，共 21 小节，则 21/2=10余 1，则 01 周 11 小节、02 周 10 章；
     * 每个章末再添加一个小节，用于保存章末测试
     * 注意：7年级无需复习
     * @param weeks
     * @param reviewSections
     * @return
     */
    private List<MathActivityWeekUnit> divideReviewSections(List<MathActivityWeek> weeks, List<MathSectionDto> reviewSections) {
        if (CollUtil.isEmpty(reviewSections)) {
            return new ArrayList<>();
        }

        List<MathActivityWeekUnitDto> reviewActivityWeekUnitDtos = new ArrayList<>();
        AtomicReference<MathSectionDto> previousSection = new AtomicReference<>();
        AtomicReference<Integer> totalSortNo = new AtomicReference<>(0);
        reviewSections.stream().forEach(section -> {
            MathSectionDto preSection = previousSection.get();
            if (null != preSection && !preSection.getChapterId().equals(section.getChapterId())) {
                MathActivityWeekUnitDto chapterUnit = MathActivityWeekUnitDto.builder()
                        .chapterId(preSection.getChapterId())
                        .chapterName(StrUtil.format("第{}章 {}", numberToChinese(preSection.getChapterSortNo()), preSection.getChapterName()))
                        .type(MathActivityWeekUnitTypeEnum.CHAPTER_TEST)
                        .bookId(preSection.getTextbookId())
                        .chapterSortNo(preSection.getChapterSortNo())
                        .sectionSortNo(preSection.getSectionSortNo())
                        .totalSortNo(totalSortNo.get())
                        .build();
                reviewActivityWeekUnitDtos.add(chapterUnit);
                totalSortNo.getAndSet(totalSortNo.get() + 1);
            }
            MathActivityWeekUnitDto sectionUnit = MathActivityWeekUnitDto.builder()
                    .sectionId(section.getSectionId())
                    .sectionName(StrUtil.format("{}.{} {}", section.getChapterSortNo(), section.getSectionSortNo(), section.getSectionName()))
                    .chapterId(section.getChapterId())
                    .chapterName(StrUtil.format("第{}章 {}", numberToChinese(section.getChapterSortNo()), section.getChapterName()))
                    .type(MathActivityWeekUnitTypeEnum.SECTION_TEST)
                    .bookId(section.getTextbookId())
                    .chapterSortNo(section.getChapterSortNo())
                    .sectionSortNo(section.getSectionSortNo())
                    .totalSortNo(totalSortNo.get())
                    .build();
            reviewActivityWeekUnitDtos.add(sectionUnit);
            totalSortNo.getAndSet(totalSortNo.get() + 1);

            previousSection.set(section);
        });
        MathSectionDto preSection = previousSection.get();
        MathActivityWeekUnitDto chapterUnit = MathActivityWeekUnitDto.builder()
                .chapterId(preSection.getChapterId())
                .chapterName(StrUtil.format("第{}章 {}", numberToChinese(preSection.getChapterSortNo()), preSection.getChapterName()))
                .type(MathActivityWeekUnitTypeEnum.CHAPTER_TEST)
                .bookId(preSection.getTextbookId())
                .chapterSortNo(preSection.getChapterSortNo())
                .sectionSortNo(preSection.getSectionSortNo())
                .totalSortNo(totalSortNo.get())
                .build();
        reviewActivityWeekUnitDtos.add(chapterUnit);
        totalSortNo.getAndSet(totalSortNo.get() + 1);

        MathActivityWeekUnitDto bookUnit = MathActivityWeekUnitDto.builder()
                .type(MathActivityWeekUnitTypeEnum.COMPREHENSIVE_TEST)
                .bookId(preSection.getTextbookId())
                .totalSortNo(totalSortNo.get())
                .build();
        reviewActivityWeekUnitDtos.add(bookUnit);
        totalSortNo.getAndSet(totalSortNo.get() + 1);

        log.info("reviewActivityWeekUnitDtos {}", JSONUtil.toJsonStr(reviewActivityWeekUnitDtos));

        List<MathActivityWeekUnitDto> sectionDtos = reviewActivityWeekUnitDtos.stream().filter(unit -> MathActivityWeekUnitTypeEnum.SECTION_TEST.equals(unit.getType()))
                .collect(Collectors.toList());

        int[] seporators = calculateDivisionPoints(sectionDtos.size(), 2);
        List<Integer> seporatorTotalSortNos = new ArrayList<>();
        Arrays.stream(seporators).forEach(sep -> seporatorTotalSortNos.add(sectionDtos.get(sep - 1).getTotalSortNo()));

        log.info("review sectionCount {} weekUnitCount {} seporators {} seporatorTotalSortNos {}", sectionDtos.size(), reviewActivityWeekUnitDtos.size(), seporators, seporatorTotalSortNos);
        List<MathActivityWeek> reviewWeeks = weeks.stream().filter(week -> week.getType() == MathActivityWeekTypeEnum.REVIEW)
                .sorted(Comparator.comparing(MathActivityWeek::getSortNo))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(reviewWeeks) || reviewWeeks.size() != 2) {
            throw new BaseException("复习周数不是2周");
        }

        List<MathActivityWeekUnit> results = new ArrayList<>();
        IntStream.range(0, reviewWeeks.size()).forEach(weekIndex -> {
            MathActivityWeek reviewWeek = reviewWeeks.get(weekIndex);
            final Integer[] sortNo = {0};
            reviewActivityWeekUnitDtos.stream()
                    .filter(unit -> {
                        boolean b = false;
                        if (weekIndex == reviewWeeks.size() -1) {
                            b = unit.getTotalSortNo() >= seporatorTotalSortNos.get(weekIndex);
                        } else {
                            b = unit.getTotalSortNo() >= seporatorTotalSortNos.get(weekIndex) && unit.getTotalSortNo() < seporatorTotalSortNos.get(weekIndex + 1);
                        }
                        return b;
                    }).forEach(unit -> {
                        MathActivityWeekUnit mathActivityWeekUnit = BeanUtil.copyProperties(unit, MathActivityWeekUnit.class);
                        mathActivityWeekUnit.setWeekId(reviewWeek.getId());
                        mathActivityWeekUnit.setSortNo(sortNo[0]);
                        results.add(mathActivityWeekUnit);
                        sortNo[0]++;
                    });
        });

        log.info("review results {}", JSONUtil.toJsonStr(results));
        return results;
    }

    /**
     * 分割预习单元
     * 预习，按 6 周平均划分预习教材章节学习计划；标题显示 预习+年级+上/下册+版本
     * eg：八年级上册，共34 小节，34/6=5余 4
     * 则 01 到 04 周每周为 6 小节，05 、06 周为 5 小节
     * 每个章末再添加一个小节，用于保存章末测试
     * @param weeks
     * @param reviewSections
     * @return
     */
    private List<MathActivityWeekUnit> dividePeviewSections(List<MathActivityWeek> weeks, List<MathSectionDto> reviewSections, Integer previewWeekCount) {
        if (CollUtil.isEmpty(reviewSections)) {
            return new ArrayList<>();
        }

        List<MathActivityWeekUnitDto> previewActivityWeekUnitDtos = new ArrayList<>();
        AtomicReference<MathSectionDto> previousSection = new AtomicReference<>();
        AtomicReference<Integer> totalSortNo = new AtomicReference<>(0);
        reviewSections.stream().forEach(section -> {
            MathSectionDto preSection = previousSection.get();
            if (null != preSection && !preSection.getChapterId().equals(section.getChapterId())) {
                MathActivityWeekUnitDto chapterUnit = MathActivityWeekUnitDto.builder()
                        .chapterId(preSection.getChapterId())
                        .chapterName(StrUtil.format("第{}章 {}", numberToChinese(preSection.getChapterSortNo()), preSection.getChapterName()))
                        .type(MathActivityWeekUnitTypeEnum.CHAPTER_TEST)
                        .bookId(preSection.getTextbookId())
                        .chapterSortNo(preSection.getChapterSortNo())
                        .sectionSortNo(preSection.getSectionSortNo())
                        .totalSortNo(totalSortNo.get())
                        .build();
                previewActivityWeekUnitDtos.add(chapterUnit);
                totalSortNo.getAndSet(totalSortNo.get() + 1);
            }
            MathActivityWeekUnitDto sectionUnit = MathActivityWeekUnitDto.builder()
                    .sectionId(section.getSectionId())
                    .sectionName(StrUtil.format("{}.{} {}", section.getChapterSortNo(), section.getSectionSortNo(), section.getSectionName()))
                    .chapterId(section.getChapterId())
                    .chapterName(StrUtil.format("第{}章 {}", numberToChinese(section.getChapterSortNo()), section.getChapterName()))
                    .type(MathActivityWeekUnitTypeEnum.SECTION_TEST)
                    .bookId(section.getTextbookId())
                    .chapterSortNo(section.getChapterSortNo())
                    .sectionSortNo(section.getSectionSortNo())
                    .totalSortNo(totalSortNo.get())
                    .build();
            previewActivityWeekUnitDtos.add(sectionUnit);
            totalSortNo.getAndSet(totalSortNo.get() + 1);

            previousSection.set(section);
        });
        MathSectionDto preSection = previousSection.get();
        MathActivityWeekUnitDto chapterUnit = MathActivityWeekUnitDto.builder()
                .chapterId(preSection.getChapterId())
                .chapterName(StrUtil.format("第{}章 {}", numberToChinese(preSection.getChapterSortNo()), preSection.getChapterName()))
                .type(MathActivityWeekUnitTypeEnum.CHAPTER_TEST)
                .bookId(preSection.getTextbookId())
                .chapterSortNo(preSection.getChapterSortNo())
                .sectionSortNo(preSection.getSectionSortNo())
                .totalSortNo(totalSortNo.get())
                .build();
        previewActivityWeekUnitDtos.add(chapterUnit);
        totalSortNo.getAndSet(totalSortNo.get() + 1);

        MathActivityWeekUnitDto bookUnit = MathActivityWeekUnitDto.builder()
                .type(MathActivityWeekUnitTypeEnum.COMPREHENSIVE_TEST)
                .bookId(preSection.getTextbookId())
                .totalSortNo(totalSortNo.get())
                .build();
        previewActivityWeekUnitDtos.add(bookUnit);
        totalSortNo.getAndSet(totalSortNo.get() + 1);

        log.info("previewActivityWeekUnitDtos {}", JSONUtil.toJsonStr(previewActivityWeekUnitDtos));

        List<MathActivityWeekUnitDto> sectionDtos = previewActivityWeekUnitDtos.stream().filter(unit -> MathActivityWeekUnitTypeEnum.SECTION_TEST.equals(unit.getType()))
                .collect(Collectors.toList());

        int[] seporators = calculateDivisionPoints(sectionDtos.size(), previewWeekCount);

        List<Integer> seporatorTotalSortNos = new ArrayList<>();
        Arrays.stream(seporators).forEach(seporator -> seporatorTotalSortNos.add(sectionDtos.get(seporator - 1).getTotalSortNo()));

        log.info("preview sectionSize {} totalSize {} seporator: {}, seporatorTotalSortNo: {}", sectionDtos.size(), previewActivityWeekUnitDtos.size(),
                seporators, seporatorTotalSortNos);

        List<MathActivityWeek> previewWeeks = weeks.stream().filter(week -> week.getType() == MathActivityWeekTypeEnum.PREVIEW)
                .sorted(Comparator.comparing(MathActivityWeek::getSortNo))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(previewWeeks) || !CollUtil.toList(6,8).contains(previewWeeks.size())) {
            throw new BaseException("预习周数不是6周或8周");
        }

        List<MathActivityWeekUnit> results = new ArrayList<>();
        IntStream.range(0, previewWeeks.size()).forEach(weekIndex -> {
            MathActivityWeek reviewWeek = previewWeeks.get(weekIndex);
            final Integer[] sortNo = {0};
            previewActivityWeekUnitDtos.stream()
                    .filter(unit -> {
                        boolean b = false;
                        if (weekIndex == previewWeeks.size() - 1) {
                            b = unit.getTotalSortNo() >= seporatorTotalSortNos.get(weekIndex);
                        } else {
                            b = unit.getTotalSortNo() >= seporatorTotalSortNos.get(weekIndex) && unit.getTotalSortNo() < seporatorTotalSortNos.get(weekIndex + 1);
                        }
                        return b;
                    }).forEach(unit -> {
                        MathActivityWeekUnit mathActivityWeekUnit = BeanUtil.copyProperties(unit, MathActivityWeekUnit.class);
                        mathActivityWeekUnit.setWeekId(reviewWeek.getId());
                        mathActivityWeekUnit.setSortNo(sortNo[0]);
                        results.add(mathActivityWeekUnit);
                        sortNo[0]++;
                    });
        });

        log.info("preview results {}", JSONUtil.toJsonStr(results));
        return results;
    }

    /**
     * 计算预习分配的中间分隔点
     * 返回每周结束时的小节索引
     * 根据传入的预习周数量动态计算分隔点
     *
     * @param totalSections 总小节数
     * @param previewWeekCount 预习周数量，动态参数
     * @return 每周结束的小节索引数组（从0开始计数）
     */
    public int[] calculateDivisionPoints(int totalSections, Integer previewWeekCount) {
        int weekCount = (previewWeekCount == null || previewWeekCount < 1) ? 6 : previewWeekCount;

        int baseCountPerWeek = totalSections / weekCount; // 基础每周小节数
        int remainder = totalSections % weekCount; // 余数

        log.info("预习小节总数: {}, 预习周数: {}, 基础每周小节数: {}, 余数: {}",
                totalSections, weekCount, baseCountPerWeek, remainder);

        int[] divisionPoints = new int[weekCount ];
        divisionPoints[0] = 1;
        int currentIndex = 1;

        for (int weekIndex = 1; weekIndex < weekCount; weekIndex++) {
            // 计算当前周应分配的小节数（前remainder周多分配1个）
            int sectionsForCurrentWeek = baseCountPerWeek + (weekIndex <= remainder ? 1 : 0);
            currentIndex += sectionsForCurrentWeek;
            divisionPoints[weekIndex] = currentIndex; // 当前周的最后一个小节索引

            log.debug("第{}周分配{}个小节，结束于索引{}", weekIndex + 1, sectionsForCurrentWeek, divisionPoints[weekIndex]);
        }

        return divisionPoints;
    }


    public static void main(String[] args) {
        int totalSections = 36;
        Integer previewWeekCount = 6;
        int[] ints = calculateDivisionPoints1(totalSections, previewWeekCount);
        log.info("ints {}", ints);
    }
    public static int[] calculateDivisionPoints1(int totalSections, Integer previewWeekCount) {
        int weekCount = (previewWeekCount == null || previewWeekCount < 1) ? 6 : previewWeekCount;

        int baseCountPerWeek = totalSections / weekCount; // 基础每周小节数
        int remainder = totalSections % weekCount; // 余数

        log.info("预习小节总数: {}, 预习周数: {}, 基础每周小节数: {}, 余数: {}",
                totalSections, weekCount, baseCountPerWeek, remainder);

        int[] divisionPoints = new int[weekCount ];
        divisionPoints[0] = 1;
        int currentIndex = 1;

        for (int weekIndex = 1; weekIndex < weekCount; weekIndex++) {
            // 计算当前周应分配的小节数（前remainder周多分配1个）
            int sectionsForCurrentWeek = baseCountPerWeek + (weekIndex <= remainder ? 1 : 0);
            currentIndex += sectionsForCurrentWeek;
            divisionPoints[weekIndex] = currentIndex; // 当前周的最后一个小节索引

            log.debug("第{}周分配{}个小节，结束于索引{}", weekIndex + 1, sectionsForCurrentWeek, divisionPoints[weekIndex]);
        }

        return divisionPoints;
    }

    /**
     * 
     * 
     * @param grade
     * @return
     * 根据年级数字获取对应的中文文本
     * @param grade 年级
     * @return 年级中文表示
     */
    private String getGradeText(Integer grade) {
        switch (grade) {
            case 7:
                return "七年级";
            case 8:
                return "八年级";
            case 9:
                return "九年级";
            default:
                return grade + "年级";
        }
    }

    private String getSemesterText(Integer semester) {
        switch (semester) {
            case 1:
                return "上册";
            case 2:
                return "下册";
            default:
                return "未知学期";
        }
    }

    // 将数字 1-99 转换为中文数字
    public static String numberToChinese(int number) {
        // 仅支持 1 到 99
        if (number < 1 || number > 99) {
            throw new IllegalArgumentException("只支持 1 到 99 的数字");
        }
        String[] digits = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        if (number < 10) {
            return digits[number];
        } else if (number == 10) {
            return "十";
        } else if (number < 20) {
            // 11-19 读作：十X，例如 12 -> 十二
            return "十" + digits[number % 10];
        } else {
            int tens = number / 10;
            int ones = number % 10;
            if (ones == 0) {
                return digits[tens] + "十";
            } else {
                return digits[tens] + "十" + digits[ones];
            }
        }
    }

}
