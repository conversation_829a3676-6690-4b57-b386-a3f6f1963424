package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.entity.ReadingActivity;
import com.joinus.study.model.param.ReadingJoinActivityParam;
import com.joinus.study.model.vo.ReadingActivityVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【reading_activity】的数据库操作service
 * @createDate 2025-06-05
 * @Entity com.joinus.study.model.entity.ReadingActivity
 */
public interface ReadingActivityService extends IService<ReadingActivity> {

    void joinActivity(ReadingJoinActivityParam param) throws BaseException;

    List<ReadingActivityVO> queryReadingActivityList(Long studentId) throws BaseException;
}
