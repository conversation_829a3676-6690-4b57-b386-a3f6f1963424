package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.MathActivityWeekMapper;
import com.joinus.study.model.entity.MathActivityWeek;
import com.joinus.study.service.MathActivityWeekService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_activity_week(数学活动周期表)】的数据库操作Service实现
* @createDate 2025-06-16 10:07:51
*/
@Service
public class MathActivityWeekServiceImpl extends ServiceImpl<MathActivityWeekMapper, MathActivityWeek>
    implements MathActivityWeekService{

}




