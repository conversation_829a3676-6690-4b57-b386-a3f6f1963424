package com.joinus.study.service;


import com.joinus.study.model.param.AiAnalyticsExamParam;
import com.joinus.study.model.param.ExamErrorCorrectionFeedbackParam;

import java.util.UUID;

/**
 * 试卷纠错service
 */
public interface ExamErrorCorrectionFeedbackService {

    /**
     * 试卷反馈提交
     * @param param
     */
    void saveExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param);

    /**
     * 试卷反馈完成
     * @param param
     */
    void finishExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param);

    /**
     * AI试卷切题完成回调方法
     * @param param
     */
    void aiCutQuestionsResult(AiAnalyticsExamParam param);

    /**
     * AI完成纠错试卷分析完成回调
     * @param param
     */
    void errorCorrectionAnalyticsResult(AiAnalyticsExamParam param);

    /**
     * 判断是否有正在AI分析中试题纠错
     * @param examId
     * @return
     */
    Boolean isCorrectingExamWithAI(UUID examId);

    /**
     * 试卷纠错标记试卷作废
     * @param param
     */
    void invalidExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param);
}
