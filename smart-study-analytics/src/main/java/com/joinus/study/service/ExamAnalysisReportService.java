package com.joinus.study.service;

import com.joinus.study.model.dto.ExamQuestionInfoDto;
import com.joinus.study.model.param.AiAnalyticsExamParam;
import com.joinus.study.model.param.CreateExamAnalysisReportParam;
import com.joinus.study.model.vo.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 考情分析报告
 */
public interface ExamAnalysisReportService {

    /**
     * 生成考情分析报告
     * @param param
     * @return
     */
    void createExamAnalysisReport(@Valid CreateExamAnalysisReportParam param);

    Long createExamAnalysisReport(AiAnalyticsExamParam param);

    ExamAnalysisReportVo getExamAnalysisReport(Long id);

    Long createExamAnalysisReportHasExam(@Valid CreateExamAnalysisReportParam param);

    /**
     * 获取指定试卷考点题目
     * @param personalExamId
     * @param knowledgePointId
     * @return
     */
    List<ExamQuestionFileVo> getExamknowledgePointQuestions(Long personalExamId, String knowledgePointId);

    /**
     * 获取考情分析报告pdf包含的数据
     * @param id
     * @param publisher
     * @return
     */
    ExamAnalysisReportPdfVo getExamAnalysisReportPdfInfo(Long id, String publisher);

    /**
     * 获取考情分析报告pdf包含的数据V2
     * @param id
     * @return
     */
    ExamAnalysisReportPdfVo2 getExamAnalysisReportPdfInfoV2(Long id);

    /**
     * 获取试卷题目列表
     * @param examId 试卷ID
     * @return 题目列表，按题号排序
     */
    List<ExamQuestionInfoDto> listExamQuestions(UUID examId);

    /**
     * 已有试卷校验是否需要重新分析
     * @param param
     * @return
     */
    Boolean checkExistExamShouldReanalyze(CreateExamAnalysisReportParam param);

    void rebuildExamQuestionPublisher();

    void reanalyzeExamKnowledgePoint(CreateExamAnalysisReportParam param);
}
