package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.MathKnowledgePointMapper;
import com.joinus.study.mapper.MathStudentStudyPlanMapper;
import com.joinus.study.mapper.QuestionKnowledgePointMapper;
import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.entity.MathStudentStudyPlan;
import com.joinus.study.model.enums.GradeEnum;
import com.joinus.study.model.vo.MathKnowledgePointVO;
import com.joinus.study.model.vo.MathStudentStudyPlanVo;
import com.joinus.study.service.MathStudentStudyPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_student_study_plan(学生学习计划)】的数据库操作Service实现
* @createDate 2025-09-12 16:19:30
*/
@Service
public class MathStudentStudyPlanServiceImpl extends ServiceImpl<MathStudentStudyPlanMapper, MathStudentStudyPlan>
    implements MathStudentStudyPlanService{

    @Autowired
    private MathKnowledgePointMapper mathKnowledgePointMapper;
    @Override
    public MathStudentStudyPlanVo getStudentCurrentPlan(Long studentId) {
        List<MathStudentStudyPlan> plans = baseMapper.selectStudentPlanList(studentId);
        for (MathStudentStudyPlan plan : plans){
            if(!plan.getIsCompleted()){
                MathStudentStudyPlanVo studyPlanVo = MathStudentStudyPlanVo.builder()
                        .studentId(plan.getStudentId())
                        .weekNo(plan.getWeekNo())
                        .build();
                KnowledgePointDto point = mathKnowledgePointMapper.getKnowledgePointFromViewById(plan.getKnowledgePointId());
                List<MathKnowledgePointVO> planKnowledgePoint = new ArrayList<>();
                planKnowledgePoint.add(MathKnowledgePointVO.builder()
                        .id(point.getId())
                        .name(point.getName())
                        .sortNo(plan.getSortNo())
                        .build());
                studyPlanVo.setGradeName(GradeEnum.ofValue(point.getGrade()).getDesc());
                studyPlanVo.setPlanKnowledgePoint(planKnowledgePoint);
                return studyPlanVo;
            }
        }
        return null;
    }

    @Override
    public List<MathStudentStudyPlanVo> getStudentCurrentPlanList(Long studentId) {
        List<MathStudentStudyPlan> plans = baseMapper.selectStudentPlanList(studentId);
        if (CollectionUtils.isNotEmpty(plans)) {
            List<KnowledgePointDto> knowledgePointByIds = mathKnowledgePointMapper.getKnowledgePointFromViewByIds(plans.stream().map(MathStudentStudyPlan::getKnowledgePointId).collect(Collectors.toList()));
            CommonResponse.ERROR.assertIsTrue(CollectionUtils.isNotEmpty(knowledgePointByIds), "获取学生计划知识点失败");
            String gradeName = GradeEnum.ofValue(knowledgePointByIds.get(0).getGrade()).getDesc();
            Map<UUID, KnowledgePointDto> pointDtoMap = knowledgePointByIds.stream().collect(Collectors.toMap(KnowledgePointDto::getId, point -> point));
            List<MathStudentStudyPlanVo> studyPlanVos = new ArrayList<>();
            Map<Integer, List<MathStudentStudyPlan>> listMap = plans.stream().collect(Collectors.groupingBy(MathStudentStudyPlan::getWeekNo));
            for (Map.Entry<Integer, List<MathStudentStudyPlan>> entry : listMap.entrySet()){
                MathStudentStudyPlanVo studyPlanVo = MathStudentStudyPlanVo.builder()
                        .studentId(studentId)
                        .weekNo(entry.getKey())
                        .build();
                List<MathKnowledgePointVO> planKnowledgePoints = new ArrayList<>();
                entry.getValue().forEach(plan -> {
                    KnowledgePointDto point = pointDtoMap.get(plan.getKnowledgePointId());
                    planKnowledgePoints.add(MathKnowledgePointVO.builder()
                            .id(point.getId())
                            .name(point.getName())
                            .sortNo(plan.getSortNo())
                            .build());
                    planKnowledgePoints.sort(Comparator.comparingInt(MathKnowledgePointVO::getSortNo));
                    studyPlanVo.setPlanKnowledgePoint(planKnowledgePoints);
                });
                studyPlanVo.setGradeName(gradeName);
                studyPlanVos.add(studyPlanVo);
            }
            return studyPlanVos;
        }
        return null;
    }
}




