package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.param.ReadingPassageQuestionsPageParam;
import com.joinus.study.model.param.ReadingPassageQuestionsUpdateParam;
import com.joinus.study.model.param.ReadingPassagesUpdateBatchParam;
import com.joinus.study.model.vo.ReadingPassageQuestionsVO;

import java.util.UUID;


public interface ReadingPassageQuestionsBackendService {

    /**
     * 分页
     *
     * @param pageParam
     * @return
     */
    Page<ReadingPassageQuestionsVO> pages(ReadingPassageQuestionsPageParam pageParam);

    /**
     * 批量删除
     *
     * @param updateParam
     */
    void batchDelete(ReadingPassagesUpdateBatchParam updateParam);

    void disable(ReadingPassagesUpdateBatchParam updateParam);

    void enable(ReadingPassagesUpdateBatchParam updateParam);

    /**
     * 修改
     *
     * @param updateParam
     */
    void update(ReadingPassageQuestionsUpdateParam updateParam);

    ReadingPassageQuestionsVO query(UUID id);

    void auditPass(ReadingPassagesUpdateBatchParam updateParam);

    void auditNoPass(ReadingPassagesUpdateBatchParam updateParam);
}
