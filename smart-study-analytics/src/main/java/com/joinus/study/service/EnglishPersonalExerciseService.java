package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.EnglishPersonalExercise;
import com.joinus.study.model.param.EnglishPersonalExerciseCreateParam;
import com.joinus.study.model.param.EnglishPersonalExerciseUpdateParam;
import com.joinus.study.model.vo.EnglishPersonalExerciseReportVO;
import com.joinus.study.model.vo.EnglishPersonalExerciseResultVO;

public interface EnglishPersonalExerciseService extends IService<EnglishPersonalExercise> {
    EnglishPersonalExerciseResultVO questionAnswerSubmit(EnglishPersonalExerciseUpdateParam updateParam);

    EnglishPersonalExerciseReportVO reportDetail(Long exerciseReportId);

    Long create(EnglishPersonalExerciseCreateParam createParam);
}
