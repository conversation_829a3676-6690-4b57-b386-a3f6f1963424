package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingPersonalAnalysisReport;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【reading_personal_analysis_report】的数据库操作service
 * @createDate 2025-03-28 09:47:33
 * @Entity com.joinus.study.model.entity.ReadingPersonalAnalysisReport
 */
public interface ReadingPersonalAnalysisReportService extends IService<ReadingPersonalAnalysisReport> {

    Page<ReadingHistoryReportVo> queryReadingHistoryReportList(ReadingHistoryReportPageParam pageParam);

    /**
     * 查询用户训练统计信息
     *
     * @param param personalPassageId 训练id
     * @param param startDate 开始日期
     * @param param endDate 结束日期
     * @return
     */
    Map<String, Object> queryPersonalPassageData(ReadingPersonalPassageDataParam param);

    /**
     * 生成训练报告
     *
     * @param personalPassageId 用户阅读文章ID（对应 reading_personal_passage 表主键）
     * @return 报告生成结果 true-成功 false-失败
     */
    Long generateTrainingReport(Long personalPassageId);

    /**
     * 学情分析报告详情
     *
     * @param reportId 学情分析报告id
     * @return
     */
    ReadingReportDetailVo reportDetail(Long reportId, Integer requestType);

    List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointList(ReadingPersonalKnowledgePointDataParam param);

    List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointData(ReadingPersonalKnowledgePointDataParam param);

    List<ReadingQuestionTypeVo> queryPersonalPassageQuestionTypeData(ReadingPersonalQuestionTypeDataParam param);

    List<ReadingPeriodicReportDetailLineChartVo> queryPersonalPassageDataForLineChart(ReadingPersonalPassageDataParam param);

    List<ReadingHighFreqPerformanceVo> queryPersonalPassageHighFreqPerformanceData(ReadingPersonalPassageDataParam param);

    /**
     * 纠错
     *
     * @param param
     * @return
     */
    void errorCorrecting(ReadingErrorCorrectingParam param);

    String getKnowledgePoint(UUID questionId);

    Boolean checkReportIsAnalysisCompleted(Long reportId);

    /**
     * 根据阅读记录id查询
     *
     * @param personalPassageId
     * @return
     */
    ReadingPersonalAnalysisReport getByPersonalPassageId(Long personalPassageId);

    Long getReportIdByPersonalPassageId(Long personalPassageId);

    Integer getReportNotViewCountByStudentId(Long studentId);

    /**
     * 根据报告ID查询训练文章title
     *
     * @param reportId
     * @return
     */
    List<Map<String, Object>> queryPassageInfo(Long reportId);

    void againGenerateSingleReport();
}
