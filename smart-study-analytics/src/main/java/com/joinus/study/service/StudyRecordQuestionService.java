package com.joinus.study.service;

import com.joinus.study.model.entity.StudyRecordQuestion;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【study_record_question】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface StudyRecordQuestionService extends IService<StudyRecordQuestion> {

    List<StudyRecordQuestion> getByStudyIdAndQuestionId(Long sourceId, UUID questionId);
}
