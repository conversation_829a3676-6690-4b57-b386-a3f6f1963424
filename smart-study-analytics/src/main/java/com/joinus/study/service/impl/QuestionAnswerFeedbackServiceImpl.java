package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.entity.QuestionAnswerFeedback;
import com.joinus.study.model.enums.QuestionAnswerFeedbackTypeEnum;
import com.joinus.study.model.param.QuestionAnswerFeedbackParam;
import com.joinus.study.service.QuestionAnswerFeedbackService;
import com.joinus.study.mapper.QuestionAnswerFeedbackMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_answer_feedback】的数据库操作Service实现
* @createDate 2025-03-11 09:48:14
*/
@Service
public class QuestionAnswerFeedbackServiceImpl extends ServiceImpl<QuestionAnswerFeedbackMapper, QuestionAnswerFeedback>
    implements QuestionAnswerFeedbackService{

    @Override
    public void add(QuestionAnswerFeedbackParam param) {
            CommonResponse.ERROR.assertNotEmpty(param.getQuestionAnswer(), "答案不能为空");
            CommonResponse.ERROR.assertNotEmpty(param.getQuestionAnswerContent(), "答案解析能为空");

            if (QuestionAnswerFeedbackTypeEnum.problem_feedback.equals(param.getType())) {
                CommonResponse.ERROR.assertNotEmpty(param.getSuggestion(), "反馈意见不能为空");

            }
            QuestionAnswerFeedback answerFeedback = BeanUtil.copyProperties(param, QuestionAnswerFeedback.class);
            answerFeedback.setCreatedAt(new Date());
            answerFeedback.setUpdatedAt(new Date());
            // this.save(answerFeedback);
            baseMapper.save(answerFeedback);

    }

    @Override
    public void cancelUpvote(QuestionAnswerFeedbackParam param) {
        CommonResponse.ERROR.assertNotNull(param.getQuestionId(), "问题不能为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生不能为空");
        LambdaUpdateWrapper<QuestionAnswerFeedback> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(QuestionAnswerFeedback::getQuestionId, param.getQuestionId())
                .eq(QuestionAnswerFeedback::getStudentId, param.getStudentId())
                .eq(QuestionAnswerFeedback::getType, QuestionAnswerFeedbackTypeEnum.upvote_count)
                .set(QuestionAnswerFeedback::getDeletedAt, new Date());
        this.update(queryWrapper);
    }

    @Override
    public List<QuestionAnswerFeedback> getIsUpvote(Long studentId, UUID questionId) {
        LambdaQueryWrapper<QuestionAnswerFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionAnswerFeedback::getStudentId, studentId);
        queryWrapper.eq(QuestionAnswerFeedback::getQuestionId, questionId);
        queryWrapper.eq(QuestionAnswerFeedback::getType, QuestionAnswerFeedbackTypeEnum.upvote_count);
        queryWrapper.isNull(QuestionAnswerFeedback::getDeletedAt);
        return this.list(queryWrapper);
    }
}




