package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.po.SchoolInfoPO;
import com.joinus.study.model.entity.MathSchoolExam;
import com.joinus.study.model.param.ImportSchoolExamParam;
import com.joinus.study.model.vo.MathSchoolExamVo;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.service.MathSchoolExamService;
import com.joinus.study.mapper.MathSchoolExamMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_school_exam(数学学校试卷关系表)】的数据库操作Service实现
* @createDate 2025-07-14 19:24:52
*/
@Service
public class MathSchoolExamServiceImpl extends ServiceImpl<MathSchoolExamMapper, MathSchoolExam>
    implements MathSchoolExamService{

    @Autowired
    private BasicBusinessService basicBusinessService;

    @Override
    public void importSchoolExamFromExcel(List<ImportSchoolExamParam> dataList) {

        for (ImportSchoolExamParam data : dataList) {
            MathSchoolExam existSchoolExam = lambdaQuery().eq(MathSchoolExam::getExamId, data.getExamId())
                    .eq(MathSchoolExam::getSchoolId, data.getSchoolId())
                    .one();
            if (null == existSchoolExam) {
                MathSchoolExam schoolExam = MathSchoolExam.builder()
                        .examId(data.getExamId())
                        .schoolId(data.getSchoolId())
                        .build();
                save(schoolExam);
            }
        }
    }

    @Override
    public void deleteExamSchoolRelation(UUID examId) {
        baseMapper.delete(Wrappers.lambdaQuery(MathSchoolExam.class).eq(MathSchoolExam::getExamId, examId));
    }

    @Override
    public void updateExamSchoolRelation(UUID examId, List<Long> schoolIds) {
        List<MathSchoolExam> mathSchoolExams = listExamSchoolRelationByExamId(examId);
        for (MathSchoolExam mathSchoolExam : mathSchoolExams) {
            if (!schoolIds.contains(mathSchoolExam.getSchoolId())) {
                removeById(mathSchoolExam);
            }
        }
        List<Long> existSchoolIds = mathSchoolExams.stream().map(mathSchoolExam -> mathSchoolExam.getSchoolId()).collect(Collectors.toList());
        for (Long schoolId : schoolIds) {
            if (!existSchoolIds.contains(schoolId)) {
                MathSchoolExam schoolExam = MathSchoolExam.builder()
                        .examId(examId)
                        .schoolId(schoolId)
                        .build();
                this.save(schoolExam);
            }
        }
    }

    @Override
    public List<MathSchoolExam> listExamSchoolRelationByExamId(UUID examId) {
        return lambdaQuery().eq(MathSchoolExam::getExamId, examId).list();
    }

    @Override
    public List<MathSchoolExamVo> listExamSchoolRelation(Long schoolId, List<UUID> examIds) {
        if (null == schoolId && CollUtil.isEmpty(examIds)) {
            return List.of();
        }
        List<MathSchoolExam> existMathSchoolExams = lambdaQuery()
                .eq(null != schoolId, MathSchoolExam::getSchoolId, schoolId)
                .in(CollUtil.isNotEmpty(examIds), MathSchoolExam::getExamId, examIds)
                .list();
        if (CollUtil.isNotEmpty(existMathSchoolExams)) {
            List<Long> schoolIds = existMathSchoolExams.stream().map(MathSchoolExam::getSchoolId).collect(Collectors.toList());
            List<SchoolInfoPO> schoolInfoPOS = basicBusinessService.listSchoolByIds(schoolIds);
            return schoolInfoPOS.stream().map(schoolInfoPO -> MathSchoolExamVo.builder()
                    .schoolId(schoolInfoPO.getId())
                    .schoolName(schoolInfoPO.getSchoolName())
                    .examId(existMathSchoolExams.stream().filter(mathSchoolExam -> mathSchoolExam.getSchoolId().equals(schoolInfoPO.getId())).findFirst().get().getExamId())
                    .build()).collect(Collectors.toList());
        }
        return List.of();
    }
}




