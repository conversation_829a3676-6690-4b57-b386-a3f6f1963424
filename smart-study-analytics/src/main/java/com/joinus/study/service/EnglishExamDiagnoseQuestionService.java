package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO;
import com.joinus.study.model.entity.EnglishExamDiagnoseQuestion;
import com.joinus.study.model.vo.EnglishExamReportKnowledgePointVO;
import com.joinus.study.model.vo.EnglishExamReportQuestionTypeVO;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/2 10:17
 */
public interface EnglishExamDiagnoseQuestionService extends IService<EnglishExamDiagnoseQuestion> {

    Integer queryKnowledgePointNumber(Long recordId);

    Integer queryWeakKnowledgeNumber(Long recordId);

    List<EnglishExamDiagnoseQuestion> listByRecordId(Long recordId);

    List<EnglishExamDiagnoseQuestion> listQuestionByRecordId(Long recordId);

    List<EnglishExamReportQuestionTypeVO> listQuestionType(Long recordId);

    List<EnglishExamReportKnowledgePointVO> listKnowledgePoints(Long recordId);

    List<EnglishExamDiagnoseQuestion> listMasterQuestions(UUID examId);

    List<String> listQuestionTypeByPointId(Long recordId, UUID pointId);

    Integer queryCountByPointId(Long recordId, UUID pointId);

    Integer queryWrongCountByPointId(Long recordId, UUID pointId);

    List<EnglishExamDiagnoseQuestionDTO> listByRecordIdsAndKnowledgePointId(List<Long> diagnoseRecordIds, UUID knowledgePointId);

    /**
     * 试卷诊断原题列表
     *
     * @param diagnoseRecordId
     * @param knowledgePointId
     * @return
     */
    List<EnglishExamDiagnoseQuestionDTO> listByRecordIdAndKnowledgePointId(Long diagnoseRecordId, UUID knowledgePointId);

}
