package com.joinus.study.service;


import com.joinus.dao.po.SchoolInfoPO;
import com.joinus.study.model.dto.ParentDTO;
import com.joinus.study.model.dto.StudentBasicInfoDTO;
import com.joinus.study.model.param.AddStudentForPadParam;
import com.joinus.study.model.param.AddStudentParam;

import java.util.List;

public interface BasicBusinessService {


    List<StudentBasicInfoDTO> getStudentsByParentPhone(String parentPhone);

    Long  addStudentBasicData(AddStudentParam addStudentParam);

    Long  addStudentBasicDataForPad(AddStudentForPadParam addStudentForPadParam);

    ParentDTO getParentByParentId(Long parentId);

    Long  addParent(String phone);

    StudentBasicInfoDTO getStudentRegionIdInfo(Long studentId);

    /**
     * 获取下级地区列表
     * @param regionName
     * @return
     */
    List<String> getLowerRegionList(String regionName);

    List<SchoolInfoPO> listSchoolByIds(List<Long> list);
}
