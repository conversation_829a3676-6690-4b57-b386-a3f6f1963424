package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishPersonalWeakKnowledgePointMapper;
import com.joinus.study.model.entity.EnglishPersonalWeakKnowledgePoint;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointCreateParam;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointPageParam;
import com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO;
import com.joinus.study.service.EnglishPersonalWeakKnowledgePointService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@Service("englishPersonalWeakKnowledgePointService")
@Slf4j
public class EnglishPersonalWeakKnowledgePointServiceImpl extends ServiceImpl<EnglishPersonalWeakKnowledgePointMapper, EnglishPersonalWeakKnowledgePoint> implements EnglishPersonalWeakKnowledgePointService {

    @Override
    public Page<EnglishPersonalWeakKnowledgePointVO> pages(EnglishPersonalWeakKnowledgePointPageParam pageParam) {
        Page<EnglishPersonalWeakKnowledgePointVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<EnglishPersonalWeakKnowledgePointVO> list = this.baseMapper.pages(page, pageParam);
        page.setRecords(list);
        return page;
    }

    @Override
    public void create(EnglishPersonalWeakKnowledgePointCreateParam createParam) {
        // CommonResponse.ERROR.assertNotNull(personalPassagesId, "保存失败！");
        EnglishPersonalWeakKnowledgePoint englishPersonalWeakKnowledgePoint = BeanUtil.copyProperties(createParam, EnglishPersonalWeakKnowledgePoint.class);
        englishPersonalWeakKnowledgePoint.setCreatedAt(new Date());
        englishPersonalWeakKnowledgePoint.setUpdatedAt(new Date());
        englishPersonalWeakKnowledgePoint.setStatus(1);
        BigDecimal errorPercentage = this.calculateErrorPercentage(createParam.getWrongAnswerCount(), createParam.getAnsweredQuestionCount());
        englishPersonalWeakKnowledgePoint.setErrorPercentage(errorPercentage);
        this.save(englishPersonalWeakKnowledgePoint);
    }

    public BigDecimal calculateErrorPercentage(Integer wrongAnswerCount, Integer answeredQuestionCount) {
        // 处理除数为0的情况（题目数量为0时返回0）
        if (answeredQuestionCount == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal wrongCount = new BigDecimal(wrongAnswerCount);
        BigDecimal totalCount = new BigDecimal(answeredQuestionCount);
        return wrongCount.divide(totalCount, 2, RoundingMode.HALF_UP);
    }

    @Override
    public EnglishPersonalWeakKnowledgePoint query(Long id) {
        return this.getById(id);
    }

    @Override
    public EnglishPersonalWeakKnowledgePoint selectByPointIdAndStudentId(UUID pointId, Long studentId) {
        return lambdaQuery().eq(EnglishPersonalWeakKnowledgePoint::getKnowledgePointId, pointId)
                .eq(EnglishPersonalWeakKnowledgePoint::getStudentId, studentId)
                .one();
    }

    @Override
    public List<EnglishPersonalWeakKnowledgePointVO> list(Long studentId, Long diagnoseRecordId) {
        return this.baseMapper.list(studentId, diagnoseRecordId);
    }
}
