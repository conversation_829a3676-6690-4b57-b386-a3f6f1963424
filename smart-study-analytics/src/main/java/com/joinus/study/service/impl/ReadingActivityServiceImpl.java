package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingActivityMapper;
import com.joinus.study.model.entity.ReadingActivity;
import com.joinus.study.model.entity.ReadingActivityStudent;
import com.joinus.study.model.param.ReadingJoinActivityParam;
import com.joinus.study.model.vo.ReadingActivityDayVO;
import com.joinus.study.model.vo.ReadingActivityVO;
import com.joinus.study.service.ReadingActivityService;
import com.joinus.study.service.ReadingActivityStudentService;
import com.joinus.study.service.ReadingPersonalPassagesService;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ReadingActivityServiceImpl extends BaseServiceImpl<ReadingActivityMapper, ReadingActivity>
        implements ReadingActivityService {

    @Resource
    private ReadingActivityStudentService  readingActivityStudentService;

    @Resource
    private ReadingPersonalPassagesService personalPassagesService;

    @Override
    public void joinActivity(ReadingJoinActivityParam param) throws BaseException {
        //查询暑期训练营活动对应的活动id
        ReadingActivity activity = getOne(Wrappers.<ReadingActivity>lambdaQuery()
                .eq(ReadingActivity::getName, "暑期训练营活动").isNull(ReadingActivity::getDeletedAt));
        if (activity == null) {
            log.error("未找到暑期训练营活动");
            throw new BaseException("未找到暑期训练营活动");
        }

        //校验是否已加入暑期训练营
        CommonResponse.ERROR.assertIsTrue(readingActivityStudentService.checkStudentIsJoinActivity(param.getStudentId())==0, "该学生已加入训练营，请勿重复操作");

        //年级格式转换
        Integer grade = DataUtil.formatGrade(param.getGrade());

        ReadingActivityStudent student = ReadingActivityStudent.builder()
                .activityId(activity.getId())
                .schoolId(param.getSchoolId())
                .studentId(param.getStudentId())
                .grade(grade)
                .semester(2)
                .build();
        readingActivityStudentService.save(student);
    }

    @Override
    public List<ReadingActivityVO> queryReadingActivityList(Long studentId) throws BaseException {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        //判断学生是否加入训练营
        if(readingActivityStudentService.checkStudentIsJoinActivity(studentId)==0){
            return null;
        }
        //获取有效的活动列表
        List<ReadingActivityVO> activityList = baseMapper.queryReadingActivityList();
        if(activityList != null){
            for (ReadingActivityVO activity : activityList) {
                // 计算剩余天数
                if (activity.getEndTime() != null) {
                    LocalDate endDate = LocalDate.parse(activity.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    long daysBetween = ChronoUnit.DAYS.between(LocalDate.now(), endDate);
                    activity.setSurplusDays((int) Math.max(daysBetween, 0));
                } else {
                    activity.setSurplusDays(0); // 处理endTime为空的情况
                }

                activity.setCompleteNum(getStudentCompleteNum(studentId, activity.getId()));
                //根据活动id、学生id获取今日类型
                int todayType = personalPassagesService.getActivityEntryType(activity.getId(), studentId);
                activity.setPlanName(todayType == 4 ? "强化训练" : "巩固复习");
                //7天数据
                activity.setSevenDayList(getSevenDayList(todayType));
            }
            return activityList;
        }
        return null;
    }

    public Integer getStudentCompleteNum(Long studentId, Long activityId) throws BaseException {
        return baseMapper.getStudentCompleteNum(studentId, activityId);
    }

    public List<ReadingActivityDayVO> getSevenDayList(Integer todayType) throws BaseException {
        // 生成七日数据
        List<ReadingActivityDayVO> sevenDayList = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();
        // 生成包括今天在内的7天数据
        Integer currentType = todayType;
        for (int i = 0; i < 7; i++) {
            LocalDate dayDate = currentDate.plusDays(i);

            ReadingActivityDayVO dayPlan = new ReadingActivityDayVO();
            dayPlan.setActivityDate(dayDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dayPlan.setIsRest(0);
            // 周日保持前一天类型
            if (dayDate.getDayOfWeek() == DayOfWeek.SUNDAY) {
                dayPlan.setIsRest(1);
            }
            // 非周日且不是第一天则交替
            else if (i > 0) {
                currentType = currentType == 4 ? 5 : 4;
            }

            dayPlan.setPlanName(currentType == 4 ? "强化训练" : "巩固复习");
            sevenDayList.add(dayPlan);
        }
        return sevenDayList;
    }
}
