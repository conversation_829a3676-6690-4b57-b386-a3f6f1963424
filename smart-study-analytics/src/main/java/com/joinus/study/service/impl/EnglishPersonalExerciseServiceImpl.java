package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.EnglishPersonalExerciseMapper;
import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import com.joinus.study.model.entity.EnglishPersonalExercise;
import com.joinus.study.model.entity.EnglishPersonalExerciseReport;
import com.joinus.study.model.entity.EnglishPersonalWeakKnowledgePoint;
import com.joinus.study.model.param.EnglishPersonalExerciseCreateParam;
import com.joinus.study.model.param.EnglishPersonalExerciseQuestionParam;
import com.joinus.study.model.param.EnglishPersonalExerciseReportCreateParam;
import com.joinus.study.model.param.EnglishPersonalExerciseUpdateParam;
import com.joinus.study.model.vo.EnglishPersonalExerciseQuestionVO;
import com.joinus.study.model.vo.EnglishPersonalExerciseReportVO;
import com.joinus.study.model.vo.EnglishPersonalExerciseResultVO;
import com.joinus.study.service.EnglishPersonalExerciseQuestionService;
import com.joinus.study.service.EnglishPersonalExerciseReportService;
import com.joinus.study.service.EnglishPersonalExerciseService;
import com.joinus.study.model.entity.EnglishPersonalExerciseQuestion;
import com.joinus.study.service.EnglishPersonalWeakKnowledgePointService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service("englishPersonalExerciseService")
@Slf4j
public class EnglishPersonalExerciseServiceImpl extends ServiceImpl<EnglishPersonalExerciseMapper, EnglishPersonalExercise> implements EnglishPersonalExerciseService {

    private EnglishPersonalExerciseQuestionService englishPersonalExerciseQuestionService;
    private EnglishPersonalExerciseReportService englishPersonalExerciseReportService;
    private EnglishPersonalWeakKnowledgePointService englishPersonalWeakKnowledgePointService;


    @Override
    public EnglishPersonalExerciseResultVO questionAnswerSubmit(EnglishPersonalExerciseUpdateParam updateParam) {
        CommonResponse.ERROR.assertNotNull(updateParam.getPersonalExerciseId(), "练习做题id不能为空！");

        EnglishPersonalExercise englishPersonalExercise = this.getById(updateParam.getPersonalExerciseId());
        CommonResponse.ERROR.assertNotNull(englishPersonalExercise, "未查询到练习做题记录！");
        // 更新练习题目表
        List<EnglishPersonalExerciseQuestionDTO> exerciseQuestionList = englishPersonalExerciseQuestionService.listByPersonalExerciseId(updateParam.getPersonalExerciseId());
        CommonResponse.ERROR.assertCollNotNull(exerciseQuestionList, "未查询到练习题目列表");
        CommonResponse.ERROR.assertIsTrue(CollectionUtil.size(exerciseQuestionList) == CollectionUtil.size(updateParam.getQuestions()), "提交的练习题目数量和数据库题目数量不相等");
        List<EnglishPersonalExerciseQuestionParam> questions = updateParam.getQuestions();
        Map<UUID, EnglishPersonalExerciseQuestionParam> questionMap = questions.stream().collect(Collectors.toMap(EnglishPersonalExerciseQuestionParam::getQuestionId, v -> v));
        List<EnglishPersonalExerciseQuestion> exerciseQuestionUpdateList = new ArrayList<>(exerciseQuestionList.size());
        exerciseQuestionList.forEach(p -> {
            EnglishPersonalExerciseQuestionParam questionAnswer = questionMap.get(p.getQuestionId());
            CommonResponse.ERROR.assertNotNull(questionAnswer, "题目序号:" + p.getQuestionNo() + "练习题目答案未提交");
            // 更新用户做题答案
            EnglishPersonalExerciseQuestion updateEntity = new EnglishPersonalExerciseQuestion();
            updateEntity.setId(p.getId());
            updateEntity.setUpdatedAt(new Date());
            // 做题结果：1-正确，0-错误
            JSONArray referenceAnswerJsonArray = new JSONArray(p.getReferenceAnswer());
            String userAnswer = questionAnswer.getUserAnswer();
            boolean isCorrect = this.isAnswerCorrect(referenceAnswerJsonArray, userAnswer);

            updateEntity.setResult(isCorrect ? 1 : 0);
            updateEntity.setUserAnswer(questionAnswer.getUserAnswer());
            exerciseQuestionUpdateList.add(updateEntity);
        });
        englishPersonalExerciseQuestionService.updateBatchById(exerciseQuestionUpdateList);
        // 更新练习做题表
        EnglishPersonalExercise personalExerciseUpdate = new EnglishPersonalExercise();
        personalExerciseUpdate.setId(englishPersonalExercise.getId());
        personalExerciseUpdate.setUpdatedAt(new Date());
        personalExerciseUpdate.setStatus(2);
        personalExerciseUpdate.setAnswerTime(updateParam.getAnswerTime());
        personalExerciseUpdate.setAnswerMethod(1);
        personalExerciseUpdate.setEndAt(new Date());
        this.updateById(personalExerciseUpdate);
        // 生成练习报告
        EnglishPersonalExerciseReportCreateParam reportCreateParam = new EnglishPersonalExerciseReportCreateParam();
        reportCreateParam.setAnswerTime(updateParam.getAnswerTime());
        // 统计正确题目数量
        Integer correctCount = (int) exerciseQuestionUpdateList.stream().filter(q -> q.getResult() != null && q.getResult() == 1).count();
        Integer errorCount = (int) exerciseQuestionUpdateList.stream().filter(q -> q.getResult() != null && q.getResult() == 0).count();
        reportCreateParam.setAnsweredQuestionCount(exerciseQuestionUpdateList.size());
        reportCreateParam.setCorrectAnswerCount(correctCount);
        reportCreateParam.setCorrectPercentage(this.calculateCorrectPercentage(correctCount, exerciseQuestionUpdateList.size()));
        reportCreateParam.setPersonalExerciseId(updateParam.getPersonalExerciseId());
        reportCreateParam.setStudentId(updateParam.getStudentId());
        Long exerciseReportId = englishPersonalExerciseReportService.create(reportCreateParam);
        // 计算薄弱知识点错误率
        EnglishPersonalWeakKnowledgePoint weakKnowledgePointUpdate = new EnglishPersonalWeakKnowledgePoint();
        Long personalWeakKnowledgePointId = englishPersonalExercise.getPersonalWeakKnowledgePointId();
        EnglishPersonalWeakKnowledgePoint personalWeakKnowledgePoint = englishPersonalWeakKnowledgePointService.getById(personalWeakKnowledgePointId);
        weakKnowledgePointUpdate.setId(personalWeakKnowledgePoint.getId());
        weakKnowledgePointUpdate.setAnsweredQuestionCount(personalWeakKnowledgePoint.getAnsweredQuestionCount() + exerciseQuestionUpdateList.size());
        weakKnowledgePointUpdate.setWrongAnswerCount(personalWeakKnowledgePoint.getWrongAnswerCount() + errorCount);
        weakKnowledgePointUpdate.setErrorPercentage(this.calculateErrorPercentage(weakKnowledgePointUpdate.getWrongAnswerCount(), weakKnowledgePointUpdate.getAnsweredQuestionCount()));
        englishPersonalWeakKnowledgePointService.updateById(weakKnowledgePointUpdate);

        EnglishPersonalExerciseResultVO exerciseResultVO = new EnglishPersonalExerciseResultVO();
        exerciseResultVO.setExerciseReportId(exerciseReportId);
        return exerciseResultVO;
    }

    /**
     * 检查用户答案是否正确
     *
     * @param referenceAnswerJsonArray 参考答案 JSONArray
     * @param userAnswer               用户提交的答案
     * @return 是否正确 (true/false)
     */
    public boolean isAnswerCorrect(JSONArray referenceAnswerJsonArray, String userAnswer) {
        // 空值处理
        if (referenceAnswerJsonArray == null || referenceAnswerJsonArray.isEmpty()) {
            return false;
        }
        if (userAnswer == null || userAnswer.isEmpty()) {
            return false;
        }
        // 处理用户答案可能是JSONArray的情况
        if (userAnswer.startsWith("[") && userAnswer.endsWith("]")) {
            JSONArray userAnswerArray = new JSONArray(userAnswer);
            userAnswer = String.join(",", userAnswerArray.toList(String.class));
        }

        // 处理单选题情况（参考答案只有一个）
        if (referenceAnswerJsonArray.size() == 1) {
            String correctAnswer = referenceAnswerJsonArray.get(0).toString().trim();
            return userAnswer.trim().equalsIgnoreCase(correctAnswer);
        }

        // 处理多选题情况
        String[] userAnswers = userAnswer.split("\\s*[,，]\\s*");
        if (userAnswers.length != referenceAnswerJsonArray.size()) {
            return false;
        }
        Set<String> referenceAnswerSet = new HashSet<>(referenceAnswerJsonArray.size());
        for (int i = 0; i < referenceAnswerJsonArray.size(); i++) {
            referenceAnswerSet.add(referenceAnswerJsonArray.get(i).toString().trim().toLowerCase());
        }
        for (String answer : userAnswers) {
            if (!referenceAnswerSet.contains(answer.trim().toLowerCase())) {
                return false;
            }
        }
        return true;
    }

    public BigDecimal calculateCorrectPercentage(Integer correctAnswerCount, Integer answeredQuestionCount) {
        // 处理除数为0的情况（题目数量为0时返回0）
        if (answeredQuestionCount == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal correctCount = new BigDecimal(correctAnswerCount);
        BigDecimal totalCount = new BigDecimal(answeredQuestionCount);
        return correctCount.divide(totalCount, 2, RoundingMode.HALF_UP);
    }

    public BigDecimal calculateErrorPercentage(Integer wrongAnswerCount, Integer answeredQuestionCount) {
        // 处理除数为0的情况（题目数量为0时返回0）
        if (answeredQuestionCount == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal wrongCount = new BigDecimal(wrongAnswerCount);
        BigDecimal totalCount = new BigDecimal(answeredQuestionCount);
        return wrongCount.divide(totalCount, 2, RoundingMode.HALF_UP);
    }

    @Override
    public EnglishPersonalExerciseReportVO reportDetail(Long exerciseReportId) {
        // 查询练习报告表
        EnglishPersonalExerciseReport exerciseReport = englishPersonalExerciseReportService.getById(exerciseReportId);
        // 查询练习题目表
        EnglishPersonalExercise personalExercise = this.getById(exerciseReport.getPersonalExerciseId());
        List<EnglishPersonalExerciseQuestionDTO> questions = englishPersonalExerciseQuestionService.listByPersonalExerciseId(exerciseReport.getPersonalExerciseId());
        List<EnglishPersonalExerciseQuestionVO> result = questions.stream().map(p -> {
            EnglishPersonalExerciseQuestionVO exerciseQuestionVO = BeanUtil.copyProperties(p, EnglishPersonalExerciseQuestionVO.class);
            exerciseQuestionVO.setOptions(new JSONArray(p.getOptions()));
            return exerciseQuestionVO;
        }).collect(Collectors.toList());
        // 计算薄弱知识点错误率
        EnglishPersonalWeakKnowledgePoint personalWeakKnowledgePoint = englishPersonalWeakKnowledgePointService.getById(personalExercise.getPersonalWeakKnowledgePointId());
        EnglishPersonalExerciseReportVO exerciseReportVO = new EnglishPersonalExerciseReportVO();
        exerciseReportVO.setAnswerTime(this.formatSeconds(exerciseReport.getAnswerTime()));
        exerciseReportVO.setCorrectPercentage(this.formatToPercentage(exerciseReport.getCorrectPercentage()));
        exerciseReportVO.setQuestions(result);
        exerciseReportVO.setCorrectAnswerCount(exerciseReport.getCorrectAnswerCount());
        Integer isEliminate = personalWeakKnowledgePoint.getErrorPercentage().compareTo(new BigDecimal(0.20)) <= 0 ? 1 : 0;
        exerciseReportVO.setIsEliminate(isEliminate);
        return exerciseReportVO;
    }

    public String formatSeconds(int totalSeconds) {
        if (totalSeconds <= 0) {
            return "0秒";
        }
        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;

        StringBuilder sb = new StringBuilder();
        if (minutes > 0) {
            sb.append(minutes).append("分");
        }
        if (seconds > 0 || minutes == 0) {
            sb.append(seconds).append("秒");
        }
        return sb.toString();
    }

    public String formatToPercentage(BigDecimal decimal) {
        if (decimal == null) {
            return "0%";
        }
        BigDecimal percentage = decimal.multiply(BigDecimal.valueOf(100));
        percentage = percentage.setScale(0, RoundingMode.HALF_UP);
        return percentage.toPlainString() + "%";
    }

    @Override
    public Long create(EnglishPersonalExerciseCreateParam createParam) {
        // 练习表
        EnglishPersonalExercise englishPersonalExercise = BeanUtil.copyProperties(createParam, EnglishPersonalExercise.class);
        englishPersonalExercise.setCreatedAt(new Date());
        englishPersonalExercise.setUpdatedAt(new Date());
        englishPersonalExercise.setStatus(0);
        this.save(englishPersonalExercise);
        // 练习题目表
        List<EnglishPersonalExerciseQuestion> exerciseQuestionList = createParam.getQuestions().stream().map(p -> {
            EnglishPersonalExerciseQuestion englishPersonalExerciseQuestion = BeanUtil.copyProperties(p, EnglishPersonalExerciseQuestion.class);
            englishPersonalExerciseQuestion.setPersonalExerciseId(englishPersonalExercise.getId());
            englishPersonalExerciseQuestion.setCreatedAt(new Date());
            englishPersonalExerciseQuestion.setUpdatedAt(new Date());
            return englishPersonalExerciseQuestion;
        }).collect(Collectors.toList());

        englishPersonalExerciseQuestionService.saveBatch(exerciseQuestionList);
        return englishPersonalExercise.getId();
    }
}
