package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingActivityStudent;

/**
 * <AUTHOR>
 * @description 针对表【reading_activity_student】的数据库操作service
 * @createDate 2025-06-05
 * @Entity com.joinus.study.model.entity.ReadingActivityStudent
 */
public interface ReadingActivityStudentService extends IService<ReadingActivityStudent> {

    Integer checkStudentIsJoinActivity(Long studentId);

    Integer getJoinTrainingCampCount();

}
