package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.ReadingActivityLastPracticeDto;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.entity.ReadingUnits;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description reading_personal_passages reading_personal_passage_questions 语文阅读-答题 Service
 * @createDate 2025-03-28 09:46
 */
public interface ReadingPersonalPassagesService extends IService<ReadingPersonalPassages> {

    /**
     * 方法描述 获取新的练习文章
     *
     * @param: [studentId, planId]
     * @return: com.joinus.study.model.vo.ReadingPersonalPassagesVo
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    ReadingPersonalPassagesVo getAndSavePersonalPassage(ReadingStudentPassagesQueryParam param)  throws BaseException;

    /**
     * 方法描述 AI获取新题目
     * @param: [param]
     * @return: com.joinus.study.model.vo.ReadingPersonalPassagesVo
     * @author: DELL
     * @date: 2025/4/10 16:36
     */
    ReadingPersonalPassagesVo getNewPersonalPassage(ReadingStudentPassagesQueryParam param) throws BaseException;

    /**
     * @Description 获取学生薄弱知识点
     * <AUTHOR>
     * @date 2025/5/9
     */
    List<UUID> getStudentWeakKnowledgePointIds(Long studentId) throws BaseException;

    /*
     * 方法描述 获取练习文章
     * @param: [uuid]
     * @return: com.joinus.study.model.entity.ReadingPassages
     * @author: DELL
     * @date: 2025/4/14 14:00
     */
    ReadingPassages getReadingPassagesByid(UUID uuid);
    /**
     * 方法描述 生成对应答卷html
     * @param: [id, printRange]
     * @return: java.lang.String
     * @author: DELL
     * @date: 2025/3/28 16:12
     */
    String generatePassageHtml(UUID passageId, List<UUID> questionIds, Integer printRange)  throws BaseException;

    /**
     * 方法描述 生成对应答卷pdf
     * @param: [passageId,questionIds, printRange]
     * @return: java.lang.String
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    Map<String, String> generatePassagePdf(ReadingPersonalPrintParam param) throws BaseException;

    /**
     * 方法描述 生成对应答卷pdf的文件流
     * @param: [passageId,questionIds, printRange]
     * @return: java.lang.String
     * @author: 赵建铭
     * @date: 2025-05-07
     */
    byte[] generatePassagePdfByte(Long id) throws BaseException, IOException;

    /**
     * 方法描述 暂停保存进度
     *
     * @param: [param]
     * @return: void
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    void pausePersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) throws BaseException;

    /**
     * 方法描述 提交练习答案
     * @param: [param]
     * @return: void
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    ReadingQuestionSubmitResultVo submitPersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) throws BaseException;

    /**
     * 方法描述 获取学生训练记录列表
     * @param: [studentId]
     * @return: java.util.List<com.joinus.study.model.vo.ReadingPersonalPassagesVo>
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    Page<StudentReadingPersonalPassagesVo> studentPersonalList(ReadingPersonalPassagesQueryParam param) throws BaseException;

    /**
     * 方法描述 获取单元列表
     * @param: [textbook, grade]
     * @return: java.util.List<com.joinus.study.model.entity.ReadingUnits>
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    List<ReadingUnits> getUnits(String textbook, Long grade,Integer semester,Long studentId,Long parentId);

    /**
     * 错题本-分页
     *
     * @param pageParam
     * @return
     */
    Page<ReadingErrorBookVO> pagesOfErrorBook(ReadingErrorBookPageParam pageParam);

    /**
     * 方法描述 获取用户作答题目及答案信息
     * @param: [param]
     * @return: void
     * @author: zhaojianming
     * @date: 2025-04-01
     */
    List<ReadingPassageQuestionsAnswersVO> queryPersonalPassagesQuestionList(Long id);

    List<Long> listStudentsWithTrainingRecords(LocalDate date);

    /**
     * 方法描述 弃用练习
     * @param: [id]
     * @return: void
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    void deprecatePersonalPassagesQuestions(Long id);

    /**
     * 方法描述 保存Ai批改结果
     * @param: [studentId]
     * @return: java.util.List<java.lang.Long>
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    Long saveCorrectingResults(ReadingAIAbilityParam param,Integer entryType);
    //拍照识别的错题标注
    Long annotationWrongPersonalPassagesQuestions(ReadingPersonalPassagesQuestionsParam param) throws BaseException;

    /**
     * 方法描述 根据id获取练习文章
     * @param: [studentId]
     * @return: java.util.List<java.lang.Long>
     * @author: lianli
     * @date: 2025/3/28 16:12
     **/
    ReadingPersonalPassagesVo getpersonalPassagesById(Long id) throws BaseException ;

    /**
     * 方法描述 是被二维码返回的id 对应练习详情
     * @param: [image]
     * @return: com.joinus.study.model.vo.ReadingPersonalPassagesVo
     * @author: DELL
     * @date: 2025/4/8 11:28
     */
    ReadingPersonalPassagesVo identifyQRcode(String objectKey) throws BaseException;

    ReadingPassages getPassageInfoByPersonalPassagesId(Long id);

    /**
     * 方法描述 发送pdf到邮箱
     * @param: [passagePdf, recipient]
     * @return: void
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    void sendPassagePdfEmail(String recipient, Long personalPrintId) throws BaseException ;

    void deletePersonalPassage(Long id) throws BaseException ;

    /**
     * 方法描述 根据条件获取练习详情
     * @param: [studentId]
     * @return: java.util.List<java.lang.Long>
     * @author: lianli
     * @date: 2025/3/28 16:12
     */
    ReadingPersonalPassagesVo getStudentPersonalVoDoing(ReadingPersonalPassagesQueryParam param) throws BaseException ;

    /**
     * 方法描述 根据id获取练习题目id
     * @param: [id]
     * @return: java.util.List<java.util.UUID>
     * @author: DELL
     * @date: 2025/4/10 16:31
     */
    List<UUID> getQuestionIdsByPersonalPassagesId(Long id) throws BaseException ;

    /**
     * 方法描述 修改练习状态
     * @param: [id, status,pdfUrl]
     * @return: void
     * @author: DELL
     * @date: 2025/4/10 17:12
     */
    void updateStatusById(Long id,Integer status,String pdfUrl);

    /**
     * @description: 扫码解析处理
     * @author: lifengxu
     * @date: 2025/6/20 9:42
     */
    ReadingPersonalPassagesVo getQrDataAnalysis(String qrStr, Long studentId) throws BaseException;

    ReadingPersonalPassagesVo getPersonalPassagesByPrintId(Long printId, Long studentId) throws BaseException;

    /**
     * @description: 获取活动关联的套题信息
     * @author: lifengxu
     * @date: 2025/6/19 11:43
     */
    ReadingPersonalPassagesVo getPersonalPassagesByActivitySetsId(Long activitySetsId, Long studentId) throws BaseException;

    /**
     * 方法描述 生成再次练习
     * @param: [id]
     * @return: com.joinus.study.model.vo.ReadingPersonalPassagesVo
     * @author: DELL
     * @date: 2025/4/11 17:15
     */
    ReadingPersonalPassagesVo doPersonaldoAgain(Long id) throws BaseException;

    Page<ReadingWeekKnowledgePointVo> weakKnowledgePointList(ReadingStudentWeakKnowledgePointParam pageParam) throws BaseException;

    //获取本次训练消除的知识点
    List<ReadingWeekKnowledgePointVo> getClearWeekKnowledgePointList(Long personalPassageId, Long studentId) throws BaseException;

    /**
     * @Description 获取学生练习详情
     * <AUTHOR>
     * @date 2025/5/14
     */
    ReadingPersonalPassagesVo studentPersonalDetail(Long id) throws BaseException;

    BigDecimal getAccuracyRateByParam(ReadingPersonalPassageDataParam param) throws BaseException;

    /**
     * 方法描述 获取训练记录海报数据
     */
    ReadingReportPosterVO getReportPosterData(Long studentId) throws BaseException;

    /**
     * 方法描述 获取周报月报海报数据
     */
    ReadingPeriodicReportPosterVO getPeriodicReportPosterData(Long studentId, Integer reportType) throws BaseException;

    /**
     * @description: 保存练习记录和练习题目
     * @author: lifengxu
     * @date: 2025/6/18 9:43
     */
    void saveOrUpdatePersonalPassage(ReadingPersonalPassagesVo vo, Integer entryType) throws BaseException;

    /**
     * @description: 【暑期训练营】获取活动对应上次练习记录信息
     * @author: lifengxu
     * @date: 2025/6/5
     */
    ReadingActivityLastPracticeDto getActivityLastPractice(Long planId, Long studentId) throws BaseException;

    /**
     * @description: 获取活动入口类型
     * @param: [planId, studentId]
     * @return: java.lang.Integer 4 活动-强化训练 5 活动-巩固复习
     * @author: lifengxu
     * @date: 2025/6/5
     */
    int getActivityEntryType(Long planId, Long studentId) throws BaseException;

    /**
     * 方法描述 获取剩余打印次数
     */
    Integer getRemainingPrintTimes(Long studentId);

    /**
     * @description: 时间范围内每天阅读训练时间花费统计
     * @param: [studentId, startDate， endDate]
     * @author: ls
     * @date: 2025/7/14
     */
    List<ReadingWeekTimeStatisticsVO> getWeekReadingTimeSpent(Long studentId, LocalDate startDate, LocalDate endDate) throws BaseException;

    /**
     * @description: 时间范围内每天阅读训练成长周期
     * @param: [studentId, startDate， endDate]
     * @author: ls
     * @date: 2025/7/14
     */
    List<ReadingGrowthCycleVO> getWeekReadingGrowthCycle(Long studentId, LocalDate startDate, LocalDate endDate) throws BaseException;
}

