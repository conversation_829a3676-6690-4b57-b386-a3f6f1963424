package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.MathKnowledgePointMapper;
import com.joinus.study.model.bo.MathCatalogNodeStatisticsDto;
import com.joinus.study.model.entity.MathKnowledgePoint;
import com.joinus.study.service.MathKnowledgePointService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Service实现
* @createDate 2025-02-28 14:12:06
*/
@Service
public class MathKnowledgePointServiceImpl extends ServiceImpl<MathKnowledgePointMapper, MathKnowledgePoint>
    implements MathKnowledgePointService {

}
