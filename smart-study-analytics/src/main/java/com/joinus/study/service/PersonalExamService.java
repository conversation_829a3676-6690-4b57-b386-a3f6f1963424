package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.ExamGroupQuestionTypeInfoDto;
import com.joinus.study.model.dto.ExamQuestionInfoListData;
import com.joinus.study.model.dto.PersonalExamDto;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.entity.PersonalExamQuestion;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.model.vo.FileVO;
import com.joinus.study.model.vo.MathExamQuestionDetailVo;
import com.joinus.study.model.vo.PersonalExamQuestionVo;
import com.joinus.study.model.vo.PersonalExamVo;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【personal_exam】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface PersonalExamService extends IService<PersonalExam> {

    /**
     * 根据id获取试卷信息
     *
     * @param studentId
     * @param id
     * @return
     */
    ExamQuestionInfoListData examById(Long studentId, UUID id, boolean createExam, Long personalExamId, PublisherEnum publisher);

    List<QuestionKnowledgePoint> listWeakKnowledgePoints(Long personalExamId);

    /**
     * 试卷入库
     * @param personalExamVo
     */
    String saveExam(PersonalExamVo personalExamVo);
    /**
     * 获取学生所在区域试卷
     * @param param
     * @return
     */
    Page<PersonalExamDto> getRegionExamList(StudentAndTimePageParam param)  throws BaseException;

    /**
     * 获取区域下的试卷详情
     *
     * @param examId
     * @param personalExamId
     * @return
     */
    ExamGroupQuestionTypeInfoDto getPersonalExamExamDetail(UUID examId, Long personalExamId) throws BaseException;

    /**
     * 根据examId和examSource获取试卷原图或抹除笔记图
     * @param examId
     * @param examSource
     * @return
     */
    List<String> getExamImages(String examId, String examSource);

    List<PersonalExamQuestion> listQuestionByPersonalExamId(Long personalExamId);

    PersonalExam getLatestNoAnalyzedPersonalExam(UUID examId, Long studentId);

    MathExamQuestionDetailVo getExamQuestionDetail(UUID examId, UUID questionId, Long personalExamId);
    //获取原图
    List<FileVO> listOriginalFilesById(UUID questionId);

    Page<PersonalExamQuestionVo> getErrorQuestionPages(UUID knowledgePointId, Long studentId, Integer current, Integer size);

}
