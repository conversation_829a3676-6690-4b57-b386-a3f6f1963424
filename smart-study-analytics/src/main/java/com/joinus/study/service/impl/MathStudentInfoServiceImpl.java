package com.joinus.study.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.MathStudentInfoMapper;
import com.joinus.study.model.dto.GradeInfo;
import com.joinus.study.model.entity.MathStudentInfo;
import com.joinus.study.model.enums.GradeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.MathStudentInfoVo;
import com.joinus.study.service.ActiveStudentsCacheService;
import com.joinus.study.service.GradeService;
import com.joinus.study.service.MathStudentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【math_student_info(数学学生信息表)】的数据库操作Service实现
 * @createDate 2025-08-13 14:48:09
 */
@Service
public class MathStudentInfoServiceImpl extends ServiceImpl<MathStudentInfoMapper, MathStudentInfo>
        implements MathStudentInfoService {

    @Autowired
    private ActiveStudentsCacheService activeStudentsCacheService;
    @Autowired
    private GradeService gradeService;

    @Override
    public MathStudentInfoVo getStudentInfo(Long studentId) {
        MathStudentInfo mathStudentInfo = lambdaQuery().eq(MathStudentInfo::getStudentId, studentId).one();
        if (null == mathStudentInfo) {
            GradeInfo gradeByStudentId = gradeService.getGradeByStudentId(studentId);
            if (null == gradeByStudentId || gradeByStudentId.getCurrentGradeLevel() < 3 || gradeByStudentId.getCurrentGradeLevel() > 12) {
                return MathStudentInfoVo.builder().exist(false).build();
            }
            PublisherEnum publisher = getDefaultPublisherByGrade(gradeByStudentId.getCurrentGradeLevel());
            return MathStudentInfoVo.builder()
                    .grade(gradeByStudentId.getCurrentGradeLevel())
                    .gradeName(GradeEnum.ofValue(gradeByStudentId.getCurrentGradeLevel()).getDesc())
                    .publisher(publisher)
                    .publisherName(publisher.getDescription())
                    .exist(false).build();
        }
        return MathStudentInfoVo.ofMathStudentInfo(mathStudentInfo);
    }

    private PublisherEnum getDefaultPublisherByGrade(Integer currentGradeLevel) {
        GradeEnum gradeEnum = GradeEnum.ofValue(currentGradeLevel);
        PublisherEnum publisherEnum = gradeEnum.getSchoolStage().getPublishers().get(0);
        return publisherEnum;
    }

    @Override
    public MathStudentInfoVo addStudentInfo(Long studentId, PublisherEnum publisher, Integer grade) {
        MathStudentInfo mathStudentInfo = lambdaQuery().eq(MathStudentInfo::getStudentId, studentId).one();
        Assert.isNull(mathStudentInfo, "学生信息已存在");
        mathStudentInfo = MathStudentInfo.builder()
                .studentId(studentId)
                .publisher( publisher)
                .grade(grade)
                .build();
        baseMapper.insert(mathStudentInfo);
        return getStudentInfo(studentId);
    }

    @Override
    public MathStudentInfoVo updateStudentInfo(Long studentId, PublisherEnum publisher, Integer grade) {
        MathStudentInfo mathStudentInfo = lambdaQuery().eq(MathStudentInfo::getStudentId, studentId).one();
        Assert.notNull(mathStudentInfo, "学生信息不存在");
        lambdaUpdate().set(MathStudentInfo::getPublisher, publisher)
                .set(MathStudentInfo::getGrade, grade)
                .eq(MathStudentInfo::getStudentId, studentId)
                .update();
        return getStudentInfo(studentId);
    }
}




