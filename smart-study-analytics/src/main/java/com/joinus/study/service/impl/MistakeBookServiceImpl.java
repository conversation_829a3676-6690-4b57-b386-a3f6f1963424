package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.exception.BaseException;
import com.joinus.common.exception.BusinessException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.MasteryDegreeEnum;
import com.joinus.study.model.enums.MistakeBookSourceEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.SubjectEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
* <AUTHOR>
* @description 针对表【mistake_book】的数据库操作Service实现
* @createDate 2025-03-11 09:47:33
*/
@Service
@Slf4j
public class MistakeBookServiceImpl extends ServiceImpl<MistakeBookMapper, MistakeBook>
    implements MistakeBookService{

    @Resource
    private QuestionKnowledgePointService questionKnowledgePointService;
    @Resource
    private StudyRecordMapper studyRecordMapper;
    @Resource
    private EduKnowLedgeHubBusinessService businessService;
    @Resource
    private ExamAnalyzeResultService resultService;
    @Resource
    private StudyRecordQuestionService studyRecordQuestionService;
    @Resource
    private QuestionAnswerFeedbackService feedbackService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private MathQuestionsMapper mathQuestionsMapper;
    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private QuestionKnowledgePointMapper questionKnowledgePointMapper;
    @Resource
    private MathKnowledgePointMapper mathKnowledgePointMapper;
    @Resource
    private PersonalExamService personalExamService;
    @Override
    public void add(List<MistakeBookAddParam> mistakeBookAddParams) {
        List<MistakeBook> mistakeBookList = new ArrayList<>();
        mistakeBookAddParams.forEach(bookAddParam -> {
            CommonResponse.ERROR.assertNotNull(bookAddParam.getStudentId(), "studentId不能为空");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getQuestionId(), "题目id不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getSource(), "来源不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getSubject(), "题目类型不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getStudyId(), "学习记录id不能为空！");
            //查询题目答案相关
            List<StudyRecordQuestionDetailsVo> questionDetailsVo = studyRecordMapper.getStudyQuestionAnswer(bookAddParam);
            CommonResponse.ERROR.assertCollNotNull(questionDetailsVo, "请勿加入错误题目！");
            CommonResponse.ERROR.assertNotNull(questionDetailsVo.get(0), "题目不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswer(), "题目答案不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswerContent(), "题目答案解析不完整！");
            MistakeBook mistakeBook = BeanUtil.copyProperties(bookAddParam, MistakeBook.class);
            mistakeBook.setSourceId(questionDetailsVo.get(0).getId());
            mistakeBook.setQuestionAnswer(questionDetailsVo.get(0).getQuestionAnswer());
            mistakeBook.setQuestionAnswerContent(questionDetailsVo.get(0).getQuestionAnswerContent());
            mistakeBook.setDifficulty(questionDetailsVo.get(0).getDifficulty());
            QueryWrapper<MistakeBook> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("student_id", mistakeBook.getStudentId());
            queryWrapper.eq("question_id", mistakeBook.getQuestionId());
            List<MistakeBook> mistakeBookBefore = this.baseMapper.selectList(queryWrapper);
            mistakeBook.setCreatedAt(new Date());
            mistakeBook.setKnowledgePointIds(bookAddParam.getKnowledgePointIds());
            if(CollectionUtils.isEmpty(mistakeBookBefore)){
                mistakeBookList.add(mistakeBook);
            }else {
//                mistakeBook.setId(mistakeBookBefore.get(0).getId());
//                mistakeBook.setUpdatedAt(new Date());
//                //this.updateById(mistakeBook);
//                baseMapper.editById(mistakeBook);
                baseMapper.deleteById(mistakeBookBefore.get(0).getId());
            }
        });
        //this.saveBatch(mistakeBookList);
        if (CollectionUtils.isNotEmpty(mistakeBookList)){
            baseMapper.batchInsert(mistakeBookList);
        }
    }

    @Override
    public void addOne(MistakeBookAddParam bookAddParam) {
        CommonResponse.ERROR.assertNotNull(bookAddParam.getStudentId(), "studentId不能为空");
        CommonResponse.ERROR.assertNotNull(bookAddParam.getQuestionId(), "题目id不能为空！");
        CommonResponse.ERROR.assertNotNull(bookAddParam.getSource(), "来源不能为空！");
        CommonResponse.ERROR.assertNotNull(bookAddParam.getSubject(), "题目类型不能为空！");
        MistakeBook mistakeBook = BeanUtil.copyProperties(bookAddParam, MistakeBook.class);
        if (bookAddParam.getSource() != MistakeBookSourceEnum.wrong_topic) {
            CommonResponse.ERROR.assertNotNull(bookAddParam.getStudyId(), "学习记录id不能为空！");
            //查询题目答案相关
            List<StudyRecordQuestionDetailsVo> questionDetailsVo = studyRecordMapper.getStudyQuestionAnswer(bookAddParam);
            CommonResponse.ERROR.assertCollNotNull(questionDetailsVo, "请勿加入错误题目！");
            CommonResponse.ERROR.assertNotNull(questionDetailsVo.get(0), "题目不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswer(), "题目答案不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswerContent(), "题目答案解析不完整！");
            mistakeBook.setSourceId(questionDetailsVo.get(0).getId());
            mistakeBook.setQuestionAnswer(questionDetailsVo.get(0).getQuestionAnswer());
            mistakeBook.setQuestionAnswerContent(questionDetailsVo.get(0).getQuestionAnswerContent());
            mistakeBook.setDifficulty(questionDetailsVo.get(0).getDifficulty());
        }
        MathQuestionsEntity questions = mathQuestionsMapper.selectById(bookAddParam.getQuestionId());
        mistakeBook.setDifficulty(questions.getDifficulty());
        QueryWrapper<MistakeBook> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", mistakeBook.getStudentId());
        queryWrapper.eq("question_id", mistakeBook.getQuestionId());
        List<MistakeBook> mistakeBookBefore = this.baseMapper.selectList(queryWrapper);
        mistakeBook.setCreatedAt(new Date());
        mistakeBook.setKnowledgePointIds(bookAddParam.getKnowledgePointIds());
        if (CollectionUtils.isEmpty(mistakeBookBefore)) {
            baseMapper.insert(mistakeBook);
        } else {
//            mistakeBook.setId(mistakeBookBefore.get(0).getId());
//            mistakeBook.setUpdatedAt(new Date());
//            baseMapper.editById(mistakeBook);
            baseMapper.deleteById(mistakeBookBefore.get(0).getId());
        }
    }

    @Override
    public Page pages(QueryMistakeBookParam param) {
       Page page = new Page(param.getCurrent(),param.getSize());
       List<MistakeBookVo> mistakeBookVoList=baseMapper.pages(page, param);
       mistakeBookVoList.forEach(mistakeBookVo -> {
           MathQuestionsEntity entity = mathQuestionsMapper.selectById(mistakeBookVo.getQuestionId());
           mistakeBookVo.setQuestionContent(eduKnowledgeHubService.decodeContentV2(entity.getContent()));
           List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointList = getKnowledgePointList(mistakeBookVo.getKnowledgePointIds());

           //处理知识点来源组装知识点
           if (MistakeBookSourceEnum.exam_diagnosis.equals(mistakeBookVo.getSource())){
               //获取题目图片
               mistakeBookVo.setFilesVos(baseMapper.getFilesByQuestionId(mistakeBookVo.getQuestionId()));
               mistakeBookVo.getFilesVos().forEach(filesVo -> {
                   PresignedUrlParam urlParam= new PresignedUrlParam();
                   urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                   urlParam.setOssKey(filesVo.getOssUrl());
                   PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                   if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                       filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                   }
               });
               if (knowledgePointList != null) {
                   mistakeBookVo.setQuestionKnowledgePointList(knowledgePointList);
               }else {
                   //试卷诊断
                   List<ExamAnalyzeResult> result = resultService.getByPersonalExamId(mistakeBookVo.getSourceId());
                   if (ObjectUtil.isNotEmpty(result)){
                       PersonalExam personalExam = personalExamService.getById(result.get(0).getPersonalExamId());
                       List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints=questionKnowledgePointService.getListByExamIdAndQuestionId(result.get(0).getExamId(), mistakeBookVo.getQuestionId(),personalExam.getPublisher());
                       mistakeBookVo.setQuestionKnowledgePointList(knowledgePoints);
                   }
               }
           }else {
               if (knowledgePointList != null) {
                   mistakeBookVo.setQuestionKnowledgePointList(knowledgePointList);
               }
               //拍照时候
               List <StudyRecordQuestion> studyRecordQuestions= studyRecordQuestionService.getByStudyIdAndQuestionId(mistakeBookVo.getSourceId(), mistakeBookVo.getQuestionId());
               if (CollectionUtils.isNotEmpty(studyRecordQuestions)){
                   String knowledgePoint = studyRecordQuestions.get(0).getKnowledgePoint();
                  // 获取图片
                           List<String> list = Arrays.asList(studyRecordQuestions.get(0).getOssKeys().split(","));
                   List<FilesVo> filesVos = new ArrayList<>();
                           list.forEach(ossKey -> {
                               FilesVo filesVo = new FilesVo();
                               PresignedUrlParam urlParam= new PresignedUrlParam();
                               urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                               urlParam.setOssKey(ossKey);
                               PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                               if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                                   filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                               }
                               filesVos.add(filesVo);
                           });
                   mistakeBookVo.setFilesVos(filesVos);
                   if (knowledgePointList == null) {
                       if (StringUtils.isNotEmpty(knowledgePoint)){
                           ObjectMapper mapper = new ObjectMapper();
                           try {
                               List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList =
                                       mapper.readValue(knowledgePoint, new TypeReference<List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO>>(){});
                               mistakeBookVo.setQuestionKnowledgePointList(knowledgePointsList);
                           } catch (IOException e) {
                               log.error(e.getMessage());
                           }
                       }
                   }
               }
           }
         });
       page.setRecords(mistakeBookVoList);
       return page;
    }

    @Override
    public MistakeBookDetailsVo geDetails(QueryMistakeBookParam param) {
        CommonResponse.ERROR.assertNotNull(param.getId(), "错题id不能为空");
        MistakeBook mistakeBook = this.getById(param.getId());
        MistakeBookDetailsVo detailsVo = BeanUtil.copyProperties(mistakeBook, MistakeBookDetailsVo.class);
        List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointList = getKnowledgePointList(mistakeBook.getKnowledgePointIds());
        if (knowledgePointList != null) {
            detailsVo.setQuestionKnowledgePointList(knowledgePointList);
        } else {
            //查询题目知识点根据来源获取
            if (MistakeBookSourceEnum.exam_diagnosis.equals(mistakeBook.getSource())){
                //试卷诊断
                List<ExamAnalyzeResult> result = resultService.getByPersonalExamId(mistakeBook.getSourceId());
                if (CollectionUtils.isNotEmpty(result)){
                    PersonalExam personalExam = personalExamService.getById(result.get(0).getPersonalExamId());
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints=questionKnowledgePointService.getListByExamIdAndQuestionId(result.get(0).getExamId(), mistakeBook.getQuestionId(),personalExam.getPublisher());
                    detailsVo.setQuestionKnowledgePointList(knowledgePoints);
                }
            }else {
                List <StudyRecordQuestion> studyRecordQuestions= studyRecordQuestionService.getByStudyIdAndQuestionId(mistakeBook.getSourceId(), mistakeBook.getQuestionId());
                if (CollectionUtils.isNotEmpty(studyRecordQuestions)){
                    String knowledgePoint = studyRecordQuestions.get(0).getKnowledgePoint();
                    if (StringUtils.isNotEmpty(knowledgePoint)){
                        ObjectMapper mapper = new ObjectMapper();
                        try {
                            List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList =
                                    mapper.readValue(knowledgePoint, new TypeReference<List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO>>(){});
                            knowledgePointsList.stream().forEach(knowledgePointsDTO -> {
                                knowledgePointsDTO.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getId()));
                            });
                            detailsVo.setQuestionKnowledgePointList(knowledgePointsList);
                        } catch (IOException e) {
                            log.error(e.getMessage());
                        }
                    }
                }
            }
        }
        //查询题目详情和文件详情
        QuestionVo questionById = studyRecordMapper.getQuestionById(mistakeBook.getQuestionId());
        if (null == questionById) {
            throw new BaseException("未查询到相关题目");
        }
        questionById.setContent(eduKnowledgeHubService.decodeContentV2(questionById.getContent()));
        detailsVo.setQuestionVo(questionById);
        PresignedUrlParam urlParam= new PresignedUrlParam();
        urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
        detailsVo.getQuestionVo().getFilesVos().forEach(filesVo -> {
            if (ObjectUtil.isNotEmpty(filesVo.getOssUrl())){
                urlParam.setOssKey(filesVo.getOssUrl());
                PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                    filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                }
            }
        });
        for (int i = 0; i < detailsVo.getQuestionVo().getFilesVos().size(); i++) {
            if (detailsVo.getQuestionVo().getFilesVos().get(i).getOssUrl() == null) {
                detailsVo.getQuestionVo().getFilesVos().remove(i);
            }
        }
        MistakeBookDetailsVo adjacentData =  baseMapper.getAdjacentData(param.getId(),mistakeBook.getStudentId());
        detailsVo.setCurrentIndex(adjacentData.getCurrentIndex());
        detailsVo.setNextId(adjacentData.getNextId());
        detailsVo.setBeforeId(adjacentData.getBeforeId());
        detailsVo.setQuestionTotal(adjacentData.getQuestionTotal());

        //是否点过赞
        List<QuestionAnswerFeedback> studyRecordQuestions = feedbackService.getIsUpvote(mistakeBook.getStudentId(), mistakeBook.getQuestionId());
        detailsVo.setIsUpvote(studyRecordQuestions.size()>0?1:0);
        return detailsVo;
    }

    @Override
    public Flux<String> getCompleteQuestionAnswerDto(Long id) {
        MistakeBook mistakeBook = this.getById(id);
        QuestionAnswerDto  questionAnswerDto= QuestionAnswerDto.builder()
                .answer(mistakeBook.getQuestionAnswer())
                .content(mistakeBook.getQuestionAnswerContent())
                .build();
        return businessService.createStreamFromQuestionAnswerDto(questionAnswerDto);
    }

    @Override
    public void examMistakeSave(ExamMistakeSaveParam param) {
        long startTime = System.currentTimeMillis();
        log.info("examMistakeSave 时间 1: " + (System.currentTimeMillis()-startTime));
//        QueryWrapper<PersonalExam> queryPersonalWrapper = new QueryWrapper<>();
//        queryPersonalWrapper.eq("exam_id", param.getExamId());
//        queryPersonalWrapper.eq("student_id", param.getStudentId());
//        PersonalExam personalExam = personalExamService.getOne(queryPersonalWrapper);
        PersonalExam personalExam = personalExamService.getById(param.getPersonalExamId());
        log.info("examMistakeSave 时间 2: " + (System.currentTimeMillis()-startTime));
        List<ExamMistakeInfoVo> examQuestionMisInfoList = this.baseMapper.getExamMistake(personalExam.getId());
        log.info("examMistakeSave 时间 3: " + (System.currentTimeMillis()-startTime));
        if(DataUtils.isNotEmpty(examQuestionMisInfoList)){
            List<UUID> questionIdList = examQuestionMisInfoList.stream()
                    .map(ExamMistakeInfoVo::getQuestionId).collect(Collectors.toList());
            List<ExamMistakeInfoVo> answerList = this.baseMapper.getAnswerByQuestionId(questionIdList);
            log.info("examMistakeSave 时间 4: " + (System.currentTimeMillis()-startTime));
            Map<UUID, ExamMistakeInfoVo> answerMap = answerList.stream()
                    .collect(Collectors.toMap(ExamMistakeInfoVo::getQuestionId, vo -> vo, (existing, replacement) -> existing));
            for (ExamMistakeInfoVo examInfo : examQuestionMisInfoList) {
                ExamMistakeInfoVo answerInfo = answerMap.get(examInfo.getQuestionId());
                if (answerInfo != null) {
                    if (answerInfo.getQuestionAnswer() != null) {
                        examInfo.setQuestionAnswer(answerInfo.getQuestionAnswer());
                    }
                    if (answerInfo.getQuestionContent() != null) {
                        examInfo.setQuestionContent(answerInfo.getQuestionContent());
                    }
                }
            }
            log.info("examMistakeSave 时间 5: " + (System.currentTimeMillis()-startTime));

            LambdaQueryWrapper<QuestionKnowledgePoint> batchQueryWrapper = new LambdaQueryWrapper<>();
            batchQueryWrapper.in(QuestionKnowledgePoint::getQuestionId, questionIdList);
            batchQueryWrapper.eq(QuestionKnowledgePoint::getExamId, param.getExamId());
            batchQueryWrapper.eq(QuestionKnowledgePoint::getPublisher, personalExam.getPublisher());

            List<QuestionKnowledgePoint> allKnowledgePoints = questionKnowledgePointMapper.selectList(batchQueryWrapper);

            // 按questionId分组知识点
            Map<UUID, List<QuestionKnowledgePoint>> knowledgePointMap = allKnowledgePoints.stream()
                    .collect(Collectors.groupingBy(QuestionKnowledgePoint::getQuestionId));
            log.info("examMistakeSave 时间 6: " + (System.currentTimeMillis()-startTime));
            for(ExamMistakeInfoVo eqmp : examQuestionMisInfoList){
                /*LambdaQueryWrapper<QuestionKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(QuestionKnowledgePoint::getQuestionId, eqmp.getQuestionId());
                queryWrapper.eq(QuestionKnowledgePoint::getExamId, eqmp.getExamId());
                queryWrapper.eq(QuestionKnowledgePoint::getPublisher, eqmp.getPublisher());
                List<QuestionKnowledgePoint> list = questionKnowledgePointMapper.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    UUID[] knowledgePointIds = list.stream()
                            .map(QuestionKnowledgePoint::getKnowledgePointId)
                            .toArray(UUID[]::new);
                    eqmp.setKnowledgePointIds(knowledgePointIds);
                }*/

                // 使用批量查询结果
                List<QuestionKnowledgePoint> list = knowledgePointMap.getOrDefault(eqmp.getQuestionId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(list)) {
                    UUID[] knowledgePointIds = list.stream()
                            .map(QuestionKnowledgePoint::getKnowledgePointId)
                            .toArray(UUID[]::new);
                    eqmp.setKnowledgePointIds(knowledgePointIds);
                }

                List<MistakeBook> mistakeBooks = this.baseMapper.selectList(Wrappers.lambdaQuery(MistakeBook.class)
                        .eq(MistakeBook::getStudentId, param.getStudentId()).eq(MistakeBook::getQuestionId, eqmp.getQuestionId()));
                log.info("examMistakeSave 时间 7: " + (System.currentTimeMillis()-startTime));
                if(DataUtils.isNotEmpty(mistakeBooks)){
                    MistakeBook mistakeBook = mistakeBooks.get(0);
                    if(DataUtils.isNotEmpty(eqmp.getDifficulty())){
                        mistakeBook.setDifficulty(eqmp.getDifficulty());
                    }
                    mistakeBook.setSourceId(eqmp.getPerExamId());
                    mistakeBook.setSubject(SubjectEnum.math);
                    mistakeBook.setSource(MistakeBookSourceEnum.exam_diagnosis);
                    mistakeBook.setQuestionAnswer(eqmp.getQuestionAnswer());
                    mistakeBook.setQuestionAnswerContent(eqmp.getQuestionContent());
                    mistakeBook.setKnowledgePointIds(eqmp.getKnowledgePointIds());
                    baseMapper.updateByMBId(mistakeBook);
                }else{
                    MistakeBook mistakeBook = new MistakeBook();
                    mistakeBook.setStudentId(param.getStudentId());
                    mistakeBook.setQuestionId(eqmp.getQuestionId());
                    mistakeBook.setSource(MistakeBookSourceEnum.exam_diagnosis);
                    mistakeBook.setSubject(SubjectEnum.math);
                    mistakeBook.setQuestionAnswer(eqmp.getQuestionAnswer());
                    mistakeBook.setQuestionAnswerContent(eqmp.getQuestionContent());
                    mistakeBook.setSourceId(eqmp.getPerExamId());
                    if(DataUtils.isNotEmpty(eqmp.getDifficulty())){
                        mistakeBook.setDifficulty(eqmp.getDifficulty());
                    }
                    mistakeBook.setKnowledgePointIds(eqmp.getKnowledgePointIds());
                    this.baseMapper.insert(mistakeBook);
                }
            }
            log.info("examMistakeSave 时间 8: " + (System.currentTimeMillis()-startTime));
        }
    }

    @Override
    public void deleteById(Long id) {
//        MistakeBook mistakeBook = new MistakeBook();
//        mistakeBook.setUpdatedAt(new Date());
//        mistakeBook.setDeletedAt(new Date());
//        mistakeBook.setId(id);
//        baseMapper.updateById(mistakeBook);
        baseMapper.deleteById(id);
    }

    @Override
    public Boolean getIsAddMistakesBook(Long studentId, UUID questionId, Long sourceid, MistakeBookSourceEnum type) {
        LambdaQueryWrapper<MistakeBook> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MistakeBook::getStudentId, studentId)
                .isNull(MistakeBook::getDeletedAt);
        if (questionId  != null) {
            queryWrapper.eq(MistakeBook::getQuestionId, questionId);
        }
        if (sourceid != null) {
            queryWrapper.eq(MistakeBook::getSourceId, sourceid);
        }
        if (type != null){
            queryWrapper.eq(MistakeBook::getSource, type.toString());
        }
        return baseMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public void batchUpdate(ExamMistakeUpdateParam param) {
        CommonResponse.ERROR.assertNotNull(param.getMistakeBookIds(), "错题本id集合不能为空");
        if (param.getMasteryDegree() != null){
            UpdateWrapper<MistakeBook> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().in(MistakeBook::getId, param.getMistakeBookIds())
                    .set(MistakeBook::getMasteryDegree, param.getMasteryDegree())
                    .set(MistakeBook::getUpdatedAt, new Date());
            this.update(updateWrapper);
        }
        if (param.getIsDelete() != null && param.getIsDelete()){
            LambdaQueryWrapper<MistakeBook> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.in(MistakeBook::getId, param.getMistakeBookIds());
            this.remove(deleteWrapper);
        }
    }
    //获取知识点
    private List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getKnowledgePointList(Object KnowledgePointIds) {
        if (KnowledgePointIds != null) {
            List<UUID> knowledgePointIdList = new ArrayList<>();
            if (KnowledgePointIds instanceof UUID[]) {
                UUID[] uuidArray = (UUID[]) KnowledgePointIds;
                Collections.addAll(knowledgePointIdList, uuidArray);
            }
            List<KnowledgePointDto> knowledgePointFromViewByIds = mathKnowledgePointMapper.getKnowledgePointFromViewByIds(knowledgePointIdList);
            if (CollectionUtils.isNotEmpty(knowledgePointFromViewByIds)) {
                List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList = new ArrayList<>();
                for (KnowledgePointDto knowledgePointDto : knowledgePointFromViewByIds) {
                    KnowledgePointsVO.DataDTO.KnowledgePointsDTO dto = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
                    // 根据实际字段进行映射
                    dto.setId(knowledgePointDto.getId());
                    dto.setName(knowledgePointDto.getName());
                    dto.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointDto.getId()));
                    // 映射其他需要的字段
                    knowledgePointsList.add(dto);
                }
                return knowledgePointsList;
            }
        }
        return null;
    }
}




