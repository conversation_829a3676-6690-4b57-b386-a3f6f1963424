package com.joinus.study.service;

import com.joinus.study.model.entity.QuestionAnswerFeedback;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.param.QuestionAnswerFeedbackParam;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_answer_feedback】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface QuestionAnswerFeedbackService extends IService<QuestionAnswerFeedback> {

    void add(QuestionAnswerFeedbackParam param);

    void cancelUpvote(QuestionAnswerFeedbackParam param);


    List<QuestionAnswerFeedback> getIsUpvote(Long studentId, UUID questionId);
}
