package com.joinus.study.service;

import com.joinus.study.model.enums.MathSyncLearningTestNodeTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.MistakeSetQuestionsParam;
import com.joinus.study.model.param.SpecializedTrainingUpdateExamParam;
import com.joinus.study.model.vo.*;

import java.util.List;
import java.util.UUID;

public interface MathSyncLearningTestService {

    List<MathCatalogNodeVo> listCatalogRootNodes(Integer grade, PublisherEnum publisher, Integer semester, Long studentId);

    List<MathCatalogNodeVo> listCatalogSubNodes(UUID rootNodeId, Long studentId);

    MathSectionPointsVo getSectionDetailInfo(UUID sectionId,Long studentId);

    /**
     * 查询节点掌握程度
     * @param nodeId
     * @param studentId
     * @param testType MathSyncLearningTestNodeTypeEnum
     * @return
     */
    Double selectNodesMasteryDegree(UUID nodeId, Long studentId, MathSyncLearningTestNodeTypeEnum testType);

    /**
     * 生成同步学练测记录表数据
     * @param param
     */
    void saveSyncLearningTestRecord(SpecializedTrainingUpdateExamParam param);

    /**
     * 查询知识点详情 精讲
     * @param knowledgePointId
     * @param publisher
     * @return
     */
    MathKnowledgePointHandoutVo getMathKnowledgePointVOById(UUID knowledgePointId, String publisher);

    /**
     * 创建知识点精讲题
     * @param knowledgePointId
     * @return
     */
    SpecializedTrainingNewResultVoV2 createQuestionsByKnowledgePoint(UUID knowledgePointId,PublisherEnum  publisher);

    /**
     * 创建错题集
     * @param
     * @return
     */
    SpecializedTrainingNewResultVoV2 createMistakeSetQuestions(MistakeSetQuestionsParam param);
}
