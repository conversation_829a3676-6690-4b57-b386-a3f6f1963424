package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.param.ReadingPassagesPageParam;
import com.joinus.study.model.param.ReadingPassagesUpdateBatchParam;
import com.joinus.study.model.param.ReadingPassagesUpdateParam;
import com.joinus.study.model.vo.ReadingGradeVO;
import com.joinus.study.model.vo.ReadingPassagesBackendVO;

import java.util.List;
import java.util.UUID;

public interface ReadingPassagesBackendService {
    /**
     * 分页
     *
     * @param pageParam
     * @return
     */
    Page<ReadingPassagesBackendVO> pages(ReadingPassagesPageParam pageParam);

    /**
     * 批量删除
     *
     * @param updateParam
     */
    void batchDelete(ReadingPassagesUpdateBatchParam updateParam);

    /**
     * 挂起
     *
     * @param updateParam
     */
    void disable(ReadingPassagesUpdateBatchParam updateParam);

    /**
     * 启用
     *
     * @param updateParam
     */
    void enable(ReadingPassagesUpdateBatchParam updateParam);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ReadingPassagesBackendVO query(UUID id);

    /**
     * 修改
     *
     * @param updateParam
     */
    void update(ReadingPassagesUpdateParam updateParam);

    /**
     * 年级-学期-单元列表
     *
     * @return
     */
    List<ReadingGradeVO> listGradeSemesterUnit();

    /**
     * 审核通过
     *
     * @param updateParam
     */
    void auditPass(ReadingPassagesUpdateBatchParam updateParam);

    void auditNoPass(ReadingPassagesUpdateBatchParam updateParam);
}
