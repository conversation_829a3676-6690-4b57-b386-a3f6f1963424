package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.StudyRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.param.ExampleQuestionParam;
import com.joinus.study.model.param.QueryStudyRecordParam;
import com.joinus.study.model.param.QuestionDetailParam;
import com.joinus.study.model.param.StudyRecordSaveParam;
import com.joinus.study.model.vo.ExampleQuestionVo;
import com.joinus.study.model.vo.QuestionVo;
import com.joinus.study.model.vo.StudyRecordQuestionDetailsVo;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【study_record】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface StudyRecordService extends IService<StudyRecord> {

    /**
     * 查询学习记录分页列表
     * @param param
     * @return
     */
    Page pages(QueryStudyRecordParam param);

    /**
     * 查询学习记录详情
     * @param id
     * @return
     */
    List<StudyRecordQuestionDetailsVo> detail(Long id);

    /**
     * 查询题目
     * @return
     */
    Flux<String> questionDetail(Long studyId,String questionId, Long studentId);

    /*
     * 学习记录单题查询
     * */
    Flux<String> questionsDetail(QuestionDetailParam param);

}
