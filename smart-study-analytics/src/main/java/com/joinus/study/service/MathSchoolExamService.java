package com.joinus.study.service;

import com.joinus.study.model.entity.MathSchoolExam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.param.ImportSchoolExamParam;
import com.joinus.study.model.vo.MathSchoolExamVo;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_school_exam(数学学校试卷关系表)】的数据库操作Service
* @createDate 2025-07-14 19:24:52
*/
public interface MathSchoolExamService extends IService<MathSchoolExam> {

    void importSchoolExamFromExcel(List<ImportSchoolExamParam> dataList);

    void deleteExamSchoolRelation(UUID examId);

    void updateExamSchoolRelation(UUID examId, List<Long> schoolIds);

    List<MathSchoolExam> listExamSchoolRelationByExamId(UUID examId);

    List<MathSchoolExamVo> listExamSchoolRelation(Long schoolId, List<UUID> examIds);
}
