package com.joinus.study.service;

import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.IpayRefundParam;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/12 14:17
 **/
@Slf4j
@Component
public class PayService {
    @Value("${ipay-url:http://127.0.0.1:8690}")
    private String payUrl;

    public String invokePay(IpayPayRequest request) throws BaseException {
        String response = LocalHttpUtil.post(payUrl + "/api/ipay/trade/order/pay", request);
        if (response != null && response.contains("data")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            return parsedObject.getData();
        } else if (response != null && response.contains("message")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            throw new BaseException(parsedObject.getCode(), parsedObject.getMessage());
        }
        return null;
    }

    public String invokeRefund(IpayRefundParam refundParam) throws BaseException{
        String response = LocalHttpUtil.post(payUrl + "/api/ipay/trade/order/refund", refundParam);
        if (response != null && response.contains("data")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            return parsedObject.getData();
        } else if (response != null && response.contains("message")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            throw new BaseException(parsedObject.getCode(), parsedObject.getMessage());
        }
        return null;
    }
    //退款订单退款状态查询
    public String refundQuery(IpayRefundParam refundParam) {
        String response = LocalHttpUtil.post(payUrl + "/api/ipay/trade/order/refund/query", refundParam);
        if (response != null && response.contains("data")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            return parsedObject.getData();
        } else if (response != null && response.contains("message")){
            Response parsedObject = JSONObject.parseObject(response, Response.class);
            throw new BaseException(parsedObject.getCode(), parsedObject.getMessage());
        }
        return null;
    }

    @Data
    private class Response {
        private Integer code;
        private String message;
        private String data;
    }
}
