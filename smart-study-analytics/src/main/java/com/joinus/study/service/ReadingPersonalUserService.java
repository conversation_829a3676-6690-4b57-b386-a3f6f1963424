package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.ReadingPersonalUserPageParam;
import com.joinus.study.model.vo.ReadingPersonalUserVO;
import com.joinus.study.model.vo.ReadingUserDataOverviewVO;

public interface ReadingPersonalUserService extends IService<ReadingPersonalUser> {
    /**
     * 根据学生id查询
     *
     * @param studentId
     * @return
     */
    ReadingPersonalUser getByStudentId(Long studentId);

    Page<ReadingPersonalUserVO> pages(ReadingPersonalUserPageParam pageParam);

    ReadingUserDataOverviewVO dataOverview();

}
