package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishFowDeputyQuestionMapper;
import com.joinus.study.model.entity.EnglishFowDeputyQuestion;
import com.joinus.study.service.EnglishFowDeputyQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class EnglishFowDeputyQuestionServiceImpl extends ServiceImpl<EnglishFowDeputyQuestionMapper, EnglishFowDeputyQuestion> implements EnglishFowDeputyQuestionService {

    @Override
    public List<EnglishFowDeputyQuestion> listByExamId(UUID examId) {
        LambdaQueryWrapper<EnglishFowDeputyQuestion> queryWrapper = Wrappers.lambdaQuery(EnglishFowDeputyQuestion.class)
                .eq(EnglishFowDeputyQuestion::getExamId, examId)
                .orderByAsc(EnglishFowDeputyQuestion::getQuestionNum)
                .orderByAsc(EnglishFowDeputyQuestion::getOrderNo);
        return this.list(queryWrapper);
    }
}
