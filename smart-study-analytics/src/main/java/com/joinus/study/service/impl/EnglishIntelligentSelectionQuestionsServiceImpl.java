package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO;
import com.joinus.study.model.entity.EnglishPersonalWeakKnowledgePoint;
import com.joinus.study.model.param.EnglishAiQuestionQueryParam;
import com.joinus.study.model.param.EnglishIntelligentSelectionQuestionQueryParam;
import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import com.joinus.study.model.vo.EnglishAiQuestionVO;
import com.joinus.study.model.vo.EnglishPersonalExerciseQuestionVO;
import com.joinus.study.model.vo.EnglishPersonalExerciseVO;
import com.joinus.study.service.*;
import com.joinus.study.model.param.EnglishPersonalExerciseCreateParam;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("englishIntelligentSelectionQuestionsService")
@Slf4j
public class EnglishIntelligentSelectionQuestionsServiceImpl implements EnglishIntelligentSelectionQuestionsService {

    @Value("${english.ai.host.url:https://bun-hono-ai-english-server.uat.qingyulan.net}")
    private String englishAiHostUrl;

    @Resource
    private EnglishAiQuestionService englishAiQuestionService;
    @Resource
    private EnglishPersonalWeakKnowledgePointService englishPersonalWeakKnowledgePointService;
    @Resource
    private EnglishPersonalExerciseService englishPersonalExerciseService;
    @Resource
    private EnglishExamDiagnoseQuestionService englishExamDiagnoseQuestionService;
    @Resource
    private EnglishPersonalExerciseQuestionService englishPersonalExerciseQuestionService;

    @Override
    public EnglishPersonalExerciseVO directionalBlastingExerciseQuestion(EnglishIntelligentSelectionQuestionQueryParam queryParam) {
        CommonResponse.ERROR.assertNotNull(queryParam.getPersonalWeakKnowledgePointId(), "学生薄弱知识点id不能为空！");
        // 查询学生的薄弱知识点
        EnglishPersonalWeakKnowledgePoint personalWeakKnowledgePoint = englishPersonalWeakKnowledgePointService.query(queryParam.getPersonalWeakKnowledgePointId());
        CommonResponse.ERROR.assertNotNull(personalWeakKnowledgePoint, "未获取到学生薄弱知识点！");
        CommonResponse.ERROR.assertNotNull(personalWeakKnowledgePoint.getKnowledgePointId(), "未获取到学生薄弱知识点！");
        CommonResponse.ERROR.assertNotNull(personalWeakKnowledgePoint.getErrorPercentage(), "学生薄弱知识点当前错误率不能为空！");
        // 首先查询英语题题库,获取符合条件的题目,如果成功则返回
        UUID knowledgePointId = personalWeakKnowledgePoint.getKnowledgePointId();
        BigDecimal errorPercentage = personalWeakKnowledgePoint.getErrorPercentage();
        String knowledgePointName = personalWeakKnowledgePoint.getKnowledgePointName();
        String diagnoseRecordId = personalWeakKnowledgePoint.getDiagnoseRecordId();

        EnglishAiQuestionQueryParam questionQueryParam = new EnglishAiQuestionQueryParam();
        questionQueryParam.setKnowledgePointId(knowledgePointId);
        questionQueryParam.setGrade(queryParam.getGrade());
        questionQueryParam.setSemester(queryParam.getSemester());
        // 定向爆破推题策略 (数量与难度)
        //（1）错误率 > 80%，固定推送5题。难度：一星
        //（2）错误率在 40% - 80%之间，推送 5 - 10题（随机），难度：一星、二星、三星
        //（3）错误率 ≤ 40%，推送 10 - 15题，难度：二星、三星、四星、五星
        if (errorPercentage.compareTo(new BigDecimal(0.80)) > 0) {
            questionQueryParam.setQuestionCount(5);
            questionQueryParam.setQuestionDifficultyList(Arrays.asList(1));
        } else if (errorPercentage.compareTo(new BigDecimal(0.40)) <= 0) {
            questionQueryParam.setQuestionCount(15);
            questionQueryParam.setQuestionDifficultyList(Arrays.asList(2, 3, 4, 5));
        } else {
            questionQueryParam.setQuestionCount(10);
            questionQueryParam.setQuestionDifficultyList(Arrays.asList(1, 2, 3));
        }

        EnglishPersonalExerciseVO englishPersonalExerciseVO = new EnglishPersonalExerciseVO();
        List<EnglishAiQuestionVO> list = englishAiQuestionService.list(questionQueryParam);
        if (CollectionUtil.isNotEmpty(list) && CollectionUtil.size(list) >= questionQueryParam.getQuestionCount()) {
            List<EnglishPersonalExerciseQuestionVO> exerciseQuestions = list.stream().map(p -> {
                EnglishPersonalExerciseQuestionVO exerciseQuestionVO = new EnglishPersonalExerciseQuestionVO();
                exerciseQuestionVO.setQuestionId(p.getId());
                exerciseQuestionVO.setQuestionNo(p.getQuestionNo());
                exerciseQuestionVO.setQuestionContent(p.getContent());
                exerciseQuestionVO.setQuestionAnalysis(p.getAnalysis());
                exerciseQuestionVO.setQuestionType(p.getType());
                exerciseQuestionVO.setOptions(new JSONArray(p.getOptions()));
                exerciseQuestionVO.setKnowledgePointName(p.getKnowledgePointName());
                exerciseQuestionVO.setReferenceAnswer(p.getAnswer());
                return exerciseQuestionVO;
            }).collect(Collectors.toList());

            // 生成个人练习及练习题目
            EnglishPersonalExerciseCreateParam exerciseCreateParam = new EnglishPersonalExerciseCreateParam();
            exerciseCreateParam.setDifficulty(queryParam.getDifficulty());
            exerciseCreateParam.setGrade(queryParam.getGrade());
            exerciseCreateParam.setSemester(queryParam.getSemester());
            exerciseCreateParam.setStudentId(personalWeakKnowledgePoint.getStudentId());
            exerciseCreateParam.setPersonalWeakKnowledgePointId(queryParam.getPersonalWeakKnowledgePointId());
            exerciseCreateParam.setQuestions(BeanUtil.copyToList(exerciseQuestions, EnglishPersonalExerciseQuestionDTO.class));
            Long personalExerciseId = englishPersonalExerciseService.create(exerciseCreateParam);
            List<EnglishPersonalExerciseQuestionDTO> questions = englishPersonalExerciseQuestionService.listByPersonalExerciseId(personalExerciseId);
            List<EnglishPersonalExerciseQuestionVO> result = questions.stream().map(p -> {
                EnglishPersonalExerciseQuestionVO exerciseQuestionVO = BeanUtil.copyProperties(p, EnglishPersonalExerciseQuestionVO.class);
                exerciseQuestionVO.setOptions(new JSONArray(p.getOptions()));
                return exerciseQuestionVO;
            }).collect(Collectors.toList());
            englishPersonalExerciseVO.setPersonalExerciseId(personalExerciseId);
            englishPersonalExerciseVO.setQuestions(result);
            return englishPersonalExerciseVO;
        }

        // 查询英语题题库获取题目失败,调用AI接口生成题目,返回智能选题中…
        // 获取薄弱知识点的试卷诊断原题
        List<Long> diagnoseRecordIds = Arrays.stream(diagnoseRecordId.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<EnglishExamDiagnoseQuestionDTO> originalQuestions = englishExamDiagnoseQuestionService.listByRecordIdsAndKnowledgePointId(diagnoseRecordIds, knowledgePointId);
        List<String> originalQuestionContentList = originalQuestions.stream().map(EnglishExamDiagnoseQuestionDTO::getQuestionContent).collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("grade", queryParam.getGrade());
        params.put("semester", queryParam.getSemester());
        params.put("knowledgePoint", knowledgePointName);
        params.put("knowledgePointId", knowledgePointId);
        params.put("wrongQuestions", originalQuestionContentList);

        String response = LocalHttpUtil.post(englishAiHostUrl + "/aiTarget", params, 300000);
        return englishPersonalExerciseVO;
    }
}
