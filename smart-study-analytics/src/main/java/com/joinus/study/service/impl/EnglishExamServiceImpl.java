package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishExamMapper;
import com.joinus.study.model.entity.*;
import com.joinus.study.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnglishExamServiceImpl extends ServiceImpl<EnglishExamMapper, EnglishExam> implements EnglishExamService {

    @Override
    public List<EnglishExam> listByName(String name) {
        return this.baseMapper.listByName(name);
    }
}
