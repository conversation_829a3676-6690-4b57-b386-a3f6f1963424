package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.dao.po.SchoolInfoPO;
import com.joinus.study.constant.Constant;
import com.joinus.study.model.dto.ParentDTO;
import com.joinus.study.model.dto.StudentBasicInfoDTO;
import com.joinus.study.model.param.AddParentParam;
import com.joinus.study.model.param.AddStudentForPadParam;
import com.joinus.study.model.param.AddStudentParam;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.utils.BasicApiLocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class BasicBusinessImpl implements BasicBusinessService {
    @Value("${basic-api.domain.url:https://basic-api.uat.ijiaxiao.net}")
    private String basicApiDomainUrl;

    @Value("${virtual.school-id:29660}")
    private Long schoolId;

    @Value("${grade.mappings:{\"gradeMappings\": [{\"gradeName\": \"七年级\",\"gradeId\": 14614,\"classId\": 78527},{\"gradeName\": \"八年级\",\"gradeId\": 14615,\"classId\": 78528},{\"gradeName\": \"九年级\",\"gradeId\":14616 ,\"classId\":78529}]}}")
    private String gradeMappingsJson;

    @Override
    public List<StudentBasicInfoDTO> getStudentsByParentPhone(String parentPhone) {
        List<StudentBasicInfoDTO> studentResultList =new ArrayList<>();
        if (StrUtil.isBlank(parentPhone)){
            return studentResultList;
        }

        // 调用基础服务获取学生信息
        String url = basicApiDomainUrl + "/external/student/by-parent-phone/" + parentPhone;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONArray dataArray = jsonResponse.getJSONArray("data");
                if (CollUtil.isNotEmpty(dataArray)) {
                    studentResultList = dataArray.toList(StudentBasicInfoDTO.class);
                }
            }
        } catch (Exception e) {
            log.warn("家长手机号:{},获取学生信息失败:{}", e.getMessage(), parentPhone, e);
        }
        return studentResultList;
    }

    @Override
    public Long addStudentBasicData(AddStudentParam addStudentParam) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String logPrefix = "[uuid:" + uuid + "],调用基础业务新增学生信息,";
        if (isParamInvalid(addStudentParam)) {
            log.warn("{} 参数校验失败: parentPhone={}, studentName={}, gradeName={}",
                    logPrefix,
                    addStudentParam.getParentPhone(),
                    addStudentParam.getStudentName(),
                    addStudentParam.getGradeName());
            return null;
        }
        try {
            // 解析JSON配置字符串
            JSONObject config = JSONUtil.parseObj(gradeMappingsJson);
            JSONArray mappings = config.getJSONArray("gradeMappings");
            if (mappings == null) {
                log.warn("{} 配置格式错误，缺少gradeMappings字段", logPrefix);
                return null;
            }
            // 查找匹配的年级配置
            JSONObject gradeInfo = findGradeInfo(mappings, addStudentParam.getGradeName());
            if (gradeInfo == null) {
                log.warn("{} 未找到年级配置: gradeName={}", logPrefix, addStudentParam.getGradeName());
                return null;
            }

            Long gradeId = gradeInfo.getLong("gradeId");
            Long classId = gradeInfo.getLong("classId");
            addStudentParam.setGradeId(gradeId);
            addStudentParam.setClassId(classId);
            addStudentParam.setSchoolId(schoolId);

            try {
                String addStudentUrl = basicApiDomainUrl + "/external/student";
                log.info("{} 调用基础服务新增学生信息,参数:{}", logPrefix, addStudentParam);
                // 调用基础服务新增学生信息
                String responseBody = BasicApiLocalHttpUtil.post(addStudentUrl, JSONUtil.toJsonStr(addStudentParam), addStudentParam.getParentPhone(), "ADD", Constant.BASIC_API_OPERATE_INFO_SOURCE_MATH_H5);
                if (StrUtil.isNotBlank(responseBody)) {
                    JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                    JSONObject dataJson = jsonResponse.getJSONObject("data");
                    if (ObjectUtil.isNotEmpty(dataJson)) {
                        Long studentId = Convert.toLong(dataJson.get("studentId"));
                        if (studentId != null) {
                            // 调用基础服务新增家长手机号
                            String addParentPhoneUrl = basicApiDomainUrl + "/api/parent/add";
                            AddParentParam addParentParam = AddParentParam.builder().studentId(studentId).phone(addStudentParam.getParentPhone()).build();
                            String addParentResponseBody = BasicApiLocalHttpUtil.post(addParentPhoneUrl, JSONUtil.toJsonStr(addParentParam), addStudentParam.getParentPhone(), "ADD", Constant.BASIC_API_OPERATE_INFO_SOURCE_MATH_H5);
                            if (StrUtil.isNotBlank(addParentResponseBody)) {
                                JSONObject addParentJsonResponse = JSONUtil.parseObj(addParentResponseBody);
                                Integer code = addParentJsonResponse.getInt("code");
                                if (code != null && 200 == code) {
                                    return studentId;
                                }
                            }
                        }
                        return null;
                    }
                }

            } catch (Exception e) {
                log.warn("{} 调用基础服务新增学生信息失败,参数 {} ,错误原因 {}", logPrefix, addStudentParam, e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error("{} 配置解析失败: {}", logPrefix, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public Long addStudentBasicDataForPad(AddStudentForPadParam param) {

        String uuid = UUID.randomUUID().toString().replace("-", "");
        String logPrefix = "[uuid:" + uuid + "],调用基础业务新增学生信息,";
        if (null == param || null == param.getParentId() || StrUtil.isBlank(param.getStudentName()) || null == param.getGrade()) {
            log.warn("{} 参数校验失败: ", logPrefix, JSONUtil.toJsonStr(param));
            throw new BaseException("参数不完整");
        }
        // 解析JSON配置字符串
        JSONObject config = JSONUtil.parseObj(gradeMappingsJson);
        JSONArray mappings = config.getJSONArray("gradeMappings");
        if (mappings == null) {
            log.warn("{} 配置格式错误，缺少gradeMappings字段", logPrefix);
            throw new BaseException("参数不完整");
        }
        // 查找匹配的年级配置
        JSONObject gradeInfo = findGradeInfoByGrade(mappings, param.getGrade());
        if (gradeInfo == null) {
            log.warn("{} 未找到对应的年级配置: grade={}", logPrefix, param.getGrade());
            throw new BaseException("未找到对应的年级配置");
        }

        Long gradeId = gradeInfo.getLong("gradeId");
        Long classId = gradeInfo.getLong("classId");
        AddStudentParam addStudentParam = AddStudentParam.builder()
                .gradeId(gradeId)
                .classId(classId)
                .schoolId(schoolId)
                .studentName(param.getStudentName())
                .parentPhone(param.getParentPhone())
                .build();

        try {
            String addStudentUrl = basicApiDomainUrl + "/external/student";
            log.info("{} 调用基础服务新增学生信息,参数:{}", logPrefix, addStudentParam);
            // 调用基础服务新增学生信息
            String responseBody = BasicApiLocalHttpUtil.post(addStudentUrl, JSONUtil.toJsonStr(addStudentParam), addStudentParam.getParentPhone(), "ADD", Constant.BASIC_API_OPERATE_INFO_SOURCE_MATH_PAD);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONObject dataJson = jsonResponse.getJSONObject("data");
                if (ObjectUtil.isNotEmpty(dataJson)) {
                    Long studentId = Convert.toLong(dataJson.get("studentId"));
                    if (studentId != null) {
                        // TODO 调用基础服务绑定学生家长关系
                        String addParentPhoneUrl = basicApiDomainUrl + "/api/parent/bind";
                        AddParentParam addParentParam = AddParentParam.builder().studentId(studentId).parentId(param.getParentId()).build();
                        String addParentResponseBody = BasicApiLocalHttpUtil.post(addParentPhoneUrl, JSONUtil.toJsonStr(addParentParam), addStudentParam.getParentPhone(), "ADD", Constant.BASIC_API_OPERATE_INFO_SOURCE_MATH_PAD);
                        if (StrUtil.isNotBlank(addParentResponseBody)) {
                            JSONObject addParentJsonResponse = JSONUtil.parseObj(addParentResponseBody);
                            Integer code = addParentJsonResponse.getInt("code");
                            if (code != null && 200 == code) {
                                return studentId;
                            }
                        }
                        log.warn("{} 新增家长手机号失败,参数 {} ,错误原因 {}", logPrefix, addParentParam, addParentResponseBody);
                        throw new BaseException("新增学生信息失败");
                    }
                    return null;
                }
            }

        } catch (Exception e) {
            log.warn("{} 调用基础服务新增学生信息失败,参数 {} ,错误原因 {}", logPrefix, addStudentParam, e.getMessage(), e);
            throw new BaseException("新增学生信息失败");
        }
        return null;
    }

    @Override
    public ParentDTO getParentByParentId(Long parentId) {
        ParentDTO parentDTO = new ParentDTO();
        if (parentId != null) {
            String url = basicApiDomainUrl + "/api/parent/" + parentId;
            try {
                String responseBody = BasicApiLocalHttpUtil.get(url);
                if (StrUtil.isNotBlank(responseBody)) {
                    JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                    JSONObject data = jsonResponse.getJSONObject("data");
                    if (ObjectUtil.isNotEmpty(data)) {
                        parentDTO = JSONUtil.toBean(data, ParentDTO.class);
                    }
                }
            }catch (Exception e){
                log.warn("{} 调用基础服务获取家长信息失败: {}", url, e.getMessage(), e);
            }
        }
        return parentDTO;
    }

    @Override
    public Long addParent(String phone) {
        try {
            // 调用基础服务新增家长手机号
            String addParentPhoneUrl = basicApiDomainUrl + "/api/parent/addParent";
            AddParentParam addParentParam = new AddParentParam();
            addParentParam.setPhone(phone);
            addParentParam.setParentName( phone);
            log.info("调用基础服务新增家长信息,参数  {}", addParentParam);
            String addParentResponseBody = BasicApiLocalHttpUtil.post(addParentPhoneUrl, JSONUtil.toJsonStr(addParentParam), phone, "ADD", Constant.BASIC_API_OPERATE_INFO_SOURCE_MATH_H5);
            if (StrUtil.isNotBlank(addParentResponseBody)) {
                JSONObject addParentJsonResponse = JSONUtil.parseObj(addParentResponseBody);
                Long parentId = addParentJsonResponse.getLong("data");
                Integer code = addParentJsonResponse.getInt("code");
                if (code != null && 200 == code) {
                    return parentId;
                }
            }
        } catch (Exception e) {
            log.warn("{} 调用基础服务新增家长信息失败: {}", phone, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public StudentBasicInfoDTO getStudentRegionIdInfo(Long studentId) {
        // 调用基础服务获取学生信息
        String url = basicApiDomainUrl + "/student/regions/" + studentId;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                StudentBasicInfoDTO data = jsonResponse.get("data", StudentBasicInfoDTO.class);
                return data;
            }
        } catch (Exception e) {
            log.error("基础业务获取学生 {} 区域信息失败:{}", studentId, e.getMessage());
        }
        return null;
    }

    @Override
    public List<String> getLowerRegionList(String regionName) {
        // 调用基础服务获取学生信息
        String url = basicApiDomainUrl + "/regions/sub-regions?regionName=" + regionName;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url);
            //log.info("基础业务获取 {} 下级区域信息返回 {} ", regionName, responseBody);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONArray dataArray = jsonResponse.getJSONArray("data");
                if (dataArray != null) {
                    return dataArray.toList(String.class); // 安全转换为字符串列表
                }
            }
        } catch (Exception e) {
            log.error("基础业务获取 {} 下级区域信息失败:{}", regionName, e.getMessage());
        }
        return null;
    }

    @Override
    public List<SchoolInfoPO> listSchoolByIds(List<Long> list) {
        // 调用基础服务获取学生信息
        String url = basicApiDomainUrl + "/external/school/by-ids";
        try {
            HttpResponse response = HttpUtil.createGet(url)
                    .timeout(15 * 1000)
                    .form("schoolIds", list)
                    .execute();
            log.info("基础业务获取学校信息 {} {} {}", url, list, response.body());
            if (response.isOk()) {
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    List<SchoolInfoPO> data = JSONUtil.toList(jsonObject.getJSONArray("data"), SchoolInfoPO.class);
                    return data;
                }
            }
        } catch (Exception e) {
            log.error("基础业务获取学校信息成功 {}", list);
        }
        return List.of();
    }


    private JSONObject findGradeInfo(JSONArray mappings, String gradeName) {
        for (int i = 0; i < mappings.size(); i++) {
            JSONObject item = mappings.getJSONObject(i);
            if (gradeName.equals(item.getStr("gradeName"))) {
                return item;
            }
        }
        return null;
    }

    private JSONObject findGradeInfoByGrade(JSONArray mappings, Integer grade) {
        for (int i = 0; i < mappings.size(); i++) {
            JSONObject item = mappings.getJSONObject(i);
            if (grade.equals(item.getInt("grade"))) {
                return item;
            }
        }
        return null;
    }

    private boolean isParamInvalid(AddStudentParam param) {
        return StrUtil.isBlank(param.getParentPhone())
                || !param.getParentPhone().matches("^1[3-9]\\d{9}$")
                || StrUtil.isBlank(param.getStudentName())
                || StrUtil.isBlank(param.getGradeName());
    }
}
