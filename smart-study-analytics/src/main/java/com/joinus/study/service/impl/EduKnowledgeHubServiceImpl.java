package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.model.bo.MathCatalogNodeStatisticsDto;
import com.joinus.study.model.bo.QueryElectronicTextbookBo;
import com.joinus.study.model.bo.SpecialTrainingBo;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EduKnowledgeHubServiceImpl implements EduKnowledgeHubService {

    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
//    @Value("${edu-knowledge-hub-host-url:http://127.0.0.1:8080}")
    private String eduKnowLedgeHubHostUrl;

    @Qualifier("eduKnowledgeHubWebClient")
    @Autowired
    private WebClient webClient;
    private static Pattern imgTagPattern = Pattern.compile("<img[^>]*src=\"\"[^>]*>");
    private static Pattern dataS3EnumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
    private static Pattern dataS3KeyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");

    @Override
    public QuestionCoordinateDto questionCoordinateInfo(QueryQuestionCoordinateParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossKey", param.getOssKey());
        params.put("ossEnum", param.getOssEnum());
        params.put("paperSubjectType", param.getPaperSubjectType());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/coordinate-point/multi", param);
        return JSONUtil.toBean(response, QuestionCoordinateDto.class);
    }

    @Override
    public Flux<String> chatStream(SolveQuestionFromImgParam param) {
        return webClient.post()
                .uri("/api/edu-knowledge-hub/ai/ability/text/stream")
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(param), SolveQuestionFromImgParam.class)
                .accept(MediaType.APPLICATION_STREAM_JSON)
                .retrieve()
                .bodyToFlux(String.class);
    }

    @Override
    public EraseHandwritingFromQuestionDto eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/erase-pen-marks", param);
        return JSONUtil.toBean(response, EraseHandwritingFromQuestionDto.class);
    }

    @Override
    public QueryMultiQuestionResultDto queryMultiQuestionAnalysisResults(QueryMultiQuestionResultParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("imgUrl", param.getImgUrl());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/xxx", params);
        return JSONUtil.toBean(response, QueryMultiQuestionResultDto.class);
    }

    @Override
    public Flux<String> querySingleQuestionAnalysisResults(SolveQuestionFromImgParam param) {
        log.info("edu-knowledge-hub solve stream  {}", JSONUtil.toJsonStr(param));
        return webClient.post()
                .uri("/api/edu-knowledge-hub/ai/ability/math/problem/solve/stream")
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(param), SolveQuestionFromImgParam.class)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class);
    }

    @Override
    public ExamAnalysisQuestionLabelDto examAnalysisQuestionLabel(ExamAnalysisQuestionLabelParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("imgUrl", param.getImgUrlList());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/xxx", params);
        return JSONUtil.toBean(response, ExamAnalysisQuestionLabelDto.class);
    }

    /**
     * 检测是否试卷是否存在
     */
    @Override
    public CheckExamExsitenceDto checkExamExistence(CheckExamExsitenceParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-exam", param);
        return JSONUtil.toBean(response, CheckExamExsitenceDto.class);
    }

    @Override
    public CheckExamExsitenceDtoV2 checkExamExistenceV2(CheckExamExsitenceParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-exam/V2", param);
        return JSONUtil.toBean(response, CheckExamExsitenceDtoV2.class);
    }

    @Override
    public KnowledgePointsVO knowledgePoints(KnowledgePointsParams params) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/problem/knowledge-points", params);
        return JSONUtil.toBean(response, KnowledgePointsVO.class);
    }

    @Override
    public KnowledgePointsVO knowledgePointsViaDB(KnowledgePointsParams param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/problem/knowledge-points/db", param);
        return JSONUtil.toBean(response, KnowledgePointsVO.class);
    }

    @Override
    public CutImageFromPositionsDto cutImageFromPositions(SingleCutImageFromPositionsParam cutImageFromPositionsParam) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/img/cut", cutImageFromPositionsParam);
        return JSONUtil.toBean(response, CutImageFromPositionsDto.class);
    }

    @Override
    public OssTokenDto ossToken(OssTokenParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossEnum", param.getOssEnum());
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/ali/token", params);
        return JSONUtil.toBean(response, OssTokenDto.class);
    }

    @Override
    public CheckExistGraphicsDto checkExistGraphics(CheckExistGraphicsParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("questionText", param.getQuestionText());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-graphics", params);
        return JSONUtil.toBean(response, CheckExistGraphicsDto.class);
    }

    @Override
    public PresignedUrlDto presignedUrl(PresignedUrlParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossEnum", param.getOssEnum());
        params.put("ossKey", param.getOssKey());
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/presigned-url", params);
        return JSONUtil.toBean(response, PresignedUrlDto.class);
    }

    @Override
    public PresignedUrlDto presignedUrlV2(PresignedUrlParam param, Long expireTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossEnum", param.getOssEnum());
        params.put("ossKey", param.getOssKey());
        params.put("expireTime", expireTime);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/presigned-url/v2", params);
        return JSONUtil.toBean(response, PresignedUrlDto.class);
    }

    @Override
    public SpecialTrainingDto specialTraining(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training", bo);
        return JSONUtil.toBean(response, SpecialTrainingDto.class);
    }

    @Override
    public SpecialTrainingPdfDto specialTrainingPdf(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/pdf", bo);
        return JSONUtil.toBean(response, SpecialTrainingPdfDto.class);
    }

    @Override
    public SpecialTrainingPdfDto specialTrainingPdfPreview(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/pdf/preview", bo);
        return JSONUtil.toBean(response, SpecialTrainingPdfDto.class);
    }

    @Override
    public UUID queryFlexiblyGenerating(FlexiblyQuestionParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("questionId", param.getQuestionId());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/problem/generate", params);
        if (DataUtils.isNotEmpty(response)) {
            FlexiblyGenerateDto bean = JSONUtil.toBean(response, FlexiblyGenerateDto.class);
            return bean.getData();
        } else {
            return null;
        }
    }

    @Override
    public CheckExistQuestionDto checkExistQuestion(CheckExistQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-question", param);
        return JSONUtil.toBean(response, CheckExistQuestionDto.class);
    }

    @Override
    public CreateExamDto createExam(CreateExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/cut/v2", param);
        return JSONUtil.toBean(response, CreateExamDto.class);
    }

    @Override
    public ExamAnalyzeVo examAnalyze(AiAnalyticsExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/analyze", param);
        return JSONUtil.toBean(response, ExamAnalyzeVo.class);
    }

    @Override
    public ParseAnswerVo parseAnswer(ParseAnswerParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/parse-answer", param);
        return JSONUtil.toBean(response, ParseAnswerVo.class);
    }

    @Override
    public ExamHasKnowledgeDto examHasKnowledge(ExamHasKnowledgeParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exam/exist-knowledge-points", param);
        return JSONUtil.toBean(response, ExamHasKnowledgeDto.class);
    }

    @Override
    public CreateQuestionByKnowledgeVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/questions", param);
        return JSONUtil.toBean(response, CreateQuestionByKnowledgeVo.class);
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/questions/V2", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 createHolidayTrainingSectionQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/section", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }
    @Override
    public CreateQuestionByKnowledgeVoV2 createHolidayTrainingChapterQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/chapter", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 selectSpecializedTrainingExamDetail(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exams/"+examId+"/training", null);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateTextBookQuestioVo selectSpecializedTextBookTrainingExamDetail(UUID examId, PublisherEnum publisher) {
        String url = StrUtil.format(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exams/{}", examId);
        if (null != publisher) {
            url += "?publisher=" + publisher.getValue();
        }
        String response = LocalHttpUtil.get(url, null);
        CreateTextBookQuestioVo createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateTextBookQuestioVo.class);
        return createQuestionByKnowledgeVo;
    }

    @Override
    public MathElecttronicTextbookVo getElectronicTextbook(QueryElectronicTextbookBo bo) {
        String url = StrUtil.format(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/electronic-textbook?publisher={}" +
                        "&grade={}",bo.getPublisher(), bo.getGrade());
        if(null != bo.getSemester()){
            url += "&semester=" + bo.getSemester();
        }if (null != bo.getBookVolume()){
            url += "&bookVolume=" + bo.getBookVolume();
        }
        String responseBody = LocalHttpUtil.get(url, null);
        JSONObject jsonObject = JSONUtil.parseObj(responseBody);
        if (jsonObject.containsKey("data")) {
            JSONObject data = jsonObject.getJSONObject("data");
            return JSONUtil.toBean(data, MathElecttronicTextbookVo.class);
        }
        return null;
    }

    @Override
    public List<QuerySectionVideoDto> querySectionVideos(List<UUID> sectionIdList) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/section-videos", sectionIdList);
        if (StrUtil.isBlank(response)) {
            return new ArrayList<>();
        }
        JSONObject responseBody = JSONUtil.parseObj(response);
        List<QuerySectionVideoDto> sectionVideoDtos = JSONUtil.toList(responseBody.getJSONArray("data"), QuerySectionVideoDto.class);
        return sectionVideoDtos;
    }

    @Override
    public List<KnowledgePointDto> listKnowledgePoints(List<UUID> kpIds) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/api/math/knowledge-points/list" , kpIds);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  KnowledgePointDto.class);
    }

    @Override
    public void reanalyzeExamKnowledgePoint(AiAnalyticsExamParam examAnalyzeParam) {
        LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/knowledge-point/reanalyze", examAnalyzeParam);
    }

    @Override
    public CreateTextBookQuestioVo createHolidayTrainingTextBookQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/textbook", param);
        CreateTextBookQuestioVo createTextBookQuestioVo = JSONUtil.toBean(response, CreateTextBookQuestioVo.class);
        CommonResponse.ERROR.assertNotNull(createTextBookQuestioVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createTextBookQuestioVo.getData(), "生成题目失败");

        return createTextBookQuestioVo;
    }

    @Override
    public SpecialTrainingQuestionTypesDto getSpecialTrainingQuestionTypes(String knowledgePointIds) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("knowledgePointIds", knowledgePointIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/question-types", params);
        return JSONUtil.toBean(response, SpecialTrainingQuestionTypesDto.class);
    }

    @Override
    public QuestionTypesDto getQuestionTypesByQuestionId(UUID questionId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("id", questionId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/questions/" + questionId + "/knowledge-points-and-question-types", null);
        return JSONUtil.toBean(response, QuestionTypesDto.class);
    }


    @Override
    public MathExamVO getExamsByExamId(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId, null);
        return JSONUtil.toBean(response, KnowLedgeExamsDTO.class).getData();
    }

    @Override
    public List<MathExamQuestionVO> getQuestionsByExamId(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions", null);
        return JSONUtil.toBean(response, KnowLedgeMathQuestionDTO.class).getData();
    }

    @Override
    public QuestionDetailDTO editQuestion(UUID examId, UUID questionId, AddExamQuestionParam param) {
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions/" + questionId, JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public QuestionDeleteDto deleteQuestions(UUID examId, UUID questionId) {
        String response = LocalHttpUtil.delete(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions/" + questionId, null);
        return JSONUtil.toBean(response, QuestionDeleteDto.class);
    }

    @Override
    public QuestionDetailDTO addQuestion(UUID examId, AddExamQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions", JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public QuestionDetailDTO updateExamDetail(UUID examId) {
        Map<String, Object> params = new HashMap<>();
        params.put("state", "HUMAN_RECOGNIZED");
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId, JSONUtil.toJsonStr(params));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public SpecializedTrainingCrateExamDto saveExam(SpecializedTrainingCreateExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/by-questions", param);
        return JSONUtil.toBean(response, SpecializedTrainingCrateExamDto.class);
    }

    @Override
    public SpecializedTrainingUpdateExamDto specializedTrainingUpdateExam(AISpecializedTrainingUpdateExamParam updateExamParam) {
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + updateExamParam.getExamId(), JSONUtil.toJsonStr(updateExamParam));
        return JSONUtil.toBean(response, SpecializedTrainingUpdateExamDto.class);
    }

    @Override
    public ExamAnalyzeVo cutQuestions(AiAnalyticsExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/cut-questions" , JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response,  ExamAnalyzeVo.class);
    }

    @Override
    public TextBookCatalogueDto textbooksChaptersSections(UUID examId, PublisherEnum  publisher, Integer grade) {
        String url = UriComponentsBuilder.fromHttpUrl(eduKnowLedgeHubHostUrl)
                .path("/api/edu-knowledge-hub/ai/ability/math/books-chapters-sections/exams/{examId}")
                .uriVariables(Collections.singletonMap("examId", examId))
                .queryParamIfPresent("publisher", Optional.ofNullable(publisher).map(PublisherEnum::getValue))
                .queryParamIfPresent("grade", Optional.ofNullable(grade))
                .toUriString();
        String response = LocalHttpUtil.get(url,null);
        return JSONUtil.toBean(response,  TextBookCatalogueDto.class);
    }

    @Override
    public KnowledgePointBySelectDto getKnowledgePointBySection(List<UUID> sectionIds) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("sectionIds", sectionIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/sections/knowledge-points",params);
        return JSONUtil.toBean(response,  KnowledgePointBySelectDto.class);
    }

    @Override
    public KnowledgePointQuestionTypesDtoV2New questionTypeV2(String knowledgePointIds) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/question-types/V2" , knowledgePointIds);
        return JSONUtil.toBean(response,  KnowledgePointQuestionTypesDtoV2New.class);
    }

    @Override
    public List<MathSectionDto> listMathSections(Integer grade, Integer semester, PublisherEnum publisher) {
        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("grade", grade);
        requestMap.put("semester", semester);
        requestMap.put("publisher", publisher);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/sections" , requestMap);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  MathSectionDto.class);
    }

    @Override
    public List<MathChapterDto> listMathChapters(Integer grade, PublisherEnum publisher, Integer semester) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/chapters" , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  MathChapterDto.class);
    }

    @Override
    public List<MathCatalogNodeDto> listCatalogNodes(Integer grade, PublisherEnum publisher, Integer semester) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/catalogs/nodes?grade={}&publisher={}&semester={}",
                grade, publisher, semester);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathCatalogNodeDto.class);
    }

    @Override
    public List<MathCatalogNodeDto> listCatalogSubNodes(UUID catalogNodeId) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/catalogs/sub-nodes?catalogNodeId={}", catalogNodeId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathCatalogNodeDto.class);
    }

    @Override
    public MathCatalogNodeDto getCatalogNodeByid(UUID nodeId) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/catalogs/{}", nodeId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toBean(responseBody.getJSONObject("data"), MathCatalogNodeDto.class);
    }

    @Override
    public MathKnowledgePointHandoutVo getExplanationByKnowledgePointId(UUID knowledgePointId) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/knowledge/materials/{}", knowledgePointId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        if(responseBody.getJSONObject("data") ==  null){
            return null;
        }
        return JSONUtil.toBean(responseBody.getJSONObject("data"), MathKnowledgePointHandoutVo.class);
    }

    @Override
    public Boolean checkExplanationExistByKnowledgePointId(UUID knowledgePointId) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/knowledge/materials/{}/exists", knowledgePointId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url, null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        if (responseBody.getJSONObject("data") == null) {
            return false;
        }
        return responseBody.getBool("data");
    }

    @Override
    public String decodeContentV2(String content) {
        if (content == null) {
            return "";
        }

        Matcher imgMatcher = imgTagPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (imgMatcher.find()) {
            String tag = imgMatcher.group();

            // 提取 data-s3-enum 属性（如果存在）
            Matcher enumMatcher = dataS3EnumPattern.matcher(tag);
            String s3Enum = enumMatcher.find() ? enumMatcher.group(1) : "MINIO_EDU_KNOWLEDGE_HUB";  // 默认 aliyun

            OssEnum ossEnum = OssEnum.valueOf(s3Enum);

            // 提取 data-s3-key 属性（必须有）
            Matcher keyMatcher = dataS3KeyPattern.matcher(tag);
            if (!keyMatcher.find()) {
                continue; // 或根据需求作处理
            }
            String s3Key = keyMatcher.group(1);

            // 根据修正后的 s3Enum 和 s3Key 生成新的 src
            PresignedUrlDto presignedUrlDto = this.presignedUrl(PresignedUrlParam.builder()
                    .ossEnum(ossEnum)
                    .ossKey(s3Key)
                    .build());
            String newTag = tag.replace("src=\"\"", "src=\"" + presignedUrlDto.getData().getPresignedUrl() + "\"");

            imgMatcher.appendReplacement(sb, newTag.replace("$", "\\$")); // 注意替换 $ 符号避免干扰
        }
        imgMatcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public List<MathCatalogNodeStatisticsDto> listKnowledgePointAndQuestionTypeCountByNodeIds(List<UUID> catalogNodeIds) {

        Map<String, Object> params = new HashMap<>(1);
        params.put("catalogNodeIds", catalogNodeIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/catalog-nodes/statistics",params);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathCatalogNodeStatisticsDto.class);
    }

    @Override
    public List<UUID> createQuestionsByKnowledgePoint(UUID knowledgePointId) throws BaseException {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/knowledge-points/"+ knowledgePointId.toString() +"/training/questions",null);

        System.out.println( response);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), UUID.class);
    }


}
