package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.SchoolConfig;

/**
* <AUTHOR>
* @description 针对表【school_config(学科教育学校配置表)】的数据库操作Service
* @createDate 2025-06-25 17:28:59
*/
public interface SchoolConfigService extends IService<SchoolConfig> {
    // 学校配置表新增
    void schoolConfigAdd(SchoolConfig schoolConfig) throws Exception;
    // 学校配置表更新
    void updateSchoolConfigById(SchoolConfig schoolConfig);
}
