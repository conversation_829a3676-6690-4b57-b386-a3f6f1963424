package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.joinus.study.model.param.ReadingPersonalFeedbackPageParam;
import com.joinus.study.model.param.ReadingPersonalFeedbackParam;
import com.joinus.study.model.vo.ReadingPersonalFeedbackDetailsVo;
import com.joinus.study.model.vo.ReadingPersonalFeedbackVO;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/6 15:02
 */
public interface ReadingPersonalFeedbackService {
    /**
     * 后台反馈分页
     * @param pageParam
     * @return
     */
    Page<ReadingPersonalFeedbackVO> pages(ReadingPersonalFeedbackPageParam pageParam);

    /**
     * 获取反馈详情
     * @param id
     * @return
     */
    ReadingPersonalFeedbackDetailsVo getDetails(Long id) throws JsonProcessingException;

    /**
     * 修改反馈详情
     * @param vo
     * @return void
     *
     *
    */
    void update(ReadingPersonalFeedbackDetailsVo vo);

    /**
     * 添加反馈
     * @param param
     * @return void
     *
     *
    */
    void add(ReadingPersonalFeedbackParam param);
}
