package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.MathStudentStudyVideoHistory;
import com.joinus.study.model.enums.MathVideoTypeEnum;
import com.joinus.study.model.param.MathStudentStudyVideoParam;
import com.joinus.study.service.MathStudentStudyVideoHistoryService;
import com.joinus.study.mapper.MathStudentStudyVideoHistoryMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【math_student_study_video_history(学生视频观看记录)】的数据库操作Service实现
* @createDate 2025-09-12 16:19:30
*/
@Service
public class MathStudentStudyVideoHistoryServiceImpl extends ServiceImpl<MathStudentStudyVideoHistoryMapper, MathStudentStudyVideoHistory>
    implements MathStudentStudyVideoHistoryService{

    @Override
    public void addStudyVideoHistory(MathStudentStudyVideoParam mathQueryParam) {
        MathStudentStudyVideoHistory mathStudentStudyVideoHistory = new MathStudentStudyVideoHistory();
        mathStudentStudyVideoHistory.setStudentId(mathQueryParam.getStudentId());
        mathStudentStudyVideoHistory.setCompleted(mathQueryParam.getCompleted());
        mathStudentStudyVideoHistory.setCreatedAt(new Date());
        if(mathQueryParam.getKnowledgePointId() != null){
            mathStudentStudyVideoHistory.setVideoType(MathVideoTypeEnum.KNOWLEDGE_POINT);
            mathStudentStudyVideoHistory.setVideoId(mathQueryParam.getKnowledgePointId());
        } else {
            mathStudentStudyVideoHistory.setVideoType(MathVideoTypeEnum.SECTION);
        }
        baseMapper.insert(mathStudentStudyVideoHistory);
    }
}




