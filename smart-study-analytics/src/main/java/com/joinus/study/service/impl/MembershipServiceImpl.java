package com.joinus.study.service.impl;

import cn.hutool.core.util.StrUtil;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.dto.BusinessSubscriptionDto;
import com.joinus.study.model.dto.ParentDTO;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.param.MathMembershipNoticeParam;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.service.MathActivityStudentService;
import com.joinus.study.service.MembershipService;
import com.joinus.study.service.QylService;
import com.joinus.study.util.CurrentUserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 会员服务实现类
 * 实现会员资格校验逻辑
 */
@Slf4j
@Service
public class MembershipServiceImpl implements MembershipService {

    @Autowired
    private QylService qylService;
    @Resource(name = "redisTemplateJson")
    private RedisTemplate<String, Object> redisTemplate;
    @Lazy
    @Autowired
    private MathActivityStudentService mathActivityStudentService;
    @Autowired
    private BasicBusinessService basicBusinessService;

    private static final String MEMBERSHIP_KEY = "smart-study:membership:math:{}";

    /**
     * 检查指定学生是否是有效会员
     * 
     * 实际实现中，可能需要查询会员表、调用会员系统API等方式获取会员状态
     *
     * @param studentId 学生ID
     * @return true表示是有效会员，false表示非会员或会员已过期
     */
    @Override
    @Transactional(readOnly = true)
    public boolean isMathHolidayActivityMember(Long studentId, Long parentId) {
        if (studentId == null) {
            log.warn("检查会员状态失败：学生ID为空");
            return false;
        }

        boolean isMember = queryStudentMathMembership(studentId, parentId);

        // 模拟：学生ID为偶数的是会员（仅作示例）
        log.debug("学生 {} 的会员状态: {}", studentId, isMember ? "是会员" : "非会员");
        
        return isMember;
    }
    
    /**
     * 获取当前登录学生的会员状态
     * 
     * @return true表示当前用户是有效会员，false表示非会员或会员已过期
     */
    @Override
    public boolean isCurrentUserMember() {
        try {
            // 从当前登录用户上下文中获取学生ID
            // 需要根据项目实际的用户认证体系调整实现
            CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
            if (null == currentUser) {
                return false;
            }
            Long currentStudentId = currentUser.getStudentId();
            return isMathHolidayActivityMember(currentStudentId, currentUser.getUserId());
        } catch (Exception e) {
            log.error("获取当前用户会员状态失败", e);
            return false;
        }
    }


    @Override
    public boolean queryStudentMathMembership(Long studentId, Long parentId) {
        String membershipKey = StrUtil.format(MEMBERSHIP_KEY, studentId);
        Object isMemberObject = redisTemplate.opsForValue().get(membershipKey);
        if (null != isMemberObject) {
            return (boolean) isMemberObject;
        } else {
            boolean isMember = qylService.queryStudentMathMembership(studentId, parentId);
            redisTemplate.opsForValue().set(membershipKey, isMember, 5, TimeUnit.MINUTES);
            return isMember;
        }
    }

    @Override
    public void updateMathMembershipStatus(MathMembershipNoticeParam param) {
        if (null != param.getIsMember() && param.getIsMember()) {
            redisTemplate.opsForValue().set(StrUtil.format(MEMBERSHIP_KEY, param.getStudentId()), true, 5, TimeUnit.MINUTES);
            mathActivityStudentService.updateMembershipLevel(param.getStudentId(), param.getSource().getMathMemberLevel());
        } else {
            redisTemplate.delete(StrUtil.format(MEMBERSHIP_KEY, param.getStudentId()));
            mathActivityStudentService.updateMembershipLevel(param.getStudentId(), MathMemberLevelEnum.NONE);
        }
    }

    @Override
    public boolean giftMathMember(Long studentId, Long parentId, Integer businessDays) {
        ParentDTO parent = basicBusinessService.getParentByParentId(parentId);
        return qylService.studentOpenProduct(studentId, parent.getTelNum(),businessDays);
    }

    @Override
    public BusinessSubscriptionDto getStudentMathMemberInfo(Long studentId, Long parentId) {
        return qylService.getStudentOpenProductInfo(6, studentId, parentId);
    }
}
