package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishExamReportKnowledgePointMapper;
import com.joinus.study.model.entity.EnglishExamReportKnowledgePoint;
import com.joinus.study.service.EnglishExamReportKnowledgePointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnglishExamReportKnowledgePointServiceImpl extends ServiceImpl<EnglishExamReportKnowledgePointMapper, EnglishExamReportKnowledgePoint> implements EnglishExamReportKnowledgePointService {

    @Override
    public List<EnglishExamReportKnowledgePoint> listByReportId(Long reportId) {
        return lambdaQuery()
                .eq(EnglishExamReportKnowledgePoint::getReportId, reportId)
                .orderByDesc(EnglishExamReportKnowledgePoint::getId)
                .list();
    }
}
