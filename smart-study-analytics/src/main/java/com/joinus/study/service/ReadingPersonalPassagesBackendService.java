package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.param.ReadingPersonalPassagesPageParam;
import com.joinus.study.model.param.ReadingPersonalPassagesUpdateParam;
import com.joinus.study.model.vo.ReadingGenreVO;
import com.joinus.study.model.vo.ReadingPeriodicReportViewDetailVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesBackendVO;
import com.joinus.study.model.vo.ReadingReportDetailVo;

import java.util.List;

public interface ReadingPersonalPassagesBackendService {
    /**
     * 分页
     *
     * @param pageParam
     * @return
     */
    Page<ReadingPersonalPassagesBackendVO> pages(ReadingPersonalPassagesPageParam pageParam);

    /**
     * 批量删除
     *
     * @param updateParam
     */
    void batchDelete(ReadingPersonalPassagesUpdateParam updateParam);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ReadingPersonalPassagesBackendVO query(Long id);

    /**
     * 学情分析报告
     *
     * @param id
     * @return
     */
    ReadingReportDetailVo reportDetail(Long id);

    /**
     * 学情分析报告-周报、月报
     *
     * @param param
     * @return
     */
    ReadingPeriodicReportViewDetailVo periodicReportDetail(ReadingPeriodicReportDetailParam param);

    List<ReadingGenreVO> listGenre();

    ReadingPersonalPassages selectById(Long personalPassageId);
}
