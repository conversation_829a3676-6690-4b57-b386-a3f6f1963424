package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPersonalFeedbackMapper;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.param.ReadingPassageQuestionsUpdateParam;
import com.joinus.study.model.param.ReadingPassagesUpdateParam;
import com.joinus.study.model.param.ReadingPersonalFeedbackPageParam;
import com.joinus.study.model.param.ReadingPersonalFeedbackParam;
import com.joinus.study.model.vo.OptionsVo;
import com.joinus.study.model.vo.ReadingPersonalFeedbackDetailsVo;
import com.joinus.study.model.vo.ReadingPersonalFeedbackVO;
import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
import com.joinus.study.service.*;
import com.joinus.study.utils.DataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/6 15:02
 */
@AllArgsConstructor
@Service("readingPersonalFeedbackService")
@Slf4j
public class ReadingPersonalFeedbackServiceImpl extends ServiceImpl<ReadingPersonalFeedbackMapper, ReadingPersonalFeedback> implements ReadingPersonalFeedbackService {

    private ReadingPersonalPassageQuestionsService readingPersonalPassageQuestionsService;
    private ReadingPassagesBackendService readingPassagesBackendService;
     private ReadingPassageQuestionsBackendService readingPassageQuestionsBackendService;
     private ReadingPersonalPassagesBackendService personalPassagesBackendService;
    @Override
    public Page<ReadingPersonalFeedbackVO> pages(ReadingPersonalFeedbackPageParam pageParam) {
        Page<ReadingPersonalFeedbackVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalFeedbackVO> list = this.baseMapper.pages(page, pageParam);
        list.forEach(obj -> {
            obj.setGenre(GenreEnum.getByName(obj.getGenre()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    public ReadingPersonalFeedbackDetailsVo getDetails(Long id) throws JsonProcessingException {
        ReadingPersonalFeedbackDetailsVo details = this.baseMapper.getDetails(id);
        if (ObjectUtil.isEmpty(details)){
            CommonResponse.assertError("此反馈不存在！");
        }
        String questionContent = DataUtil.processQuestionContent(details.getQuestionContent()).get("questionContent").toString();
        JSONArray options = (JSONArray)DataUtil.processQuestionContent(details.getQuestionContent()).get("options");
        details.setQuestionContent(questionContent);
        details.setOptions(options);
        details.setAnswer(DataUtil.parseAnswer(details.getAnswer()));
        if (ObjectUtil.isNotEmpty(details)){
            List<ReadingKnowledgePoints> knowledgePoints= this.baseMapper.getKnowledgePoints(details.getQuestionId());
            details.setKnowledgePoints(knowledgePoints);
        }
        return details;
    }

    @Override
    public void update(ReadingPersonalFeedbackDetailsVo vo) {
        //更新本表内容
         ReadingPersonalFeedback entity = new ReadingPersonalFeedback();
         entity.setId(vo.getId());
         entity.setFeedbackType(vo.getFeedbackType());
         entity.setSuggestion(vo.getSuggestion());
         entity.setStatus(vo.getStatus());
         entity.setUpdatedAt(new Date());
         this.updateById(entity);
         //处理文章
        ReadingPassagesUpdateParam passages = new ReadingPassagesUpdateParam();
        passages.setId(vo.getPassageId());
        passages.setContent(vo.getContent());
        passages.setTitle(vo.getTitle());
        readingPassagesBackendService.update(passages);
        //处理问题
        //处理答案、一个接口可以完成掉ai组
        ReadingPassageQuestionsUpdateParam questionsUpdateParam = new ReadingPassageQuestionsUpdateParam();
        questionsUpdateParam.setId(vo.getQuestionId());
        questionsUpdateParam.setAnswer(vo.getAnswer());
        questionsUpdateParam.setContent(vo.getAnswerContent());
        questionsUpdateParam.setOptions(vo.getOptions());
        questionsUpdateParam.setAnswerContent(vo.getAnswerContent());
        readingPassageQuestionsBackendService.update(questionsUpdateParam);
    }

    @Override
    public void add(ReadingPersonalFeedbackParam param) {
        ReadingPersonalPassages passages = personalPassagesBackendService.selectById(param.getPersonalPassageId());
        if (passages == null){
            CommonResponse.assertError("此练习不存在，无法提交反馈");
        }
        List<ReadingPersonalPassagesQuestions> questions=readingPersonalPassageQuestionsService.getByPersonalPassageIdAndPersonalQuestionId(param.getPersonalPassageId(),param.getPersonalPassageQuestionId());
        if (ObjectUtil.isEmpty(questions)){
            CommonResponse.assertError("问题和联系不匹配！");
        }
        LambdaQueryWrapper<ReadingPersonalFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReadingPersonalFeedback::getPersonalPassageQuestionId, param.getPersonalPassageQuestionId());
        queryWrapper.eq(ReadingPersonalFeedback::getPersonalPassageId, param.getPersonalPassageId());
        queryWrapper.eq(ReadingPersonalFeedback::getStudentId, param.getStudentId());
        if (this.count(queryWrapper) > 0){
            CommonResponse.assertError("问题已反馈请勿重复提交！");
        }
        ReadingPersonalFeedback entity = new ReadingPersonalFeedback();
        BeanUtil.copyProperties(param, entity);
        entity.setStatus(0);
        entity.setCreatedAt(new Date());
        this.save(entity);
       readingPersonalPassageQuestionsService.setIsFeedback(param.getPersonalPassageQuestionId(), 1);

    }
}
