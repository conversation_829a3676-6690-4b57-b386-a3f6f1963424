package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.MathActivityWeekUnitMapper;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.service.MathActivityWeekUnitService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_activity_week_unit(数学活动周期单元表)】的数据库操作Service实现
* @createDate 2025-06-16 10:07:51
*/
@Service
public class MathActivityWeekUnitServiceImpl extends ServiceImpl<MathActivityWeekUnitMapper, MathActivityWeekUnit>
    implements MathActivityWeekUnitService{

}




