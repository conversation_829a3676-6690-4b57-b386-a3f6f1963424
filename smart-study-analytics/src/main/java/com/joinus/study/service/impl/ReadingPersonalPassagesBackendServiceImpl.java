package com.joinus.study.service.impl;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPersonalPassagesMapper;
import com.joinus.study.mapper.ReadingPersonalSuggestionAnalysisMapper;
import com.joinus.study.mapper.ReadingPersonalWeakKnowledgePointsAnalysisMapper;
import com.joinus.study.model.entity.ReadingPersonalAnalysisReport;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.entity.ReadingPersonalSuggestionAnalysis;
import com.joinus.study.model.entity.ReadingPersonalWeakKnowledgePointsAnalysis;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.enums.ReadingQuestionTypeEnum;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.param.ReadingPersonalPassagesPageParam;
import com.joinus.study.model.param.ReadingPersonalPassagesUpdateParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.DataUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service("readingPersonalPassagesBackendService")
@Slf4j
public class ReadingPersonalPassagesBackendServiceImpl extends ServiceImpl<ReadingPersonalPassagesMapper, ReadingPersonalPassages>
        implements ReadingPersonalPassagesBackendService {

    private ReadingPersonalAnalysisReportService readingPersonalAnalysisReportService;
    private ReadingPersonalWeakKnowledgePointsAnalysisMapper weakKnowledgePointsAnalysisMapper;
    private ReadingPersonalSuggestionAnalysisMapper suggestionAnalysisMapper;
    private ReadingPersonalPassagesService readingPersonalPassagesService;
    private ReadingPersonalAnalysisPeriodicReportService readingPersonalAnalysisPeriodicReportService;

    @Override
    public Page<ReadingPersonalPassagesBackendVO> pages(ReadingPersonalPassagesPageParam pageParam) {
        Page<ReadingPersonalPassagesBackendVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalPassagesBackendVO> list = this.baseMapper.pages(page, pageParam);
        list.forEach(obj -> {
            obj.setGenre(GenreEnum.getByName(obj.getGenre()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ReadingPersonalPassagesUpdateParam updateParam) {
        List<ReadingPersonalPassages> readingPersonalPassages = this.listByIds(updateParam.getIds());
        CommonResponse.ERROR.assertCollNotNull(readingPersonalPassages, "为查询到阅读记录数据");
        List<ReadingPersonalPassages> updateEntityList = readingPersonalPassages.stream().map(obj -> {
            ReadingPersonalPassages updateEntity = new ReadingPersonalPassages();
            updateEntity.setId(obj.getId());
            updateEntity.setDeletedAt(new Date());
            return updateEntity;
        }).collect(Collectors.toList());
        this.updateBatchById(updateEntityList);
    }

    @Override
    public ReadingPersonalPassagesBackendVO query(Long id) {
        ReadingPersonalPassagesBackendVO passagesVO = this.baseMapper.query(id);
        passagesVO.getQuestionList().forEach(obj -> {
            //处理题目的内容和选项、答案的解析
            Map<String, Object> questionMap = DataUtil.processQuestionContent(obj.getContent());
            obj.setContent(questionMap.get("questionContent").toString());
            obj.setOptions((JSONArray)questionMap.get("options"));
            obj.setAnswer(DataUtil.parseAnswer(obj.getAnswer()));
            obj.setQuestionType(ReadingQuestionTypeEnum.getByName(obj.getQuestionType()));
        });
        passagesVO.setGenre(GenreEnum.getByName(passagesVO.getGenre()));
        return passagesVO;
    }

    @Override
    public ReadingReportDetailVo reportDetail(Long id) {
        ReadingPersonalAnalysisReport report = readingPersonalAnalysisReportService.getByPersonalPassageId(id);
        CommonResponse.ERROR.assertNotNull(report, "未查询到对应学情分析报告");
        return readingPersonalAnalysisReportService.reportDetail(report.getId(), 2);
    }

    @Override
    public ReadingPeriodicReportViewDetailVo periodicReportDetail(ReadingPeriodicReportDetailParam param) {
        return readingPersonalAnalysisPeriodicReportService.periodicReportDetail(param);
    }

    @Override
    public List<ReadingGenreVO> listGenre() {
        GenreEnum[] values = GenreEnum.values();
        List<ReadingGenreVO> list = Arrays.stream(values)
                .map(obj -> {
                    ReadingGenreVO genreVO = new ReadingGenreVO();
                    genreVO.setGenre(obj.name());
                    genreVO.setGenreValue(obj.getGenreValue());
                    genreVO.setId(obj.ordinal());
                    return genreVO;
                }).collect(Collectors.toList());
        return list;
    }

    @Override
    public ReadingPersonalPassages selectById(Long personalPassageId) {
        return baseMapper.selectById(personalPassageId);
    }
}
