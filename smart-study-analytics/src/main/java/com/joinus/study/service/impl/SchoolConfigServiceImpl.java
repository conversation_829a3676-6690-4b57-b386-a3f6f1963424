package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.SchoolConfigMapper;
import com.joinus.study.model.entity.SchoolConfig;
import com.joinus.study.service.SchoolConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【school_config(学科教育学校配置表)】的数据库操作Service实现
* @createDate 2025-06-25 17:28:59
*/
@Service
public class SchoolConfigServiceImpl extends ServiceImpl<SchoolConfigMapper, SchoolConfig> implements SchoolConfigService {

    @Autowired
    private SchoolConfigMapper schoolConfigMapper;

    @Override
    public void schoolConfigAdd(SchoolConfig schoolConfig) throws Exception{
        QueryWrapper<SchoolConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("school_id", schoolConfig.getSchoolId());
        queryWrapper.isNull("deleted_at");
        SchoolConfig schoolConfigBySchoolId = schoolConfigMapper.selectOne(queryWrapper);
        if (schoolConfigBySchoolId != null) {
            throw new Exception("该学校已存在配置");
        } else {
            schoolConfigMapper.schoolConfigAdd(schoolConfig);
        }
    }

    @Override
    public void updateSchoolConfigById(SchoolConfig schoolConfig) {
        schoolConfigMapper.updateSchoolConfigById(schoolConfig);
    }
}




