package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.kafka.*;
import com.joinus.study.mapper.ExamAnalyzeResultMapper;
import com.joinus.study.mapper.ExamErrorCorrectionFeedbackMapper;
import com.joinus.study.mapper.PersonalExamMapper;
import com.joinus.study.model.dto.QuestionDetailDTO;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.joinus.study.model.entity.ExamErrorCorrectionFeedback;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.ExamErrorCorrectionFeedbackEnum;
import com.joinus.study.model.enums.FeedbackTypeEnum;
import com.joinus.study.model.param.AiAnalyticsExamParam;
import com.joinus.study.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.ExamAnalysisReportService;
import com.joinus.study.service.ExamErrorCorrectionFeedbackService;
import com.joinus.study.utils.RedisUtil;
import com.joinus.study.utils.SendSmsUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ExamErrorCorrectionFeedbackServiceImpl extends ServiceImpl<ExamErrorCorrectionFeedbackMapper, ExamErrorCorrectionFeedback> implements ExamErrorCorrectionFeedbackService {

    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private ExamErrorCorrectionFeedbackMapper examErrorCorrectionFeedbackMapper;
    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ExamAnalysisReportService examAnalysisReportService;
    @Autowired
    private ExamQylMsgSender examQylMsgSender;

    public static final String REDIS_KEY_ERROR_CORRECTION_FEEDBACK_REDIS_KEY = "error_correction_feedback:ai_handling:%s";

    @Override
    public void saveExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param) {
        //试卷入库
        Long personalExamId = param.getPersonalExamId();
        CommonResponse.ERROR.assertNotNull(param.getPersonalExamId(), "personalId不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getParentId(), "parentId不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getParentName(), "parentName不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getTelephoneNumber(), "telephoneNumber不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getExamName(), "试卷名称不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getExamId(), "examId不能为空！");

        if (null != param.getExamAnalyzeResultId()) {
            ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(param.getExamAnalyzeResultId());
            //纠错完成的试卷，如果同一家长继续提交纠错，则更新原数据不新增
            if (ExamAnalyzeResultEnum.FINISHED_ERROR.equals(examAnalyzeResult.getResult())) {
                //更新exam_analyze_result状态为纠错完成
                ExamAnalyzeResult updateParams = new ExamAnalyzeResult();
                updateParams.setId(param.getExamAnalyzeResultId());
                updateParams.setUpdatedAt(new Date());
                updateParams.setResult(ExamAnalyzeResultEnum.IN_ERROR);
                examAnalyzeResultMapper.updateById(updateParams);

                //试题反馈更新
                UpdateWrapper<ExamErrorCorrectionFeedback> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("UPDATED_AT", new Date())
                        .set("result", ExamErrorCorrectionFeedbackEnum.AI_CUT_QUESTIONS)
                        .eq("FEEDBACK_TYPE", FeedbackTypeEnum.USER_FEEDBACK)
                        .eq("EXAM_ID", param.getExamId())
                        .eq("EXAM_ANALYZE_RESULT_ID",  param.getExamAnalyzeResultId());
                examErrorCorrectionFeedbackMapper.update(null,updateWrapper);
            }
        } else {
            //exam_analyze_result入库
            ExamAnalyzeResult build = ExamAnalyzeResult.builder()
                    .examId(param.getExamId())
                    .studentId(param.getStudentId())
                    .result(ExamAnalyzeResultEnum.IN_ERROR)
                    .personalExamId(personalExamId)
                    .parentId(param.getParentId())
                    .build();
            Long analyzeResultId = examAnalyzeResultMapper.insertExamAnalyzeResult(build);

            //试题反馈入库
            ExamErrorCorrectionFeedback feedback = new ExamErrorCorrectionFeedback();
            BeanUtil.copyProperties(param, feedback);
            feedback.setResult(ExamErrorCorrectionFeedbackEnum.AI_CUT_QUESTIONS);
            feedback.setExamAnalyzeResultId(analyzeResultId);
            feedback.setFeedbackType(FeedbackTypeEnum.USER_FEEDBACK);
            examErrorCorrectionFeedbackMapper.insert(feedback);
        }

        //调用试卷拆分接口
        AiAnalyticsExamParam aiParam = AiAnalyticsExamParam.builder()
                .examId(param.getExamId())
                .grade(param.getGrade())
                .bookVolume(param.getBookVolume())
                .build();
        eduKnowledgeHubService.cutQuestions(aiParam);

    }

    @Override
    public void finishExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param) {
//        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空！");
//        CommonResponse.ERROR.assertNotNull(param.getParentId(), "parentId不能为空！");
//        CommonResponse.ERROR.assertNotNull(param.getExamAnalyzeResultId(), "examAnalyzeResultId不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getHandlerId(), "handlerId不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getHandlerName(), "handlerName不能为空！");
//        CommonResponse.ERROR.assertNotNull(param.getId(), "Id不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getExamId(), "examId不能为空！");

        redisUtil.set(String.format(REDIS_KEY_ERROR_CORRECTION_FEEDBACK_REDIS_KEY, param.getExamId().toString()), param.getExamId().toString(), 60 * 60);

        //更新examId相同的数据的处理人和处理人id
        UpdateWrapper<ExamErrorCorrectionFeedback> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("UPDATED_AT", new Date())
                .set("HANDLER_ID", param.getHandlerId())
                .set("HANDLER_NAME", param.getHandlerName())
                .set("RESULT", ExamErrorCorrectionFeedbackEnum.AI_HANDLING)
                .eq("EXAM_ID", param.getExamId());
        examErrorCorrectionFeedbackMapper.update(null,updateWrapper);

        //调用AI提交录入完成接口
        QuestionDetailDTO questionDetailDTO = eduKnowledgeHubService.updateExamDetail(param.getExamId());
        if (questionDetailDTO.getCode() != 200) {
            throw new RuntimeException("调用AI提交录入完成接口失败:" + questionDetailDTO.getMessage());
        }

    }

    @Override
    public void aiCutQuestionsResult(AiAnalyticsExamParam param) {
        //更新exam_error_correction_feedback状态为PENDING 待处理
        //更新examId相同的所有数据
        UpdateWrapper<ExamErrorCorrectionFeedback> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("UPDATED_AT", new Date())
                .set("RESULT", ExamErrorCorrectionFeedbackEnum.PENDING)
                .eq("EXAM_ID", param.getExamId());
        examErrorCorrectionFeedbackMapper.update(null,updateWrapper);
    }

    @Override
    public void errorCorrectionAnalyticsResult(AiAnalyticsExamParam param) {
        //更新exam_error_correction_feedback状态为FINISHED
        UpdateWrapper<ExamErrorCorrectionFeedback> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("UPDATED_AT", new Date())
                .set("RESULT", ExamErrorCorrectionFeedbackEnum.FINISHED)
                .set("FINISHED_AT", new Date())
                .eq("EXAM_ID", param.getExamId());
        examErrorCorrectionFeedbackMapper.update(null,updateWrapper);

        //更新exam_analyze_result状态为纠错完成
        UpdateWrapper<ExamAnalyzeResult> updateParams = new UpdateWrapper<>();
        updateParams.set("UPDATED_AT", new Date())
                .set("RESULT", ExamAnalyzeResultEnum.FINISHED_ERROR)
                .eq("EXAM_ID", param.getExamId())
                .eq("RESULT",  ExamAnalyzeResultEnum.IN_ERROR);
        examAnalyzeResultMapper.update(null,updateParams);

        redisUtil.delete(String.format(REDIS_KEY_ERROR_CORRECTION_FEEDBACK_REDIS_KEY, param.getExamId().toString()));

        //查询examId相同的数据，并发送消息到kafka
        QueryWrapper<ExamErrorCorrectionFeedback> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EXAM_ID", param.getExamId());
        List<ExamErrorCorrectionFeedback> examErrorCorrectionFeedbacks = examErrorCorrectionFeedbackMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(examErrorCorrectionFeedbacks)) {
            examErrorCorrectionFeedbacks.forEach(examErrorCorrectionFeedback -> {
                //用户反馈的发送纠错完成通知
                if (examErrorCorrectionFeedback.getFeedbackType() == FeedbackTypeEnum.USER_FEEDBACK) {
                    /**
                     * 纠错完成后发送短信通知
                     * 短信模板
                     * 您反馈的试卷错误已修正，可继续生成考试分析报告，立即标注错题，开启薄弱知识精准练习👉 [短链]
                     * 短链点击可以打开 青于蓝 APP-考情分析报告列表页
                     *
                     * 站内信息通知
                     * 标题：试卷纠错已完成
                     * 点击继续标注错题，生成考试分析报告！
                     * 点击跳转到试卷 考情分析报告列表页
                     */
                    Map<String, String> msgMap = new HashMap<>();
                    msgMap.put("messageTitle", "试卷纠错已完成");
                    msgMap.put("content", "点击继续标注错题，生成考试分析报告！");
                    msgMap.put("bizId", String.valueOf(examErrorCorrectionFeedback.getExamAnalyzeResultId())); // 其他键值对
                    msgMap.put("studentId", String.valueOf(examErrorCorrectionFeedback.getStudentId())); // 其他键值对
                    msgMap.put("messageType", "36");
                    examQylMsgSender.sendMessageToQylESMsg(msgMap);

                    /*Map<String, String> smsMap = new HashMap<>();
                    smsMap.put("studentId", String.valueOf(examErrorCorrectionFeedback.getStudentId()));
                    smsMap.put("parentId", String.valueOf(examErrorCorrectionFeedback.getParentId()));
                    smsMap.put("reportId", String.valueOf(examErrorCorrectionFeedback.getExamAnalyzeResultId()));
                    examErrorCorrectionFeedbackSendSmsSender.sendMessageToQylESSms(smsMap);*/
                    SendSmsUtils.sendSms(examErrorCorrectionFeedback.getParentId(), examErrorCorrectionFeedback.getExamAnalyzeResultId(), null, examErrorCorrectionFeedback.getTelephoneNumber());
                } else if (examErrorCorrectionFeedback.getFeedbackType() == FeedbackTypeEnum.SYSTEM_RECOGNITION) {//系统识别
                    ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(examErrorCorrectionFeedback.getExamAnalyzeResultId());
                    if (examAnalyzeResult != null) {
                        //调用AI分析知识点生成考情分析报告
                        AiAnalyticsExamParam.ExtraParamsDTO extraParamsDTO = AiAnalyticsExamParam
                                .ExtraParamsDTO.builder()
                                .personalExamId(examAnalyzeResult.getPersonalExamId())
                                .analysisReportId(examErrorCorrectionFeedback.getExamAnalyzeResultId())
                                .parentId(examAnalyzeResult.getParentId())
                                .studentId(examAnalyzeResult.getStudentId())
                                .telNumber(examErrorCorrectionFeedback.getTelephoneNumber())
                                .hasExam(false)
                                .build();
                        AiAnalyticsExamParam aiAnalyticsExamParam = AiAnalyticsExamParam
                                .builder()
                                .examId(examErrorCorrectionFeedback.getExamId())
                                .extraParams(extraParamsDTO)
                                .publisher(param.getPublisher())
                                .build();
                        examAnalysisReportService.createExamAnalysisReport(aiAnalyticsExamParam);
                    }
                }
            });
        }

    }

    @Override
    public void invalidExamErrorCorrectionFeedback(ExamErrorCorrectionFeedbackParam param) {
        CommonResponse.ERROR.assertNotNull(param.getHandlerId(), "handlerId不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getHandlerName(), "handlerName不能为空！");
        CommonResponse.ERROR.assertNotNull(param.getExamId(), "examId不能为空！");

        //更新examId相同的数据的处理人和处理人id
        UpdateWrapper<ExamErrorCorrectionFeedback> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("UPDATED_AT", new Date())
                .set("HANDLER_ID", param.getHandlerId())
                .set("HANDLER_NAME", param.getHandlerName())
                .set("RESULT", ExamErrorCorrectionFeedbackEnum.INVALID)
                .eq("EXAM_ID", param.getExamId());
        examErrorCorrectionFeedbackMapper.update(null,updateWrapper);

        QueryWrapper<ExamErrorCorrectionFeedback> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EXAM_ID", param.getExamId());
        List<ExamErrorCorrectionFeedback> examErrorCorrectionFeedbacks = examErrorCorrectionFeedbackMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(examErrorCorrectionFeedbacks)) {
            examErrorCorrectionFeedbacks.forEach(examErrorCorrectionFeedback -> {
                ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(examErrorCorrectionFeedback.getExamAnalyzeResultId());
                Map<String, String> msgMap = new HashMap<>();
                msgMap.put("messageTitle", "考情分析报告");
                msgMap.put("content", "您上传的试卷未识别到完整试题，请重新上传清晰的试卷照片。");
                msgMap.put("jumpUrlType", "1");
                msgMap.put("jumpUrl", "https://cdn-ali-static.ijx.ink/h5-learning-tools/index.html#/mathEnter?type=notice");
                msgMap.put("bizId", String.valueOf(examErrorCorrectionFeedback.getExamAnalyzeResultId())); // 其他键值对
                msgMap.put("studentId", String.valueOf(examAnalyzeResult.getStudentId())); // 其他键值对
                msgMap.put("messageType", "34");
                examQylMsgSender.sendMessageToQylESMsg(msgMap);
                /*Map<String, String> smsMap = new HashMap<>();
                smsMap.put("smsType", "invalid");
                smsMap.put("studentId", String.valueOf(examAnalyzeResult.getStudentId()));
                smsMap.put("parentId", String.valueOf(examAnalyzeResult.getParentId()));
                smsMap.put("reportId", String.valueOf(examAnalyzeResult.getExamId()));
                examErrorCorrectionFeedbackSendSmsSender.sendMessageToQylESSms(smsMap);*/
                SendSmsUtils.sendSms(examErrorCorrectionFeedback.getParentId(), examErrorCorrectionFeedback.getExamAnalyzeResultId(), "invalid", examErrorCorrectionFeedback.getTelephoneNumber());

                examAnalyzeResult.setId(examErrorCorrectionFeedback.getExamAnalyzeResultId());
                examAnalyzeResult.setResult(ExamAnalyzeResultEnum.INVALID);
                examAnalyzeResult.setUpdatedAt(new Date());
                examAnalyzeResultMapper.updateById(examAnalyzeResult);
            });
        }
    }

    @Override
    public Boolean isCorrectingExamWithAI(UUID examId) {
        return redisUtil.hasKey(String.format(REDIS_KEY_ERROR_CORRECTION_FEEDBACK_REDIS_KEY, examId.toString()));
    }
}
