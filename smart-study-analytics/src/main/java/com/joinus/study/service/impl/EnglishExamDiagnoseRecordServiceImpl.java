package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.kafka.ReadingPersonalPlanStartRemindersSender;
import com.joinus.study.mapper.ActiveStudentsMapper;
import com.joinus.study.mapper.EnglishExamDiagnoseRecordMapper;
import com.joinus.study.model.dto.OssTokenDto;
import com.joinus.study.model.dto.PresignedUrlDto;
import com.joinus.study.model.dto.StudentBasicInfoDTO;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnglishExamDiagnoseRecordServiceImpl extends ServiceImpl<EnglishExamDiagnoseRecordMapper, EnglishExamDiagnoseRecord> implements EnglishExamDiagnoseRecordService {

    @Value("${english.ai.host.url:https://bun-hono-ai-english-server.uat.qingyulan.net}")
    private String englishAiHostUrl;
    @Value("${english.report.url}")
    private String reportUrl;
    @Resource
    private ReadingPersonalPlanStartRemindersSender readingPersonalPlanStartRemindersSender;
    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private EnglishExamDiagnoseQuestionService englishExamDiagnoseQuestionService;
    @Resource
    private EnglishExamDiagnoseReportService englishExamDiagnoseReportService;
    @Resource
    private EnglishExamReportQuestionTypeService englishExamReportQuestionTypeService;
    @Resource
    private EnglishExamReportKnowledgePointService englishExamReportKnowledgePointService;
    @Resource
    private EnglishExamService englishExamService;
    @Resource
    private ActiveStudentsMapper activeStudentsMapper;
    @Resource
    private BasicBusinessService basicBusinessService;

    @Override
    public Page<EnglishExamDiagnoseRecordVO> pages(EnglishDiagnoseReportPageParam param) {
        Page<EnglishExamDiagnoseRecord> page = new Page<>(param.getCurrent(), param.getSize());
        LambdaQueryChainWrapper<EnglishExamDiagnoseRecord> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EnglishExamDiagnoseRecord::getStudentId, param.getStudentId())
                .isNull(EnglishExamDiagnoseRecord::getDeletedAt)
                .ne(EnglishExamDiagnoseRecord::getStatus, -1)
                .orderByDesc(EnglishExamDiagnoseRecord::getId);
        Page<EnglishExamDiagnoseRecord> basicResourcePage = baseMapper.selectPage(page, wrapper.getWrapper());
        Page<EnglishExamDiagnoseRecordVO> resourcePageResultPage = new Page<>();
        BeanUtil.copyProperties(basicResourcePage, resourcePageResultPage);
        resourcePageResultPage.setRecords(basicResourcePage.getRecords().stream()
                .map(report -> BeanUtil.copyProperties(report, EnglishExamDiagnoseRecordVO.class))
                .collect(Collectors.toList()));
        return resourcePageResultPage;
    }

    @Override
    public EnglishExamDiagnoseDetailVO query(Long id) {
        EnglishExamDiagnoseRecord report = getById(id);
        Assert.notNull(report, "未找到相关记录");
        EnglishExamDiagnoseDetailVO examDiagnoseDetailVO = BeanUtil.copyProperties(report, EnglishExamDiagnoseDetailVO.class);
        List<EnglishExamDiagnoseReport> reports = englishExamDiagnoseReportService.listByRecordId(id);
        if (CollectionUtil.isNotEmpty(reports)) {
            EnglishExamDiagnoseReport englishExamDiagnoseReport = reports.get(0);
            BeanUtil.copyProperties(englishExamDiagnoseReport, examDiagnoseDetailVO);
            List<EnglishExamReportQuestionType> questionTypes = englishExamReportQuestionTypeService.listByReportId(englishExamDiagnoseReport.getId());
            questionTypes = questionTypes.stream().filter(t -> !StrUtil.equals("书面表达", t.getQuestionType())).collect(Collectors.toList());
            examDiagnoseDetailVO.setQuestionTypeList(BeanUtil.copyToList(questionTypes, EnglishExamReportQuestionTypeVO.class));
            List<EnglishExamReportKnowledgePoint> knowledgePoints = englishExamReportKnowledgePointService.listByReportId(englishExamDiagnoseReport.getId());
            examDiagnoseDetailVO.setKnowledgePointList(BeanUtil.copyToList(knowledgePoints, EnglishExamReportKnowledgePointVO.class));
        }
        return examDiagnoseDetailVO;
    }

    @Override
    public void sendMessage(Long id, Long studentId) {
        //单次分析完成后  发送通知
        String url = reportUrl + id;
        Map<String, String> message = Map.of(
                "senderType", "system",
                "studentId", String.valueOf(studentId),
                "messageType", "53",
                "messageTitle", "英语试卷诊断报告生成提醒",
                "content", "您的试卷诊断报告已生成，点击查看失分知识点，开启薄弱知识点定向爆破！",
                "bizId", String.valueOf(id),
                "isJPush", "0",
                "jumpUrlType", "1",
                "jumpUrl", url
        );
        readingPersonalPlanStartRemindersSender.sendMessageToQylMsg(message);
    }

    @Override
    public OssTokenVo ossToken(OssTokenParam param) {
        OssTokenDto ossTokenDto = eduKnowledgeHubService.ossToken(param);
        if (ossTokenDto == null) {
            CommonResponse.assertError("调用AI服务器失败");
        }
        OssTokenVo ossTokenVo = OssTokenVo.builder().build();
        BeanUtil.copyProperties(ossTokenDto, ossTokenVo);
        return ossTokenVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EnglishExamAnalysisVO analysisExam(EnglishExamAnalysisParam param) {
        // 创建记录
        EnglishExamDiagnoseRecord record = createDiagnoseRecord(param);

        EnglishExamAnalysisVO analysisVO = new EnglishExamAnalysisVO();
        analysisVO.setId(record.getId());
        // 获取图片完成链接
        List<String> presignedUrls = getPresignedUrls(param.getOssKeyList());
        // 调用AI服务识别试卷内容
        JSONObject jsonObject = analysisExam(record, param, presignedUrls);
        boolean isEn = jsonObject.getBool("is_en");
        Assert.isTrue(isEn, "ai识别出图片不是英语卷子，请确认后重新上传");
        record.setExamId(UUID.fromString(jsonObject.getStr("id")));
        record.setExamName(jsonObject.getStr("name"));
        record.setGrade(jsonObject.getInt("grade"));
        record.setTotalScore(jsonObject.getBigDecimal("score"));
        record.setQuestionNumber(jsonObject.getInt("small_questions_num"));
        this.updateById(record);
        // 获取解析出来的试卷信息
        EnglishExamAnalysisInfoVO analysisExamVO = getAnalysisExamInfoVO(record, jsonObject, presignedUrls);
        analysisVO.setAnalysisExamVO(analysisExamVO);
        // 获取已存在的试卷信息
        EnglishExamAnalysisInfoVO existExamVO = getExistExamInfoVO(record);
        analysisVO.setExistExamVO(existExamVO);

        return analysisVO;
    }

    private EnglishExamAnalysisInfoVO getExistExamInfoVO(EnglishExamDiagnoseRecord record) {
        List<EnglishExam> englishExams = englishExamService.listByName(record.getExamName());
        if (CollectionUtil.isEmpty(englishExams)) {
            return null;
        }
        EnglishExam englishExam = englishExams.get(0);
        EnglishExamAnalysisInfoVO existExamVO = new EnglishExamAnalysisInfoVO();
        existExamVO.setExamId(englishExam.getId().toString());
        existExamVO.setExamName(englishExam.getName());
        existExamVO.setGrade(englishExam.getGrade());
        existExamVO.setQuestionNumber(englishExam.getSmallQuestionsNum());
        List<EnglishExamAnalysisQuestionNumberVO> questionNumbers = JSONUtil.toList(englishExam.getQuestionsNums(), EnglishExamAnalysisQuestionNumberVO.class);
        try {
            questionNumbers = convertQuestionStructure(JSONUtil.toJsonStr(questionNumbers));
        } catch (JsonProcessingException e) {
            log.error("转换题型结构异常", e);
            throw new IllegalArgumentException("试卷解析异常：分析题号异常");
        }
        existExamVO.setQuestionNumbers(questionNumbers);
        // 获取图片完成链接
        List<String> presignedUrls = new ArrayList<>();
        JSONUtil.toList(englishExam.getOssUrls(), String.class)
                .forEach(ossUrl -> {
                    PresignedUrlParam urlParam = PresignedUrlParam.builder()
                            .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                            .ossKey(ossUrl)
                            .build();
                    PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(urlParam);
                    presignedUrls.add(presignedUrlDto.getData().getPresignedUrl());
                });
        existExamVO.setImageUrls(presignedUrls);
        return existExamVO;
    }

    private EnglishExamAnalysisInfoVO getAnalysisExamInfoVO(EnglishExamDiagnoseRecord record, JSONObject jsonObject, List<String> presignedUrls) {
        EnglishExamAnalysisInfoVO analysisExamVO = new EnglishExamAnalysisInfoVO();
        analysisExamVO.setExamId(record.getExamId().toString());
        analysisExamVO.setExamName(record.getExamName());
        analysisExamVO.setQuestionNumber(record.getQuestionNumber());
        analysisExamVO.setImageUrls(presignedUrls);
        analysisExamVO.setGrade(record.getGrade());
        List<EnglishExamAnalysisQuestionNumberVO> questionNumbers = JSONUtil.toList(jsonObject.getJSONArray("questions_nums"), EnglishExamAnalysisQuestionNumberVO.class);
        try {
            questionNumbers = convertQuestionStructure(JSONUtil.toJsonStr(questionNumbers));
        } catch (JsonProcessingException e) {
            log.error("转换题型结构异常", e);
            throw new IllegalArgumentException("试卷解析异常：分析题号异常");
        }
        analysisExamVO.setQuestionNumbers(questionNumbers);
        return analysisExamVO;
    }

    private JSONObject analysisExam(EnglishExamDiagnoseRecord record, EnglishExamAnalysisParam param, List<String> presignedUrls) {
        Map<String, Object> params = new HashMap<>();
        params.put("imageUrls", presignedUrls);
        params.put("requestId", record.getId().toString());
        params.put("ossUrls", param.getOssKeyList());
        // 试卷所属学校使用上传学生所在的学校
        params.put("school", record.getSchoolName());
        // 试卷所属区域使用上传学生所在的区域
        StudentBasicInfoDTO studentBasicInfoDTO = basicBusinessService.getStudentRegionIdInfo(param.getStudentId());
        if (Objects.nonNull(studentBasicInfoDTO)) {
            params.put("area", studentBasicInfoDTO.getProvince() + studentBasicInfoDTO.getCity() + studentBasicInfoDTO.getDistrict());
        }
        String response = LocalHttpUtil.post(englishAiHostUrl + "/parseExam", params, 300000);
        return JSONUtil.parseObj(response);
    }

    private List<String> getPresignedUrls(List<String> ossKeyList) {
        List<String> presignedUrls = new ArrayList<>();
        ossKeyList.forEach(ossKey -> {
            PresignedUrlParam urlParam = PresignedUrlParam.builder()
                    .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                    .ossKey(ossKey)
                    .build();
            PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrlV2(urlParam, 86400000L);
            presignedUrls.add(presignedUrlDto.getData().getPresignedUrl());
        });
        return presignedUrls;
    }

    private EnglishExamDiagnoseRecord createDiagnoseRecord(EnglishExamAnalysisParam param) {
        EnglishExamDiagnoseRecord record = new EnglishExamDiagnoseRecord();
        record.setStudentId(param.getStudentId());
        record.setStatus(-1);
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        ActiveStudentsEntity studentsCache = activeStudentsMapper.getByStudentId(param.getStudentId());
        if (Objects.nonNull(studentsCache)) {
            record.setSchoolId(studentsCache.getSchoolId());
            record.setSchoolName(studentsCache.getSchoolName());
            record.setStudentName(studentsCache.getStudentName());
            record.setClassId(studentsCache.getClassId());
            record.setClassName(studentsCache.getClassName());
            record.setGradeId(studentsCache.getGradeId());
            record.setGradeName(studentsCache.getGradeName());
        }
        this.save(record);
        return record;
    }

    private List<EnglishExamAnalysisQuestionNumberVO> convertQuestionStructure(String json) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode originalArray = (ArrayNode) mapper.readTree(json);

        // 1. 按大题类型分组，保留顺序
        Map<String, List<EnglishExamAnalysisShortQuestionVO>> groupedMap = new LinkedHashMap<>();

        // 2. 遍历原始数据
        for (int i = 0; i < originalArray.size(); i++) {
            ObjectNode bigQuestion = (ObjectNode) originalArray.get(i);
            int bigNum = bigQuestion.get("num").asInt();
            String type = bigQuestion.get("type").asText();
            ArrayNode children = (ArrayNode) bigQuestion.get("child");

            // 跳过没有小题的大题
            if (children == null || children.isEmpty()) {
                continue;
            }

            // 3. 处理小题
            List<EnglishExamAnalysisShortQuestionVO> childList = groupedMap.computeIfAbsent(type, k -> new ArrayList<>());
            for (int j = 0; j < children.size(); j++) {
                int shortNum = children.get(j).asInt();
                childList.add(new EnglishExamAnalysisShortQuestionVO(shortNum, bigNum, shortNum));
            }
        }

        // 4. 构建结果
        ArrayNode resultArray = mapper.createArrayNode();
        for (Map.Entry<String, List<EnglishExamAnalysisShortQuestionVO>> entry : groupedMap.entrySet()) {
            String type = entry.getKey();
            List<EnglishExamAnalysisShortQuestionVO> children = entry.getValue();

            // 5. 创建大题节点
            ObjectNode bigNode = mapper.createObjectNode();
            bigNode.put("type", type);

            // 6. 添加小题数组
            ArrayNode childArray = mapper.valueToTree(children);
            bigNode.set("children", childArray);

            resultArray.add(bigNode);
        }

        String jsonStr = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(resultArray);
        return JSONUtil.toList(jsonStr, EnglishExamAnalysisQuestionNumberVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitRecord(EnglishExamAnalysisSubmitReportParam param) {
        List<EnglishExamFalseQuestionNumberParam> questions = param.getQuestions();
        boolean exist = questions.stream()
                .anyMatch(q ->
                        q.getChild().stream()
                                .anyMatch(c -> c.getIsWrong() == 1));
        Assert.isTrue(exist, "错题集不能为空，请标注错题集后再次提交");
        EnglishExamDiagnoseRecord record = this.getById(param.getId());
        Assert.notNull(record, "记录不存在");
        // 更新所属年级
        record.setStatus(0);
        record.setGrade(param.getGrade());
        if (!Objects.equals(record.getExamId(), param.getExamId())) {
            EnglishExam englishExam = englishExamService.getById(param.getExamId());
            record.setExamId(englishExam.getId());
            record.setExamName(englishExam.getName());
            record.setQuestionNumber(englishExam.getSmallQuestionsNum());
            record.setTotalScore(englishExam.getScore());
        }
        this.updateById(record);
        // 调用AI 服务生成报告
        Map<String, Object> params = new HashMap<>();
        params.put("examId", record.getExamId());
        params.put("requestId", record.getId().toString());
        params.put("imageUrls", new ArrayList<>());
        params.put("grade", param.getGrade());
        List<EnglishExamQuestionNumberParamV2> falseQuestionNumbers = param.getQuestions().stream()
                .filter(t -> t.getChild().stream().anyMatch(q -> Objects.equals(q.getIsWrong(), 1)))
                .map(t -> new EnglishExamQuestionNumberParamV2(t.getNum(),
                        t.getChild().stream().filter(q -> Objects.equals(q.getIsWrong(), 1))
                                .map(EnglishExamQuestionNumberParam::getNum).collect(Collectors.toList())))
                .collect(Collectors.toList());
        params.put("falseQuestionNum", falseQuestionNumbers);
        CompletableFuture.runAsync(() -> {
            try {
                // 使用try-with-resources确保自动关闭连接
                try (var ignored = HttpRequest.post(englishAiHostUrl + "/parseQuestion")
                        .body(JSONUtil.toJsonStr(params))
                        .contentType("application/json")
                        .timeout(300000)  // 设置超时防止永久阻塞
                        .execute()) {
                    // 不处理响应结果
                }
            } catch (Exception e) {
                // 简单记录异常（生产环境应使用日志框架）
                log.error("调用AI 服务生成报告失败，参数：{}，错误信息：{}", param, e.getMessage(), e);
            }
        });
    }

    @Override
    public List<String> queryOssUrl(EnglishExamQuryOssUrlParam param) {
        List<String> presignedUrls = new ArrayList<>();
        param.getOssKeyList().forEach(ossKey -> {
            PresignedUrlParam urlParam = PresignedUrlParam.builder()
                    .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                    .ossKey(ossKey)
                    .build();
            PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(urlParam);
            presignedUrls.add(presignedUrlDto.getData().getPresignedUrl());
        });
        return presignedUrls;
    }

}
