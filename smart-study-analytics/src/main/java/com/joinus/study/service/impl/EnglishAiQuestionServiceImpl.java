package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishAiQuestionMapper;
import com.joinus.study.model.entity.EnglishAiQuestion;
import com.joinus.study.model.param.EnglishAiQuestionQueryParam;
import com.joinus.study.model.vo.EnglishAiQuestionVO;
import com.joinus.study.service.EnglishAiQuestionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Service("englishAiQuestionService")
@Slf4j
public class EnglishAiQuestionServiceImpl extends ServiceImpl<EnglishAiQuestionMapper, EnglishAiQuestion> implements EnglishAiQuestionService {

    @Override
    public List<EnglishAiQuestionVO> list(EnglishAiQuestionQueryParam queryParam) {
        return this.baseMapper.list(queryParam);
    }
}
