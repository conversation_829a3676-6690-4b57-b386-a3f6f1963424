package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.MathStudentStudyDayHistory;
import com.joinus.study.service.MathStudentStudyDayHistoryService;
import com.joinus.study.mapper.MathStudentStudyDayHistoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_student_study_day_history(学生学习天数记录)】的数据库操作Service实现
* @createDate 2025-09-12 16:19:30
*/
@Service
public class MathStudentStudyDayHistoryServiceImpl extends ServiceImpl<MathStudentStudyDayHistoryMapper, MathStudentStudyDayHistory>
    implements MathStudentStudyDayHistoryService{

}




