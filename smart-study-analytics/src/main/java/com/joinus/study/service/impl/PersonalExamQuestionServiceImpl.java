package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.PersonalExamQuestion;
import com.joinus.study.service.PersonalExamQuestionService;
import com.joinus.study.mapper.PersonalExamQuestionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【personal_exam_question】的数据库操作Service实现
* @createDate 2025-03-11 09:48:14
*/
@Service
public class PersonalExamQuestionServiceImpl extends ServiceImpl<PersonalExamQuestionMapper, PersonalExamQuestion>
    implements PersonalExamQuestionService{

    @Override
    public List<PersonalExamQuestion> listByPersonalExamId(Long personalExamId) {
        return lambdaQuery().eq(PersonalExamQuestion::getPersonalExamId, personalExamId)
                .list();
    }
}




