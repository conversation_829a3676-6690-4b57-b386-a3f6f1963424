package com.joinus.study.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPersonalAnalysisReportMapper;
import com.joinus.study.mapper.ReadingPersonalPassagesQuestionMapper;
import com.joinus.study.mapper.ReadingPersonalSuggestionAnalysisMapper;
import com.joinus.study.mapper.ReadingPersonalWeakKnowledgePointsAnalysisMapper;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.enums.ReadingAIAbilityType;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReadingPersonalAnalysisReportServiceImpl extends ServiceImpl<ReadingPersonalAnalysisReportMapper, ReadingPersonalAnalysisReport>
        implements ReadingPersonalAnalysisReportService {

    @Resource
    private ReadingPersonalPassagesService readingPersonalPassagesService;

    @Resource
    private ReadingPersonalPassagesQuestionMapper passagesQuestionMapper;

    @Resource
    private ReadingPersonalWeakKnowledgePointsAnalysisMapper weakKnowledgePointsAnalysisMapper;
    @Resource
    private ReadingPersonalSuggestionAnalysisMapper suggestionAnalysisMapper;
    @Resource
    @Lazy
    private ReadingPersonalCorrectionService readingPersonalCorrectionService;

    @Resource
    private ReadingAiAnalysisService aiAnalysisService;

    @Resource
    private ReadingPersonalAnalysisPeriodicReportService  readingPersonalAnalysisPeriodicReportService;

    @Override
    public Page<ReadingHistoryReportVo> queryReadingHistoryReportList(ReadingHistoryReportPageParam pageParam) {
        log.info("queryReadingHistoryReportList,param:{},startTime:{}", JSONUtil.toJsonStr(pageParam),System.currentTimeMillis());
        CommonResponse.ERROR.assertNotNull(pageParam.getStudentId(), "studentId不能为空");
        Page<ReadingHistoryReportVo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingHistoryReportVo> reportList = baseMapper.queryReadingHistoryReportList(page, pageParam.getStudentId());
        page.setRecords(reportList);
        log.info("queryReadingHistoryReportList,endTime:{}", System.currentTimeMillis());
        return page;
    }

    @Override
    public Map<String, Object> queryPersonalPassageData(ReadingPersonalPassageDataParam param) {
        return baseMapper.queryPersonalPassageData(param);
    }

    @Override
    @Transactional
    public Long generateTrainingReport(Long personalPassageId) {
        CommonResponse.ERROR.assertNotNull(personalPassageId, "用户训练id不能为空");

        ReadingPersonalPassages personalPassages = readingPersonalPassagesService.getById(personalPassageId);
        log.info("generateTrainingReport 查询训练记录数据：{}", personalPassages);
        CommonResponse.ERROR.assertNotNull(personalPassages, "找不到对应训练记录，id：" + personalPassageId + "异常");
        CommonResponse.ERROR.assertIsFalse(ObjectUtil.isNotEmpty(personalPassages.getDeletedAt()), "对应训练记录已被删除，id：" + personalPassageId + "异常");

        Long reportId = null;
        //判断是否已生成报告数据 只重新出发ai交互
        ReadingPersonalAnalysisReport oldReport = baseMapper.selectOne(Wrappers.<ReadingPersonalAnalysisReport>lambdaQuery()
                .eq(ReadingPersonalAnalysisReport::getPersonalPassageId, personalPassageId)
                .isNull(ReadingPersonalAnalysisReport::getDeletedAt));
        int isAnalysis = 1;

        //获取训练记录表数据
        ReadingPersonalPassageDataParam trainingRecordParam = ReadingPersonalPassageDataParam.builder().personalPassageId(personalPassageId).build();
        Map<String, Object> trainingRecordData = queryPersonalPassageData(trainingRecordParam);
        log.info("generateTrainingReport 查询训练记录统计数据：{}", trainingRecordData);
        CommonResponse.ERROR.assertNotNull(trainingRecordData, "找不到对应训练统计数据");
        int solveTime = 0;
        if(personalPassages.getAnswerMethod() != 1){
//                solveTime = DateUtil.getDiffInSeconds(personalPassages.getCreatedAt(), personalPassages.getEndAt());
            solveTime = Integer.parseInt(trainingRecordData.get("timeSpent").toString());
        }
        ReadingPersonalAnalysisReport build = new ReadingPersonalAnalysisReport();
        if(oldReport == null){
            build = ReadingPersonalAnalysisReport.builder()
                    .personalPassageId(personalPassageId)
                    .studentId(personalPassages.getStudentId())
                    .totalKnowledgePoints(Integer.valueOf(trainingRecordData.get("totalKnowledgePoints").toString()))
                    .weakPointCount(Integer.valueOf(trainingRecordData.get("weakPointCount").toString()))
                    .genre(trainingRecordData.get("genre").toString())
                    .correctCount(Integer.valueOf(trainingRecordData.get("correctCount").toString()))
                    .incorrectCount(Integer.valueOf(trainingRecordData.get("incorrectCount").toString()))
                    .solveTime(solveTime)
                    .accuracyRate(readingPersonalPassagesService.getAccuracyRateByParam(trainingRecordParam))
                    .status(0)
                    .isView(0)
                    .build();
        }else{
            oldReport.setTotalKnowledgePoints(Integer.valueOf(trainingRecordData.get("totalKnowledgePoints").toString()));
            oldReport.setWeakPointCount(Integer.valueOf(trainingRecordData.get("weakPointCount").toString()));
            oldReport.setCorrectCount(Integer.valueOf(trainingRecordData.get("correctCount").toString()));
            oldReport.setIncorrectCount(Integer.valueOf(trainingRecordData.get("incorrectCount").toString()));
            oldReport.setGenre(trainingRecordData.get("genre").toString());
            oldReport.setSolveTime(solveTime);
            oldReport.setAccuracyRate(readingPersonalPassagesService.getAccuracyRateByParam(trainingRecordParam));
            oldReport.setStatus(0);
            build = oldReport;
        }

        log.info("generateTrainingReport 封装报告信息准备插入数据：{}", build);
        int res = 0;
        if(build.getId() == null){
            res = baseMapper.insert(build);
        }else{
            res = baseMapper.updateById(build);
        }
        if (res != 1) {
            log.error("generateTrainingReport 报告插入失败，数据：{}", build);
            CommonResponse.assertError("生成报告失败！");
        }
        reportId = build.getId();

        //封装ai分析需要的参数
        ReadingAIAbilityParam param = new ReadingAIAbilityParam();
        param.setId(reportId.toString());
        param.setType(1);
        //查询题目信息
        List<ReadingPersonalPassagesQuestions> questionsList = passagesQuestionMapper.selectList(Wrappers.<ReadingPersonalPassagesQuestions>lambdaQuery()
                .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, personalPassageId)
                .isNull(ReadingPersonalPassagesQuestions::getDeletedAt));

        if(questionsList == null){
            log.error("generateTrainingReport 未查询到题目信息，数据异常");
            CommonResponse.assertError("未查询到题目信息，数据异常！");
        }
        List<ReadingAIAbilityItemParam> itemParamList = new ArrayList<>();
        for (ReadingPersonalPassagesQuestions questions : questionsList) {
            String knowledgePoints = getKnowledgePoint(questions.getQuestionId());
            if(knowledgePoints == null){
                continue;
            }
            ReadingAIAbilityItemParam itemParam = new ReadingAIAbilityItemParam();
            //根据题目id获取知识点集合
            itemParam.setId(questions.getQuestionId().toString());
            itemParam.setKnowledgePoint(knowledgePoints);
            itemParam.setQuestionType(questions.getQuestionType());
            itemParam.setAnswer(questions.getUserAnswer());
            itemParam.setResult(questions.getResult());
            itemParamList.add(itemParam);
        }
        param.setItems(itemParamList);
        //触发ai交互
        if(param.getItems() != null){
            param.setReadingAIAbilityType(ReadingAIAbilityType.SINGLE_REPORT.getType());
            aiAnalysisService.analysesAIRequest(param);
        }else{
            log.info("generateTrainingReport 周报或月报薄弱项为空，数据异常");
        }
        return reportId;
    }

    @Override
    public String getKnowledgePoint(UUID questionId) {
        return baseMapper.getKnowledgePointByQuestionId(questionId);
    }

    @Override
    public Boolean checkReportIsAnalysisCompleted(Long reportId) {
        //检查是否分析薄弱知识点
        if (weakKnowledgePointsAnalysisMapper.selectCount(Wrappers.<ReadingPersonalWeakKnowledgePointsAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getReportId, reportId)
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getAnalysisType, 1)
                .isNull(ReadingPersonalWeakKnowledgePointsAnalysis::getDeletedAt)) == 0) {
            return false;
        }
        //检查是否分析综合训练建议
        if (suggestionAnalysisMapper.selectCount(Wrappers.<ReadingPersonalSuggestionAnalysis>lambdaQuery()
                .eq(ReadingPersonalSuggestionAnalysis::getReportId, reportId)
                .eq(ReadingPersonalSuggestionAnalysis::getAnalysisType, 1)
                .isNull(ReadingPersonalSuggestionAnalysis::getDeletedAt)) == 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取报告详情
     * @param reportId 学情分析报告id
     * @param requestType 请求类型 1青于蓝  2管理后台
     * @return
     */
    @Override
    public ReadingReportDetailVo reportDetail(Long reportId, Integer requestType) {
        CommonResponse.ERROR.assertNotNull(reportId, "学情分析报告id不能为空");
        ReadingPersonalAnalysisReport report = baseMapper.selectById(reportId);
        CommonResponse.ERROR.assertNotNull(report, "找不到对应学情分析报告，id：" + reportId + "异常");
        //增加判断，设置已查看
        if(report.getIsView() == 0 && report.getStatus() == 1 && requestType == 1){
            report.setIsView(1);
            baseMapper.updateById(report);
        }

        ReadingReportDetailVo reportDetailVo = new ReadingReportDetailVo();
        // 基础属性拷贝
        BeanUtils.copyProperties(report, reportDetailVo);
        //文体
        reportDetailVo.setGenre(GenreEnum.getByName(reportDetailVo.getGenre()));
        //获取文章信息
        ReadingPassages passages = readingPersonalPassagesService.getPassageInfoByPersonalPassagesId(report.getPersonalPassageId());
        CommonResponse.ERROR.assertNotNull(passages, "数据异常！找不到做题文章信息");
        reportDetailVo.setPassageId(passages.getId());
        reportDetailVo.setUnitId(passages.getUnitId());

/*        //获取薄弱知识点分析数据
        List<ReadingPersonalWeakKnowledgePointsAnalysis> weakKnowledgePointsAnalysisList = weakKnowledgePointsAnalysisMapper.selectList(Wrappers.<ReadingPersonalWeakKnowledgePointsAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getReportId, reportId)
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getAnalysisType, 1)
                .isNull(ReadingPersonalWeakKnowledgePointsAnalysis::getDeletedAt));
        log.info("reportDetail 获取薄弱知识点分析数据：{}", weakKnowledgePointsAnalysisList);
//        CommonResponse.ERROR.assertNotNull(weakKnowledgePointsAnalysisList, "报告生成中");
        reportDetailVo.setWeakPointsAnalyses(weakKnowledgePointsAnalysisList);

        //综合训练建议分析数据
        List<ReadingPersonalSuggestionAnalysis> suggestionAnalyses = suggestionAnalysisMapper.selectList(Wrappers.<ReadingPersonalSuggestionAnalysis>lambdaQuery()
                .eq(ReadingPersonalSuggestionAnalysis::getReportId, reportId)
                .eq(ReadingPersonalSuggestionAnalysis::getAnalysisType, 1)
                .isNull(ReadingPersonalSuggestionAnalysis::getDeletedAt));
        log.info("reportDetail 获取综合训练建议分析数据：{}", suggestionAnalyses);
//        CommonResponse.ERROR.assertNotNull(suggestionAnalyses, "报告生成中");
        reportDetailVo.setSuggestionAnalyses(suggestionAnalyses);*/

        //题目信息
        List<ReadingPassageQuestionsAnswersVO> questionsAnswers = readingPersonalPassagesService.queryPersonalPassagesQuestionList(report.getPersonalPassageId());
        for (ReadingPassageQuestionsAnswersVO questionAnswers : questionsAnswers){
            Map<String, Object> questionMap = DataUtil.processQuestionContent(questionAnswers.getContent());
            questionAnswers.setContent(questionMap.get("questionContent").toString());
            questionAnswers.setOptions((JSONArray)questionMap.get("options"));
            questionAnswers.setAnswer(DataUtil.parseAnswer(questionAnswers.getAnswer()));
            questionAnswers.setFormulaAnswer(DataUtil.parseAnswer(questionAnswers.getFormulaAnswer()));
            questionAnswers.setFormulaTemplate(questionAnswers.getFormulaTemplate());
        }
        reportDetailVo.setQuestionsAnswers(questionsAnswers);
        log.info("reportDetail 获取题目信息：{}", reportDetailVo.getQuestionsAnswers());
        return reportDetailVo;
    }

    @Override
    public List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointList(ReadingPersonalKnowledgePointDataParam param) {
        return baseMapper.queryPersonalPassageKnowledgePointList(param);
    }

    @Override
    public List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointData(ReadingPersonalKnowledgePointDataParam param) {
        return baseMapper.queryPersonalPassageKnowledgePointData(param);
    }

    @Override
    public List<ReadingQuestionTypeVo> queryPersonalPassageQuestionTypeData(ReadingPersonalQuestionTypeDataParam param) {
        return baseMapper.queryPersonalPassageQuestionTypeData(param);
    }

    @Override
    public List<ReadingPeriodicReportDetailLineChartVo> queryPersonalPassageDataForLineChart(ReadingPersonalPassageDataParam param) {
        return baseMapper.queryPersonalPassageDataForLineChart(param);
    }

    @Override
    public List<ReadingHighFreqPerformanceVo> queryPersonalPassageHighFreqPerformanceData(ReadingPersonalPassageDataParam param) {
        return baseMapper.queryPersonalPassageHighFreqPerformanceData(param);
    }

    @Override
    public void errorCorrecting(ReadingErrorCorrectingParam param) {
        //校验该题目是否生成周报或月报
        CommonResponse.ERROR.assertNotNull(param.getPersonalPassageId(), "用户答题训练id不能为空");
        boolean existReport = readingPersonalAnalysisPeriodicReportService.checkPersonalPassageIsExistReport(param.getPersonalPassageId());
        CommonResponse.ERROR.assertIsTrue(!existReport, "对应训练记录已生成周报/月报");
        readingPersonalCorrectionService.create(param);
    }

    @Override
    public ReadingPersonalAnalysisReport getByPersonalPassageId(Long personalPassageId) {
        LambdaQueryWrapper<ReadingPersonalAnalysisReport> wrapper = Wrappers.lambdaQuery(ReadingPersonalAnalysisReport.class);
        wrapper.eq(ReadingPersonalAnalysisReport::getPersonalPassageId, personalPassageId);
        return this.getOne(wrapper);
    }

    @Override
    public Long getReportIdByPersonalPassageId(Long personalPassageId) {
        CommonResponse.ERROR.assertNotNull(personalPassageId, "用户答题训练id不能为空");
        ReadingPersonalPassages personalPassages = readingPersonalPassagesService.getById(personalPassageId);
        log.info("getReportIdByPersonalPassageId 查询训练记录数据：{}", personalPassages);
        CommonResponse.ERROR.assertNotNull(personalPassages, "找不到对应训练记录，id：" + personalPassageId + "异常");
        CommonResponse.ERROR.assertIsTrue(ObjectUtil.isNull(personalPassages.getDeletedAt()), "对应训练记录已被删除，id：" + personalPassageId + "异常");

        //判断是否已生成报告数据 只重新出发ai交互
        ReadingPersonalAnalysisReport report = baseMapper.selectOne(Wrappers.<ReadingPersonalAnalysisReport>lambdaQuery()
                .eq(ReadingPersonalAnalysisReport::getPersonalPassageId, personalPassageId)
                .isNull(ReadingPersonalAnalysisReport::getDeletedAt));
        if(report != null){
            return report.getId();
        }else{
            CommonResponse.assertError("未查询到报告信息，数据异常！");
        }
        return null;
    }

    @Override
    public Integer getReportNotViewCountByStudentId(Long studentId) {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        return Math.toIntExact(count(Wrappers.<ReadingPersonalAnalysisReport>lambdaQuery()
                .eq(ReadingPersonalAnalysisReport::getStudentId, studentId)
                .eq(ReadingPersonalAnalysisReport::getIsView, 0)
                .eq(ReadingPersonalAnalysisReport::getStatus, 1)
                .isNull(ReadingPersonalAnalysisReport::getDeletedAt)));
    }


    @Override
    public List<Map<String, Object>> queryPassageInfo(Long reportId) {
        CommonResponse.ERROR.assertNotNull(reportId, "用户训练报告id不能为空");
        return baseMapper.queryPassageInfo(reportId);
    }

    @Override
    public void againGenerateSingleReport() {
        List<ReadingPersonalAnalysisReport> reportList = baseMapper.selectList(Wrappers.<ReadingPersonalAnalysisReport>lambdaQuery()
                .eq(ReadingPersonalAnalysisReport::getStatus, 1)
                .isNull(ReadingPersonalAnalysisReport::getDeletedAt)
                .isNull(ReadingPersonalAnalysisReport::getAnalysesResult)
                .orderByDesc(ReadingPersonalAnalysisReport::getId)
        );
        reportList = reportList.stream().limit(100).collect(Collectors.toList());
        reportList.forEach(report -> {
            Long personalPassageId = report.getPersonalPassageId();
            Long reportId = report.getId();

            ReadingPersonalPassages personalPassages = readingPersonalPassagesService.getById(personalPassageId);
            if(personalPassages == null){return;}

            //封装ai分析需要的参数
            ReadingAIAbilityParam param = new ReadingAIAbilityParam();
            param.setId(reportId.toString());
            //查询题目信息
            List<ReadingPersonalPassagesQuestions> questionsList = passagesQuestionMapper.selectList(Wrappers.<ReadingPersonalPassagesQuestions>lambdaQuery()
                    .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, personalPassageId)
                    .isNull(ReadingPersonalPassagesQuestions::getDeletedAt));

            if(questionsList == null){
                log.error("againGenerateSingleReport,未查询到题目信息,报告ID：{}", reportId);
                return;
            }
            List<ReadingAIAbilityItemParam> itemParamList = new ArrayList<>();
            for (ReadingPersonalPassagesQuestions questions : questionsList) {
                String knowledgePoints = getKnowledgePoint(questions.getQuestionId());
                if(knowledgePoints == null){
                    continue;
                }
                ReadingAIAbilityItemParam itemParam = new ReadingAIAbilityItemParam();
                //根据题目id获取知识点集合
                itemParam.setId(questions.getQuestionId().toString());
                itemParam.setKnowledgePoint(knowledgePoints);
                itemParam.setQuestionType(questions.getQuestionType());
                itemParam.setAnswer(questions.getUserAnswer());
                itemParam.setResult(questions.getResult());
                itemParamList.add(itemParam);
            }
            param.setItems(itemParamList);
            //触发ai交互
            if(param.getItems() != null){
                param.setReadingAIAbilityType(ReadingAIAbilityType.SINGLE_REPORT.getType());
                aiAnalysisService.analysesAIRequest(param);
            }else{
                log.info("againGenerateSingleReport,报告ID：{}生成异常", reportId);
            }
        });
    }
}
