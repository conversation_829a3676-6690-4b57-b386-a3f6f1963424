package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.heaven.util.util.CollectionUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.MathExamsMapper;
import com.joinus.study.mapper.MathQuestionsMapper;
import com.joinus.study.mapper.PersonalExamMapper;
import com.joinus.study.mapper.PersonalExamQuestionMapper;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.MathExamsEntity;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.entity.PersonalExamQuestion;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.PresignedUrlParam;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.DataUtil;
import com.joinus.study.utils.PDFToImageUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【personal_exam】的数据库操作Service实现
 * @createDate 2025-03-11 09:48:14
 */
@Slf4j
@Service
@ApiModel(value = "个人考试", description = "个人考试")
public class PersonalExamServiceImpl extends ServiceImpl<PersonalExamMapper, PersonalExam>
        implements PersonalExamService {

    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private AiRequestService requestService;
    @Resource
    private MathExamsMapper mathExamsMapper;
/*    @Resource
    private SpecializedTrainingService specializedTrainingService;*/
    @Resource
    private GradeService gradeService;
    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private PersonalExamQuestionService personalExamQuestionService;
    @Autowired
    private MathQuestionsMapper mathQuestionsMapper;
    @Autowired
    private QuestionKnowledgePointService questionKnowledgePointService;
    @Resource
    private PersonalExamQuestionMapper personalExamQuestionMapper;
    @Autowired
    private PersonalExamMapper personalExamMapper;
    @Override
    @ApiModelProperty(value = "根据试卷id获取试卷信息")
    public ExamQuestionInfoListData examById(Long studentId, UUID id, boolean createExam, Long personalExamId, PublisherEnum publisher) {
        //todo:
        List<ExamQuestionInfoDto> examQuestionInfo = baseMapper.getExamQuestionInfo(id);
        if (CollUtil.isEmpty(examQuestionInfo)) {
            CommonResponse.assertError("试卷获取失败");
        }
        List<ExamQuestionInfoListData.ExamQuestionInfoDtoData> examQuestionInfoDtoDataList = new ArrayList<>();
        examQuestionInfo.forEach(examQuestionInfoDto -> {
            QuestionTypeEnum questionType = QuestionTypeEnum.getEnumByDesc(examQuestionInfoDto.getQuestionType());
            ExamQuestionInfoListData.ExamQuestionInfoDtoData examQuestionInfoDtoData = ExamQuestionInfoListData.ExamQuestionInfoDtoData
                    .builder()
                    .sortNo(examQuestionInfoDto.getSortNo())
                    .questionType(questionType)
                    .questionTypeName(questionType.getDesc())
                    .questionId(examQuestionInfoDto.getQuestionId())
                    .build();
            examQuestionInfoDtoDataList.add(examQuestionInfoDtoData);
        });


        ExamQuestionInfoListData examQuestionInfoListData = ExamQuestionInfoListData
                .builder()
                .examName(examQuestionInfo.get(0).getExamName())
                .questionCount(examQuestionInfoDtoDataList.size())
                .questionList(examQuestionInfoDtoDataList)
                .examId(id)
                .build();

        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(id);
        PersonalExam personalExam = personalExamMapper.selectById(personalExamId);
        if (null != personalExam) {
            examQuestionInfoListData.setPublisher(personalExam.getPublisher());
            examQuestionInfoListData.setGrade(personalExam.getGrade());
        } else {
            examQuestionInfoListData.setPublisher(PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()));
            examQuestionInfoListData.setGrade(mathExamsEntity.getGrade());
        }

        examQuestionInfoListData.setPersonalExamId(personalExamId);
        return examQuestionInfoListData;
    }

    @Override
    public List<QuestionKnowledgePoint> listWeakKnowledgePoints(Long personalExamId) {
        return baseMapper.listWeakKnowledgePoints(personalExamId);
    }

    @Override
    public String saveExam(PersonalExamVo personalExamVo) {
        //获取pdfUrl
        //pdf转图片
        try {
            //获取pdf oss临时链接
            JSONObject param = new JSONObject();
            param.put("ossEnum", personalExamVo.getOssEnum());
            param.put("ossKey", personalExamVo.getPdfUrl());
            String data = requestService.getRequest("/ai/ability/oss/presigned-url", param);
            if (StringUtils.isEmpty(data)) {
                CommonResponse.assertError("获取pdf文件失败");
            }
            //pdf转图片
            byte[][] pngBytes = PDFToImageUtils.convertPDFToImageBytes(JSONObject.parseObject(data).get("presignedUrl").toString(), "png");
            List<String> pdfImageUrls = new ArrayList<>();
            for (byte[] pngByte : pngBytes) {
                String uuid = UUID.randomUUID().toString();
                String objectName = aliOssUtils.upload(pngByte, "question/", uuid + ".png");
                pdfImageUrls.add(objectName);
            }
            personalExamVo.setObjectNames(pdfImageUrls);
            personalExamVo.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);

            //调用ai的试卷上传接口
            JSONObject mathExamsParam = new JSONObject();
            mathExamsParam.put("objectNames", personalExamVo.getObjectNames());
            mathExamsParam.put("ossEnum", personalExamVo.getOssEnum());
            mathExamsParam.put("grade", personalExamVo.getGrade());
            mathExamsParam.put("semester", personalExamVo.getSemester());
            String examId = requestService.postRequest("/math/exams/upload", mathExamsParam);
            if (StringUtils.isEmpty(examId)) {
                CommonResponse.assertError("试卷入库失败");
            }

            PersonalExam personalExam = PersonalExam.builder()
                    .examId(UUID.fromString(examId))
                    .studentId(personalExamVo.getStudentId())
                    .flag("own")
                    .build();
            baseMapper.insert(personalExam);
            return examId;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Page<PersonalExamDto> getRegionExamList(StudentAndTimePageParam param) {
        Page<PersonalExamDto> page = new Page(param.getCurrent(), param.getSize());
        Long total = mathExamsMapper.selectExamListByRegion_count(param);
        page.setTotal(total);
        // 手动设置分页参数
        if (total > 0) {
            List<PersonalExamDto> personalExamDtos = mathExamsMapper.selectExamListByRegion(param, page.offset(), page.getSize());
            personalExamDtos.forEach(personalExamDto -> {
                personalExamDto.setBookVolume(BookVolumeEnum.fromVolumeNum(personalExamDto.getSemester()));
            });
            page.setRecords(personalExamDtos);
        }
        return page;
    }

    @Override
    public ExamGroupQuestionTypeInfoDto getPersonalExamExamDetail(UUID examId, Long personalExamId) throws BaseException {
        Map<UUID, PersonalExamQuestionResultEnum> questionResultMap = new HashMap<>();
        if (null != personalExamId) {
            List<PersonalExamQuestion> questionList = listQuestionByPersonalExamId(personalExamId);
            questionResultMap = questionList.stream().collect(Collectors.toMap(PersonalExamQuestion::getQuestionId, PersonalExamQuestion::getResult));
        }

        MathExamsEntity exams = mathExamsMapper.selectExamById(examId);
        CommonResponse.ERROR.assertNotNull(exams, "试卷不存在");
        ExamGroupQuestionTypeInfoDto examDto = ExamGroupQuestionTypeInfoDto.builder()
                .examName(exams.getName())
                .examId(examId)
                .build();
        List<QuestionInfoByIdDto> questionInfoList = baseMapper.getQuestionInfoListByExamId(examId);
        Boolean isAnswer = false;
        for (QuestionInfoByIdDto questionInfo : questionInfoList) {
            questionInfo.setQuestionContent(eduKnowledgeHubService.decodeContentV2(questionInfo.getQuestionContent()));
            questionInfo.setAnalyzeContent(eduKnowledgeHubService.decodeContentV2(questionInfo.getAnalyzeContent()));
            questionInfo.setAnswer(eduKnowledgeHubService.decodeContentV2(questionInfo.getAnswer()));
            if(StringUtils.isNotBlank(questionInfo.getStructuredContent())){
                questionInfo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfo.getStructuredContent()), ChoiceContentVo.class));
            } else {
                if(questionInfo.getQuestionType()!=null && questionInfo.getQuestionType().contains("选择")){
                    questionInfo.setChoiceContent(DataUtil.formatChoiceContent(questionInfo.getQuestionContent()));
                }
            }
            if (!isAnswer && StringUtils.isNotBlank(questionInfo.getAnswer())) {
                isAnswer = true;
            }
            questionInfo.setQuestionResult(questionResultMap.getOrDefault(questionInfo.getQuestionId(), PersonalExamQuestionResultEnum.correct));
        }
        examDto.setQuestionCount(questionInfoList.size());
        Map<String, List<QuestionInfoByIdDto>> questionTypeMap = questionInfoList.stream().collect(Collectors.groupingBy(QuestionInfoByIdDto::getQuestionType));
        List<ExamGroupQuestionTypeInfoDto.QuestionTypeDto> questionTypeDtoList = new ArrayList<>();
        JSONObject examSection = new JSONObject();
        if(exams.getExamSectionDescriptions() != null){
            List<JSONObject> array = JSONArray.parseArray(exams.getExamSectionDescriptions(), JSONObject.class);
            array.stream().collect(Collectors.toList()).forEach(item -> {
                examSection.put(item.getString("question_type"), item.getString("description"));
            });
        }
        for (String key : questionTypeMap.keySet()) {
            List<QuestionInfoByIdDto> questionInfoByIdDtos = questionTypeMap.get(key);
            questionInfoByIdDtos.sort(Comparator.comparingInt(QuestionInfoByIdDto::getSortNo));
            questionTypeDtoList.add(ExamGroupQuestionTypeInfoDto.QuestionTypeDto.builder()
                    .questionType(QuestionType.ofValue(key))
                    .questionTypeName(key)
                    .examSectionDescription(examSection.getString(key))
                    .questionList(questionInfoByIdDtos)
                    .build());
        }

        examDto.setIsAnswer(isAnswer);
        // 按照QuestionType的sortNo升序排序
        questionTypeDtoList.sort(Comparator.comparingInt(dto -> dto.getQuestionType().getSortNo()));
        examDto.setQuestionList(questionTypeDtoList);
        return examDto;
    }

    @Override
    public List<String> getExamImages(String examId, String examSource) {
        List<String> ossUrls = new ArrayList<>();
        if (ExamSourceType.USER_UPLOAD.equals(ExamSourceType.valueOf(examSource))) {
            ossUrls = mathExamsMapper.selectExamPhotos(UUID.fromString(examId), ExamFileType.HANDWRITING_REMOVED_PAPER);
        } else if (ExamSourceType.HOLIDAY_TRAINING.equals(ExamSourceType.valueOf(examSource))
                || ExamSourceType.SPECIAL_TRAINING.equals(ExamSourceType.valueOf(examSource))
                || ExamSourceType.EXAM_BLIND_SPOTS_TRAINING.equals(ExamSourceType.valueOf(examSource))) {
            ossUrls = mathExamsMapper.selectExamPhotos(UUID.fromString(examId), ExamFileType.ORIGINAL_PAPER);
        }
        if (CollectionUtil.isNotEmpty(ossUrls)) {
            List<String> urls = ossUrls.stream().map(url -> {
                PresignedUrlParam param = new PresignedUrlParam(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB, url);
                PresignedUrlDto presignedUrlVo = eduKnowledgeHubService.presignedUrl(param);
                if (presignedUrlVo != null && presignedUrlVo.getData() != null) {
                    return presignedUrlVo.getData().getPresignedUrl();
                } else {
                    return null;
                }
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(urls)) {
                return urls;
            }
        }
        return null;
    }

    @Override
    public List<PersonalExamQuestion> listQuestionByPersonalExamId(Long personalExamId) {
        return personalExamQuestionService.listByPersonalExamId(personalExamId);
    }

    @Override
    public PersonalExam getLatestNoAnalyzedPersonalExam(UUID examId, Long studentId) {
        return baseMapper.selectLatestNoAnalyzedPersonalExam(examId, studentId);
    }

    @Override
    public MathExamQuestionDetailVo getExamQuestionDetail(UUID examId, UUID questionId, Long personalExamId) {
        PublisherEnum publisher = null;
        if (null != personalExamId) {
            PersonalExam personalExam = baseMapper.selectById(personalExamId);
            publisher = personalExam.getPublisher();
        }

        //查询题目详情
        MathExamsEntity mathExams = mathExamsMapper.selectById(examId);
        QuestionInfoByIdDto examQuestionInfoById = baseMapper.getQuestionInfoById(questionId);
        if (ExamSourceType.USER_UPLOAD == mathExams.getSource()) {
            //用户上传的试卷展示为图片格式
            examQuestionInfoById.setOriginalFiles(listOriginalFilesById(questionId));
        }

        MathExamQuestionDetailVo examQuestionDetailVo = MathExamQuestionDetailVo.ofQuestionInfoByIdDto(examQuestionInfoById);

        if (ExamSourceType.USER_UPLOAD == mathExams.getSource()) {
            examQuestionDetailVo.setShowAsImages(true);
        }

        //查询题目知识点
        List<MathKnowledgePointVO> knowledgePoints = questionKnowledgePointService.listByExamIdAndQuestionIdAndPublisher(examId, questionId, publisher);
        examQuestionDetailVo.setKnowledgePoints(knowledgePoints);

        return examQuestionDetailVo;
    }

    public List<FileVO> listOriginalFilesById(UUID questionId) {
        List<FileVO> files = mathQuestionsMapper.listFilesById(questionId);
        List<FileVO> originalFiles = files.stream().filter(file -> file.getType() == 1).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(originalFiles)) {
            OssEnum ossEnum = OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB;
            originalFiles.stream().forEach(file -> {
                PresignedUrlParam param = PresignedUrlParam.builder()
                        .ossKey(file.getOssKey())
                        .ossEnum(ossEnum)
                        .build();
                try {
                    PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param);
                    if (null != presignedUrlDto) {
                        file.setOssUrl(presignedUrlDto.getData().getPresignedUrl());
                    }
                } catch (Exception e) {
                    log.error("试卷题目详情获取图片临时链接异常 questionId {} ossEnum {} ossKey {}", questionId, ossEnum, file.getOssKey(), e);
                }
            });
        }
        return originalFiles;
    }
    @Override
    public Page<PersonalExamQuestionVo> getErrorQuestionPages(UUID knowledgePointId, Long studentId, Integer current, Integer size) {
        Page<PersonalExamQuestionVo> page = new Page(current == null ? 1 : current, size == null ? 10 : size);
        MathQueryPageVo pageVo = MathQueryPageVo.builder()
                .knowledgePointId(knowledgePointId)
                .studentId(studentId)
                .result(PersonalExamQuestionResultEnum.mistake.name())
                .build();
        List<PersonalExamQuestionVo> list = personalExamQuestionMapper.listByVo(page, pageVo);
        if (CollUtil.isNotEmpty(list)){
            list.stream().forEach(personalExamQuestion -> {
                QuestionAnalyzeResultDto questionInfoById = baseMapper.questionAnalyzeResult(personalExamQuestion.getQuestionId());
                if(questionInfoById != null){
                    personalExamQuestion.setQuestionContent(eduKnowledgeHubService.decodeContentV2(questionInfoById.getQuestionContent()));
                    personalExamQuestion.setAnswer(eduKnowledgeHubService.decodeContentV2(questionInfoById.getAnswer()));
                    personalExamQuestion.setAnalyzeContent(eduKnowledgeHubService.decodeContentV2(questionInfoById.getAnalyzeContent()));
                    personalExamQuestion.setPoints(questionKnowledgePointService.getMathKnowledgePointVOByIds(Arrays.stream(questionInfoById.getKnowledgePointIds().split(",")).map(UUID::fromString).collect(Collectors.toList())
                                    ,personalExamQuestion.getPublisher()));
                }
                //查询题目详情
                MathExamsEntity mathExams = mathExamsMapper.selectById(personalExamQuestion.getExamId());
                if (ExamSourceType.USER_UPLOAD == mathExams.getSource()) {
                    //用户上传的试卷展示为图片格式
                    personalExamQuestion.setOriginalFiles(listOriginalFilesById(personalExamQuestion.getQuestionId()));
                    personalExamQuestion.setShowAsImages(true);
                }
            });
        }
        page.setRecords(list);
        return page;
    }
}
