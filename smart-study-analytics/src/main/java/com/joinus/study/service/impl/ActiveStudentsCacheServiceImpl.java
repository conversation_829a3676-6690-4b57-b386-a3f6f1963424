package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.ActiveStudentsCacheMapper;
import com.joinus.study.model.entity.ActiveStudentsCacheEntity;
import com.joinus.study.service.ActiveStudentsCacheService;
import org.springframework.stereotype.Service;

@Service("activeStudentsCacheService")
public class ActiveStudentsCacheServiceImpl extends ServiceImpl<ActiveStudentsCacheMapper, ActiveStudentsCacheEntity> implements ActiveStudentsCacheService {

    @Override
    public ActiveStudentsCacheEntity getByStudentId(Long studentId) {
        LambdaQueryWrapper<ActiveStudentsCacheEntity> wrapper = Wrappers.lambdaQuery(ActiveStudentsCacheEntity.class)
                .eq(ActiveStudentsCacheEntity::getStudentId, studentId);
        return this.getOne(wrapper);
    }
}
