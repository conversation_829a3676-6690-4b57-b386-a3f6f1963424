package com.joinus.study.service.impl;

import com.joinus.study.service.WechatMaService;
import com.joinus.study.utils.ThirdPartyApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WechatMaServiceImpl implements WechatMaService {

    @Value("${server.domain.wechat-platform:}")
    private String wechatPlatformDomain;

    @Autowired
    private ThirdPartyApiUtil thirdPartyApiUtil;

    @Override
    public String getOpenId(String code) {
        try {
            // 构建请求URL
            String url = wechatPlatformDomain + "/api/wechat/getOpenId";

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("code", code);

            // 调用第三方接口
            String response = thirdPartyApiUtil.postJson(url, params);

            return response;
        } catch (Exception e) {
            log.error("调用微信中心平台获取openid失败, code: {}", code, e);
            throw new RuntimeException("获取openid失败: " + e.getMessage());
        }
    }
}
