package com.joinus.study.service.impl;

import com.joinus.study.service.WechatMaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WechatMaServiceImpl implements WechatMaService {

    @Value("${server.domain.wechat-platform:}")
    private String wechatPlatformDomain;

    @Override
    public String getOpenId(String code) {
        
        return "";
    }
}
