package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.StudyRecordQuestion;
import com.joinus.study.service.StudyRecordQuestionService;
import com.joinus.study.mapper.StudyRecordQuestionMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【study_record_question】的数据库操作Service实现
* @createDate 2025-03-11 09:48:14
*/
@Service
public class StudyRecordQuestionServiceImpl extends ServiceImpl<StudyRecordQuestionMapper, StudyRecordQuestion>
    implements StudyRecordQuestionService{

    @Override
    public List<StudyRecordQuestion> getByStudyIdAndQuestionId(Long sourceId, UUID questionId) {
        LambdaQueryWrapper<StudyRecordQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecordQuestion::getStudyRecordId, sourceId);
        queryWrapper.eq(StudyRecordQuestion::getQuestionId, questionId);
        queryWrapper.orderByDesc(StudyRecordQuestion::getSortNo);
        return this.list(queryWrapper);
    }
}




