package com.joinus.study.service.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.KnowledgePointChapterDto;
import com.joinus.study.model.dto.KnowledgePointQuestionTypesDtoV2New;
import com.joinus.study.model.param.KnowledgePointQuestionTypesParam;
import com.joinus.study.model.param.ReadingAIAbilityParam;
import com.joinus.study.service.EduKnowledgeHubService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;


/**
 * @Description ai 接口转发实现类
 * <AUTHOR>
 * @Date 2025/4/9 14:28
 **/
@Service
@Slf4j
public class AiRequestService {
    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
    private String hubHostUrl;
    @Value("${exam_has_grade_semester:false}")
    private boolean examHasGradeSemester;

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;

    /**
     * AI批改请求
     *
     * @param param
     */
    public ReadingAIAbilityParam correctingAIRequest(ReadingAIAbilityParam param) {
        try {
            String result = HttpRequest.post(hubHostUrl + "/api/edu-knowledge-hub/ai/reading/ability/ai-correct") // 使用put方法创建HttpRequest对象
                    .body(JSONObject.toJSONString(param)) //请求参数
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("correctingAIRequest 参数："+param);
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") == 200) {
                    log.info("correctingAIRequest 调用AI能力接口成功");
                    return jsonObject.getObject("data", ReadingAIAbilityParam.class);
                } else {
                    log.error("AI接口返回错误: code={}, msg={}",
                            jsonObject.getInteger("code"),
                            jsonObject.getString("msg"));
                }
            }
            return null;
        } catch (Exception e){
            log.error("correctingAIRequest 调用AI能力接口失败 {}",e.getMessage());
            return null;
        }
    }

    public String postRequest(String url, JSONObject param) throws BaseException {
        try {
            // 打印请求参数，调试用（完成后可删除）
            String requestBody = param.toJSONString();
            log.info("Request URL: {}, Body: {}", url, requestBody);
            String result = HttpRequest.post(hubHostUrl + "/api/edu-knowledge-hub" + url)
                    .header("Content-Type", "application/json")  // 明确指定 JSON 类型
                    .body(requestBody)
                    .timeout(60000)
                    .execute()
                    .body();
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") == 200) {
                    Object data = jsonObject.get("data");
                    return data.toString();
                } else {
                    // 记录非200状态的具体错误
                    log.error("接口返回错误: code={}, msg={}",
                            jsonObject.getInteger("code"),
                            jsonObject.getString("msg"));
                    throw new BaseException("接口返回错误: " + jsonObject.getString("msg"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("correctingAIRequest 调用AI能力接口失败", e);  // 打印完整堆栈信息
            throw new BaseException("调用AI能力接口失败: " + e.getMessage());
        }
        return null;
    }


    public String putRequest(String url, JSONObject param) throws BaseException {
        try {
            String result = HttpRequest.put(hubHostUrl + "/api/edu-knowledge-hub" + url) // 使用put方法创建HttpRequest对象
                    .form(param)
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") == 200) {
                    String data = jsonObject.getString("data");
                    log.info("调用AI能力接口 {} 成功 返回 ", url, data);
                    return data;
                }
            }
        } catch (Exception e) {
            log.error("调用AI能力接口 {} 失败 {}", url, e.getMessage());
            e.printStackTrace();
            throw new BaseException("调用AI能力接口失败" + e.getMessage());
        }
        return null;
    }

    public String putRequest(String url, JSONObject param, MultipartFile file) {
        try {
            // 构建多部分表单
            HttpRequest request = HttpRequest.put(hubHostUrl + "/api/edu-knowledge-hub" + url)
                    .timeout(30000); // 设置超时

            // 添加普通参数
            if (param != null) {
                Map<String, Object> paramMap = param;
                paramMap.forEach(request::form);
            }
            // 添加文件参数
            if (file != null && !file.isEmpty()) {
                request.form("file", file.getBytes(), file.getOriginalFilename());
            }
            // 执行请求并处理响应
            String result = request.timeout(60000).execute().body();
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                log.info("***************** " + result);
                if (jsonObject.getInteger("code") == 200) {
                    String data = jsonObject.getString("data");
                    log.info("调用AI能力接口 {} 成功 返回 {}", url, data);
                    return data;
                }
            }
        } catch (Exception e) {
            log.error("AI接口调用异常: {}", e.getMessage());
            throw new RuntimeException("AI服务调用失败", e);
        }
        return null;
    }

    public String getRequest(String url, JSONObject param) throws BaseException {
        try {
            String requestUrl = hubHostUrl + "/api/edu-knowledge-hub" + url;
            if (param != null) {
                requestUrl = requestUrl + "?";
                for (String key : param.keySet()) {
                    requestUrl = requestUrl + "&" + key + "=" + param.getString(key);
                }
            }
            String result = HttpRequest.get(requestUrl)// 使用put方法创建HttpRequest对象
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("调用AI能力接口 {}，result:{} ", url, result);
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") == 200) {
                    String data = jsonObject.getString("data");
                    return data;
                }
            }
        } catch (Exception e) {
            log.error("调用AI能力接口 {} 失败 {}", url, e.getMessage());
            throw new BaseException("调用AI能力接口失败" + e.getMessage());
        }
        return null;
    }

    //获取知识点 对应题型 knowledgePointIds 逗号间隔
    public KnowledgePointQuestionTypesDtoV2New.DataDTO mathSpecialTrainingQuestionTypes(List<KnowledgePointQuestionTypesParam> params) {
        JSONArray jsonArray = new JSONArray();
        params.forEach(param -> jsonArray.add(param.getId().toString()));
        String jsonBody = jsonArray.toString();
        KnowledgePointQuestionTypesDtoV2New knowledgePointQuestionTypesDtoV2 = eduKnowledgeHubService.questionTypeV2(jsonBody);
        if (knowledgePointQuestionTypesDtoV2 == null || knowledgePointQuestionTypesDtoV2.getData() == null) {
            return null;
        }
        return knowledgePointQuestionTypesDtoV2.getData();
    }

    //获取知识点 对应章节信息
    public List<KnowledgePointChapterDto> mathKnowledgePointsChapter(String publisher, Integer grade, Integer semester, String knowledgePointIds) {
        String url = "/ai/ability/math/exam-analysis/knowledge-points";
        JSONObject param = new JSONObject();
        param.put("publisher", publisher);
        if (examHasGradeSemester) {
            param.put("grade", grade);
            param.put("semester", semester);
        }
        param.put("knowledgePointIds", knowledgePointIds);
        log.info("调用AI能力接口 {}, 参数:{}", url,param);
        String request = this.getRequest(url, param);
        if (request != null) {
            List<KnowledgePointChapterDto> typesDtos = JSONArray.parseArray(request, KnowledgePointChapterDto.class);
            typesDtos.sort(Comparator.comparingInt(KnowledgePointChapterDto::getChapterSortNo).reversed()
                    .thenComparingInt(KnowledgePointChapterDto::getSectionSortNo));
            return typesDtos;
        }
        return null;
    }


}
