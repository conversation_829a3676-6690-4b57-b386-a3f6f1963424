package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishPersonalExerciseQuestionMapper;
import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import com.joinus.study.model.entity.EnglishPersonalExerciseQuestion;
import com.joinus.study.model.vo.EnglishPersonalExerciseQuestionVO;
import com.joinus.study.service.EnglishPersonalExerciseQuestionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Service("englishPersonalExerciseQuestionService")
@Slf4j
public class EnglishPersonalExerciseQuestionServiceImpl extends ServiceImpl<EnglishPersonalExerciseQuestionMapper, EnglishPersonalExerciseQuestion> implements EnglishPersonalExerciseQuestionService {

    @Override
    public List<EnglishPersonalExerciseQuestionDTO> listByPersonalExerciseId(Long personalExerciseId) {
        return this.baseMapper.listByPersonalExerciseId(personalExerciseId);
    }
}
