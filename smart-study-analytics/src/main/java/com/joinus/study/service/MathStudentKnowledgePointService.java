package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.MathStudentKnowledgePoint;

import java.util.List;
import java.util.UUID;

public interface MathStudentKnowledgePointService extends IService<MathStudentKnowledgePoint> {

    /**
     * 添加或更新知识点的PPT HTMLs完成状态
     *
     * @param student
     * @param knowledgePointId
     */
    void addOrUpdatePptHtmlsCompleted(Long student, UUID knowledgePointId);

    /**
     * 获取某个学生的知识点学习记录
     *
     * @param studentId
     * @return
     */
    List<MathStudentKnowledgePoint> listByStudentId(Long studentId, List<UUID> knowledgePointIds);
}
