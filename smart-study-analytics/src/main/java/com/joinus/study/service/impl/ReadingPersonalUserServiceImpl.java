package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.BaseServiceImpl;
import com.joinus.study.mapper.ReadingPersonalPassagesMapper;
import com.joinus.study.mapper.ReadingPersonalUserMapper;
import com.joinus.study.model.dto.ResponseIsOpenDto;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.ReadingPersonalUserPageParam;
import com.joinus.study.model.vo.ReadingPersonalUserVO;
import com.joinus.study.model.vo.ReadingUserDataOverviewVO;
import com.joinus.study.service.ReadingActivityStudentService;
import com.joinus.study.service.ReadingPersonalUserService;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("readingPersonalUserService")
@Slf4j
public class ReadingPersonalUserServiceImpl extends BaseServiceImpl<ReadingPersonalUserMapper, ReadingPersonalUser> implements ReadingPersonalUserService {

    @Value("${qyl.host.url}")
    private String qylHostUrl;
    @Value("${check.students.isOpenInfo}")
    private String isOpenInfo;
    @Value("${count.members.opened}")
    private String countMembersOpened;

    @Resource
    private ReadingActivityStudentService readingActivityStudentService;

    @Resource
    private ReadingPersonalPassagesMapper personalPassagesMapper;
    @Override
    public ReadingPersonalUser getByStudentId(Long studentId) {
        LambdaQueryWrapper<ReadingPersonalUser> wrapper = Wrappers.lambdaQuery(ReadingPersonalUser.class)
                .eq(studentId != null, ReadingPersonalUser::getStudentId, studentId)
                .isNull(ReadingPersonalUser::getDeletedAt);
        return this.getOne(wrapper);
    }

    @Override
    public Page<ReadingPersonalUserVO> pages(ReadingPersonalUserPageParam pageParam) {
        Page<ReadingPersonalUserVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalUserVO> list = this.baseMapper.pages(page, pageParam);
        if (CollectionUtil.isNotEmpty( list)){
        //用流获取学生id把学生id拼接成字符串
        List<Long> studentIds = list.stream().map(ReadingPersonalUserVO::getStudentId).collect(Collectors.toList());
        String studentIdsStr = studentIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        //远程调用青于蓝是否是会员接口返回列表
        Map<String, Object> mapParams =new HashMap<>();
        mapParams.put("studentIds", studentIdsStr);
        mapParams.put("subjectType", 5);
        log.info("调用青于蓝获取会员开通信息接口参数：{}", mapParams);
        String result = LocalHttpUtil.get(qylHostUrl + isOpenInfo, mapParams);
        log.info("调用青于蓝获取会员开通信息接口返回结果：{}", result);
        Map<Long, Integer> studentInfoMap=new HashMap<>();
        if (result != null&& result.contains("\"code\":\"200\"")){
            //把获取的date  转换为StudentSubjectMemberOpenInfoVo 列表
            ResponseIsOpenDto isOpenDto = JSONUtil.toBean(result, ResponseIsOpenDto.class);
            List<ResponseIsOpenDto.StudentRecord> isOpenDtoRows = isOpenDto.getRows();
            //把这个isOpenDtoRows 列表转换为以学生id为key的map
            studentInfoMap = isOpenDtoRows.stream()
                    .collect(Collectors.toMap(ResponseIsOpenDto.StudentRecord::getStudentId, po -> po.getIsOpen()));
        }
        Map<Long, Integer> finalStudentInfoMap = studentInfoMap;
        list.forEach(personalUserVO -> {
            //获取类别练习次数
            ReadingPersonalUserVO po = personalPassagesMapper.getCountByStudentId(personalUserVO.getStudentId());
            personalUserVO.setDirectionalBlastCount(po.getDirectionalBlastCount());
            personalUserVO.setReadingTrainingCampCount(po.getReadingTrainingCampCount());
            personalUserVO.setSummerTrainingCampCount(po.getSummerTrainingCampCount());
          //获取暑期训练营练习天数
            personalUserVO.setSummerTrainingCampDays(personalPassagesMapper.getSummerTrainingCampDays(personalUserVO.getStudentId()));
            personalUserVO.setSummerTrainingCampCountDaysStr(personalUserVO.getSummerTrainingCampCount()+"/"+ personalUserVO.getSummerTrainingCampDays());
            if (finalStudentInfoMap.containsKey(personalUserVO.getStudentId())){
               personalUserVO.setIsOpen(finalStudentInfoMap.get(personalUserVO.getStudentId()));
            }
            if (personalUserVO.getIsOpen() == 1){
                personalUserVO.setIsOpenStr("学科会员");
            }else {
                personalUserVO.setIsOpenStr("普通会员");
            }
            if (personalUserVO.getSource() == 1){
                personalUserVO.setSourceStr("B端用户");
            }else {
                personalUserVO.setSourceStr("C端用户");
            }
        });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public ReadingUserDataOverviewVO dataOverview() {
        //查询用户总人数
        ReadingUserDataOverviewVO vo = new ReadingUserDataOverviewVO();
        //查询会员人数开通
        Map<String, Object> mapParams =new HashMap<>();
        mapParams.put("subjectType", 5);
        log.info("调用青于蓝获取会员开通信息接口参数：{}", mapParams);
        String result = LocalHttpUtil.get(qylHostUrl + countMembersOpened, mapParams);
        log.info("调用青于蓝获取会员开通信息接口返回结果：{}", result);
        if (result != null&& result.contains("\"code\":\"200\"")) {
            JSONObject jsonObject = JSONUtil.parseObj(result);
            Object count = jsonObject.get("data");
            vo.setJoinMemberCount(Integer.parseInt(count.toString()));
        }
        //查询用户总数
        Long aLong = this.baseMapper.selectCount(null);
        //转换为int
        vo.setTotalCount(aLong.intValue());
        //查询加入训练营人数
        vo.setJoinTrainingCampCount(readingActivityStudentService.getJoinTrainingCampCount());
        //查询连续练习三天人数
        vo.setContinuousContactThreeDaysCount(this.baseMapper.getContinuousContactCountByDays(3));
        //查询连续练习七天人数
        vo.setContinuousContactSevenDaysCount(this.baseMapper.getContinuousContactCountByDays(7));
        //查询连续练习七天以上人数
        vo.setContinuousContactSevenDaysAboveCount(this.baseMapper.getContinuousContactCountByDays(8));
        return vo;
    }
}
