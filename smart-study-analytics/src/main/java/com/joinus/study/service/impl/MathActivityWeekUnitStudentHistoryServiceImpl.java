package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.MathActivityWeekUnitStudentHistory;
import com.joinus.study.service.MathActivityWeekUnitStudentHistoryService;
import com.joinus.study.mapper.MathActivityWeekUnitStudentHistoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_activity_week_unit_student_history(学生参与活动历史记录表)】的数据库操作Service实现
* @createDate 2025-06-19 09:36:40
*/
@Service
public class MathActivityWeekUnitStudentHistoryServiceImpl extends ServiceImpl<MathActivityWeekUnitStudentHistoryMapper, MathActivityWeekUnitStudentHistory>
    implements MathActivityWeekUnitStudentHistoryService{

}




