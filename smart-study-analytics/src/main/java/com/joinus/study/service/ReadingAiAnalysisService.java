package com.joinus.study.service;

import com.joinus.study.model.param.*;

/**
 * <AUTHOR>
 * @date 2025/4/2
 * description：阅读提分训练营AI交互service
 */
public interface ReadingAiAnalysisService {

    /**
     * 薄弱知识点分析AI请求
     * @param param
     */
    void weakKnowledgePointsAnalysisAIRequest(ReadingAIAbilityParam param);

    /**
     * 综合训练建议分析AI请求
     * @param param
     */
    void suggestionAnalysesAIRequest(ReadingAIAbilityParam param);

    /**
     * 题型分析AI请求
     * @param param
     */
    void weakQuestionTypeAnalysesAIRequest(ReadingAIAbilityParam param);

    /**
     * 获取薄弱知识点分析结果
     * @param param
     */
    void weakKnowledgePointsAnalysisResults(ReadingAIWeakKnowledgePointsParam param);

    /**
     * 获取综合训练建议结果
     * @param param
     */
    void suggestionAnalysesResults(ReadingAISuggestionParam param);

    /**
     * 获取题型分析结果
     * @param param
     */
    void weakQuestionTypeAnalysesResults(ReadingAIWeakQuestionTypeParam param);

    void analysesAIRequest(ReadingAIAbilityParam param);

    void analysesResults(ReadingAICallBackParam param);
}
