package com.joinus.study.service.impl;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.*;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.ReportTypeEnum;
import com.joinus.study.model.param.ReadingAIAbilityParam;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.param.ReadingPersonalPassageDataParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.ReadingAiAnalysisService;
import com.joinus.study.service.ReadingPersonalAnalysisPeriodicReportService;
import com.joinus.study.service.ReadingPersonalAnalysisReportService;
import com.joinus.study.service.ReadingPersonalPassagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReadingPersonalAnalysisPeriodicReportServiceImpl extends ServiceImpl<ReadingPersonalAnalysisPeriodicReportMapper, ReadingPersonalAnalysisPeriodicReport>
        implements ReadingPersonalAnalysisPeriodicReportService {

    @Resource
    private ReadingPersonalPassagesService personalPassagesService;

    @Resource
    @Lazy
    private ReadingPersonalAnalysisReportService reportService;
    @Resource
    private ReadingPersonalWeakKnowledgePointsAnalysisMapper weakKnowledgePointsAnalysisMapper;

    @Resource
    private ReadingPersonalSuggestionAnalysisMapper suggestionAnalysisMapper;

    @Resource
    private ReadingPersonalWeakQuestionTypeAnalysisMapper weakQuestionTypeAnalysisMapper;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private ReadingPersonalPassagesQuestionMapper passagesQuestionMapper;

    @Resource
    private ReadingAiAnalysisService aiAnalysisService;
    /**
     * 周报月报生成 -定时任务执行
     * @author: zhaojianming
     * @date: 2025-04-01
     */
    @Override
    public void autoGeneratePeriodicReports() {
        generateReportWithTransaction(ReportTypeEnum.WEEKLY);
//        generateReportWithTransaction(ReportTypeEnum.MONTHLY); //去掉月报
    }

    private void generateReportWithTransaction(ReportTypeEnum reportType) {
        try {
            // 每个报告生成使用独立事务
            TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
            transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

            Boolean result = transactionTemplate.execute(status -> {
                try {
                    log.info("开始生成{}报告", reportType.getName());
                    generatePeriodicReport(reportType.getCode());
                    log.info("{}报告生成完成", reportType.getName());
                    return true;
                } catch (Exception e) {
                    log.info("{}报告生成异常: {}", reportType.getName(), e.getMessage());
                    status.setRollbackOnly();
                    return false;
                }
            });

            if (!Boolean.TRUE.equals(result)) {
                log.info("{}报告生成失败", reportType.getName());
            }
        } catch (Exception e) {
            log.info("{}报告事务执行异常: {}", reportType.getName(), e.getMessage());
        }
    }

    /**
     * 周报月报生成
     * @param reportType 报告类型：1周报，2月报
     * @return
     * @author: zhaojianming
     * @date: 2025-04-01
     */
    @Override
    public void generatePeriodicReport(Integer reportType) {
        // 获取昨天日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDate startDate = null;
        if(reportType == 1){
            //周报开始日期
            startDate = yesterday.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        }else{
            startDate = yesterday.with(TemporalAdjusters.firstDayOfMonth());
        }
        //确定yesterday日期存在数据的学生 也就是需要更新周报或日报的学生
        List<Long> studentIds = personalPassagesService.listStudentsWithTrainingRecords(yesterday);
        log.info("generatePeriodicReport 需要更新周报或日报的学生id：{}",studentIds);
        for (Long studentId : studentIds){
            //查询学生周报或月报确定是否有历史数据
            ReadingPersonalAnalysisPeriodicReport periodicReport = baseMapper.selectOne(Wrappers.<ReadingPersonalAnalysisPeriodicReport>lambdaQuery()
                    .eq(ReadingPersonalAnalysisPeriodicReport::getStudentId, studentId)
                    .eq(ReadingPersonalAnalysisPeriodicReport::getStartDate, startDate)
                    .eq(ReadingPersonalAnalysisPeriodicReport::getReportType, reportType)
                    .isNull(ReadingPersonalAnalysisPeriodicReport::getDeletedAt)
            );
            if(periodicReport == null){
                periodicReport = new ReadingPersonalAnalysisPeriodicReport();
                periodicReport.setStartDate(startDate);
                periodicReport.setStudentId(studentId);
            }
            periodicReport.setEndDate(yesterday);
            //获取训练记录表统计数据
            ReadingPersonalPassageDataParam trainingRecordParam = ReadingPersonalPassageDataParam.builder()
                    .startDate(startDate).endDate(yesterday).studentId(studentId).build();
            Map<String, Object> trainingRecordData = reportService.queryPersonalPassageData(trainingRecordParam);
            log.info("generatePeriodicReport 查询训练记录统计数据：{}", trainingRecordData);
            CommonResponse.ERROR.assertNotNull(trainingRecordData, "找不到对应训练统计数据");

            periodicReport.setTotalKnowledgePoints(Integer.valueOf(trainingRecordData.get("totalKnowledgePoints").toString()));
            periodicReport.setWeakPointCount(Integer.valueOf(trainingRecordData.get("weakPointCount").toString()));
            periodicReport.setCorrectCount(Integer.valueOf(trainingRecordData.get("correctCount").toString()));
            periodicReport.setIncorrectCount(Integer.valueOf(trainingRecordData.get("incorrectCount").toString()));
            periodicReport.setTotalQuestions(Integer.valueOf(trainingRecordData.get("totalQuestions").toString()));
            periodicReport.setAccuracyRate(personalPassagesService.getAccuracyRateByParam(trainingRecordParam));
            periodicReport.setPassageCount(Integer.valueOf(trainingRecordData.get("passageCount").toString()));
            periodicReport.setReportType(reportType);
            //如果薄弱知识点为0 不触发ai请求
            if(periodicReport.getWeakPointCount() == 0){
                periodicReport.setStatus(1);
                saveOrUpdate(periodicReport);
                log.info("generatePeriodicReport 周报或月报没有薄弱知识点，不触发ai请求");
                continue;
            }else{
                periodicReport.setStatus(0);
                saveOrUpdate(periodicReport);
            }

            //封装ai分析需要的参数
            ReadingAIAbilityParam param = new ReadingAIAbilityParam();
            param.setId(periodicReport.getId().toString());
            param.setReadingAIAbilityType(7);
            //拼接ai分析字符串
            ReadingPersonalPassageDataParam dataParam = new ReadingPersonalPassageDataParam();
            dataParam.setStartDate(startDate);
            dataParam.setEndDate(yesterday);
            dataParam.setStudentId(studentId);
            param.setDirectMessage(formatDirectMessage(dataParam));
            //ai请求
            aiAnalysisService.analysesAIRequest(param);
        }
    }

    public String formatDirectMessage(ReadingPersonalPassageDataParam dataParam){
        String currStr = null;
        String lastStr = null;
        List<ReadingHighFreqPerformanceVo> currList = reportService.queryPersonalPassageHighFreqPerformanceData(dataParam);
        if(currList != null){
            currStr = currList.stream()
                    .map(vo -> String.format("知识点名称：[%s] | 题型：[%s] 正确率：[%.2f%%]",
                            vo.getName(), vo.getQuestionType(), vo.getAccuracyRate().multiply(BigDecimal.valueOf(100)).doubleValue()))
                    .collect(Collectors.joining("\n", "【本周数据】\n", ""));
        }

        LocalDate currStartDate = dataParam.getStartDate();
        dataParam.setStartDate(currStartDate.minusWeeks(1));
        dataParam.setEndDate(currStartDate.minusDays(1));
        List<ReadingHighFreqPerformanceVo> lastList = reportService.queryPersonalPassageHighFreqPerformanceData(dataParam);
        if(lastList != null){
            lastStr = currList.stream()
                    .map(vo -> String.format("知识点名称：[%s] | 题型：[%s] 正确率：[%.2f%%]",
                            vo.getName(), vo.getQuestionType(), vo.getAccuracyRate().multiply(BigDecimal.valueOf(100)).doubleValue()))
                    .collect(Collectors.joining("\n", "【上周数据】\n", ""));
        }

        log.info("generatePeriodicReport 拼接ai分析字符串：{}", lastStr + "\n" + currStr);
        return lastStr + "\n" + currStr;
    }

    @Override
    public ReadingPeriodicReportViewDetailVo periodicReportDetail(ReadingPeriodicReportDetailParam param) {
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getReportType(), "报告类型不能为空");

        ReadingPeriodicReportViewDetailVo detailVo = new ReadingPeriodicReportViewDetailVo();
        LocalDate today = LocalDate.now();
        // 本周/月
        LocalDate periodStartDate = null;
        LocalDate periodLastDate = null;
        LocalDate periodLastTwoDate = null;
        if(param.getReportType() == 1){
            periodStartDate = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            // 上周
            periodLastDate = periodStartDate.minusWeeks(1);
            //上上周
            periodLastTwoDate = periodStartDate.minusWeeks(2);
        }else{
            periodStartDate = today.with(TemporalAdjusters.firstDayOfMonth());
            // 上月
            periodLastDate = today.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
            //上上月
            periodLastTwoDate = today.minusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
        }

        ReadingPeriodicReportDetailVo thisPeriodicData = queryPeriodicReportDetail(param.getStudentId(), periodStartDate, param.getReportType());
        ReadingPeriodicReportDetailVo lastPeriodicData = queryPeriodicReportDetail(param.getStudentId(), periodLastDate, param.getReportType());
        ReadingPeriodicReportDetailVo lastTwoPeriodicData = queryPeriodicReportDetail(param.getStudentId(), periodLastTwoDate, param.getReportType());
        log.info("generatePeriodicReport 周报或月报数据：{},上周或上月数据：{},上上周或上上月数据：{}", thisPeriodicData, lastPeriodicData, lastTwoPeriodicData);

        //对比高频知识点
        List<ReadingHighFreqPerformanceVo> thisHighFreqPerformanceList = thisPeriodicData != null ? thisPeriodicData.getHighFreqPerformanceList() : null;
        List<ReadingHighFreqPerformanceVo> lastHighFreqPerformanceList = lastPeriodicData != null ? lastPeriodicData.getHighFreqPerformanceList() : null;
        List<ReadingHighFreqPerformanceVo> lastTwoHighFreqPerformanceList = lastTwoPeriodicData != null ? lastTwoPeriodicData.getHighFreqPerformanceList() : null;
        if(thisPeriodicData != null){
            thisPeriodicData.setHighFreqPerformanceList(comparisonResult(thisHighFreqPerformanceList, lastHighFreqPerformanceList));
        }
        if(lastPeriodicData != null){
            lastPeriodicData.setHighFreqPerformanceList(comparisonResult(lastHighFreqPerformanceList, lastTwoHighFreqPerformanceList));
        }

        detailVo.setThisPeriodicData(thisPeriodicData);
        detailVo.setLastPeriodicData(lastPeriodicData);
        log.info("generatePeriodicReport 比较后周报或月报数据：{},上周或上月数据：{}", thisPeriodicData, lastPeriodicData);
        return detailVo;
    }

    List<ReadingHighFreqPerformanceVo> comparisonResult(List<ReadingHighFreqPerformanceVo> executeList, List<ReadingHighFreqPerformanceVo> targetList){
        if(executeList == null){
            return executeList;
        }
        //第一步 使用 Map 进行映射，便于快速匹配相同知识点和题型的数据
        Map<Pair<String, String>, ReadingHighFreqPerformanceVo> targetMap = new HashMap<>();
        if(targetList != null){
            for (ReadingHighFreqPerformanceVo vo : targetList) {
                targetMap.put(Pair.of(vo.getName(), vo.getQuestionType()), vo);
            }
        }
        //第二步 遍历当前列表，将匹配到的数据进行比较
        for (ReadingHighFreqPerformanceVo currentVo : executeList) {
            Pair<String, String> key = Pair.of(currentVo.getName(), currentVo.getQuestionType());
            ReadingHighFreqPerformanceVo lastVo = targetMap.get(key);
            if (lastVo != null && currentVo.getAccuracyRate() != null && lastVo.getAccuracyRate() != null) {
                // 比较 accuracyRate  趋势 0:无变化  1:上升  2:下降
                BigDecimal diff = currentVo.getAccuracyRate().subtract(lastVo.getAccuracyRate()).abs();
                // 设置误差阈值，比如 0.01 表示允许 1% 的浮动视为无变化
                if (diff.compareTo(BigDecimal.ZERO) == 0) {
                    currentVo.setTrend(0); // 无变化
                } else if (currentVo.getAccuracyRate().compareTo(lastVo.getAccuracyRate()) > 0) {
                    currentVo.setTrend(1); // 上升
                } else {
                    currentVo.setTrend(2); // 下降
                }
            } else {
                // 如果没有对应的历史数据或精度率为 null，默认设为 1
                currentVo.setTrend(1);
            }
        }
        //第三步 截取前五条  返回的对象实例和入参的对象实例 不一致，返回的是个新的对象，无需考虑原数据被修改而导致的对比出现问题
        executeList = executeList.stream().limit(5).collect(Collectors.toList());
        return executeList;
    }

    @Override
    public Boolean checkPeriodicReportIsAnalysisCompleted(Long reportId) {
        //检查是否分析薄弱知识点
        if(weakKnowledgePointsAnalysisMapper.selectCount(Wrappers.<ReadingPersonalWeakKnowledgePointsAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getReportId, reportId)
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalWeakKnowledgePointsAnalysis::getDeletedAt)) == 0){
            return false;
        }
        //检查是否分析综合训练建议
        if(suggestionAnalysisMapper.selectCount(Wrappers.<ReadingPersonalSuggestionAnalysis>lambdaQuery()
                .eq(ReadingPersonalSuggestionAnalysis::getReportId, reportId)
                .eq(ReadingPersonalSuggestionAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalSuggestionAnalysis::getDeletedAt)) == 0){
            return false;
        }
        //检查是否分析薄弱题型
        if(weakQuestionTypeAnalysisMapper.selectCount(Wrappers.<ReadingPersonalWeakQuestionTypeAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getReportId, reportId)
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalWeakQuestionTypeAnalysis::getDeletedAt)) == 0){
            return false;
        }
        return true;
    }

    @Override
    public ReadingPeriodicReportDetailVo queryPeriodicReportDetail(Long studentId, LocalDate thisWeekStart, Integer reportType) {
        //确认报告
        ReadingPersonalAnalysisPeriodicReport report = baseMapper.selectOne(Wrappers.<ReadingPersonalAnalysisPeriodicReport>lambdaQuery()
                .eq(ReadingPersonalAnalysisPeriodicReport::getStudentId, studentId)
                .eq(ReadingPersonalAnalysisPeriodicReport::getStartDate, thisWeekStart)
                .eq(ReadingPersonalAnalysisPeriodicReport::getReportType, reportType)
                .isNull(ReadingPersonalAnalysisPeriodicReport::getDeletedAt)
        );
        if(report == null){
            return null;
        }
        ReadingPeriodicReportDetailVo periodicReportDetailVo = new ReadingPeriodicReportDetailVo();
        // 基础属性拷贝
        BeanUtils.copyProperties(report, periodicReportDetailVo);
        periodicReportDetailVo.setReportId(report.getId());

        //获取薄弱知识点分析数据
        List<ReadingPersonalWeakKnowledgePointsAnalysis> weakKnowledgePointsAnalysisDTOS = weakKnowledgePointsAnalysisMapper.selectList(Wrappers.<ReadingPersonalWeakKnowledgePointsAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getReportId, report.getId())
                .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalWeakKnowledgePointsAnalysis::getDeletedAt));
        if(weakKnowledgePointsAnalysisDTOS == null){
            return null;
        }
        periodicReportDetailVo.setWeakPointsAnalysis(weakKnowledgePointsAnalysisDTOS);

        //综合训练建议分析数据
        List<ReadingPersonalSuggestionAnalysis> suggestionAnalyses = suggestionAnalysisMapper.selectList(Wrappers.<ReadingPersonalSuggestionAnalysis>lambdaQuery()
                .eq(ReadingPersonalSuggestionAnalysis::getReportId, report.getId())
                .eq(ReadingPersonalSuggestionAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalSuggestionAnalysis::getDeletedAt));
        if(suggestionAnalyses == null){
            return null;
        }
        periodicReportDetailVo.setSuggestionAnalyses(suggestionAnalyses);

        //综合训练建议分析数据
        List<ReadingPersonalWeakQuestionTypeAnalysis> weakQuestionTypeAnalyses = weakQuestionTypeAnalysisMapper.selectList(Wrappers.<ReadingPersonalWeakQuestionTypeAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getReportId, report.getId())
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getAnalysisType, 2)
                .isNull(ReadingPersonalWeakQuestionTypeAnalysis::getDeletedAt));
        if(weakQuestionTypeAnalyses == null){
            return null;
        }
        periodicReportDetailVo.setWeakQuestionsTypeAnalysis(weakQuestionTypeAnalyses);

//        2025-07-11 老版的统计图都不要了
//        //雷达图待定  需求未明确
//
//        //柱状图 查询学生条件内答题情况统计
//        ReadingPersonalKnowledgePointDataParam param = new ReadingPersonalKnowledgePointDataParam();
//        param.setStartDate(report.getStartDate());
//        param.setEndDate(report.getEndDate());
//        param.setStudentId(studentId);
//        List<ReadingKnowledgePointVo> knowledgePointVos = reportService.queryPersonalPassageKnowledgePointData(param);
//        periodicReportDetailVo.setKnowledgePointList(knowledgePointVos);
//
//        //折线图 周期内每天的答题情况
//        ReadingPersonalPassageDataParam passageDataParam = ReadingPersonalPassageDataParam.builder()
//                .studentId(studentId)
//                .startDate(report.getStartDate())
//                .endDate(report.getEndDate()).build();
//        List<ReadingPeriodicReportDetailLineChartVo> lineChartVos = reportService.queryPersonalPassageDataForLineChart(passageDataParam);
//        for (ReadingPeriodicReportDetailLineChartVo lineChartVo : lineChartVos){
//            lineChartVo.setAccuracyRate(personalPassagesService.getAccuracyRateByParam(passageDataParam));
//        }
//        periodicReportDetailVo.setStudyAccuracyTrendList(lineChartVos);

        //高频知识点&题型表现
        ReadingPersonalPassageDataParam param = new ReadingPersonalPassageDataParam();
        param.setStartDate(report.getStartDate());
        param.setEndDate(report.getEndDate());
        param.setStudentId(studentId);
        List<ReadingHighFreqPerformanceVo> highFreqPerformanceVos = reportService.queryPersonalPassageHighFreqPerformanceData(param);
        periodicReportDetailVo.setHighFreqPerformanceList(highFreqPerformanceVos);


        List<ReadingWeekTimeStatisticsVO> currentWeekReadingTimeSpent = personalPassagesService.getWeekReadingTimeSpent(studentId, report.getStartDate(), report.getEndDate());
        if(CollectionUtils.isNotEmpty(currentWeekReadingTimeSpent)){
            int totalSeconds = currentWeekReadingTimeSpent.stream().mapToInt(ReadingWeekTimeStatisticsVO::getTotalTimeSpent).sum();
            periodicReportDetailVo.setReadingTotalMin((int) ((totalSeconds + 59) / 60));
            currentWeekReadingTimeSpent.stream()
                    .max(Comparator.comparingInt(ReadingWeekTimeStatisticsVO::getTotalTimeSpent))
                    .ifPresent(maxVo -> maxVo.setMaxMinFlag(1));
            currentWeekReadingTimeSpent.forEach(vo -> {
                Integer totalTimeSpent = vo.getTotalTimeSpent();
                vo.setTotalTimeSpent((int) ((totalTimeSpent + 59) / 60));
                vo.setWeekFlag("current");
            });
        }
        LocalDate thisMonday = report.getStartDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        LocalDate lastSunday = thisMonday.minusDays(1);
        List<ReadingWeekTimeStatisticsVO> lastWeekReadingTimeSpent = personalPassagesService.getWeekReadingTimeSpent(studentId, lastMonday, lastSunday);
        if(CollectionUtils.isNotEmpty(lastWeekReadingTimeSpent)){
            lastWeekReadingTimeSpent.stream()
                    .max(Comparator.comparingInt(ReadingWeekTimeStatisticsVO::getTotalTimeSpent))
                    .ifPresent(maxVo -> maxVo.setMaxMinFlag(1));
            lastWeekReadingTimeSpent.forEach(vo -> {
                Integer totalTimeSpent = vo.getTotalTimeSpent();
                vo.setTotalTimeSpent((int) ((totalTimeSpent + 59) / 60));
                vo.setWeekFlag("last");
            });
            currentWeekReadingTimeSpent.addAll(lastWeekReadingTimeSpent);
        }
        periodicReportDetailVo.setWeekReadingTimeSpentList(currentWeekReadingTimeSpent);

        List<ReadingGrowthCycleVO> weekReadingGrowthCycle = personalPassagesService.getWeekReadingGrowthCycle(studentId, report.getStartDate(), report.getEndDate());
        if(CollectionUtils.isNotEmpty(weekReadingGrowthCycle)){
            weekReadingGrowthCycle= weekReadingGrowthCycle.stream()
                    .map(vo -> {
                        vo.setAccuracy((int) ((vo.getCorrectCount() / (double)  vo.getTotalQuestions()) * 100));
                        return vo;
                    })
                    .collect(Collectors.toList());
            periodicReportDetailVo.setWeekReadingGrowthCycleList(weekReadingGrowthCycle);
        }

        return periodicReportDetailVo;
    }

    @Override
    public Boolean checkPersonalPassageIsExistReport(Long personalPassageId) {
        CommonResponse.ERROR.assertNotNull(personalPassageId, "用户答题训练id不能为空");
        ReadingPersonalPassages personalPassages = personalPassagesService.getById(personalPassageId);
        CommonResponse.ERROR.assertNotNull(personalPassages, "找不到对应训练记录，id：" + personalPassageId + "异常");

        LocalDate createDate = personalPassages.getEndAt().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return baseMapper.exists(Wrappers.<ReadingPersonalAnalysisPeriodicReport>lambdaQuery()
                .le(ReadingPersonalAnalysisPeriodicReport::getStartDate, createDate)
                .ge(ReadingPersonalAnalysisPeriodicReport::getEndDate, createDate)
                .isNull(ReadingPersonalAnalysisPeriodicReport::getDeletedAt)
        );
    }

    @Override
    public void againGeneratePeriodicReport() {
        List<ReadingPersonalAnalysisPeriodicReport> periodicReports = baseMapper.selectList(
                Wrappers.<ReadingPersonalAnalysisPeriodicReport>lambdaQuery()
                        .eq(ReadingPersonalAnalysisPeriodicReport::getStatus, 1)
                        .eq(ReadingPersonalAnalysisPeriodicReport::getReportType, 1)
                        .isNull(ReadingPersonalAnalysisPeriodicReport::getDeletedAt)
                        .isNull(ReadingPersonalAnalysisPeriodicReport::getAnalysesResult)
                        .orderByDesc(ReadingPersonalAnalysisPeriodicReport::getId)
        );
        periodicReports = periodicReports.stream().limit(100).collect(Collectors.toList());
        periodicReports.forEach(report -> {
            Long reportId = report.getId();
            Long studentId = report.getStudentId();
            LocalDate startDate = report.getStartDate();
            LocalDate endDate = report.getEndDate();

            //封装ai分析需要的参数
            ReadingAIAbilityParam param = new ReadingAIAbilityParam();
            param.setId(reportId.toString());
            param.setReadingAIAbilityType(7);
            //拼接ai分析字符串
            ReadingPersonalPassageDataParam dataParam = new ReadingPersonalPassageDataParam();
            dataParam.setStartDate(startDate);
            dataParam.setEndDate(endDate);
            dataParam.setStudentId(studentId);
            param.setDirectMessage(formatDirectMessage(dataParam));
            //ai请求
            aiAnalysisService.analysesAIRequest(param);
        });
    }
}
