package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.entity.ReadingPassageQuestionSets;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.QuestionAuditResultVo;
import com.joinus.study.model.vo.ReadingPassageQuestionSetsVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
import com.joinus.study.model.vo.ReadingQuestionSetsViewVo;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【reading_passage_question_sets】的数据库操作service
 * @createDate 2025-03-28 09:47:33
 * @Entity com.joinus.study.model.entity.ReadingPassageQuestionSets
 */
public interface ReadingPassageQuestionSetsService extends IService<ReadingPassageQuestionSets> {

    Page<ReadingPassageQuestionSetsVo> queryQuestionSetsList(ReadingPassageQuestionSetsParam param);

    Boolean questionSetsEdit(ReadingQuestionSetEditParam param);

    Boolean operateEnabled(ReadingPassageQuestionSetsEnabledParam param);

    Boolean operateAudit(ReadingPassageQuestionSetsAuditParam param);

    Boolean questionSetsDelete(ReadingPassageQuestionSetsParam param);

    ReadingQuestionSetsViewVo questionSetsView(ReadingPassageQuestionSetsParam param);

    Integer getQuestionSetsCount(UUID passageId);

    /**
     * @description: 根据套题ID获取套题信息
     * @author: lifengxu
     * @date: 2025/6/19 14:58
     */
    ReadingPersonalPassagesVo getQuestionsBySetsId(UUID setId) throws BaseException;

    String generateQuestionSetHtml(ReadingPassageQuestionSetsParam param,Integer printRange);

    String generateQuestionSetHtmlNew(UUID questionSetId);

    List<QuestionAuditResultVo> getQuestionAuditResult(UUID questionId);
}
