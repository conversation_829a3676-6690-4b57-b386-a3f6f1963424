package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.entity.MathPageViewTracking;
import com.joinus.study.model.param.MathPageViewTrackingParam;
import com.joinus.study.service.MathPageViewTrackingService;
import com.joinus.study.mapper.MathPageViewTrackingMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_page_view_tracking(页面访问记录表)】的数据库操作Service实现
* @createDate 2025-07-29 08:42:47
*/
@Service
public class MathPageViewTrackingServiceImpl extends ServiceImpl<MathPageViewTrackingMapper, MathPageViewTracking>
    implements MathPageViewTrackingService{

    @Override
    public boolean addTracking(MathPageViewTrackingParam mathPageViewTrackingParam, CurrentUser currentUser) {
        Long studentId = null != currentUser ? currentUser.getStudentId() : null;
        Long userId = null != currentUser ? currentUser.getUserId() : null;
        MathPageViewTracking tracking = MathPageViewTracking.builder()
                .userId(userId)
                .studentId(studentId)
                .fullPath(mathPageViewTrackingParam.getFullPath())
                .businessType(mathPageViewTrackingParam.getBusinessType())
                .build();
        return baseMapper.insert(tracking) > 0;
    }
}




