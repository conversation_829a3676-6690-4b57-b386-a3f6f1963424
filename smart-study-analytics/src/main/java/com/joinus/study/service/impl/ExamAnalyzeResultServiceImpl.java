package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.*;
import com.joinus.study.model.bo.SpecialTrainingBo;
import com.joinus.study.model.dto.PresignedUrlDto;
import com.joinus.study.model.dto.SpecialTrainingDto;
import com.joinus.study.model.dto.SpecialTrainingPdfDto;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.MathStudentSyncLearningTest;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.param.PresignedUrlParam;
import com.joinus.study.model.param.SpecialTrainingPdfParam;
import com.joinus.study.model.po.MathActivityWeekUnitPo;
import com.joinus.study.model.po.MathExamQuestinCountPo;
import com.joinus.study.model.vo.ExamAnalyzeResultVo;
import com.joinus.study.model.vo.SpecialTrainingPdfVo;
import com.joinus.study.model.vo.SpecialTrainingVo;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.ExamAnalyzeResultService;
import com.joinus.study.service.PersonalExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExamAnalyzeResultServiceImpl extends ServiceImpl<ExamAnalyzeResultMapper, ExamAnalyzeResult> implements ExamAnalyzeResultService {

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private PersonalExamService personalExamService;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;
    @Autowired
    private MathExamsMapper mathExamsMapper;
    @Autowired
    private MathStudentSyncLearningTestMapper mathStudentSyncLearningTestMapper;

    @Override
    public Page<ExamAnalyzeResultVo> situationAnalysisPage(Long parentId, Long studentId, Integer current, Integer size) {
        Page<ExamAnalyzeResultVo> page = new Page<ExamAnalyzeResultVo>(current == null ? 1 : current, size == null ? 10 : size);
        List<ExamAnalyzeResultVo> examAnalyzeResultVoList = this.baseMapper.selectExamAnalyzeResultList(page, studentId);

        if (CollUtil.isNotEmpty(examAnalyzeResultVoList)) {
            List<MathExamQuestinCountPo> mathExamQuestinCountPos = mathExamsMapper.listQuestionCountByIds(examAnalyzeResultVoList.stream().map(ExamAnalyzeResultVo::getExamId).collect(Collectors.toList()));
            Map<UUID, Integer> examQuestionCountMap = mathExamQuestinCountPos.stream().collect(Collectors.toMap(MathExamQuestinCountPo::getExamId, MathExamQuestinCountPo::getQuestionCount));
            examAnalyzeResultVoList.stream().forEach(examAnalyzeResultVo -> {
                examAnalyzeResultVo.setQuestionCount(examQuestionCountMap.getOrDefault(examAnalyzeResultVo.getExamId(), 0));
            });
        }

        examAnalyzeResultVoList.forEach(examAnalyzeResultVo -> {
            if (ExamAnalyzeResultEnum.INVALID.equals(examAnalyzeResultVo.getResult())) {
                String ossUrlString = examAnalyzeResultMapper.getOssUrlString(examAnalyzeResultVo.getExamId());
                if(DataUtils.isNotEmpty(ossUrlString)){
                    List<String> ossUrls = new ArrayList<>();
                    String[] split = ossUrlString.split(",");
                    for (String ossUrl : split) {
                        PresignedUrlParam param = new PresignedUrlParam();
                        param.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                        param.setOssKey(ossUrl);
                        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param);
                        if(DataUtils.isNotEmpty(presignedUrlDto) && DataUtils.isNotEmpty(presignedUrlDto.getData())){
                            ossUrls.add(presignedUrlDto.getData().getPresignedUrl());
                        }
                    }
                    examAnalyzeResultVo.setOssUrls(ossUrls);
                }
            }
        });
        page.setRecords(examAnalyzeResultVoList);
        return page;
    }

    @Override
    public Page<ExamAnalyzeResultVo> specializedRecordPage(Long parentId, Long studentId, Integer current, Integer size) {
        long startTime = System.currentTimeMillis();
        log.info("开始查询专项训练记录, studentId: {}, parentId: {}, page: {}, size: {}",
                studentId, parentId, current, size);

        Page<ExamAnalyzeResultVo> page = new Page<ExamAnalyzeResultVo>(current == null ? 1 : current, size == null ? 10 : size);
        long queryStart = System.currentTimeMillis();
        List<ExamAnalyzeResultVo> examAnalyzeResultVoList = this.baseMapper.selectSpecializedExamAnalyzeResultListV2(page, studentId);
        log.info("查询专项训练记录完成, 耗时: {}ms, 记录数: {}",
                System.currentTimeMillis() - queryStart,
                examAnalyzeResultVoList == null ? 0 : examAnalyzeResultVoList.size());

        if (CollUtil.isNotEmpty(examAnalyzeResultVoList)) {
            List<MathExamQuestinCountPo> mathExamQuestinCountPos = mathExamsMapper.listQuestionCountByIds(examAnalyzeResultVoList.stream().map(ExamAnalyzeResultVo::getExamId).collect(Collectors.toList()));
            Map<UUID, Integer> examQuestionCountMap = mathExamQuestinCountPos.stream().collect(Collectors.toMap(MathExamQuestinCountPo::getExamId, MathExamQuestinCountPo::getQuestionCount));
            examAnalyzeResultVoList.stream().forEach(examAnalyzeResultVo -> {
                examAnalyzeResultVo.setQuestionCount(examQuestionCountMap.getOrDefault(examAnalyzeResultVo.getExamId(), 0));
            });
        }
        if (CollUtil.isEmpty(examAnalyzeResultVoList)) {
            log.info("未查询到专项训练记录, 总耗时: {}ms", System.currentTimeMillis() - startTime);
            return page;
        }

        //查询属期练习的类型：小节练习、章末测试、综合测试
        long processHolidayStart = System.currentTimeMillis();
        List<UUID> holidayExamIds = examAnalyzeResultVoList.stream()
                .filter(examAnalyzeResultVo -> ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource()))
                .map(ExamAnalyzeResultVo::getExamId)
                .collect(Collectors.toList());
        log.info("筛选假期训练记录完成, 耗时: {}ms, 假期训练数量: {}",
                System.currentTimeMillis() - processHolidayStart,
                holidayExamIds.size());

        Map<UUID, MathActivityWeekUnitTypeEnum> examIdWeenUnitTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(holidayExamIds)) {
            long queryExamTypeStart = System.currentTimeMillis();
            List<MathActivityWeekUnitPo> weekUnitExamIdPos = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(holidayExamIds);
            log.info("查询假期训练类型完成, 耗时: {}ms, 查询到类型数量: {}",
                    System.currentTimeMillis() - queryExamTypeStart,
                    weekUnitExamIdPos.size());

            long mapConvertStart = System.currentTimeMillis();
            examIdWeenUnitTypeMap = weekUnitExamIdPos.stream()
                    .collect(Collectors.toMap(
                            MathActivityWeekUnitPo::getExamId,
                            MathActivityWeekUnitPo::getType,
                            (existing, replacement) -> existing
                    ));
            log.info("转换训练类型映射完成, 耗时: {}ms",
                    System.currentTimeMillis() - mapConvertStart);
        }

        long setExamTypeStart = System.currentTimeMillis();
        Map<UUID, MathActivityWeekUnitTypeEnum> finalExamIdWeenUnitTypeMap = examIdWeenUnitTypeMap;
        examAnalyzeResultVoList.forEach(examAnalyzeResultVo -> {
            if (ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource())) {
                MathActivityWeekUnitTypeEnum mathActivityWeekUnitTypeEnum = finalExamIdWeenUnitTypeMap.get(examAnalyzeResultVo.getExamId());
                examAnalyzeResultVo.setExamType(null != mathActivityWeekUnitTypeEnum ? mathActivityWeekUnitTypeEnum.getValue() : null);
            }
            //处理同步练试卷
            if (ExamSourceType.SYNC_LEARNING_TEST.equals(examAnalyzeResultVo.getSource())) {
                MathStudentSyncLearningTest mathStudentSyncLearningTest = mathStudentSyncLearningTestMapper.selectByExamHistoryId(examAnalyzeResultVo.getExamId());
                examAnalyzeResultVo.setExamType(null != mathStudentSyncLearningTest ? mathStudentSyncLearningTest.getTestType() : null);
            }
        });
        log.info("设置考试类型完成, 耗时: {}ms",
                System.currentTimeMillis() - setExamTypeStart);

        page.setRecords(examAnalyzeResultVoList);
        log.info("专项训练记录查询完成, 总耗时: {}ms",
                System.currentTimeMillis() - startTime);
        return page;
    }

    @Override
    public MathActivityWeekUnit getMathActivityWeekUnitByTrainingExamId(UUID examId) {
        return mathActivityWeekUnitStudentMapper.getMathActivityWeekUnitByTrainingExamId(examId);
    }

    @Override
    public Page pageAllRecords(Long studentId, Integer current, Integer size) {
        Page<ExamAnalyzeResultVo> page = new Page<ExamAnalyzeResultVo>(current == null ? 1 : current, size == null ? 10 : size);
        List<ExamAnalyzeResultVo> examAnalyzeResultVoList = this.baseMapper.pageAllRecords(page, studentId);

        if (CollUtil.isNotEmpty(examAnalyzeResultVoList)) {
            List<MathExamQuestinCountPo> mathExamQuestinCountPos = mathExamsMapper.listQuestionCountByIds(examAnalyzeResultVoList.stream().map(ExamAnalyzeResultVo::getExamId).collect(Collectors.toList()));
            Map<UUID, Integer> examQuestionCountMap = mathExamQuestinCountPos.stream().collect(Collectors.toMap(MathExamQuestinCountPo::getExamId, MathExamQuestinCountPo::getQuestionCount));
            examAnalyzeResultVoList.stream().forEach(examAnalyzeResultVo -> {
                examAnalyzeResultVo.setQuestionCount(examQuestionCountMap.getOrDefault(examAnalyzeResultVo.getExamId(), 0));
            });
        }

        //查询属期练习的类型：小节练习、章末测试、综合测试
        List<UUID> holidayExamIds = examAnalyzeResultVoList.stream()
                .filter(examAnalyzeResultVo -> ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource()))
                .map(ExamAnalyzeResultVo::getExamId)
                .collect(Collectors.toList());
        Map<UUID, MathActivityWeekUnitTypeEnum> examIdWeenUnitTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(holidayExamIds)) {
            List<MathActivityWeekUnitPo> weekUnitExamIdPos = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(holidayExamIds);
            examIdWeenUnitTypeMap = weekUnitExamIdPos.stream()
                    .collect(Collectors.toMap(
                            MathActivityWeekUnitPo::getExamId,
                            MathActivityWeekUnitPo::getType,
                            (existing, replacement) -> existing
                    ));
        }

        Map<UUID, MathActivityWeekUnitTypeEnum> finalExamIdWeenUnitTypeMap = examIdWeenUnitTypeMap;
        examAnalyzeResultVoList.forEach(examAnalyzeResultVo -> {
            //处理考情分析试卷的无效试卷情况（展示图片）
            if (ExamAnalyzeResultEnum.INVALID.equals(examAnalyzeResultVo.getResult())) {
                String ossUrlString = examAnalyzeResultMapper.getOssUrlString(examAnalyzeResultVo.getExamId());
                if(DataUtils.isNotEmpty(ossUrlString)){
                    List<String> ossUrls = new ArrayList<>();
                    String[] split = ossUrlString.split(",");
                    for (String ossUrl : split) {
                        PresignedUrlParam param = new PresignedUrlParam();
                        param.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                        param.setOssKey(ossUrl);
                        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param);
                        if(DataUtils.isNotEmpty(presignedUrlDto) && DataUtils.isNotEmpty(presignedUrlDto.getData())){
                            ossUrls.add(presignedUrlDto.getData().getPresignedUrl());
                        }
                    }
                    examAnalyzeResultVo.setOssUrls(ossUrls);
                }
            }
            //处理专项练习试卷
            if (ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource())) {
                MathActivityWeekUnitTypeEnum mathActivityWeekUnitTypeEnum = finalExamIdWeenUnitTypeMap.get(examAnalyzeResultVo.getExamId());
                examAnalyzeResultVo.setExamType(null != mathActivityWeekUnitTypeEnum ? mathActivityWeekUnitTypeEnum.getValue() : null);
            }
            //处理同步练试卷
            if (ExamSourceType.SYNC_LEARNING_TEST.equals(examAnalyzeResultVo.getSource())) {
                MathStudentSyncLearningTest mathStudentSyncLearningTest = mathStudentSyncLearningTestMapper.selectByExamHistoryId(examAnalyzeResultVo.getExamId());
                examAnalyzeResultVo.setExamType(null != mathStudentSyncLearningTest ? mathStudentSyncLearningTest.getTestType() : null);
            }
        });
        page.setRecords(examAnalyzeResultVoList);
        return page;

    }

    @Override
    public SpecialTrainingVo specialTraining(Long examAnalyzeResultId) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(examAnalyzeResultId);
        List<QuestionKnowledgePoint> questionKnowledgePoints = personalExamService.listWeakKnowledgePoints(examAnalyzeResult.getPersonalExamId());
        if (CollUtil.isEmpty(questionKnowledgePoints)) {
            throw new BaseException("没有找到对应的薄弱点");
        }
        List<UUID> knowledgePointIds = questionKnowledgePoints.stream()
                .filter(questionKnowledgePoint -> null != questionKnowledgePoint.getKnowledgePointId())
                .map(questionKnowledgePoint -> questionKnowledgePoint.getKnowledgePointId())
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(knowledgePointIds)) {
            throw new BaseException("没有找到对应的知识点");
        }
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .knowledgePointsIds(knowledgePointIds)
                .build();
        SpecialTrainingDto specialTrainingDto = eduKnowledgeHubService.specialTraining(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingVo.class);
    }

    @Override
    public SpecialTrainingPdfVo specialTrainingPdf(SpecialTrainingPdfParam param) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(param.getExamAnalyzeResultId());
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .selectQuestion(param.getSelectQuestion())
                .selectAnswer(param.getSelectAnswer())
                .pdfUUID(param.getPdfUUID())
                .build();
        SpecialTrainingPdfDto specialTrainingDto = eduKnowledgeHubService.specialTrainingPdf(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingPdfVo.class);
    }

    @Override
    public SpecialTrainingPdfVo specialTrainingPdfPreview(SpecialTrainingPdfParam param) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(param.getExamAnalyzeResultId());
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .selectQuestion(param.getSelectQuestion())
                .selectAnswer(param.getSelectAnswer())
                .pdfUUID(param.getPdfUUID())
                .build();
        SpecialTrainingPdfDto specialTrainingDto = eduKnowledgeHubService.specialTrainingPdfPreview(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingPdfVo.class);
    }

    @Override
    public  List<ExamAnalyzeResult> getByPersonalExamId(Long sourceId) {
        LambdaQueryWrapper<ExamAnalyzeResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamAnalyzeResult::getPersonalExamId, sourceId);
        queryWrapper.orderByDesc(ExamAnalyzeResult::getId);
        return this.list(queryWrapper);
    }

    @Override
    public ExamAnalyzeResult getExamAnalyzeResultById(Long examAnalyzeResultId) {
        return examAnalyzeResultMapper.selectById(examAnalyzeResultId);
    }


}
