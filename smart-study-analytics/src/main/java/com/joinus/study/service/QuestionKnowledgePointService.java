package com.joinus.study.service;

import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_knowledge_point】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface QuestionKnowledgePointService extends IService<QuestionKnowledgePoint> {

    /**
     * 根据问题id查询关联知识点
     * @param questionId
     * @return
     */
    List<QuestionKnowledgePoint> getQuestionKnowledgePointList(@Param("questionId") UUID questionId);

    List<KnowledgePointStatisticsVo> selectKnowledgePointStatisticsByExamId(Long id);

    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getListByExamIdAndQuestionId(UUID examId, UUID questionId, PublisherEnum publisher);

    List<StudentInfoVo> getStudentIds(Long studentId);

    List<KnowledgePointStatisticsVo> getClassKnowledgePointStatics(List<Long> studentIds, List<Long>classIds,UUID examId);

    List<QuestionKnowledgePoint> listKnowledgePointsByExamIdAndPublisher(UUID examId, PublisherEnum publisher);

    void rebuildExamQuestionPublisher();
    List<MathKnowledgePointVO> listByExamIdAndQuestionIdAndPublisher(UUID examId, UUID questionId, PublisherEnum publisher);
    //查询精讲是否存在
    Boolean chenckHasExplanation(UUID knowledgePointId);
    //查询对应精讲
    MathKnowledgePointHandoutVo getExplanationByKnowledgePointId(UUID knowledgePointId);
    //查询对应章节的知识点
    List<MathKnowledgePointVO> getKnowledgePointBySectionId(UUID sectionId,Long studentId);

    List<KnowledgePointDto> getKnowledgePointFromView(UUID sectionId,String publisherName,UUID knowledgePointId,List<UUID> knowledgePointIds);
    KnowledgePointDto getKnowledgePointFromViewById(UUID id);
    List<KnowledgePointDto> getKnowledgePointFromViewByIds(List<UUID> ids,PublisherEnum  publisher);

    List<MathKnowledgePointVO> getMathKnowledgePointVOByIds(List<UUID> ids,PublisherEnum  publisher);
}
