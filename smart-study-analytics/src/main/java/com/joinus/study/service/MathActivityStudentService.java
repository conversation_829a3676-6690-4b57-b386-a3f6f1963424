package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.ExamSummerPlanListDto;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.enums.MathActivityWeekTypeEnum;
import com.joinus.study.model.param.ActivityRefundParam;
import com.joinus.study.model.param.IpayPayBackParam;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.MathActivityJoinusParam;
import com.joinus.study.model.vo.MathActivityStudentStudyRecord;
import com.joinus.study.model.vo.MathActivityLearningPlanerToastParam;
import com.joinus.study.model.vo.MathActivityLearningPlanerToastVo;
import com.joinus.study.model.vo.MathActivityStudentVo;

import com.joinus.study.model.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @createDate 2025-06-13
 * @Entity com.joinus.study.model.entity.MathActivityEntity
 */
public interface MathActivityStudentService extends IService<MathActivityStudent> {

    MathActivityStudent getStudentCurrentActivity(Long studentId);

    Long studentJoinActivity(MathActivityJoinusParam mathActivityStudent) throws BaseException;
    // 支付
    String activityPay(IpayPayRequest param) throws BaseException;
    // 支付回调
    String activityPayBack(IpayPayBackParam param) throws BaseException;
    // 退款
    Boolean activityRefund(ActivityRefundParam refundParam) throws BaseException;

    // 获取学生参加活动信息
    List<MathActivityStudentVo> getMathActivityStudentVo(MathActivityStudent mathActivityStudent);

    // 根据参与id获取学生参加活动学习记录战绩
    MathActivityStudentStudyRecord getActivityStudentStudyRecord(Long activityStudentId);

    MathActivityLearningPlanerToastVo getPersonalPlanerToast(Long studentId);

    //学生暑期全部学习计划总览
    List<Map<String, Object>> selectSummerPlansStatistics(Long studentId, Long activityId);
    //暑期全部学习计划列表
    List<ExamSummerPlanListDto> selectStudentSummerPlans(Long studentId, Integer weekSort,Long activityId);
    //更新学生暑期计划完成状态（math_activity_week_unit_student）
    void updateStudentSummerPlanStatus(Long studentId,Long activityId);

    MathActivityLearningPlanerToastVo updatePersonalPlanerToast(Long studentId, MathActivityLearningPlanerToastParam param);

    //更新学生会员等级
    void updateMembershipLevel(Long studentId, MathMemberLevelEnum membershipLevel);

    void autoCancleMathMemberRightsTask();

    Map<String, Object> studentCurrentWeekInfo(Long studentId);

    //删除学生参加活动信息
    Long deleteActivityStudent(Long studentId);

    //更改活动学生选择书本信息
    Long changeJoinActivity(MathActivityJoinusParam mathActivityJoinusParam) throws BaseException;

    //暑期活动查询电子课本
    MathElecttronicTextbookVo getElectronicTextbook(Long studentId, MathActivityWeekTypeEnum weekType);

    //学生加入活动赠送会员
    Boolean checkAndGiftMember(Long studentId, Long parentId,Integer daysBetween);
}
