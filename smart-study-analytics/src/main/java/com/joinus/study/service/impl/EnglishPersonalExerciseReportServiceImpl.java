package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishPersonalExerciseReportMapper;
import com.joinus.study.model.entity.EnglishPersonalExerciseReport;
import com.joinus.study.model.param.EnglishPersonalExerciseReportCreateParam;
import com.joinus.study.service.EnglishPersonalExerciseReportService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@AllArgsConstructor
@Service("englishPersonalExerciseReportService")
@Slf4j
public class EnglishPersonalExerciseReportServiceImpl extends ServiceImpl<EnglishPersonalExerciseReportMapper, EnglishPersonalExerciseReport> implements EnglishPersonalExerciseReportService {

    @Override
    public Long create(EnglishPersonalExerciseReportCreateParam reportCreateParam) {
        EnglishPersonalExerciseReport entity = BeanUtil.copyProperties(reportCreateParam, EnglishPersonalExerciseReport.class);
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        this.save(entity);
        return entity.getId();
    }
}
