package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ctrip.framework.apollo.core.utils.PropertiesUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.MathActivityStudentRecord;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.entity.MathActivityWeek;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.MathActivityWeekUnitStudent;
import com.joinus.study.model.enums.GradeSemesterEnum;
import com.joinus.study.model.enums.MathActivityWeekTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.ActiveStudentVo;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.vo.ActiveStudentVo;
import com.joinus.study.model.vo.MathActivityStudentStudyRecord;
import com.joinus.study.service.MathActivityWeekUnitStudentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Service
@Slf4j
public class MathActivityWeekUnitStudentServiceImpl extends BaseServiceImpl<MathActivityWeekUnitStudentMapper, MathActivityWeekUnitStudent>
		implements MathActivityWeekUnitStudentService {

	@Resource
	private MathActivityWeekUnitMapper mathActivityWeekUnitMapper;
	@Resource
	private MathActivityWeekMapper mathActivityWeekMapper;
	@Resource
	private MathActivityStudentMapper mathActivityStudentMapper;

	@Value("${math.activity.special.schools:29660}")
	private String specialSchools;
	@Value("${math.activity.special.schools.2:29660}")
	private String specialSchools2;
	@Resource
	private ActiveStudentsMapper activeStudentsMapper;
	@Override
	public List<MathActivityWeekUnitStudent> list(Long activityId, Long studentId, Boolean unlocked) {
		QueryWrapper<MathActivityWeekUnitStudent> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("activity_id", activityId);
		queryWrapper.eq("student_id", studentId);
		queryWrapper.isNull("deleted_at");
		if (unlocked !=null){
			queryWrapper.eq("unlocked", unlocked);
		}
		return list(queryWrapper);
	}

	@Override
	public void saveStudentWeekUnit(MathActivityStudent mathActivityStudent) {
		CommonResponse.ERROR.assertNotNull(mathActivityStudent.getId(), "学生参与活动id不可为空");
		// 删除之前选择记录
		mathActivityStudent = mathActivityStudentMapper.selectById(mathActivityStudent.getId());
		baseMapper.deleteByActivityIdStudentId(mathActivityStudent.getActivityId(), mathActivityStudent.getStudentId());
		ActiveStudentVo studentVo = activeStudentsMapper.getClassIdByStudentId(mathActivityStudent.getStudentId());
		CommonResponse.ERROR.assertNotNull(studentVo, "查询不到学生信息");
		//活动周数据查询
		QueryWrapper<MathActivityWeek> weekWrapper = new QueryWrapper<>();
		weekWrapper.eq("activity_id", mathActivityStudent.getActivityId());
		weekWrapper.eq("publisher", mathActivityStudent.getPublisher());
		weekWrapper.eq("grade", mathActivityStudent.getGrade());
		weekWrapper.isNull("deleted_at");
		weekWrapper.orderByAsc("sort_no");
		//待保存活动周数据
		List<MathActivityWeekUnitStudent> activityWeekUnitStudents = new ArrayList<>();
		MathMemberLevelEnum level = mathActivityStudent.getMembershipLevel();
		List<MathActivityWeek> activityWeeks = mathActivityWeekMapper.selectList(weekWrapper);
		List<Long> giftWeekUnitIds = new ArrayList<>();
		if (level.equals(MathMemberLevelEnum.GIFT)){
			giftWeekUnitIds = this.getGiftWeekUnitIds(mathActivityStudent.getActivityId(), mathActivityStudent.getPublisher(),
					mathActivityStudent.getGrade(),studentVo.getSchoolId());
		}
		for (MathActivityWeek activityWeek : activityWeeks) {
			QueryWrapper<MathActivityWeekUnit> weekUnitqueryWrapper = new QueryWrapper<>();
			weekUnitqueryWrapper.eq("week_id", activityWeek.getId());
			weekUnitqueryWrapper.isNull("deleted_at");
			weekUnitqueryWrapper.orderByAsc("sort_no");
			boolean unlocked = level.equals(MathMemberLevelEnum.PAID)
					|| activityWeek.getType().equals(MathActivityWeekTypeEnum.REVIEW)
					|| this.isGiftAll(studentVo.getSchoolId(),mathActivityStudent.getGrade());
			List<MathActivityWeekUnit> weekUnits = mathActivityWeekUnitMapper.selectList(weekUnitqueryWrapper);
			if (weekUnits != null && weekUnits.size() > 0) {
				for (MathActivityWeekUnit weekUnit : weekUnits) {
					MathActivityWeekUnitStudent weekUnitStudent = MathActivityWeekUnitStudent.builder()
							.activityId(mathActivityStudent.getActivityId())
							.activityStudentId(mathActivityStudent.getId())
							.studentId(mathActivityStudent.getStudentId())
							.weekUnitId(weekUnit.getId())
							.weekId(weekUnit.getWeekId())
							.unlocked(unlocked || giftWeekUnitIds.contains(weekUnit.getId()))
							.type(weekUnit.getType())
							.build();
					activityWeekUnitStudents.add(weekUnitStudent);
				}
			}
		}
		if (activityWeekUnitStudents.size() > 0){
			baseMapper.insertBatch(activityWeekUnitStudents);
		}
	}

	@Override
	public MathActivityStudentStudyRecord getMathActivityStudentStudyRecord(Long id, Long studentId) {
		List<MathActivityStudentRecord> studentRecords = baseMapper.selectMathActivityStudentStudyRecord(id, studentId);
		Integer completedChapter = 0;
		Integer completedSection = 0;
		Integer totalPoint = 0;
		List<MathActivityStudentStudyRecord.ViewPercentage> viewPercentages = new ArrayList<>();
		for (MathActivityStudentRecord studentRecord : studentRecords){
			totalPoint = totalPoint + studentRecord.getCompletedPoint();
			Integer grade = studentRecord.getGrade();
			String name = "";
			if (studentRecord.getType().equals(MathActivityWeekTypeEnum.PREVIEW)) {
				name = "数学预习" + GradeSemesterEnum.getByName("GRADE" + grade) + "上册进度";
			} else {
				grade = grade-1;
				name = "数学复习" + GradeSemesterEnum.getByName("GRADE" + grade) + "下册进度";
			}
			int typeTotalSection = studentRecord.getTotalSection() + studentRecord.getTotalChapter() + 1;//测试算单元
			int typeCompletedSection = studentRecord.getCompletedSection()
					+ studentRecord.getCompletedChapter()
					+ studentRecord.getCompletedBook();
			completedChapter = completedChapter + studentRecord.getCompletedChapter();
			completedSection = completedSection + typeCompletedSection;
			viewPercentages.add(MathActivityStudentStudyRecord.ViewPercentage.builder()
					.name(name)
					.completedSection(typeCompletedSection)
					.totalSection(typeTotalSection)
					.percentage(typeCompletedSection * 100 / typeTotalSection)
					.build());
		}
		List<Date> dates= baseMapper.selectFinishedAtByStudentId(studentId);
		int continuousDays = calculateContinuousDays(dates);
		return MathActivityStudentStudyRecord.builder()
				.activityId(id)
				.completedChapter(completedChapter)
				.completedSection(completedSection)
				.totalPoint(totalPoint)
				.continuousDays(continuousDays) // TODO: 待完善
				.percentages(viewPercentages)
				.build();
	}

	@Override
	public List<Long> getGiftWeekUnitIds(Long activityId, PublisherEnum publisher, Integer grade , Long schoolId) {
		String[] split = specialSchools.split(",");
		if(schoolId != null && Arrays.asList(split).contains(schoolId.toString())){
			return mathActivityWeekUnitMapper.selectGiftWeekUnitIds(activityId, publisher, grade, true);
		}
		return mathActivityWeekUnitMapper.selectGiftWeekUnitIds(activityId, publisher, grade, false) ;
	}
	@Override
	public boolean isGiftAll(Long schoolId,Integer  grade) {
		if(schoolId == null || grade != 7){
			return false;
		}
		String[] split = specialSchools2.split(",");
		return  Arrays.asList(split).contains(schoolId.toString());
	}

	// 计算连续天数
	public int calculateContinuousDays(List<Date> dates) {
		if (CollectionUtil.isEmpty(dates)) {
			return 0;
		}
		// 1. 去重并转换为LocalDate格式（只关心日期，忽略时间）
		Set<LocalDate> uniqueDates = new HashSet<>();
		for (Date date : dates) {
			uniqueDates.add(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
		}
		// 2. 按日期降序排序（最近日期在前）
		List<LocalDate> sortedDates = new ArrayList<>(uniqueDates);
		Collections.sort(sortedDates, Collections.reverseOrder());
		// 3. 计算连续天数
		int continuousDays = 1;  // 至少有一天
		LocalDate currentDate = sortedDates.get(0);
		for (int i = 1; i < sortedDates.size(); i++) {
			LocalDate nextDate = sortedDates.get(i);
			// 检查是否是前一天
			if (currentDate.minusDays(1).equals(nextDate)) {
				continuousDays++;
				currentDate = nextDate;
			} else {
				// 如果日期不连续，终止计算
				break;
			}
		}
		return continuousDays;
	}


}
