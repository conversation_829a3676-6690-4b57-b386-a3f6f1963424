package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.entity.MathActivityWeekUnitStudent;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.MathActivityStudentStudyRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_activity_week_unit_student(数学活动周期单元学生表)】的数据库操作Service
* @createDate 2025-06-16 09:57:27
*/
public interface MathActivityWeekUnitStudentService extends IService<MathActivityWeekUnitStudent> {

    List<MathActivityWeekUnitStudent> list(Long activityId, Long studentId,Boolean unlocked) throws BaseException;

    void saveStudentWeekUnit(MathActivityStudent mathActivityStudent) throws BaseException;

    // 获取学习进度
    MathActivityStudentStudyRecord getMathActivityStudentStudyRecord(Long id, Long studentId);

    // 获取赠送周单元
    List<Long> getGiftWeekUnitIds(Long activityId, PublisherEnum publisher, Integer  grade , Long schoolId);

    public boolean isGiftAll(Long schoolId,Integer  grade);
}
