package com.joinus.study.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.service.ReadingMembershipService;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 会员服务实现类
 * 实现会员资格校验逻辑
 */
@Slf4j
@Service
public class ReadingMembershipServiceImpl implements ReadingMembershipService {


    @Value("${qyl.host.url}")
    private String qylHostUrl;
    @Value("${get.student.isOpen.url}")
    private String getStudentIsOpenUrl;



    @Override
    public void checkStudentMembership(Long studentId, Long parentId,Integer subjectType) throws BaseException {
        CommonResponse.ERROR.assertIsTrue(checkStudentIsOpenMember(studentId, parentId,subjectType), "学生未开通会员");
    }

    @Override
    public Boolean checkStudentIsOpenMember(Long studentId, Long parentId,Integer subjectType) throws BaseException {
        Map<String, Object> mapParams =new HashMap<>();
        mapParams.put("studentId", studentId);
        mapParams.put("subjectType", subjectType);
        mapParams.put("parentId", parentId);
        log.info("调用青于蓝获取学生开通会员信息接口参数：{}", mapParams);
        String response = LocalHttpUtil.get(qylHostUrl + getStudentIsOpenUrl, mapParams);
        log.info("调用青于蓝获取学生开通会员信息接口返回结果：{}", response);
        if (StrUtil.isNotEmpty(response)) {
            JSONObject jsonObject = JSONUtil.parseObj(response);
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                int isOpen = data.getInt("isOpen",0);
                return isOpen == 1;
            }
        }
        return false;
    }
}
