package com.joinus.study.service;

import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.entity.MathPageViewTracking;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.param.MathPageViewTrackingParam;

/**
* <AUTHOR>
* @description 针对表【math_page_view_tracking(页面访问记录表)】的数据库操作Service
* @createDate 2025-07-29 08:42:47
*/
public interface MathPageViewTrackingService extends IService<MathPageViewTracking> {

    boolean addTracking(MathPageViewTrackingParam mathPageViewTrackingParam, CurrentUser currentUser);

}
