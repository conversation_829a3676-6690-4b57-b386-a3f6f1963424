package com.joinus.study.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.enums.AppTypeEnum;
import com.joinus.study.model.enums.ExampleQuestionEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

public interface EduKnowLedgeHubBusinessService {

    /**
     * 查询题目坐标信息
     *
     * @param param studentId:学生id
     * @return 题目坐标信息
     */
    QuestionCoordinateVo questionCoordinateInfo(QueryQuestionCoordinateParam param) throws JsonProcessingException;

    Flux<String> chatStream(SolveQuestionFromImgParam param);

    Flux<String> chatStreamMock(SolveQuestionFromImgParam param);

    /**
     * 抹除题目手写笔迹
     */
    EraseHandwritingFromQuestionVo eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam param);

    /**
     * 查询多题解析结果
     *
     * @param param studentId:学生id
     */
    Flux<String> queryMultiQuestionAnalysisResults(@Valid SolveQuestionFromImgParam param);

    /**
     * 查询单题解析结果
     *
     * @param param studentId:学生id
     */
    Flux<String> querySingleQuestionAnalysisResults(@Valid SolveQuestionFromImgParam param) throws JsonProcessingException;

    MultiImageAnalyticsResultVo queryMultiImageAnalyticsResult(@Valid MultiImageAnalysicsParam param);

    /**
     * 试卷诊断-题目标注
     *
     * @param param
     * @return
     */
    ExamAnalysisQuestionLabelVo examAnalysisQuestionLabel(@Valid ExamAnalysisQuestionLabelParam param);

    FlexiblyGeneratedVo flexiblyGenerated(FlexiblyQuestionParam param);

    Flux<String> flexiblyGenerating(FlexiblyQuestionParam param);

    KnowledgePointsVO knowledgePoints(KnowledgePointsParams param, UUID examId, PublisherEnum publisher);

    KnowledgePointsVO knowledgePointsViaDB(KnowledgePointsParams param, UUID examId, PublisherEnum publisher);

    /**
     * 根据图片和坐标切题
     */
    CutImageFromPositionsVo cutImageFromPositions(@Valid CutImageFromPositionsParam param);

    /**
     * 获取上传OSS token
     *
     * @param param
     * @return
     */
    OssTokenVo ossToken(@Valid OssTokenParam param);

    CheckExistGraphicsVo checkExistGraphics(@Valid CheckExistGraphicsParam param);

    /**
     * 获取图片临时链接
     */
    PresignedUrlVo presignedUrl(@Valid PresignedUrlParam param);

    Flux<String> createStreamFromQuestionAnswerDto(QuestionAnswerDto questionAnswerDto);

    /**
     * 获取AI单题解析结果
     * @param param 参数
     * @return 流式输出
     */
    Flux<String> getAiResultFluxData(SolveQuestionFromImgParam param);

    String exampleQuestionPicture(ExampleQuestionEnum type);

    /**
     * 生成试卷
     *
     * @param param
     * @return
     */
    ExamQuestionInfoListData createExam(@Valid CreateExamParam param);

    /**
     * 试卷诊断
     */
    ExamAnalyzeVo examAnalyze(AiAnalyticsExamParam param);

    ParseAnswerVo parseAnswer(ParseAnswerParam param);

    ExampleQuestionJsonVo exampleQuestion(ExampleQuestionEnum type) throws JsonProcessingException;

    /**
     * 获取试卷详情
     * @param examId
     * @return
     */
    MathExamVO getExamsByExamId(UUID examId);

    QuestionCoordinateDto coordinatePointMulti(QueryQuestionCoordinateParam param);

    /**
     * 获取试卷题目信息
     * @param examId
     * @return
     */
    List<MathExamQuestionVO> getQuestionsByExamId(UUID examId);

    /**
     * 编辑试卷题目
     * @param examId
     * @param questionId
     * @param param
     */
    QuestionDetailDTO editQuestion(UUID examId, UUID questionId, AddExamQuestionParam param);

    /**
     * 删除试卷题目
     * @param examId
     * @param questionId
     * @return
     */
    QuestionDeleteDto deleteQuestions(UUID examId, UUID questionId);

    /**
     * 新增试卷题目
     * @param examId
     * @param param
     * @return
     */
    QuestionDetailDTO addQuestion(UUID examId, AddExamQuestionParam param);

    CheckExamExsitenceVo.DataDTO checkExamExistenceV2 (CheckExamExsitenceParam param, AppTypeEnum appType);

    void reanalyzeExamKnowledgePoint(AiAnalyticsExamParam examAnalyzeParam);
}
