package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingActivityStudentMapper;
import com.joinus.study.model.entity.ReadingActivityStudent;
import com.joinus.study.model.entity.ReadingPersonalEquityActivityCode;
import com.joinus.study.service.ReadingActivityStudentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ReadingActivityStudentServiceImpl extends BaseServiceImpl<ReadingActivityStudentMapper, ReadingActivityStudent>
        implements ReadingActivityStudentService {

    @Override
    public Integer checkStudentIsJoinActivity(Long studentId) {
        CommonResponse.ERROR.assertNotNull(studentId, "参数学生id不能为空");
        List<ReadingActivityStudent> list = baseMapper.getStudentJoinActivityInfo(studentId);
        return CollUtil.isNotEmpty(list)  ? 1 : 0;
    }

    @Override
    public Integer getJoinTrainingCampCount() {
        return this.baseMapper.getJoinTrainingCampCount();


    }
}
