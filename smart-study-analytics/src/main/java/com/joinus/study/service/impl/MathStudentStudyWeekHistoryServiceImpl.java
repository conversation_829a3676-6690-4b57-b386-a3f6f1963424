package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.model.entity.MathStudentStudyWeekHistory;
import com.joinus.study.service.MathStudentStudyWeekHistoryService;
import com.joinus.study.mapper.MathStudentStudyWeekHistoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【math_student_study_week_history(学生学习周数记录)】的数据库操作Service实现
* @createDate 2025-09-12 16:19:30
*/
@Service
public class MathStudentStudyWeekHistoryServiceImpl extends ServiceImpl<MathStudentStudyWeekHistoryMapper, MathStudentStudyWeekHistory>
    implements MathStudentStudyWeekHistoryService{

}




