package com.joinus.study.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPassagesMapper;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.param.ReadingPassagesPageParam;
import com.joinus.study.model.param.ReadingPassagesUpdateBatchParam;
import com.joinus.study.model.param.ReadingPassagesUpdateParam;
import com.joinus.study.model.vo.ReadingGradeVO;
import com.joinus.study.model.vo.ReadingPassagesBackendVO;
import com.joinus.study.service.ReadingPassageQuestionSetsService;
import com.joinus.study.service.ReadingPassagesBackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service("readingPassagesBackendService")
@Slf4j
public class ReadingPassagesBackendServiceImpl extends ServiceImpl<ReadingPassagesMapper, ReadingPassages>
        implements ReadingPassagesBackendService {

    @Value("${reading-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net/api/edu-knowledge-hub}")
    private String readingHubHostUrl;

    @Resource
    private ReadingPassageQuestionSetsService questionSetsService;

    @Override
    public Page<ReadingPassagesBackendVO> pages(ReadingPassagesPageParam pageParam) {
        Page<ReadingPassagesBackendVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPassagesBackendVO> list = this.baseMapper.pages(page, pageParam);
        list.forEach(obj -> {
            obj.setGenre(GenreEnum.getByName(obj.getGenre()));
            //获取套题数量
            obj.setQuestionSetNumber(questionSetsService.getQuestionSetsCount(obj.getId()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ReadingPassagesUpdateBatchParam updateParam) {
        List<ReadingPassages> readingPassages = this.listByIds(updateParam.getIds());
        CommonResponse.ERROR.assertIsTrue(CollectionUtil.isNotEmpty(readingPassages), "未查询到题库文章信息");
        //调用教育知识平台项目接口
        String url = readingHubHostUrl + "/reading-passages";
        try {
            // 发送DELETE请求
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题库删除接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            String result = HttpRequest.delete(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库删除接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库删除接口，删除成功");
                } else {
                    log.info("调用教育知识平台项目题库删除接口，删除失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库删除接口，删除失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库删除接口异常，信息：{}" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库删除接口异常，信息：{}" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(ReadingPassagesUpdateBatchParam updateParam) {
        preCheck(updateParam, true);

        //调用教育知识平台项目接口

        try {
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            String url = readingHubHostUrl + "/reading-passages/disable";
            log.info("调用教育知识平台项目题库挂起接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库挂起接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库挂起接口，挂起成功");
                } else {
                    log.info("调用教育知识平台项目题库挂起接口，挂起失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库挂起接口，挂起失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库挂起接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库挂起接口异常，信息：" + e.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(ReadingPassagesUpdateBatchParam updateParam) {
        preCheck(updateParam, false);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading-passages/enable";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题库启用接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库启用接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库启用接口，启用成功");
                } else {
                    log.info("调用教育知识平台项目题库启用接口，启用失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库启用接口，启用失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库启用接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库启用接口异常，信息：" + e.getMessage());
        }
    }

    private void preCheck(ReadingPassagesUpdateBatchParam updateParam, boolean isDisable) {
        List<ReadingPassages> ReadingPassages = this.listByIds(updateParam.getIds());
        ReadingPassages.stream().forEach(item -> {
            if (isDisable) {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsEnabled(), 1), "文章标题：" + item.getTitle() + " 非启用状态,挂起失败");
            } else {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsEnabled(), 0), "文章标题：" + item.getTitle() + " 非挂起状态,启用失败");
            }
        });
    }

    @Override
    public ReadingPassagesBackendVO query(UUID id) {
        ReadingPassagesBackendVO passage = this.baseMapper.query(id);
        passage.setGenre(GenreEnum.getByName(passage.getGenre()));
        return passage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReadingPassagesUpdateParam updateParam) {
        ReadingPassages passage = this.getById(updateParam.getId());
        CommonResponse.ERROR.assertIsTrue(Objects.nonNull(passage), "未查询到题库文章信息");
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading-passages";
            // 构建JSON请求体
            log.info("调用教育知识平台项目题库修改接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(updateParam));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(updateParam))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库修改接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库修改接口，修改成功");
                } else {
                    log.info("调用教育知识平台项目题库修改接口，修改失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库修改接口，修改失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库修改接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库修改接口异常，信息：" + e.getMessage());
        }
    }

    @Override
    public List<ReadingGradeVO> listGradeSemesterUnit() {
        return this.baseMapper.listGradeSemesterUnit();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(ReadingPassagesUpdateBatchParam updateParam) {
        preCheckForAudit(updateParam, true);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading-passages/audit/pass";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题库审核通过接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库审核通过接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库审核通过接口，启用成功");
                } else {
                    log.info("调用教育知识平台项目题库审核通过接口，审核通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库审核通过接口，审核通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库审核通过接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库审核通过接口异常，信息：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditNoPass(ReadingPassagesUpdateBatchParam updateParam) {
        preCheckForAudit(updateParam, false);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading-passages/audit/no-pass";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题库审核不通过接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库审核不通过接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库审核不通过接口，启用成功");
                } else {
                    log.info("调用教育知识平台项目题库审核不通过接口，审核不通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库审核不通过接口，审核不通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库审核不通过接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库审核不通过接口异常，信息：" + e.getMessage());
        }
    }

    private void preCheckForAudit(ReadingPassagesUpdateBatchParam updateParam, boolean isPass) {
        List<ReadingPassages> ReadingPassages = this.listByIds(updateParam.getIds());
        ReadingPassages.stream().forEach(item -> {
            if (isPass) {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsAudit(), 0) || Objects.equals(item.getIsAudit(), 2), "文章标题：" + item.getTitle() + " 已审核通过,勿重复审核");
            } else {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsAudit(), 0) || Objects.equals(item.getIsAudit(), 1), "文章标题：" + item.getTitle() + " 已审核不通过,勿重复审核");
            }
        });
    }
}
