package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.ReadingPersonalCorrectionMapper;
import com.joinus.study.model.entity.ReadingPersonalCorrection;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.param.ReadingErrorCorrectingParam;
import com.joinus.study.model.param.ReadingPersonalCorrectionPageParam;
import com.joinus.study.model.vo.ReadingPersonalCorrectionVO;
import com.joinus.study.service.ReadingPersonalCorrectionService;
import com.joinus.study.service.ReadingPersonalPassageQuestionsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@AllArgsConstructor
@Service("readingPersonalCorrectionService")
@Slf4j
public class ReadingPersonalCorrectionServiceImpl extends ServiceImpl<ReadingPersonalCorrectionMapper, ReadingPersonalCorrection>
        implements ReadingPersonalCorrectionService {

    private ReadingPersonalPassageQuestionsService readingPersonalPassageQuestionsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ReadingErrorCorrectingParam param) {
        ReadingPersonalCorrection readingPersonalCorrection = BeanUtil.copyProperties(param, ReadingPersonalCorrection.class);
        readingPersonalCorrection.setCreatedAt(new Date());
        this.save(readingPersonalCorrection);
        readingPersonalPassageQuestionsService.setIsErrorCorrection(param.getPersonalPassageQuestionId(), 1, param.getResult());
    }

    @Override
    public Page<ReadingPersonalCorrectionVO> pages(ReadingPersonalCorrectionPageParam pageParam) {
        Page<ReadingPersonalCorrectionVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalCorrectionVO> list = this.baseMapper.pages(page, pageParam);
        list.forEach(obj -> {
            obj.setGenre(GenreEnum.getByName(obj.getGenre()));
        });
        page.setRecords(list);
        return page;
    }
}
