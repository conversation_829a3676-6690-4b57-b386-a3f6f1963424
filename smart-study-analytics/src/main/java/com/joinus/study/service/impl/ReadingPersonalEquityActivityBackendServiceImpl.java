package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.excel.ReadingPersonalEquityActivityCodeExcel;
import com.joinus.study.mapper.ReadingPersonalEquityActivityCodeMapper;
import com.joinus.study.mapper.ReadingPersonalEquityActivityMapper;
import com.joinus.study.model.entity.ReadingPersonalEquityActivity;
import com.joinus.study.model.entity.ReadingPersonalEquityActivityCode;
import com.joinus.study.model.entity.ReadingPersonalEquityStudent;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityDetailVo;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityPageItemVO;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityUsedRecordVo;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityVO;
import com.joinus.study.service.ReadingPersonalEquityActivityBackendService;
import com.joinus.study.service.ReadingPersonalEquityActivityCodeService;
import com.joinus.study.service.ReadingPersonalEquityStudentService;
import com.joinus.study.service.ReadingPersonalUserService;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("readingPersonalEquityActivityBackendService")
@Slf4j
public class ReadingPersonalEquityActivityBackendServiceImpl extends BaseServiceImpl<ReadingPersonalEquityActivityMapper, ReadingPersonalEquityActivity>
        implements ReadingPersonalEquityActivityBackendService {
    @Resource
    private ReadingPersonalEquityActivityCodeMapper codeMapper;
    @Resource
    private ReadingPersonalEquityActivityCodeService equityActivityCodeService;

    @Resource
    private ReadingPersonalEquityActivityCodeMapper readingPersonalEquityActivityCodeMapper;
    @Resource
    private ReadingPersonalUserService readingPersonalUserService;
    @Resource
    private ReadingPersonalEquityStudentService readingPersonalEquityStudentService;

    @Value("${qyl.host.url:}")
    private String qylHostUrl;
    @Value("${exchange.member.request:}")
    private String exchangeMemberRequest;

    @Override
    public Page<ReadingPersonalEquityActivityUsedRecordVo> usedRecordPages(ReadingPersonalEquityActivityUsedRecordPageParam pageParam) {
        Page<ReadingPersonalEquityActivityUsedRecordVo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalEquityActivityUsedRecordVo> list = baseMapper.selectUsedRecordPage(page, pageParam);
        page.setRecords(list);
        return page;
    }

    @Override
    public void delete(Long id) {
        //id不能为空
        CommonResponse.ERROR.assertNotNull(id, "活动id不能为空！");
        ReadingPersonalEquityActivity entity = this.getById(id);
        CommonResponse.ERROR.assertEquals(2, entity.getStatus(), "活动未失效，不能删除！");
        entity = new ReadingPersonalEquityActivity();
        entity.setId(id);
        entity.setDeletedAt(new Date());
        this.updateById(entity);
    }

    @Override
    public void invalid(Long id) {
        CommonResponse.ERROR.assertNotNull(id, "活动id不能为空！");
        ReadingPersonalEquityActivity entity = new ReadingPersonalEquityActivity();
        entity.setId(id);
        entity.setStatus(2);
        this.updateById(entity);
    }

    @Override
    public void invalidCode(Long id) {
        CommonResponse.ERROR.assertNotNull(id, "邀请码id不能为空！");
        ReadingPersonalEquityActivityCode activityCode = codeMapper.selectById(id);
        CommonResponse.ERROR.assertNotNull(activityCode, "邀请码id不存在！");
        //已使用不能作废
        CommonResponse.ERROR.assertIsFalse(activityCode.getStatus() == 2, "该邀请码已被使用不能作废操作！");
        ReadingPersonalEquityActivityCode entity = new ReadingPersonalEquityActivityCode();
        entity.setId(id);
        entity.setStatus(3);
        codeMapper.updateById(entity);
    }

    @Override
    public ReadingPersonalEquityActivityVO equityMemberFlag(Long studentId) {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空！");
        ReadingPersonalUser student = readingPersonalUserService.getByStudentId(studentId);
        CommonResponse.ERROR.assertNotNull(student, "学生信息不存在！");
        ReadingPersonalEquityStudent queryEntity = new ReadingPersonalEquityStudent();
        queryEntity.setStatus(1);
        queryEntity.setStudentId(studentId);
        List<ReadingPersonalEquityStudent> recordList = readingPersonalEquityStudentService.list(queryEntity);
        ReadingPersonalEquityActivityVO equityActivityVO = new ReadingPersonalEquityActivityVO();
        equityActivityVO.setIsEquityMember(CollectionUtil.isNotEmpty(recordList) ? 1 : 0);
        return equityActivityVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer exchange(ReadingPersonalEquityActivityParam param) throws BaseException {
        ReadingPersonalEquityActivityCode readingPersonalEquityActivityCode = equityActivityCodeService.getOneByCode(param.getCode());
        CommonResponse.ERROR.assertNotNull(readingPersonalEquityActivityCode, "兑换码不存在！");
        CommonResponse.ERROR.assertIsNotTrue(readingPersonalEquityActivityCode.getStatus() == 2, "邀请码已兑换~");
        CommonResponse.ERROR.assertIsNotTrue(readingPersonalEquityActivityCode.getStatus() == 3, "邀请码已作废~");
//        ReadingPersonalEquityActivityCode userPhone = equityActivityCodeService.getOneByUserPhone(param.getPhone());
//        CommonResponse.ERROR.assertIsNull(userPhone, "该用户已兑换过！不能再次兑换");
        ReadingPersonalEquityActivity readingPersonalEquityActivity = this.getById(readingPersonalEquityActivityCode.getActivityId());
        CommonResponse.ERROR.assertNotNull(readingPersonalEquityActivity, "该兑换码权益活动不存在！");
        CommonResponse.ERROR.assertIsNotTrue(readingPersonalEquityActivity.getStatus() == 2, "邀请码已失效~");
        CommonResponse.ERROR.assertIsNotTrue(readingPersonalEquityActivity.getStartDate().after(new Date())
                || readingPersonalEquityActivity.getEndDate().before(new Date()), "当前兑换码不在该权益活动日期范围内！");
        Integer equityDays = readingPersonalEquityActivity.getEquityDays();
//        ReadingPersonalUser readingPersonalUser = readingPersonalUserService.getByStudentId(param.getStudentId());
//        CommonResponse.ERROR.assertNotNull(readingPersonalUser, "学生信息不存在！");
//
//        String studentName = readingPersonalUser.getStudentName();
    /*    ReadingPersonalEquityStudent readingPersonalEquityStudent = new ReadingPersonalEquityStudent();
        //查询学生是否已有权益
        ReadingPersonalEquityStudent existingStudent = readingPersonalEquityStudentService.getOne(Wrappers.<ReadingPersonalEquityStudent>lambdaQuery()
                .eq(ReadingPersonalEquityStudent::getStudentId, param.getStudentId())
                .isNull(ReadingPersonalEquityStudent::getDeletedAt)
                .eq(ReadingPersonalEquityStudent::getStatus, 1));
        Date startDate = null;
        Date endDate = null;
        if (existingStudent != null) {
            readingPersonalEquityStudent.setId(existingStudent.getId());
            startDate = existingStudent.getEndDate();
        }else{
            startDate = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_MONTH, equityDays);
        endDate = calendar.getTime();
        readingPersonalEquityStudent.setStudentId(param.getStudentId());
        if(existingStudent == null){
            readingPersonalEquityStudent.setStartDate(startDate);
        }
        readingPersonalEquityStudent.setEndDate(endDate);
        readingPersonalEquityStudent.setStatus(1);
        readingPersonalEquityStudentService.saveOrUpdate(readingPersonalEquityStudent);*/
        // 根据科目获取对应的产品类型
        Integer productType = getProductTypeBySubject(readingPersonalEquityActivity.getSubject());
        CommonResponse.ERROR.assertNotNull(productType, "该兑换码对应的科目不存在！");
        // 兑换码使用兑换新逻辑 调用青于蓝相关开通
        ExchangeMemberParam queryParam = new ExchangeMemberParam();
        queryParam.setStudentId(param.getStudentId());
        queryParam.setProductType(productType);
        queryParam.setParentTelNum(param.getPhone());
        queryParam.setBusinessDays(equityDays);
        queryParam.setSource("REDEMPTION_CODE");
        log.info("兑换码兑换调用第三方接口参数：{}", queryParam);
        HttpResponse response = HttpUtil.createPost(qylHostUrl + exchangeMemberRequest)
                .contentType("application/json")
                .body(JSONUtil.toJsonStr(queryParam))
                .timeout(3000)
                .execute();
        log.info("调用兑换码兑换开通会员权益返回结果：{}", response.body());

        // 兑换成功后更新兑换码状态
        if (response.isOk()){
            readingPersonalEquityActivityCode.setUserName(param.getStudentName());
            readingPersonalEquityActivityCode.setStatus(2);
            readingPersonalEquityActivityCode.setUserPhone(param.getPhone());
            readingPersonalEquityActivityCode.setUsedAt(new Date());
            readingPersonalEquityActivityCode.setUpdatedAt(new Date());
            readingPersonalEquityActivityCodeMapper.updateById(readingPersonalEquityActivityCode);
        }
        return equityDays;
    }

    /**
     * @description: 根据科目获取对应的产品类型
     * @author: lifengxu
     * @date: 2025/7/18 9:14
     */
    private Integer getProductTypeBySubject(Integer subject) throws BaseException {
        if (subject == 1){
            return 5;
        }else if (subject == 2){
            return 6;
        }else if (subject == 4){
            return 7;
        }
        return null;
    }

    @Override
    public List<ReadingPersonalEquityActivityCodeExcel> downloadListInvitationCode(Long id) {
        CommonResponse.ERROR.assertNotNull(this.getById(id), "未查询到活动信息！");
        List<ReadingPersonalEquityActivityCodeExcel> list = baseMapper.downloadListInvitationCode(id);
        return list;
    }

    public synchronized List<String> createEquityActivityCode(Integer count) {
        // 参数校验
        CommonResponse.ERROR.assertIsFalse(count == null || count <= 0, "生成数量必须大于0");

        List<String> finalCodes = new ArrayList<>(count);
        int maxRetry = 5; // 最大重试次数防止死循环
        int currentRetry = 0;

        while (finalCodes.size() < count && currentRetry < maxRetry) {
            // 计算需要生成的数量
            int needCount = count - finalCodes.size();

            // 批量生成随机码
            List<String> newCodes = DataUtil.getRandomStringList(8, needCount);

            // 批量查询已存在的兑换码
            List<ReadingPersonalEquityActivityCode> existCodes = codeMapper.selectList(
                    Wrappers.<ReadingPersonalEquityActivityCode>query()
                            .in("code", newCodes)
            );

            // 提取存在的code集合
            Set<String> existCodeSet = existCodes.stream()
                    .map(ReadingPersonalEquityActivityCode::getCode)
                    .collect(Collectors.toSet());

            // 过滤掉已存在的code
            List<String> validCodes = newCodes.stream()
                    .filter(code -> !existCodeSet.contains(code))
                    .collect(Collectors.toList());

            // 添加有效codes
            finalCodes.addAll(validCodes);

            // 记录日志
            log.info("createEquityActivityCode生成进度：{}/{}, 本次有效数：{}",
                    finalCodes.size(), count, validCodes.size());

            currentRetry++;
        }

        log.info("createEquityActivityCode执行结束，预期：{}，实际：{}", count, finalCodes.size());
        CommonResponse.ERROR.assertIsFalse(finalCodes.size() < count, "兑换码生成失败，请稍后重试");

        return finalCodes;
    }

    @Override
    @Transactional
    public Integer createEquityActivity(ReadingPersonalEquityActivityOperateParam param) throws BaseException {
        log.info("createEquityActivity入参：{}", param);
        //验证参数
        CommonResponse.ERROR.assertNotNull(param.getCreator(), "生成失败！参数创建人不能为空");
        CommonResponse.ERROR.assertIsTrue(param.getCodeCount() >= 1 && param.getCodeCount() <= 9999, "生成失败！生成邀请码的数量应在1到9999之间");
        CommonResponse.ERROR.assertIsTrue(param.getEquityDays() >= 1 && param.getEquityDays() <= 365, "生成失败！设定体验卡的天数应在1到365之间");
        CommonResponse.ERROR.assertIsTrue(param.getSubject() > 0 && param.getSubject() < 5, "生成失败！设定科目应该在1到5之间");
        //校验活动名称和批次是否存在重复
        CommonResponse.ERROR.assertIsTrue(baseMapper.selectCount(
                Wrappers.<ReadingPersonalEquityActivity>lambdaQuery()
                        .eq(ReadingPersonalEquityActivity::getActivityTitle, param.getActivityTitle())
                        .eq(ReadingPersonalEquityActivity::getBatchName, param.getBatchName())
                        .isNull(ReadingPersonalEquityActivity::getDeletedAt)
        ) == 0, "生成失败！该批次下已存在该名称的批次");

        Date startDate = DateUtil.parse(param.getStartDate(), "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(param.getEndDate(), "yyyy-MM-dd HH:mm:ss");
        //封装活动数据
        ReadingPersonalEquityActivity activity = ReadingPersonalEquityActivity.builder()
                .activityTitle(param.getActivityTitle())
                .batchName(param.getBatchName())
                .codeCount(param.getCodeCount())
                .startDate(startDate)
                .endDate(endDate)
                .equityDays(param.getEquityDays())
                .creator(param.getCreator())
                .status(1)
                .activityNotice(param.getActivityNotice())
                .subject(param.getSubject())
                .build();
        this.save(activity);

        // 批量生成邀请码 考虑大数量导致的异常（分批次处理）
        final int batchSize = 1000; // 每批处理量
        int total = param.getCodeCount();
        int batches = (total + batchSize - 1) / batchSize; // 计算总批次数
        for (int i = 0; i < batches; i++) {
            // 计算当前批次数量
            int currentBatchSize = Math.min(batchSize, total - i * batchSize);
            // 分批生成兑换码
            List<String> codes = createEquityActivityCode(currentBatchSize);
            // 构建批量插入数据
            List<ReadingPersonalEquityActivityCode> insertCodes = codes.stream()
                    .map(code -> ReadingPersonalEquityActivityCode.builder()
                            .activityId(activity.getId())
                            .code(code)
                            .status(1)
                            .build())
                    .collect(Collectors.toList());
            // 批量插入（使用优化后的批量插入方式）
            equityActivityCodeService.saveBatch(insertCodes, batchSize);
            // 释放内存
            insertCodes.clear();
            codes.clear();
            log.info("createEquityActivity已生成兑换码进度：{}/{}", (i + 1) * batchSize, total);
        }
        return 1;
    }

    @Override
    public Integer editEquityActivity(ReadingPersonalEquityActivityOperateParam param) throws BaseException {
        log.info("editEquityActivity入参：{}", param);

        CommonResponse.ERROR.assertNotNull(param.getId(), "编辑失败！参数id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getActivityTitle(), "活动标题不能为空");
        CommonResponse.ERROR.assertNotNull(param.getBatchName(), "批次名称不能为空");
        CommonResponse.ERROR.assertNotNull(param.getEndDate(), "活动结束日期不能为空");
        CommonResponse.ERROR.assertNotNull(param.getActivityNotice(), "使用须知不能为空");

        ReadingPersonalEquityActivity oldEquityActivity = baseMapper.selectById(param.getId());
        CommonResponse.ERROR.assertNotNull(oldEquityActivity, "参数id异常！找不到对应的邀请码信息");

        //校验活动名称和批次是否存在重复
        CommonResponse.ERROR.assertIsTrue(baseMapper.selectCount(
                Wrappers.<ReadingPersonalEquityActivity>lambdaQuery()
                        .eq(ReadingPersonalEquityActivity::getActivityTitle, param.getActivityTitle())
                        .eq(ReadingPersonalEquityActivity::getBatchName, param.getBatchName())
                        .isNull(ReadingPersonalEquityActivity::getDeletedAt)
                        .ne(ReadingPersonalEquityActivity::getId, param.getId())
        ) == 0, "编辑失败！该批次下已存在该名称的批次");

        oldEquityActivity.setActivityTitle(param.getActivityTitle());
        oldEquityActivity.setBatchName(param.getBatchName());
        oldEquityActivity.setEndDate(DateUtil.parse(param.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
        oldEquityActivity.setActivityNotice(param.getActivityNotice());
        return baseMapper.updateById(oldEquityActivity);
    }

    @Override
    public ReadingPersonalEquityActivityDetailVo detailEquityActivity(Long id) throws BaseException {
        CommonResponse.ERROR.assertNotNull(id, "参数id不能为空");
        ReadingPersonalEquityActivity equityActivity = baseMapper.selectById(id);
        CommonResponse.ERROR.assertNotNull(equityActivity, "参数id异常！找不到对应的邀请码信息");
        ReadingPersonalEquityActivityDetailVo result = ReadingPersonalEquityActivityDetailVo.builder()
                .id(equityActivity.getId())
                .activityTitle(equityActivity.getActivityTitle())
                .batchName(equityActivity.getBatchName())
                .codeCount(equityActivity.getCodeCount())
                .startDate(DateUtil.format(equityActivity.getStartDate(), "yyyy-MM-dd HH:mm:ss"))
                .endDate(DateUtil.format(equityActivity.getEndDate(), "yyyy-MM-dd HH:mm:ss"))
                .equityDays(equityActivity.getEquityDays())
                .creator(equityActivity.getCreator())
                .activityNotice(equityActivity.getActivityNotice())
                .subject(equityActivity.getSubject())
                .build();
        return result;
    }

    @Override
    public String notice(String code) {
        ReadingPersonalEquityActivityCode personalEquityActivityCode = equityActivityCodeService.getOneByCode(code);
        Long activityId = personalEquityActivityCode.getActivityId();
        ReadingPersonalEquityActivity equityActivity = baseMapper.selectById(activityId);
        return equityActivity.getActivityNotice();
    }

    @Override
    public Page<ReadingPersonalEquityActivityPageItemVO> pages(ReadingPersonalEquityActivityPageParam pageParam) {
        Page<ReadingPersonalEquityActivityPageItemVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalEquityActivityPageItemVO> list = baseMapper.pages(page, pageParam);
        page.setRecords(list);
        return page;
    }
}
