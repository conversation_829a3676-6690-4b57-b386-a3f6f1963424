package com.joinus.study.service;

import com.joinus.study.model.KnowledgePointsData;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.vo.ActiveStudentVo;
import com.joinus.study.service.impl.ClassStatisticsServiceImpl;
import com.joinus.study.service.impl.ExamAnalysisReportServiceImpl;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 班级统计服务接口
 * @Author: anpy
 * @date: 2025/4/9
 */
public interface ClassStatisticsService {

    /**
     * 根据学生ID获取班级ID
     *
     * @param studentId 学生ID
     * @return 班级ID
     */
    ActiveStudentVo getClassIdByStudentId(Long studentId);

    /**
     * 更新班级考试统计信息
     *
     * @param examId                试卷ID
     */
    void updateClassExamStatistics(Long classId,Long gradeId,UUID examId);

    /**
     * 更新班级知识点统计信息
     *
     * @param examId 试卷ID
     */
    void updateClassKnowledgePointStatistics(ClassStatisticsServiceImpl.ExamStatisticsData statsData,
                                             List<ExamAnalyzeResult> analyzeResults,
                                             Long classId,Long gradeId,
                                             UUID examId, String type);

    /**
     * 获取班级考试统计信息
     *
     * @param classId 班级ID
     * @param examId 试卷ID
     * @return 班级考试统计信息
     */
    ClassExamStatistics getClassExamStatistics(Long classId, UUID examId);

    /**
     * 获取班级题目统计信息
     *
     * @param classId 班级ID
     * @param examId 试卷ID
     * @param questionId 题目ID
     * @return 班级题目统计信息
     */
    ClassExamQuestionStatistics getClassExamQuestionStatistics(Long classId, UUID examId, UUID questionId);

    /**
     * 获取班级知识点统计信息
     *
     * @param classId 班级ID
     * @param examId 试卷ID
     * @param knowledgePointId 知识点ID
     * @return 班级知识点统计信息
     */
    ClassKnowledgePointStatistics getClassKnowledgePointStatistics(Long classId, UUID examId, UUID knowledgePointId);

    /**
     * 生成考试场次ID
     * 根据试卷ID和时间戳生成唯一的考试场次ID
     * 
     * @param examId 试卷ID
     * @return 考试场次ID
     */
    String generateExamSession(UUID examId);
}
