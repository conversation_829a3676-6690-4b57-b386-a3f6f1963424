package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.param.ActivityAutoGenerateParam;
import com.joinus.study.model.vo.ActivityChapterVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【math_activity】的数据库操作service
 * @createDate 2025-06-13
 * @Entity com.joinus.study.model.entity.MathActivityEntity
 */
public interface MathActivityService extends IService<MathActivity> {
    // 获取当前活动
    MathActivity currentActivity(Long schoolId);

    void generateAuto(ActivityAutoGenerateParam param);

    List<ActivityChapterVo> listChapters(Integer grade);

    MathActivity getMathSummerHolidayActivity();

}
