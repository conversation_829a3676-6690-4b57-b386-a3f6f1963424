package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.EnglishPersonalWeakKnowledgePoint;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointCreateParam;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointPageParam;
import com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO;

import java.util.List;
import java.util.UUID;

public interface EnglishPersonalWeakKnowledgePointService extends IService<EnglishPersonalWeakKnowledgePoint> {
    /**
     * 薄弱知识点,分页
     *
     * @param pageParam
     * @return
     */
    Page<EnglishPersonalWeakKnowledgePointVO> pages(EnglishPersonalWeakKnowledgePointPageParam pageParam);

    void create(EnglishPersonalWeakKnowledgePointCreateParam createParam);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    EnglishPersonalWeakKnowledgePoint query(Long id);

    EnglishPersonalWeakKnowledgePoint selectByPointIdAndStudentId(UUID pointId, Long studentId);

    /**
     * 试卷诊断报告,薄弱知识点列表
     *
     * @param studentId
     * @param diagnoseRecordId
     * @return
     */
    List<EnglishPersonalWeakKnowledgePointVO> list(Long studentId, Long diagnoseRecordId);

}
