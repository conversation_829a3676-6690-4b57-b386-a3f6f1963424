package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.ReadingPassageQuestionsMapper;
import com.joinus.study.mapper.ReadingPassagesMapper;
import com.joinus.study.model.entity.ReadingPassageQuestions;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.entity.ReadingQuestionAnswers;
import com.joinus.study.model.enums.QuestionTypeEnum;
import com.joinus.study.model.enums.ReadingQuestionTypeEnum;
import com.joinus.study.model.param.ReadingPassageQuestionsPageParam;
import com.joinus.study.model.param.ReadingPassageQuestionsUpdateParam;
import com.joinus.study.model.param.ReadingPassagesUpdateBatchParam;
import com.joinus.study.model.vo.OptionsVo;
import com.joinus.study.model.vo.ReadingPassageQuestionsVO;
import com.joinus.study.service.ReadingPassageQuestionsBackendService;
import com.joinus.study.service.ReadingQuestionAnswersService;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service("readingPassagesQuestionsBackendService")
@Slf4j
public class ReadingPassageQuestionsBackendServiceImpl extends ServiceImpl<ReadingPassageQuestionsMapper, ReadingPassageQuestions>
        implements ReadingPassageQuestionsBackendService {

    @Value("${reading-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net/api/edu-knowledge-hub}")
    private String readingHubHostUrl;
    @Resource
    private ReadingPassagesMapper readingPassagesMapper;
    @Resource
    private ReadingQuestionAnswersService readingQuestionAnswersService;

    @Override
    public Page<ReadingPassageQuestionsVO> pages(ReadingPassageQuestionsPageParam pageParam) {
        Page<ReadingPassageQuestionsVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPassageQuestionsVO> list = readingPassagesMapper.pagesOfQuestions(page, pageParam);
        list.forEach(obj -> {
            //封装题目选项和答案
            Map<String, Object> question = DataUtil.processQuestionContent(obj.getContent());
            obj.setContent(question.get("questionContent").toString());
            obj.setOptions((JSONArray)question.get("options"));
            obj.setAnswer(DataUtil.parseAnswer(obj.getAnswer()));
            obj.setQuestionType(ReadingQuestionTypeEnum.getByName(obj.getQuestionType()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ReadingPassagesUpdateBatchParam updateParam) {
        List<ReadingPassageQuestions> readingPassageQuestions = this.listByIds(updateParam.getIds());
        CommonResponse.ERROR.assertIsTrue(CollectionUtil.isNotEmpty(readingPassageQuestions), "未查询到题库题目信息");
        //调用教育知识平台项目接口
        String url = readingHubHostUrl + "/reading";
        try {
            // 发送DELETE请求
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题目删除接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            String result = HttpRequest.delete(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题目删除接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题目删除接口，删除成功");
                } else {
                    log.info("调用教育知识平台项目题目删除接口，删除失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题目删除接口，删除失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题目删除接口异常，信息：{}" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题目删除接口异常，信息：{}" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(ReadingPassagesUpdateBatchParam updateParam) {
        preCheck(updateParam, true);

        //调用教育知识平台项目接口

        try {
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            String url = readingHubHostUrl + "/reading/disable";
            log.info("调用教育知识平台项目题目挂起接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题目挂起接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题目挂起接口，挂起成功");
                } else {
                    log.info("调用教育知识平台项目题目挂起接口，挂起失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题目挂起接口，挂起失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题目挂起接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题目挂起接口异常，信息：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(ReadingPassagesUpdateBatchParam updateParam) {
        preCheck(updateParam, false);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading/enable";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题目启用接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题目启用接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题目启用接口，启用成功");
                } else {
                    log.info("调用教育知识平台项目题目启用接口，启用失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题目启用接口，启用失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题目启用接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题目启用接口异常，信息：" + e.getMessage());
        }
    }

    private void preCheck(ReadingPassagesUpdateBatchParam updateParam, boolean isDisable) {
        List<ReadingPassageQuestions> readingPassageQuestions = this.listByIds(updateParam.getIds());
        readingPassageQuestions.stream().forEach(item -> {
            if (isDisable) {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsEnabled(), 1), "题目：" + item.getContent() + " 非启用状态,挂起失败");
            } else {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsEnabled(), 0), "题目：" + item.getContent() + " 非挂起状态,启用失败");
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ReadingPassageQuestionsUpdateParam updateParam) {
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading";
            // 构建JSON请求体
            this.constructContentAndAnswers(updateParam);

            log.info("调用教育知识平台项目题库题目修改接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(updateParam));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(updateParam))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题库题目修改接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题库题目修改接口，修改成功");
                } else {
                    log.info("调用教育知识平台项目题库题目修改接口，修改失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题库题目修改接口，修改失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题库题目修改接口异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题库题目修改接口异常，信息：" + e.getMessage());
        }
    }

    private void constructContentAndAnswers(ReadingPassageQuestionsUpdateParam updateParam) {
        ReadingPassageQuestions question = this.getById(updateParam.getId());
        CommonResponse.ERROR.assertNotNull(question, "未查询到题目信息");
        ReadingQuestionAnswers answers = readingQuestionAnswersService.getByQuestionId(updateParam.getId());
        CommonResponse.ERROR.assertNotNull(answers, "未查询到题目答案信息");
        //构建题目内容和答案Json格式
        String questionType = question.getQuestionType();
        if (StrUtil.isNotBlank(updateParam.getContent())) {
            //修改了题目内容，需根据题目题型处理
            JSONObject jsonContent = JSONUtil.parseObj(question.getContent());
            if (StrUtil.equals("CHOICE", questionType)) {
                //选择题型，选项必传
                CommonResponse.ERROR.assertCollNotNull(updateParam.getOptions(), "选择题题型，选项必传不能空");
                JSONArray options = updateParam.getOptions();
                jsonContent.set("选项", options);
            }
            jsonContent.set("问题", updateParam.getContent());
            updateParam.setContent(jsonContent.toString());
        }
        if (StrUtil.isNotBlank(updateParam.getAnswer())) {
            //修改了题目答案，需根据题目题型处理
            JSONObject jsonAnswer = JSONUtil.parseObj(answers.getAnswer());
            jsonAnswer.set("答案", updateParam.getAnswer());
            updateParam.setAnswer(jsonAnswer.toString());
        }
    }

    @Override
    public ReadingPassageQuestionsVO query(UUID id) {
        ReadingPassageQuestionsVO question = readingPassagesMapper.queryQuestion(id);
        if (ObjectUtil.isNotEmpty(question)) {
            //处理题目的内容和选项、答案的解析
            Map<String, Object> questionMap = DataUtil.processQuestionContent(question.getContent());
            question.setContent(questionMap.get("questionContent").toString());
            question.setOptions((JSONArray)questionMap.get("options"));
            question.setAnswer(DataUtil.parseAnswer(question.getAnswer()));
            question.setQuestionType(ReadingQuestionTypeEnum.getByName(question.getQuestionType()));
        }
        return question;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPass(ReadingPassagesUpdateBatchParam updateParam) {
        preCheckForAudit(updateParam, true);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading/audit/pass";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题目审核通过接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题目审核通过接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题目审核通过接口，审核通过成功");
                } else {
                    log.info("调用教育知识平台项目题目审核通过接口，审核通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题目审核通过接口，审核通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题目审核通过异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题目审核通过接口异常，信息：" + e.getMessage());
        }
    }

    private void preCheckForAudit(ReadingPassagesUpdateBatchParam updateParam, boolean isPass) {
        List<ReadingPassageQuestions> readingPassageQuestions = this.listByIds(updateParam.getIds());
        readingPassageQuestions.stream().forEach(item -> {
            if (isPass) {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsAudit(), 0) || Objects.equals(item.getIsAudit(), 2), "题目：" + item.getContent() + " 已审核通过,勿重复审核");
            } else {
                CommonResponse.ERROR.assertIsTrue(Objects.equals(item.getIsAudit(), 0) || Objects.equals(item.getIsAudit(), 1), "题目：" + item.getContent() + " 已审核不通过,勿重复审核");
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditNoPass(ReadingPassagesUpdateBatchParam updateParam) {
        preCheckForAudit(updateParam, false);
        //调用教育知识平台项目接口
        try {
            String url = readingHubHostUrl + "/reading/audit/no-pass";
            // 构建JSON请求体
            String[] strings = updateParam.getIds().stream()
                    .map(UUID::toString)
                    .toArray(String[]::new);
            log.info("调用教育知识平台项目题目审核不通过接口, url:{},请求参数：{}", url, JSONUtil.toJsonStr(strings));
            // 发送PUT请求
            String result = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(strings))
                    .timeout(5000)
                    .execute()
                    .body();
            log.info("调用教育知识平台项目题目审核不通过接口 返回结果：" + result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if (jsonObject.getInt("code") == 200 || jsonObject.getInt("code") == 204) {
                    log.info("调用教育知识平台项目题目审核不通过接口，审核通过成功");
                } else {
                    log.info("调用教育知识平台项目题目审核不通过接口，审核不通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                    CommonResponse.assertError("调用教育知识平台项目题目审核不通过接口，审核不通过失败，状态码：" + jsonObject.getInt("code") + "，响应：" + result);
                }
            }
        } catch (Exception e) {
            log.info("调用教育知识平台项目题目审核不通过异常，信息：" + e.getMessage());
            CommonResponse.assertError("调用教育知识平台项目题目审核不通过接口异常，信息：" + e.getMessage());
        }
    }
}
