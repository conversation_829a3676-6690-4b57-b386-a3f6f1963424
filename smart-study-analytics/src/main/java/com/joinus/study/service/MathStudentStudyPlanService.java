package com.joinus.study.service;

import com.joinus.study.model.entity.MathStudentStudyPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.vo.MathStudentStudyPlanVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_student_study_plan(学生学习计划)】的数据库操作Service
* @createDate 2025-09-12 16:19:30
*/
public interface MathStudentStudyPlanService extends IService<MathStudentStudyPlan> {

    MathStudentStudyPlanVo getStudentCurrentPlan(Long studentId);

    List<MathStudentStudyPlanVo> getStudentCurrentPlanList(Long studentId);
}
