package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.study.mapper.MathStudentKnowledgePointMapper;
import com.joinus.study.model.entity.MathStudentKnowledgePoint;
import com.joinus.study.model.enums.KnowledgePointPptHtmlsStateEnum;
import com.joinus.study.service.MathStudentKnowledgePointService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class MathStudentKnowledgePointServiceImpl extends ServiceImpl<MathStudentKnowledgePointMapper, MathStudentKnowledgePoint>
    implements MathStudentKnowledgePointService {

    @Override
    public void addOrUpdatePptHtmlsCompleted(Long student, UUID knowledgePointId) {
        List<MathStudentKnowledgePoint> studentKnowledgePoints = lambdaQuery().eq(MathStudentKnowledgePoint::getStudentId, student)
                .eq(MathStudentKnowledgePoint::getKnowledgePointId, knowledgePointId)
                .list();
        if (CollUtil.isEmpty(studentKnowledgePoints)) {
            MathStudentKnowledgePoint studentKnowledgePoint = MathStudentKnowledgePoint.builder()
                    .studentId(student)
                    .knowledgePointId(knowledgePointId)
                    .pptHtmlsCompleted(KnowledgePointPptHtmlsStateEnum.COMPLETED)
                    .build();
            baseMapper.insert(studentKnowledgePoint);
        } else if (studentKnowledgePoints.size() == 1) {
            MathStudentKnowledgePoint studentKnowledgePoint = new MathStudentKnowledgePoint();
            studentKnowledgePoint.setPptHtmlsCompleted(KnowledgePointPptHtmlsStateEnum.COMPLETED);
            baseMapper.updateById(studentKnowledgePoint);
        } else {
            throw new BaseException("学生知识点数据异常");
        }
    }

    @Override
    public List<MathStudentKnowledgePoint> listByStudentId(Long studentId, List<UUID> knowledgePointIds) {
        return lambdaQuery().eq(MathStudentKnowledgePoint::getStudentId, studentId)
                .in(MathStudentKnowledgePoint::getKnowledgePointId, knowledgePointIds)
                .list();
    }
}
