package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishExamDiagnoseQuestionMapper;
import com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO;
import com.joinus.study.model.entity.EnglishExamDiagnoseQuestion;
import com.joinus.study.model.vo.EnglishExamReportKnowledgePointVO;
import com.joinus.study.model.vo.EnglishExamReportQuestionTypeVO;
import com.joinus.study.service.EnglishExamDiagnoseQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/2 10:18
 */
@Service
@Slf4j
public class EnglishExamDiagnoseQuestionServiceImpl extends ServiceImpl<EnglishExamDiagnoseQuestionMapper, EnglishExamDiagnoseQuestion> implements EnglishExamDiagnoseQuestionService {
    @Override
    public List<EnglishExamDiagnoseQuestion> listByRecordId(Long recordId) {
        return lambdaQuery()
                .eq(EnglishExamDiagnoseQuestion::getRecordId, recordId)
                .orderByDesc(EnglishExamDiagnoseQuestion::getId)
                .list();
    }

    @Override
    public List<EnglishExamDiagnoseQuestion> listQuestionByRecordId(Long recordId) {
        return lambdaQuery()
                .eq(EnglishExamDiagnoseQuestion::getRecordId, recordId)
                .isNotNull(EnglishExamDiagnoseQuestion::getQuestionId)
                .orderByDesc(EnglishExamDiagnoseQuestion::getId)
                .list();
    }

    @Override
    public Integer queryKnowledgePointNumber(Long recordId) {
        return this.baseMapper.queryKnowledgePointNumber(recordId);
    }

    @Override
    public Integer queryWeakKnowledgeNumber(Long recordId) {
        return this.baseMapper.queryWeakKnowledgeNumber(recordId);
    }

    @Override
    public List<EnglishExamReportQuestionTypeVO> listQuestionType(Long recordId) {
        return this.baseMapper.listQuestionType(recordId);
    }

    @Override
    public List<EnglishExamReportKnowledgePointVO> listKnowledgePoints(Long recordId) {
        return this.baseMapper.listKnowledgePoints(recordId);
    }

    @Override
    public List<EnglishExamDiagnoseQuestion> listMasterQuestions(UUID examId) {
        return this.baseMapper.listMasterQuestions(examId);
    }

    @Override
    public List<String> listQuestionTypeByPointId(Long recordId, UUID pointId) {
        return this.baseMapper.listQuestionTypeByPointId(recordId, pointId);
    }

    @Override
    public Integer queryCountByPointId(Long recordId, UUID pointId) {
        return this.baseMapper.queryCountByPointId(recordId, pointId);
    }

    @Override
    public Integer queryWrongCountByPointId(Long recordId, UUID pointId) {
        return this.baseMapper.queryWrongCountByPointId(recordId, pointId);
    }

    @Override
    public List<EnglishExamDiagnoseQuestionDTO> listByRecordIdsAndKnowledgePointId(List<Long> diagnoseRecordIds, UUID knowledgePointId) {
        return this.baseMapper.listByRecordIdsAndKnowledgePointId(diagnoseRecordIds, knowledgePointId);
    }

    @Override
    public List<EnglishExamDiagnoseQuestionDTO> listByRecordIdAndKnowledgePointId(Long diagnoseRecordId, UUID knowledgePointId) {
        return this.baseMapper.listByRecordIdAndKnowledgePointId(diagnoseRecordId, knowledgePointId);
    }
}
