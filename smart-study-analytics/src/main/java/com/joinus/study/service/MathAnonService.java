package com.joinus.study.service;

import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.enums.AppTypeEnum;
import com.joinus.study.model.enums.ExamSourceType;

import java.util.UUID;

public interface MathAnonService {

    JumpUrlBo queryJumpUrlForMath(String qrStr, Long studentId, AppTypeEnum appType);

    JumpUrlBo queryJumpUrlForChinese(String qrStr, String keyboard, AppTypeEnum appType);

    String getJumpUrlByExamSource(ExamSourceType examSource, UUID examId, AppTypeEnum appType);
}
