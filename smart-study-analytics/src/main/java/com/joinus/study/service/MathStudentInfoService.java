package com.joinus.study.service;

import com.joinus.study.model.entity.MathStudentInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.MathStudentInfoVo;

/**
* <AUTHOR>
* @description 针对表【math_student_info(数学学生信息表)】的数据库操作Service
* @createDate 2025-08-13 14:48:09
*/
public interface MathStudentInfoService extends IService<MathStudentInfo> {

    MathStudentInfoVo getStudentInfo(Long studentId);

    MathStudentInfoVo addStudentInfo(Long studentId, PublisherEnum publisher, Integer grade);

    MathStudentInfoVo updateStudentInfo(Long studentId, PublisherEnum publisher, Integer grade);
}
