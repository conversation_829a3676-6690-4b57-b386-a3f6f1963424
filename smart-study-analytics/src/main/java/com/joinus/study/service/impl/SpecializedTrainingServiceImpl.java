package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.exception.Assert;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.util.PerformanceMonitor;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.DataUtil;
import com.joinus.study.utils.PDFToImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 专项训练服务
 * @Author: anpy
 * @date: 2025/4/28 17:34
 */
@Service
@Slf4j
public class SpecializedTrainingServiceImpl implements SpecializedTrainingService {

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private MathExamsMapper mathExamsMapper;
    @Resource
    private MathQuestionsMapper  mathQuestionsMapper;
    @Value("${ijx.ink.q.math:https://ijx.ink/q/math/}")
    private String inkQMath;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private GradeService gradeService;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;
    @Resource
    private MathActivityStudentMapper mathActivityStudentMapper;
    @Resource
    private MathActivityStudentService mathActivityStudentService;
    @Resource
    private QuestionKnowledgePointService questionKnowledgePointService;
    @Resource
    private MathSyncLearningTestService mathSyncLearningTestService;
    @Resource
    private QuestionKnowledgePointMapper questionKnowledgePointMapper;
    @Resource
    private StudyRecordMapper studyRecordMapper;
    @Resource
    private EduKnowLedgeHubBusinessService businessService;

    private static Pattern imgTagPattern = Pattern.compile("<img[^>]*src=\"\"[^>]*>");
    private static Pattern dataS3EnumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
    private static Pattern dataS3KeyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");
    @Autowired
    @Lazy
    private PersonalExamService personalExamService;

    @Override
    public SpecializedTrainingNewResultVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param) {
        try {
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVo createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionType(param);
            CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
            CommonResponse.ERROR.assertCollNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

            // 创建新的返回结果对象
            SpecializedTrainingNewResultVo newResultVo = new SpecializedTrainingNewResultVo();

            // 初始化统计数据
            int totalQuestions = 0;
            int totalQuestionTypes = 0;
            int totalKnowledgePoints = 0;

            QuestionSortNo sortNo = new QuestionSortNo();

            // 处理知识点和题型数据
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData())) {
                // 创建知识点列表
                List<SpecializedTrainingNewResultVo.KnowledgePointDTO> knowledgePoints = new ArrayList<>();
                // 创建题型列表
                List<SpecializedTrainingNewResultVo.QuestionTypeDTO> questionTypes = new ArrayList<>();
                // 用于去重题型的Map
                Map<String, SpecializedTrainingNewResultVo.QuestionTypeDTO> questionTypeMap = new HashMap<>();

                // 遍历原始数据，提取知识点和题型
                for (CreateQuestionByKnowledgeVo.DataDTO dataDTO : createQuestionByKnowledgeVo.getData()) {
                    // 处理知识点
                    if (StringUtils.isNotBlank(dataDTO.getKnowledgePointId())) {
                        totalKnowledgePoints++;

                        SpecializedTrainingNewResultVo.KnowledgePointDTO knowledgePointDTO = new SpecializedTrainingNewResultVo.KnowledgePointDTO();
                        knowledgePointDTO.setKnowledgePointId(dataDTO.getKnowledgePointId());
                        knowledgePointDTO.setKnowledgePointName(dataDTO.getKnowledgePointName());

                        // 处理知识点下的题目
                        List<UUID> questionIds = new ArrayList<>();
                        if (CollUtil.isNotEmpty(dataDTO.getQuestionIds())) {
                            for (String id : dataDTO.getQuestionIds()) {
                                questionIds.add(UUID.fromString(id));
                            }
                        }
                        knowledgePointDTO.setQuestionIds(questionIds);
                        knowledgePointDTO.setQuestionList(getQuestionList(questionIds, sortNo));

                        // 统计知识点下的题目数量
                        if (CollUtil.isNotEmpty(knowledgePointDTO.getQuestionList())) {
                            for (SpecializedTrainingQuestionDataVo questionVo : knowledgePointDTO.getQuestionList()) {
                                if (questionVo != null && questionVo.getList() != null) {
                                    totalQuestions += questionVo.getList().size();
                                }
                            }
                        }

                        knowledgePoints.add(knowledgePointDTO);
                    }

                    // 处理题型
                    if (CollUtil.isNotEmpty(dataDTO.getQuestionTypes())) {
                        for (CreateQuestionByKnowledgeVo.DataDTO.QuestionTypesDTO typeDTO : dataDTO.getQuestionTypes()) {
                            if (typeDTO != null && StringUtils.isNotBlank(typeDTO.getQuestionTypeId())) {
                                totalQuestionTypes++;

                                // 处理题型，使用Map进行去重
                                SpecializedTrainingNewResultVo.QuestionTypeDTO questionTypeDTO = questionTypeMap.get(typeDTO.getQuestionTypeId());
                                if (questionTypeDTO == null) {
                                    // 如果Map中不存在，创建新的题型对象
                                    questionTypeDTO = new SpecializedTrainingNewResultVo.QuestionTypeDTO();
                                    questionTypeDTO.setQuestionTypeId(typeDTO.getQuestionTypeId());
                                    questionTypeDTO.setQuestionTypeName(typeDTO.getQuestionTypeName());
                                    questionTypeDTO.setQuestionIds(new ArrayList<>());
                                    questionTypeDTO.setQuestionList(new ArrayList<>());
                                    questionTypeMap.put(typeDTO.getQuestionTypeId(), questionTypeDTO);
                                }

                                // 处理题型下的题目
                                List<UUID> questionIds = new ArrayList<>();
                                if (CollUtil.isNotEmpty(typeDTO.getQuestionIds())) {
                                    for (String id : typeDTO.getQuestionIds()) {
                                        UUID uuid = UUID.fromString(id);
                                        questionIds.add(uuid);
                                        questionTypeDTO.getQuestionIds().add(uuid);
                                    }
                                }

                                // 获取题目列表并添加到题型中
                                List<SpecializedTrainingQuestionDataVo> questionList = getQuestionList(questionIds, sortNo);
                                if (CollUtil.isNotEmpty(questionList)) {
                                    questionTypeDTO.getQuestionList().addAll(questionList);

                                    // 统计题型下的题目数量
                                    for (SpecializedTrainingQuestionDataVo questionVo : questionList) {
                                        if (questionVo != null && questionVo.getList() != null) {
                                            totalQuestions += questionVo.getList().size();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 设置知识点列表
                newResultVo.setKnowledgePoint(knowledgePoints);
                // 将题型Map转换为列表并设置
                questionTypes.addAll(questionTypeMap.values());
                newResultVo.setQuestionTypes(questionTypes);
            }

            // 设置统计结果
            newResultVo.setTotalQuestions(totalQuestions);
            newResultVo.setTotalQuestionTypes(totalQuestionTypes);
            newResultVo.setTotalKnowledgePoints(totalKnowledgePoints);

            return newResultVo;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionType 方法执行出错，参数: {}", param, e);
            throw e;
        }
    }

    @Override
    public SpecializedTrainingNewResultVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param) {
        try {
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionTypeV2(param);
            QuestionSortNo sortNo = new QuestionSortNo();
            // 创建返回对象
            SpecializedTrainingNewResultVoV2 newResultVo = new SpecializedTrainingNewResultVoV2();

            // 只计算三个字段：知识点数量、题型数量和题目数量
            int totalKnowledgePoints = 0;
            int totalQuestionTypes = 0;
            int totalQuestions = 0;
            int totalDuration = 30;
            Map<String,Integer> questionTypeMap = new HashMap<>();
            // 1. 计算知识点数量
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
                totalKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().size();
            }

            // 2. 计算题型数量
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
                totalQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().size();
            }

            // 3. 计算题目数量（去重）
            Set<UUID> allQuestionIds = new HashSet<>();

            // 添加知识点中的题目ID
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
                for (CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO knowledgePointDTO : createQuestionByKnowledgeVo.getData().getKnowledgePoints()) {
                    if (CollUtil.isNotEmpty(knowledgePointDTO.getQuestions())) {
                        List<SpecializedTrainingQuestionDataVo> questionList = getQuestionListV2(knowledgePointDTO.getQuestions(), sortNo);
                        for (SpecializedTrainingQuestionDataVo questionVo : questionList){
                            if(questionTypeMap.get(questionVo.getType()) != null){
                                questionTypeMap.put(questionVo.getType(), questionTypeMap.get(questionVo.getType()) + questionVo.getList().size());
                            } else {
                                questionTypeMap.put(questionVo.getType(), questionVo.getList().size());
                            }
                        }
                        knowledgePointDTO.setQuestionList(questionList);
                        knowledgePointDTO.setQuestionIds(knowledgePointDTO.getQuestions().stream().map(CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo::getId).collect(Collectors.toList()));
                        allQuestionIds.addAll(knowledgePointDTO.getQuestionIds());
                    }
                }
            }

            // 添加题型中的题目ID
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
                for (CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO questionTypeDTO : createQuestionByKnowledgeVo.getData().getQuestionTypes()) {
                    if (CollUtil.isNotEmpty(questionTypeDTO.getQuestions())) {
                        List<SpecializedTrainingQuestionDataVo> questionList = getQuestionListV2(questionTypeDTO.getQuestions(), sortNo);
                        for (SpecializedTrainingQuestionDataVo questionVo : questionList){
                            if(questionTypeMap.get(questionVo.getType()) != null){
                                questionTypeMap.put(questionVo.getType(), questionTypeMap.get(questionVo.getType()) + questionVo.getList().size());
                            } else {
                                questionTypeMap.put(questionVo.getType(), questionVo.getList().size());
                            }
                        }
                        questionTypeDTO.setQuestionList(questionList);
                        questionTypeDTO.setQuestionIds(questionTypeDTO.getQuestions().stream().map(CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo::getId).collect(Collectors.toList()));
                        allQuestionIds.addAll(questionTypeDTO.getQuestionIds());
                    }
                }
            }

            // 题目总数就是去重后的题目ID数量
            totalQuestions = allQuestionIds.size();

            // 设置统计结果
            newResultVo.setTotalQuestions(totalQuestions);
            newResultVo.setTotalQuestionTypes(totalQuestionTypes);
            newResultVo.setTotalKnowledgePoints(totalKnowledgePoints);
            newResultVo.setTotalDuration(totalDuration);
            newResultVo.setKnowledgePoint(createQuestionByKnowledgeVo.getData().getKnowledgePoints());
            newResultVo.setQuestionTypes(createQuestionByKnowledgeVo.getData().getQuestionTypes());

            return newResultVo;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV2 方法执行出错，参数: {}", param, e);
            throw e;
        }
    }

    private List<SpecializedTrainingQuestionDataVo> getQuestionList(List<UUID> questionIds, QuestionSortNo finalSortNo) {
        if (CollUtil.isEmpty(questionIds)) {
            return new ArrayList<>();
        }

        List<SpecializedTrainingQuestionVo> collect = questionIds.stream()
                .map(questionId -> {
                    Integer sortNo = finalSortNo.getSortNo();
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(questionId);
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setSortNo(++sortNo);
                    finalSortNo.setSortNo(sortNo);


                    return specializedTrainingQuestionVo;
                })
                .collect(Collectors.toList());
        //collect 根据questionType分组
        List<SpecializedTrainingQuestionDataVo> collect1 = collect.stream()
                .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                .entrySet().stream()
                .map(entry -> {
                    SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                    specializedTrainingQuestionDataVo.setType(entry.getKey());
                    specializedTrainingQuestionDataVo.setList(entry.getValue());
                    return specializedTrainingQuestionDataVo;
                }).collect(Collectors.toList());
        return collect1;
    }

    private List<SpecializedTrainingQuestionDataVo> getQuestionListV2(List<CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo> questions, QuestionSortNo finalSortNo) {
        if (CollUtil.isEmpty(questions)) {
            return new ArrayList<>();
        }

        List<SpecializedTrainingQuestionVo> collect = questions.stream()
                .map(question -> {
                    Integer sortNo = finalSortNo.getSortNo();
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setSortNo(++sortNo);
                    specializedTrainingQuestionVo.setIsPastExamPaper(question.getIsPastExamPaper());
                    specializedTrainingQuestionVo.setPastExamPaperRegion(question.getPastExamPaperRegion());
                    specializedTrainingQuestionVo.setPastExamPaperYear(question.getPastExamPaperYear());
                    finalSortNo.setSortNo(sortNo);
                    return specializedTrainingQuestionVo;
                })
                .collect(Collectors.toList());
        //collect 根据questionType分组
        List<SpecializedTrainingQuestionDataVo> collect1 = collect.stream()
                .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                .entrySet().stream()
                .map(entry -> {
                    SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                    specializedTrainingQuestionDataVo.setType(entry.getKey());
                    specializedTrainingQuestionDataVo.setList(entry.getValue());
                    return specializedTrainingQuestionDataVo;
                }).collect(Collectors.toList());
        return collect1;
    }

    @Override
    public String decodeContentV2(String content) {
        if (content == null) {
            return "";
        }

        Matcher imgMatcher = imgTagPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (imgMatcher.find()) {
            String tag = imgMatcher.group();

            // 提取 data-s3-enum 属性（如果存在）
            Matcher enumMatcher = dataS3EnumPattern.matcher(tag);
            String s3Enum = enumMatcher.find() ? enumMatcher.group(1) : "MINIO_EDU_KNOWLEDGE_HUB";  // 默认 aliyun

            OssEnum ossEnum = OssEnum.valueOf(s3Enum);

            // 提取 data-s3-key 属性（必须有）
            Matcher keyMatcher = dataS3KeyPattern.matcher(tag);
            if (!keyMatcher.find()) {
                continue; // 或根据需求作处理
            }
            String s3Key = keyMatcher.group(1);

            // 根据修正后的 s3Enum 和 s3Key 生成新的 src
            PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(PresignedUrlParam.builder()
                    .ossEnum(ossEnum)
                    .ossKey(s3Key)
                    .build());
            String newTag = tag.replace("src=\"\"", "src=\"" + presignedUrlDto.getData().getPresignedUrl() + "\"");

            imgMatcher.appendReplacement(sb, newTag.replace("$", "\\$")); // 注意替换 $ 符号避免干扰
        }
        imgMatcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public ExamChapterQuestionDetailDto createQuestionByKnowledgeAndQuestionTypeV3(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionTypeV2(param);
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();
            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();

            List<UUID> questionIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(knowledgePointsQuestionList)) {
                knowledgePointsQuestionList.stream()
                        .map(CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO::getQuestions).flatMap(List::stream).forEach(question -> {
                            questionIds.add(question.getId());
                        });
            }
            if (CollUtil.isNotEmpty(questionTypesQuestionList)) {
                questionTypesQuestionList.stream()
                        .map(CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO::getQuestions)
                        .flatMap(List::stream).forEach(question -> {
                            questionIds.add(question.getId());
                        });
            }
            CommonResponse.ERROR.assertIsTrue(CollectionUtil.isNotEmpty(questionIds), "推荐题目失败");
            List<QuestionInfoByIdDto> questionInfoByIds = personalExamMapper.getQuestionInfoByIds(questionIds);
            if(CollUtil.isEmpty(questionInfoByIds)){
                return null;
            }
            Map<UUID, QuestionInfoByIdDto> idDtoMap = questionInfoByIds.stream().collect(Collectors.toMap(QuestionInfoByIdDto::getQuestionId, dto -> dto));
            knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {
                if (examChapterQuestionDetailDto.getGrade() == null || knowledgePointsDTO.getGrade() > examChapterQuestionDetailDto.getGrade()) {
                    examChapterQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                }
                examChapterQuestionDetailDto.setPublisher(PublisherEnum.valueOf(knowledgePointsDTO.getPublisher()));

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher())+formatPublisher(knowledgePointsDTO.getGrade(),  knowledgePointsDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getKnowledgePointId()));
                pointsDTOSet.add(knowledgePoint);

                knowledgePointsDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = idDtoMap.get(question.getId());
                    if(questionInfoById == null){
                        return;
                    }
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                    if (StringUtils.isNotBlank(questionInfoById.getStructuredContent())) {
                        specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfoById.getStructuredContent()), ChoiceContentVo.class));
                    }
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            questionTypesQuestionList.forEach(questionTypesDTO -> {
                if (examChapterQuestionDetailDto.getGrade() == null || questionTypesDTO.getGrade() > examChapterQuestionDetailDto.getGrade()) {
                    examChapterQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                }
                examChapterQuestionDetailDto.setPublisher(PublisherEnum.valueOf(questionTypesDTO.getPublisher()));

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(questionTypesDTO.getPublisher()) +formatPublisher(questionTypesDTO.getGrade(),  questionTypesDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                questionType.setSectionId(questionTypesDTO.getSectionId());
                questionTypesDTOSet.add(questionType);

                questionTypesDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = idDtoMap.get(question.getId());
                    if(questionInfoById == null){
                        return;
                    }
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                    if (StringUtils.isNotBlank(questionInfoById.getStructuredContent())) {
                        specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfoById.getStructuredContent()), ChoiceContentVo.class));
                    }
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            bookSet.stream().forEach(bookQuestionDetailDto -> {
                List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        chapterSet.stream().filter(chapterQuestionDetailDto -> chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList());
                chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                    List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList = sectionSet.stream()
                            .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId())).collect(Collectors.toList());
                    sectionList.stream().forEach(sectionQuestionDetailDto -> {
                        List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS = pointsDTOSet.stream().filter(
                                knowledgePointsDTO -> knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        pointsDTOS.stream().forEach(pointsDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getKnowledgePointId() !=null
                                            && question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId())
                                            && question.getSectionId().equals(pointsDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            pointsDTO.setQuestionList(questionList);
                        });
                        pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto
                                .KnowledgePointsDTO::getKnowledgePointId));
                        sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                        List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList = questionTypesDTOSet.stream().filter(
                                questionTypesDTO -> questionTypesDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getQuestionTypeId() !=null
                                            && question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId())
                                            && question.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            questionTypesDTO.setQuestionList(questionList);
                        });
                        questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                        sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                    });
                    sectionList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                    chapterQuestionDetailDto.setSectionList(sectionList);
                });
                chapterQuestionDetailDtos.sort(Comparator.comparing(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
            });
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            bookList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                                    .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester)
            );
            Map<String,Integer> questionTypeMap = new HashMap<>();
            questionSet.stream().forEach(question -> {
                if (question.getChoiceContent()==null && question.getQuestionType().contains("选择")){
                    question.setChoiceContent(DataUtil.formatChoiceContent(question.getQuestionContent()));
                }
                if (questionTypeMap.get(question.getQuestionType()) !=null){
                    questionTypeMap.put(question.getQuestionType(),questionTypeMap.get(question.getQuestionType())+1);
                } else {
                    questionTypeMap.put(question.getQuestionType(),1);
                }
            });
            examChapterQuestionDetailDto.setQuestionTypeDistributions(questionTypeMap.entrySet().stream()
                    .map(entry -> {
                        SpecializedTrainingNewResultVoV2.QuestionTypeDistribution questionTypeDistribution = new SpecializedTrainingNewResultVoV2.QuestionTypeDistribution();
                        questionTypeDistribution.setQuestionType(QuestionTypeEnum.getEnumByDesc(entry.getKey()).name());
                        questionTypeDistribution.setQuestionTypeName(entry.getKey());
                        questionTypeDistribution.setQuestionCount(entry.getValue());
                        return questionTypeDistribution;
                    }).collect(Collectors.toList()));
            examChapterQuestionDetailDto.setBookList(bookList);
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalDuration(questionSet.size() * 6);
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            examChapterQuestionDetailDto.setQuestionList(new ArrayList<>(questionSet));
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV3 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public ExamChapterQuestionDetailDto createHolidayTrainingQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = new CreateQuestionByKnowledgeVoV2();
            // 调用 AI 生成题目
            if (param.getSectionId() != null) {
                createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingSectionQuestion(param);
            }
            if (param.getChapterId() != null) {
                createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingChapterQuestion(param);
            }
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();
            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();
            knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {
                examChapterQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                examChapterQuestionDetailDto.setPublisher(PublisherEnum.valueOf(knowledgePointsDTO.getPublisher()));
                examChapterQuestionDetailDto.setBookVolume(BookVolumeEnum.fromVolumeNum(knowledgePointsDTO.getSemester()));

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher())+formatPublisher(knowledgePointsDTO.getGrade(),  knowledgePointsDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getKnowledgePointId()));
                pointsDTOSet.add(knowledgePoint);

                knowledgePointsDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                    if (StringUtils.isNotBlank(questionInfoById.getStructuredContent())) {
                        specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfoById.getStructuredContent()), ChoiceContentVo.class));
                    }
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            questionTypesQuestionList.forEach(questionTypesDTO -> {
                examChapterQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                examChapterQuestionDetailDto.setPublisher(PublisherEnum.valueOf(questionTypesDTO.getPublisher()));
                examChapterQuestionDetailDto.setBookVolume(BookVolumeEnum.fromVolumeNum(questionTypesDTO.getSemester()));

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                bookQuestionDetailDto.setBookStr(questionTypesDTO.getPublisher()
                        +formatPublisher(questionTypesDTO.getGrade(),  questionTypesDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                questionType.setSectionId(questionTypesDTO.getSectionId());
                questionTypesDTOSet.add(questionType);

                questionTypesDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                    if (StringUtils.isNotBlank(questionInfoById.getStructuredContent())) {
                        specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfoById.getStructuredContent()), ChoiceContentVo.class));
                    }
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            bookSet.stream().forEach(bookQuestionDetailDto -> {
                List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        chapterSet.stream().filter(chapterQuestionDetailDto -> chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList());
                chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                    List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList = sectionSet.stream()
                            .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId())).collect(Collectors.toList());
                    sectionList.stream().forEach(sectionQuestionDetailDto -> {
                        List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS = pointsDTOSet.stream().filter(
                                knowledgePointsDTO -> knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        pointsDTOS.stream().forEach(pointsDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getKnowledgePointId() !=null
                                            && question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId())
                                            && question.getSectionId().equals(pointsDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            pointsDTO.setQuestionList(questionList);
                        });
                        pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto.KnowledgePointsDTO::getKnowledgePointId));
                        sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                        List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList = questionTypesDTOSet.stream().filter(
                                questionTypesDTO -> questionTypesDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getQuestionTypeId() !=null
                                            && question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId())
                                            && question.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            questionTypesDTO.setQuestionList(questionList);
                        });
                        questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                        sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                    });
                    sectionList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                    chapterQuestionDetailDto.setSectionList(sectionList);
                });
                chapterQuestionDetailDtos.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
            });
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            bookList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                    .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester)
            );
            examChapterQuestionDetailDto.setBookList(bookList);
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalDuration(questionSet.size() * 6);
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            if (CollectionUtil.isNotEmpty(examChapterQuestionDetailDto.getBookList())) {
                examChapterQuestionDetailDto.setBookList(examChapterQuestionDetailDto.getBookList().stream().filter(bookQuestionDetailDto -> bookQuestionDetailDto.getChapterList().size() > 0).collect(Collectors.toList()));
            }
            Map<String,Integer> questionTypeMap = new HashMap<>();
            questionSet.stream().forEach(question -> {
                if (question.getChoiceContent() == null && question.getQuestionType().contains("选择")){
                    question.setChoiceContent(DataUtil.formatChoiceContent(question.getQuestionContent()));
                }
                if (questionTypeMap.get(question.getQuestionType()) !=null){
                    questionTypeMap.put(question.getQuestionType(),questionTypeMap.get(question.getQuestionType())+1);
                } else {
                    questionTypeMap.put(question.getQuestionType(),1);
                }
            });
            examChapterQuestionDetailDto.setQuestionTypeDistributions(questionTypeMap.entrySet().stream()
                    .map(entry -> {
                        SpecializedTrainingNewResultVoV2.QuestionTypeDistribution questionTypeDistribution = new SpecializedTrainingNewResultVoV2.QuestionTypeDistribution();
                        questionTypeDistribution.setQuestionType(QuestionTypeEnum.getEnumByDesc(entry.getKey()).name());
                        questionTypeDistribution.setQuestionTypeName(entry.getKey());
                        questionTypeDistribution.setQuestionCount(entry.getValue());
                        return questionTypeDistribution;
                    }).collect(Collectors.toList()));
            examChapterQuestionDetailDto.setQuestionList(new ArrayList<>(questionSet));
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV3 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public List<ExamBookQuestionDetailDto> createHolidayTrainingBookQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            CreateTextBookQuestioVo createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingTextBookQuestion(param);
            List<CreateTextBookQuestioVo.DataDTO> dataDTOList = createQuestionByKnowledgeVo.getData();
            List<ExamBookQuestionDetailDto> bookSet = new ArrayList<>();
            dataDTOList.forEach(dataDTO -> {
                QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(dataDTO.getId());
                dataDTO.setQuestionType(questionInfoById.getQuestionType());
                dataDTO.setContent(questionInfoById.getQuestionContent());

                ExamBookQuestionDetailDto examBookQuestionDetailDto = new ExamBookQuestionDetailDto();
                ExamBookQuestionDetailDto.QuestionInfoDto examBookQuestionInfoDto = new ExamBookQuestionDetailDto.QuestionInfoDto();
                examBookQuestionDetailDto.setQuestionType(questionInfoById.getQuestionType());

                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = new ArrayList<>();
                examBookQuestionInfoDto.setQuestionId(dataDTO.getId());
                examBookQuestionInfoDto.setQuestionType(questionInfoById.getQuestionType());
                examBookQuestionInfoDto.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
                examBookQuestionInfoDto.setOssUrl(questionInfoById.getOssUrl());
                examBookQuestionInfoDto.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                examBookQuestionInfoDto.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                examBookQuestionInfoDto.setSortNo(dataDTO.getSortNo());

                List<ExamBookQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new ArrayList<>();
                if (dataDTO.getKnowledgePoints() != null) {
                    dataDTO.getKnowledgePoints().forEach(knowledgePointsDTO -> {
                        ExamBookQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamBookQuestionDetailDto.KnowledgePointsDTO();
                        knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getId());
                        knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getName());
                        knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                        knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getId()));
                        pointsDTOSet.add(knowledgePoint);
                    });
                }
                examBookQuestionInfoDto.setKnowledgePoints(pointsDTOSet);
                questionInfoDtos.add(examBookQuestionInfoDto);
                examBookQuestionDetailDto.setQuestionInfoDtos(questionInfoDtos);
                bookSet.add(examBookQuestionDetailDto);
            });
            List<ExamBookQuestionDetailDto> result = new ArrayList<>(bookSet);

            Map<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> groupedData = new HashMap<>();
            // 遍历原始列表，根据questionType分组
            for (ExamBookQuestionDetailDto item : result) {
                String qType = item.getQuestionType();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> qInfos = item.getQuestionInfoDtos();
                if (!groupedData.containsKey(qType)) {
                    groupedData.put(qType, new ArrayList<>());
                }
                groupedData.get(qType).addAll(qInfos);
            }

            List<ExamBookQuestionDetailDto> resultList = new ArrayList<>();
            // 遍历Map并创建ExamBookQuestionDetailDto对象
            for (Map.Entry<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> entry : groupedData.entrySet()) {
                String questionType = entry.getKey();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = entry.getValue();
                // 创建新的DTO对象
                ExamBookQuestionDetailDto newDto = new ExamBookQuestionDetailDto();
                newDto.setQuestionType(questionType);
                newDto.setBookStr(PublisherEnum.getPublisherName(param.getPublisher())+formatPublisher(param.getGrade(), param.getSemester()));
                newDto.setQuestionInfoDtos(questionInfoDtos);
                newDto.setGrade(param.getGrade());
                newDto.setBookVolume(BookVolumeEnum.fromVolumeNum(param.getSemester()));
                newDto.setPublisher(PublisherEnum.ofCustomerName(param.getPublisher()));
                // 添加到结果列表中
                resultList.add(newDto);
            }

            return resultList;
        } catch (Exception e) {
            log.error("createHolidayTrainingBookQuestion 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public QuestionAnalyzeResultDto questionAnalyzeResult(UUID questionId, PublisherEnum publisher) {
        QuestionAnalyzeResultDto questionAnalyzeResultDto = personalExamMapper.questionAnalyzeResult(questionId);
        CommonResponse.ERROR.assertNotNull(questionAnalyzeResultDto, "题目解析失败");
        QuestionTypesDto questionTypesByQuestionId = eduKnowledgeHubService.getQuestionTypesByQuestionId(questionId);
        if (questionTypesByQuestionId.getData() != null) {
            QuestionTypesDto.DataDTO data = questionTypesByQuestionId.getData();
            Set<String> questionTypesName = new HashSet<>();
            data.getQuestionTypes().forEach(questionTypesDTO -> {
                questionTypesName.add(questionTypesDTO.getName());
            });

            questionAnalyzeResultDto.setQuestionTypesName(new ArrayList<>(questionTypesName));
        }
        List<MathKnowledgePointVO> knowledgePoints = new ArrayList<>();
        if (StringUtils.isNotBlank(questionAnalyzeResultDto.getKnowledgePointIds())) {
            String publisherName = null;
            if (publisher != null) {
                publisherName = publisher.getDescription().replace("版", "");
            }
            String[] knowledgePointIds = questionAnalyzeResultDto.getKnowledgePointIds().split(",");
            List<KnowledgePointDto> knowledgePointFromViews =
                    questionKnowledgePointService.getKnowledgePointFromView(null,publisherName, null, Arrays.stream(knowledgePointIds)
                            .map(UUID::fromString)
                            .collect(Collectors.toList()));
            for(KnowledgePointDto knowledgePointFromView : knowledgePointFromViews) {
                // 获取知识点信息TODO
                MathKnowledgePointVO knowledgePoint = new MathKnowledgePointVO();
                knowledgePoint.setId(knowledgePointFromView.getId());
                knowledgePoint.setName(knowledgePointFromView.getName());
                knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointFromView.getId()));
                knowledgePoints.add(knowledgePoint);
            }
            questionAnalyzeResultDto.setKnowledgePoints(knowledgePoints);
        }
        return questionAnalyzeResultDto;
    }

    @Override
    public CheckExamExsitenceVo examExist(String qrStr) {
        CommonResponse.ERROR.assertNotEmpty(qrStr, "请输入试卷id");
        UUID examId = null;
        if (!qrStr.startsWith(inkQMath)) {
            CommonResponse.assertError("试卷不存在");
        }
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        try {
            String printId = qrStr.replace(inkQMath, "");
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(UUID.fromString(printId));
            CommonResponse.ERROR.assertNotNull(mathExamsEntity, "试卷不存在");
            examId = mathExamsEntity.getId();
            examData.setPublisher(mathExamsEntity.getPublisher() != null ? PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : null);
            examData.setExamSource(mathExamsEntity.getSource());
        } catch (Exception e) {
            log.error("examExist 方法执行出错，参数: {}", qrStr, e);
        }

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        CheckExamExsitenceVo checkExamExsitenceVo = CheckExamExsitenceVo.builder()
                .code(ApiResultCode.SUCCESS.getCode())
                .build();
        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);

        checkExamExsitenceVo.setData(examData);
        return checkExamExsitenceVo;
    }

    @Override
    public CheckExamExsitenceVo examExistByPersonalExamId(Long personalExamId) {
        CommonResponse.ERROR.assertNotNull(personalExamId, "请输入personalExamId");
        PersonalExam personalExam = personalExamMapper.selectById(personalExamId);
        UUID examId = personalExam.getExamId();

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        CheckExamExsitenceVo checkExamExsitenceVo = CheckExamExsitenceVo.builder()
                .code(ApiResultCode.SUCCESS.getCode())
                .build();
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        examData.setPublisher(personalExam.getPublisher());
        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);
        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(examId);
        examData.setExamSource(mathExamsEntity.getSource());

        List<String> photos = personalExamMapper.selectExistExamPhotoByOriginalPaper(examData.getExamId());
        List<String> photosUrls = new ArrayList<>();
        if (CollUtil.isNotEmpty(photos)) {
            photos.forEach(photo -> {
                PresignedUrlParam param1 = new PresignedUrlParam();
                param1.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                param1.setOssKey(photo);
                PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param1);
                photosUrls.add(presignedUrlDto.getData().getPresignedUrl());
            });
        }
        examData.setPhotoUrls(photosUrls);
        checkExamExsitenceVo.setData(examData);
        return checkExamExsitenceVo;
    }

    @Override
    public String saveExam(SpecializedTrainingCreateExamParam param) {
        if (ExamSourceType.HOLIDAY_TRAINING ==  param.getExamSource()) {
            //如果是暑期训练，教材版本则取参与活动的人的教材版本
            MathActivityStudent studentCurrentActivity = mathActivityStudentService.getStudentCurrentActivity(param.getStudentId());
            PublisherEnum publisher = studentCurrentActivity.getPublisher();
            if (publisher != param.getPublisher()) {
                log.error("暑期训练的教材版本不一致，参数: {}, 活动: {}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(studentCurrentActivity));
            }
            param.setPublisher(publisher);
        }
        SpecializedTrainingCrateExamDto specializedTrainingCrateExamDto = eduKnowledgeHubService.saveExam(param);
        if (specializedTrainingCrateExamDto != null && specializedTrainingCrateExamDto.getData() != null) {
            if (StrUtil.isNotBlank(specializedTrainingCrateExamDto.getData())) {
                return specializedTrainingCrateExamDto.getData();
            }
        }
        return null;
    }

    @Override
    public SpecializedTrainingUpdateExamDto updateExam(SpecializedTrainingUpdateExamParam param) {
        long setExamTypeStart = System.currentTimeMillis();
        UUID examId = param.getExamId();
        List<String> examFiles = mathExamsMapper.selectExamPhotos(examId, ExamFileType.ORIGINAL_PAPER);
        if (CollectionUtil.isNotEmpty(examFiles)) {
            return SpecializedTrainingUpdateExamDto.builder()
                    .code(ApiResultCode.SUCCESS.getCode())
                    .message("原图文件已存在")
                    .build();
        }
        PresignedUrlParam build = PresignedUrlParam.builder()
                .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                .ossKey(param.getPdfUrl())
                .build();
        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(build);
        try {
            log.info("update试卷 2, 耗时: {}ms",
                    System.currentTimeMillis() - setExamTypeStart);
            //pdf转图片
           /* byte[][] pngBytes = PDFToImageUtils.convertPDFToImageBytes(presignedUrlDto.getData().getPresignedUrl(), "png");
            List<AISpecializedTrainingUpdateExamParam.ImagesDTO> imageList = new ArrayList<>();
            for (int i = 0; i < pngBytes.length; i++) {
                byte[] pngByte = pngBytes[i];
                String uuid = UUID.randomUUID().toString();
                String objectName = aliOssUtils.upload(pngByte, "question/", uuid + ".png");

                AISpecializedTrainingUpdateExamParam.ImagesDTO imagesDTOBuilder = AISpecializedTrainingUpdateExamParam.ImagesDTO.builder()
                        .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                        .ossKey(objectName)
                        .sortNo(i)
                        .type(0)
                        .build();
                imageList.add(imagesDTOBuilder);
            }*/
            AISpecializedTrainingUpdateExamParam updateExamParam = AISpecializedTrainingUpdateExamParam.builder()
                    .examId(param.getExamId())
//                    .images(imageList)
                    .examSource(param.getExamSource())
                    .build();
            SpecializedTrainingUpdateExamDto specializedTrainingUpdateExamDto = eduKnowledgeHubService.specializedTrainingUpdateExam(updateExamParam);
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(param.getExamId());
            PersonalExam personalExam = PersonalExam.builder()
                    .examId(param.getExamId())
                    .examName(mathExamsMapper.getExamName(param.getExamId()))
                    .studentId(param.getStudentId())
                    .flag(param.getExamSource().getName())
                    .publisher(StrUtil.isNotBlank(mathExamsEntity.getPublisher()) ? PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : null)
                    .grade(param.getGrade())
                    .bookVolume(param.getBookVolume())
                    .semester(param.getSemester())
                    .build();
            if (param.getExamSource().equals(ExamSourceType.HOLIDAY_TRAINING)) {
                MathActivityWeekUnitStudent mathActivityWeekUnitStudent = mathActivityWeekUnitStudentMapper.selectById(param.getWeekUnitStudentId());
                QueryWrapper<MathActivityStudent> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("activity_id", mathActivityWeekUnitStudent.getActivityId());
                queryWrapper.eq("student_id", mathActivityWeekUnitStudent.getStudentId());
                queryWrapper.isNull("deleted_at");
                MathActivityStudent mathActivityStudent = mathActivityStudentMapper.selectOne(queryWrapper);
                if (null != mathActivityStudent) {
                    mathActivityWeekUnitStudentMapper.updateExamIdHistoryById(param.getWeekUnitStudentId(), param.getExamId().toString());
                }
            }
            //同步学练测联系记录入库
            if (param.getCatalogNodeId() != null) {
                mathSyncLearningTestService.saveSyncLearningTestRecord(param);
            }
            personalExamMapper.insert(personalExam);
            return specializedTrainingUpdateExamDto;
        } catch (Exception e) {
            log.error("updateExam 方法执行出错，参数: {}", param, e);
        }
        return SpecializedTrainingUpdateExamDto.builder()
                .code(ApiResultCode.FAIL.getCode())
                .message("更新失败")
                .build();
    }

    /**
     * 根据年级和学期格式化出版商信息
     *
     * @param grade    年级 1-12
     * @param semester 学期 1-上册 2-下册
     * @return 格式化后的字符串，如：七年级上册、高一下册
     */
    private String formatPublisher(Integer grade, Integer semester) {
        if (grade == null || semester == null) {
            return "未知";
        }
        String formattedGrade;

        if (grade >= 1 && grade <= 9) {
            // 1-9年级显示为：一年级、二年级、三年级等
            String[] gradeNames = {"一", "二", "三", "四", "五", "六", "七", "八", "九"};
            formattedGrade = gradeNames[grade - 1] + "年级";
        } else if (grade >= 10 && grade <= 12) {
            // 10-12年级显示为：高一、高二、高三
            String[] highSchoolGradeNames = {"一", "二", "三"};
            formattedGrade = "高" + highSchoolGradeNames[grade - 10];
        } else {
            // 处理异常情况
            formattedGrade = "未知年级";
        }

        // 添加学期信息
        return formattedGrade + (semester == 1 ? "上册" : "下册");
    }

    @Override
    public TextbookCatalogLabelTree textbookCatalogue(Long analyzeReportId, UUID examId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            return JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);
        }
        PersonalExam personalExam = personalExamMapper.selectById(examAnalyzeResult.getPersonalExamId());
        CommonResponse.ERROR.assertNotNull(personalExam, "未找到个人试卷信息");
        TextBookCatalogueDto textBookCatalogueDto = eduKnowledgeHubService.textbooksChaptersSections(examId, personalExam.getPublisher(), personalExam.getGrade());
        if (CollUtil.isEmpty(textBookCatalogueDto.getData())) {
            CommonResponse.assertError("获取教材目录失败");
        }
        List<TextBookCatalogueDto.DataDTO> catalogueList = textBookCatalogueDto.getData();

        // 先按照grade升序排序，再按照semester升序排序
        catalogueList = catalogueList.stream()
                .sorted(Comparator.comparing(TextBookCatalogueDto.DataDTO::getGrade)
                        .thenComparing(TextBookCatalogueDto.DataDTO::getSemester))
                .collect(Collectors.toList());

        // 使用LinkedHashMap保持排序后的顺序
        Map<String, List<TextBookCatalogueDto.DataDTO>> publisherMap = catalogueList.stream()
                .collect(Collectors.groupingBy(
                        item -> formatPublisher(item.getGrade(), item.getSemester()),
                        LinkedHashMap::new,
                        Collectors.toList()));

        List<TextbookCatalogLabelTree.PublisherNode> publisherNodes = publisherMap.entrySet().stream()
                .map(entry -> {
                    String publisher = entry.getKey();
                    List<TextBookCatalogueDto.DataDTO> publisherItems = entry.getValue();

                    Map<String, List<TextBookCatalogueDto.DataDTO>> chapterMap = publisherItems.stream()
                            .collect(Collectors.groupingBy(TextBookCatalogueDto.DataDTO::getChapterId));

                    List<TextbookCatalogLabelTree.ChapterNode> chapterNodes = chapterMap.values().stream()
                            .map(sections -> {
                                if (sections.isEmpty()) {
                                    return null;
                                }

                                TextBookCatalogueDto.DataDTO first = sections.get(0);
                                List<TextbookCatalogLabelTree.SectionNode> sectionNodes = sections.stream()
                                        .map(section -> new TextbookCatalogLabelTree.SectionNode(
                                                section.getSectionId(),
                                                section.getSectionName(),
                                                section.getSectionSortNo(),
                                                section.getTextbookId(),
                                                section.getGrade(),
                                                section.getSemester(),
                                                false, false
                                        ))
                                        .sorted(Comparator.comparing(TextbookCatalogLabelTree.SectionNode::getSectionSortNo))
                                        .collect(Collectors.toList());

                                return new TextbookCatalogLabelTree.ChapterNode(
                                        first.getChapterId(),
                                        first.getChapterName(),
                                        first.getChapterSortNo(),
                                        sectionNodes, false, false
                                );
                            })
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparing(TextbookCatalogLabelTree.ChapterNode::getChapterSortNo))
                            .collect(Collectors.toList());

                    return new TextbookCatalogLabelTree.PublisherNode(publisher, chapterNodes, false, false);
                })
                .collect(Collectors.toList());
        return new TextbookCatalogLabelTree(publisherNodes);
    }

    @Override
    public void updateExamScope(ExamScopeParam param) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(param.getAnalyzeReportId());
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        examAnalyzeResult.setManualExamScope(param.getExamScope());
        examAnalyzeResultMapper.updateById(examAnalyzeResult);
    }

    @Override
    public TextbookCatalogLabelTree getExamScope(Long analyzeReportId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            return JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);
        }
        return null;
    }

    @Override
    public List<String> getManualExamScopeName(Long analyzeReportId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }

        List<String> selectedItems = new ArrayList<>();
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            TextbookCatalogLabelTree textbookCatalogLabelTree = JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);

            // 遍历publishers
            if (CollUtil.isNotEmpty(textbookCatalogLabelTree.getPublishers())) {
                List<TextbookCatalogLabelTree.PublisherNode> publishers = new ArrayList<>();

                for (TextbookCatalogLabelTree.PublisherNode publisher : textbookCatalogLabelTree.getPublishers()) {
                    // 如果出版商被显示，添加其名称
                    if (publisher.isShow()) {
                        selectedItems.add(publisher.getPublisher());
                    }

                    // 遍历chapters，无论出版商是否显示
                    if (CollUtil.isNotEmpty(publisher.getChapters())) {
                        for (TextbookCatalogLabelTree.ChapterNode chapter : publisher.getChapters()) {
                            // 如果章节被显示，添加其名称
                            if (chapter.isShow()) {
                                selectedItems.add("第" + chapter.getChapterSortNo() + "章 " + chapter.getChapterName());
                            }

                            // 遍历sections，无论章节是否显示
                            if (CollUtil.isNotEmpty(chapter.getSections())) {
                                for (TextbookCatalogLabelTree.SectionNode section : chapter.getSections()) {
                                    // 如果小节被显示，添加其名称
                                    if (section.isShow()) {
                                        selectedItems.add(chapter.getChapterSortNo() + "." + chapter.getChapterSortNo() + section.getSectionName());
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 将选中项列表打印到日志中以便于调试
            log.info("Selected items: {}", selectedItems);
        }

        return selectedItems;
    }

    @Override
    public KnowledgePointBySelectVo getKnowledgePointBySection(Long analyzeReportId) {
        List<UUID> sectionIds = examAnalyzeResultMapper.selectManualExamScopeSection(analyzeReportId);
        if (CollUtil.isEmpty(sectionIds)) {
            return null;
        }
        List<UUID> knowledgePointIds = examAnalyzeResultMapper.selectKnowledgePointByAnalyzeId(analyzeReportId);
        KnowledgePointBySelectDto knowledgePointBySection = eduKnowledgeHubService.getKnowledgePointBySection(sectionIds);
        if (knowledgePointBySection == null || CollUtil.isEmpty(knowledgePointBySection.getData())) {
            return null;
        }

        // 对知识点按ID去重
        List<KnowledgePointBySelectDto.DataDTO> data = knowledgePointBySection.getData().stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                KnowledgePointBySelectDto.DataDTO::getId,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));

        if (CollUtil.isEmpty(knowledgePointIds)) {
            return KnowledgePointBySelectVo.builder()
                    .knowledgePointSize(data.size())
                    .coveredKnowledgePointSize(0)
                    .notCoveredKnowledgePointSize(data.size())
                    .knowledgePointList(data)
                    .build();
        }

        // 计算本次考试知识点在所有知识点中的覆盖情况
        // 过滤出所有知识点中包含在本次考试知识点列表中的知识点
        List<KnowledgePointBySelectDto.DataDTO> coveredData = data.stream()
                .filter(dataDTO -> knowledgePointIds.contains(dataDTO.getId()))
                .collect(Collectors.toList());

        // 本次考试知识点覆盖的数量
        int coveredSize = coveredData.size();
        // 未覆盖的知识点数量
        int notCoveredSize = data.size() - coveredSize;

        return KnowledgePointBySelectVo.builder()
                .knowledgePointSize(data.size())
                .coveredKnowledgePointSize(coveredSize)
                .notCoveredKnowledgePointSize(notCoveredSize)
                .knowledgePointList(data) // 返回所有知识点列表
                .build();
    }

    @Override
    public SpecializedTrainingQuestionVo getMathQuestionDetailById(UUID questionId,PublisherEnum  publisher) throws BaseException {
        QuestionAnalyzeResultDto questionInfoById = personalExamMapper.questionAnalyzeResult(questionId);
        SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
        BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
        specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
        specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
        specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
        if (StringUtils.isNotBlank(questionInfoById.getStructuredContent())) {
            specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(questionInfoById.getStructuredContent()), ChoiceContentVo.class));
        }
        if (specializedTrainingQuestionVo.getChoiceContent()==null && questionInfoById.getQuestionType() !=null && questionInfoById.getQuestionType().contains("选择")){
            specializedTrainingQuestionVo.setChoiceContent(DataUtil.formatChoiceContent(specializedTrainingQuestionVo.getQuestionContent()));
        }
        // 查询知识点信息
        if (StringUtils.isNotBlank(questionInfoById.getKnowledgePointIds())) {
            String[] knowledgePointIds = questionInfoById.getKnowledgePointIds().split(",");
            List<MathKnowledgePointVO> knowledgePoints = questionKnowledgePointService.getMathKnowledgePointVOByIds(Arrays.stream(knowledgePointIds)
                    .map(UUID::fromString)
                    .collect(Collectors.toList()), publisher);
            specializedTrainingQuestionVo.setKnowledgePoints(knowledgePoints);
        }
        //查询真题信息
        PastExamQuestionDto pastExamQuestionDto = mathQuestionsMapper.selectPastExamQuestionDto(null, questionId);
        specializedTrainingQuestionVo.setIsPastExamPaper(false);
        if (pastExamQuestionDto != null && pastExamQuestionDto.getIsPastExamPaper()){
            specializedTrainingQuestionVo.setIsPastExamPaper(true);
            specializedTrainingQuestionVo.setPastExamPaperYear(pastExamQuestionDto.getPastExamPaperYear());
            specializedTrainingQuestionVo.setPastExamPaperRegion(pastExamQuestionDto.getPastExamPaperRegion());
        }
        return specializedTrainingQuestionVo;
    }

    @Override
    public ExamChapterQuestionDetailDto selectSpecializedTrainingExamDetail(UUID examId, Long personalExamId) {
        // 创建性能监控器
        PerformanceMonitor monitor = PerformanceMonitor.create(examId, personalExamId);
        monitor.recordMemoryUsage("方法开始");

        Map<UUID, PersonalExamQuestionResultEnum> questionResultMap = new HashMap<>();
        PublisherEnum publisherEnum = null;
        Integer grade = null;
        BookVolumeEnum bookVolumeEnum = null;

        if (null != personalExamId) {
            monitor.startStep("获取个人考试数据");
            List<PersonalExamQuestion> questionList = monitor.recordDbQuery("listQuestionByPersonalExamId",
                () -> personalExamService.listQuestionByPersonalExamId(personalExamId));

            questionResultMap = monitor.recordDataProcess("构建questionResultMap",
                () -> questionList.stream().collect(Collectors.toMap(PersonalExamQuestion::getQuestionId, PersonalExamQuestion::getResult)));

            PersonalExam personalExam = monitor.recordDbQuery("selectPersonalExamById",
                () -> personalExamMapper.selectById(personalExamId));
            publisherEnum = personalExam.getPublisher();
            grade = personalExam.getGrade();
            bookVolumeEnum = personalExam.getBookVolume();
            monitor.endStep("获取个人考试数据");
        } else {
            monitor.startStep("获取数学考试数据");
            MathExamsEntity mathExamsEntity = monitor.recordDbQuery("selectMathExamById",
                () -> mathExamsMapper.selectById(examId));
            publisherEnum = PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher());
            grade = mathExamsEntity.getGrade();
            bookVolumeEnum = BookVolumeEnum.fromVolumeNum(mathExamsEntity.getSemester());
            monitor.endStep("获取数学考试数据");
        }
        try {
            monitor.startStep("初始化数据结构");
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            examChapterQuestionDetailDto.setGrade(grade);
            examChapterQuestionDetailDto.setPublisher(publisherEnum);
            examChapterQuestionDetailDto.setBookVolume(bookVolumeEnum);
            monitor.endStep("初始化数据结构");

            // 调用外部服务获取考试详情
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = monitor.recordExternalCall("eduKnowledgeHubService.selectSpecializedTrainingExamDetail",
                () -> eduKnowledgeHubService.selectSpecializedTrainingExamDetail(examId));

            monitor.startStep("初始化集合对象");
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();

            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();

            monitor.endStep("初始化集合对象");
            log.info("[ExamId:{}-PersonalExamId:{}] 数据概览 - 知识点数量: {}, 题型数量: {}",
                    examId, personalExamId,
                    knowledgePointsQuestionList != null ? knowledgePointsQuestionList.size() : 0,
                    questionTypesQuestionList != null ? questionTypesQuestionList.size() : 0);
            Map<UUID, PersonalExamQuestionResultEnum> finalQuestionResultMap = questionResultMap;

            // 处理知识点数据
            monitor.recordLoop("处理知识点数据", knowledgePointsQuestionList.size(), () -> {
                int[] knowledgePointIndex = {0}; // 使用数组来在lambda中修改值
                knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {
                    knowledgePointIndex[0]++;
                    monitor.recordProgress("知识点处理", knowledgePointIndex[0], knowledgePointsQuestionList.size());

                    ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                    bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                    bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                    bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                    bookQuestionDetailDto.setBookStr(StrUtil.format("{} {}",
                            PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher()), formatPublisher(knowledgePointsDTO.getGrade(), knowledgePointsDTO.getSemester())));
                    bookSet.add(bookQuestionDetailDto);

                    ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                    chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                    chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                    chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                    chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                    chapterSet.add(chapterQuestionDetailDto);

                    ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                    sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                    sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                    sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                    sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                    sectionSet.add(sectionQuestionDetailDto);

                    ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                    knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());

                    // 检查是否有解释（可能是性能瓶颈）
                    boolean hasExplanation = monitor.recordDbQuery("checkHasExplanation-" + knowledgePointsDTO.getKnowledgePointId(),
                        () -> questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getKnowledgePointId()));
                    knowledgePoint.setHasExplanation(hasExplanation);
                    pointsDTOSet.add(knowledgePoint);

                    // 处理知识点下的题目
                    if (knowledgePointsDTO.getQuestions() != null && !knowledgePointsDTO.getQuestions().isEmpty()) {
                        int questionCount = knowledgePointsDTO.getQuestions().size();
                        monitor.recordLoop("处理知识点题目-" + knowledgePointsDTO.getKnowledgePointName(), questionCount, () -> {
                            knowledgePointsDTO.getQuestions().forEach(question -> {
                                // 获取题目详细信息（可能是性能瓶颈）
                                QuestionInfoByIdDto questionInfoById = monitor.recordDbQuery("getQuestionInfoById-" + question.getId(),
                                    () -> personalExamMapper.getQuestionInfoById(question.getId()));

                                SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                                BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                                specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());

                                // 内容解码（可能是性能瓶颈）
                                monitor.recordDataProcess("解码题目内容-" + question.getId(), () -> {
                                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                                    return null;
                                });

                                specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                                specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                                specializedTrainingQuestionVo.setQuestionResult(finalQuestionResultMap.getOrDefault(question.getId(), PersonalExamQuestionResultEnum.correct));
                                questionSet.add(specializedTrainingQuestionVo);
                            });
                        });
                    }
                });
            });
            // 处理题型数据
            monitor.recordLoop("处理题型数据", questionTypesQuestionList.size(), () -> {
                int[] questionTypeIndex = {0}; // 使用数组来在lambda中修改值
                questionTypesQuestionList.forEach(questionTypesDTO -> {
                    questionTypeIndex[0]++;
                    monitor.recordProgress("题型处理", questionTypeIndex[0], questionTypesQuestionList.size());

                    ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                    bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                    bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                    bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                    bookQuestionDetailDto.setBookStr(StrUtil.format("{}{}",
                            PublisherEnum.getPublisherName(questionTypesDTO.getPublisher()), formatPublisher(questionTypesDTO.getGrade(), questionTypesDTO.getSemester())));
                    bookSet.add(bookQuestionDetailDto);

                    ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                    chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                    chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                    chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                    chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                    chapterSet.add(chapterQuestionDetailDto);

                    ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                    sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                    sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                    sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                    sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                    sectionSet.add(sectionQuestionDetailDto);

                    ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                    questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                    questionType.setSectionId(questionTypesDTO.getSectionId());
                    questionTypesDTOSet.add(questionType);

                    // 处理题型下的题目
                    if (questionTypesDTO.getQuestions() != null && !questionTypesDTO.getQuestions().isEmpty()) {
                        int questionCount = questionTypesDTO.getQuestions().size();
                        monitor.recordLoop("处理题型题目-" + questionTypesDTO.getQuestionTypeName(), questionCount, () -> {
                            questionTypesDTO.getQuestions().forEach(question -> {
                                // 获取题目详细信息（可能是性能瓶颈）
                                QuestionInfoByIdDto questionInfoById = monitor.recordDbQuery("getQuestionInfoById-" + question.getId(),
                                    () -> personalExamMapper.getQuestionInfoById(question.getId()));

                                SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                                BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);

                                // 内容解码（可能是性能瓶颈）
                                monitor.recordDataProcess("解码题目内容-" + question.getId(), () -> {
                                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                                    return null;
                                });

                                specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                                specializedTrainingQuestionVo.setQuestionType(questionTypesDTO.getQuestionTypeName());
                                specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                                specializedTrainingQuestionVo.setQuestionResult(finalQuestionResultMap.getOrDefault(question.getId(), PersonalExamQuestionResultEnum.correct));
                                questionSet.add(specializedTrainingQuestionVo);
                            });
                        });
                    }
                });
            });
            monitor.recordMemoryUsage("数据组装前");

            // 数据组装阶段 - 这是一个复杂的嵌套循环，可能是主要性能瓶颈
            monitor.recordCollectionOp("数据组装-构建层级结构", bookSet.size(), () -> {
                bookSet.stream().forEach(bookQuestionDetailDto -> {
                    // 过滤章节数据
                    List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        monitor.recordCollectionOp("过滤章节数据", chapterSet.size(), () ->
                            chapterSet.stream().filter(chapterQuestionDetailDto ->
                                chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList()));

                    chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                        // 过滤小节数据
                        List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList =
                            monitor.recordCollectionOp("过滤小节数据", sectionSet.size(), () ->
                                sectionSet.stream()
                                    .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId() != null &&
                                           sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId()))
                                    .collect(Collectors.toList()));

                        sectionList.stream().forEach(sectionQuestionDetailDto -> {
                            // 过滤知识点数据
                            List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS =
                                monitor.recordCollectionOp("过滤知识点数据", pointsDTOSet.size(), () ->
                                    pointsDTOSet.stream().filter(knowledgePointsDTO ->
                                        knowledgePointsDTO.getSectionId() != null &&
                                        knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId()))
                                    .collect(Collectors.toList()));

                            pointsDTOS.stream().forEach(pointsDTO -> {
                                // 过滤题目数据
                                List<SpecializedTrainingQuestionVo> questionList =
                                    monitor.recordCollectionOp("过滤知识点题目", questionSet.size(), () ->
                                        questionSet.stream().filter(question ->
                                            question.getKnowledgePointId() != null &&
                                            question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId()) &&
                                            question.getSectionId().equals(pointsDTO.getSectionId()))
                                        .collect(Collectors.toList()));

                                questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                                pointsDTO.setQuestionList(questionList);
                            });
                            pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto.KnowledgePointsDTO::getKnowledgePointId));
                            sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                            // 过滤题型数据
                            List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList =
                                monitor.recordCollectionOp("过滤题型数据", questionTypesDTOSet.size(), () ->
                                    questionTypesDTOSet.stream().filter(questionTypesDTO ->
                                        sectionQuestionDetailDto.getSectionId().equals(questionTypesDTO.getSectionId()))
                                    .collect(Collectors.toList()));

                            questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                                // 过滤题型题目数据
                                List<SpecializedTrainingQuestionVo> questionList =
                                    monitor.recordCollectionOp("过滤题型题目", questionSet.size(), () ->
                                        questionSet.stream().filter(question ->
                                            question.getQuestionTypeId() != null &&
                                            question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId()) &&
                                            question.getSectionId().equals(questionTypesDTO.getSectionId()))
                                        .collect(Collectors.toList()));

                                questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                                questionTypesDTO.setQuestionList(questionList);
                            });
                            questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                            sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                        });
                        sectionList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                        chapterQuestionDetailDto.setSectionList(sectionList);
                    });
                    chapterQuestionDetailDtos.sort(Comparator.comparing(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                    bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
                });
                return null;
            });
            monitor.startStep("最终数据处理");
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            examChapterQuestionDetailDto.setBookList(bookList);

            // 过滤和排序操作
            if (CollectionUtil.isNotEmpty(examChapterQuestionDetailDto.getBookList())) {
                List<ExamChapterQuestionDetailDto.BookQuestionDetailDto> filteredBookList =
                    monitor.recordCollectionOp("过滤书籍数据", examChapterQuestionDetailDto.getBookList().size(), () ->
                        examChapterQuestionDetailDto.getBookList().stream()
                            .filter(bookQuestionDetailDto ->
                                bookQuestionDetailDto.getGrade() != null &&
                                bookQuestionDetailDto.getSemester() != null &&
                                bookQuestionDetailDto.getChapterList().size() > 0 &&
                                bookQuestionDetailDto.getChapterList().get(0).getChapterId() != null)
                            .collect(Collectors.toList()));
                examChapterQuestionDetailDto.setBookList(filteredBookList);
            }

            monitor.recordDataProcess("排序书籍数据", () -> {
                examChapterQuestionDetailDto.getBookList().sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                        .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester));
                return null;
            });

            // 设置统计信息
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            examChapterQuestionDetailDto.setQuestionList(new ArrayList<>(questionSet));
            monitor.endStep("最终数据处理");
            monitor.recordMemoryUsage("方法结束前");

            log.info("[ExamId:{}-PersonalExamId:{}] 最终统计 - 总题目数: {}, 总知识点数: {}, 总题型数: {}",
                    examId, personalExamId, questionSet.size(), pointsDTOSet.size(), questionTypesDTOSet.size());

            monitor.endMethod();
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            monitor.endMethod();
            log.error("[ExamId:{}-PersonalExamId:{}] 方法执行异常: {}", examId, personalExamId, e.getMessage(), e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public List<ExamBookQuestionDetailDto> selectSpecializedTextBookTrainingExamDetail(UUID examId, Long personalExamId) {

        Map<UUID, PersonalExamQuestionResultEnum> questionResultMap = new HashMap<>();
        PublisherEnum publisher = null;
        if (null != personalExamId) {
            List<PersonalExamQuestion> questionList = personalExamService.listQuestionByPersonalExamId(personalExamId);
            questionResultMap = questionList.stream().collect(Collectors.toMap(PersonalExamQuestion::getQuestionId, PersonalExamQuestion::getResult));
            PersonalExam personalExam = personalExamService.getById(personalExamId);
            publisher = personalExam.getPublisher();
        }

        try {
            CreateTextBookQuestioVo createQuestionByKnowledgeVo = eduKnowledgeHubService.selectSpecializedTextBookTrainingExamDetail(examId, publisher);
            List<CreateTextBookQuestioVo.DataDTO> dataDTOList = createQuestionByKnowledgeVo.getData();
            List<ExamBookQuestionDetailDto> bookSet = new ArrayList<>();
            Map<UUID, PersonalExamQuestionResultEnum> finalQuestionResultMap = questionResultMap;
            dataDTOList.forEach(dataDTO -> {
                QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(dataDTO.getId());
                dataDTO.setQuestionType(questionInfoById.getQuestionType());
                dataDTO.setContent(decodeContentV2(questionInfoById.getQuestionContent()));

                ExamBookQuestionDetailDto examBookQuestionDetailDto = new ExamBookQuestionDetailDto();
                ExamBookQuestionDetailDto.QuestionInfoDto examBookQuestionInfoDto = new ExamBookQuestionDetailDto.QuestionInfoDto();
                examBookQuestionDetailDto.setQuestionType(questionInfoById.getQuestionType());

                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = new ArrayList<>();
                examBookQuestionInfoDto.setQuestionId(dataDTO.getId());
                examBookQuestionInfoDto.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
                examBookQuestionInfoDto.setOssUrl(questionInfoById.getOssUrl());
                examBookQuestionInfoDto.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                examBookQuestionInfoDto.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                examBookQuestionInfoDto.setSortNo(dataDTO.getSortNo());
                examBookQuestionInfoDto.setQuestionResult(finalQuestionResultMap.getOrDefault(examBookQuestionInfoDto.getQuestionId(), PersonalExamQuestionResultEnum.correct));
                List<ExamBookQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new ArrayList<>();
                if (CollUtil.isNotEmpty(dataDTO.getKnowledgePoints())) {
                    dataDTO.getKnowledgePoints().forEach(knowledgePointsDTO -> {
                        ExamBookQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamBookQuestionDetailDto.KnowledgePointsDTO();
                        knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getId());
                        knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getName());
                        knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                        knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointsDTO.getId()));
                        pointsDTOSet.add(knowledgePoint);
                    });
                }
                examBookQuestionInfoDto.setKnowledgePoints(pointsDTOSet);
                questionInfoDtos.add(examBookQuestionInfoDto);
                examBookQuestionDetailDto.setQuestionInfoDtos(questionInfoDtos);
                bookSet.add(examBookQuestionDetailDto);
            });
            List<ExamBookQuestionDetailDto> result = new ArrayList<>(bookSet);

            Map<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> groupedData = new HashMap<>();
            // 遍历原始列表，根据questionType分组
            for (ExamBookQuestionDetailDto item : result) {
                String qType = item.getQuestionType();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> qInfos = item.getQuestionInfoDtos();
                if (!groupedData.containsKey(qType)) {
                    groupedData.put(qType, new ArrayList<>());
                }
                groupedData.get(qType).addAll(qInfos);
            }

            List<ExamBookQuestionDetailDto> resultList = new ArrayList<>();
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(examId);
            PersonalExam personalExam = personalExamMapper.selectById(personalExamId);
            // 遍历Map并创建ExamBookQuestionDetailDto对象
            for (Map.Entry<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> entry : groupedData.entrySet()) {
                String questionType = entry.getKey();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = entry.getValue();
                // 创建新的DTO对象
                ExamBookQuestionDetailDto newDto = new ExamBookQuestionDetailDto();
                newDto.setQuestionType(questionType);
                newDto.setBookStr(mathExamsEntity.getPublisher()+formatPublisher(mathExamsEntity.getGrade(), personalExam.getBookVolume().getVolumeNum()));
                newDto.setQuestionInfoDtos(questionInfoDtos);
                newDto.setGrade(personalExam.getGrade());
                newDto.setBookVolume(personalExam.getBookVolume());
                newDto.setPublisher(personalExam.getPublisher());
                // 添加到结果列表中
                resultList.add(newDto);
            }

            return resultList;
        } catch (Exception e) {
            log.error("查询专项训练详情异常", e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public PersonalExam onlineCreateExam(SpecializedTrainingCreateExamParam param) {
        log.info("创建在线答题考试参数：{}", param);
        //KNOWLEDGE_POINTS_TRAINING-知识点专项训练 ,MISTAKE_SET_TRAINING-错题集专项训练
        CommonResponse.ERROR.assertNotNull(param.getName(), "试卷名称不能为空");
        CommonResponse.ERROR.assertCollNotNull(param.getQuestionIds(), "题目id列表不能为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getGrade(), "年级不能为空");
        CommonResponse.ERROR.assertNotNull(param.getPublisher(), "出版商不能为空");
        CommonResponse.ERROR.assertNotNull(param.getExamSource(), "试卷类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getExamDataList(), "试卷数据列表不能为空");
        CommonResponse.ERROR.assertNotNull(param.getParentId(), "父级id不能为空");
        String examId = null;
        if (null == param.getExamId()) {
            SpecializedTrainingCrateExamDto specializedTrainingCrateExamDto = eduKnowledgeHubService.saveExam(param);
            if (specializedTrainingCrateExamDto != null && specializedTrainingCrateExamDto.getData() != null) {
                examId = specializedTrainingCrateExamDto.getData();
            }
        } else {
            examId = param.getExamId().toString();
        }
        log.info("创建在线答题考试成功：{}", examId);

        PersonalExam personalExam = PersonalExam.builder()
                .examId(UUID.fromString(examId))
                .examName(param.getName())
                .studentId(param.getStudentId())
                .flag(param.getExamSource().getName())
                .publisher(param.getPublisher())
                .grade(param.getGrade())
                .bookVolume(BookVolumeEnum.fromVolumeNum(param.getSemester()))
                .semester(param.getSemester())
                .build();
        personalExamMapper.insert(personalExam);

        return personalExam;
    }

    @Override
    public SpecializedTrainingNewResultVoV2 selectOnlineSpecializedTrainingExamDetail(Long personalExamId, UUID examId) {
        List<UUID> questionIds = new ArrayList<>();
        PublisherEnum publisherEnum = null;
        if (null != personalExamId) {
            List<ExamOnlineQuestionDetailDto> examOnlineQuestionDetailDtos = personalExamMapper.selectOnlineSpecializedTrainingExamDetail(personalExamId);
            publisherEnum = examOnlineQuestionDetailDtos.get(0).getPublisher();
            questionIds = examOnlineQuestionDetailDtos.stream().map(ExamOnlineQuestionDetailDto::getQuestionId).collect(Collectors.toList());
        } else {
            List<ExamQuestionInfoDto> examQuestionInfo = personalExamMapper.getExamQuestionInfo(examId);
            examQuestionInfo.stream().map(ExamQuestionInfoDto::getQuestionId).forEach(questionIds::add);
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(examId);
            publisherEnum = PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher());
        }
      /*  if (CollUtil.isNotEmpty(examOnlineQuestionDetailDtos)) {
            examOnlineQuestionDetailDtos.forEach(examOnlineQuestionDetailDto -> {
                if (examOnlineQuestionDetailDto.getKnowledgePointIds() != null) {
                    String[] split = examOnlineQuestionDetailDto.getKnowledgePointIds().split(",");
                    List<MathKnowledgePointVO> mathKnowledgePointVOS = questionKnowledgePointMapper.selectKnowledgePointByKnowledgeIds(Arrays.asList(split));
                    examOnlineQuestionDetailDto.setKnowledgePoints(mathKnowledgePointVOS);
                }
            });
        }*/
        SpecializedTrainingNewResultVoV2 specializedTrainingNewResultVoV2 = buildSpecializedTrainingNewResultByIds(questionIds, publisherEnum, null, personalExamId);
        for (SpecializedTrainingQuestionVo question : specializedTrainingNewResultVoV2.getQuestionList()) {
            question.setIsAddMistakesBook(true);
            //查询题目详情和文件详情
            QuestionVo questionById = studyRecordMapper.getQuestionById(question.getQuestionId());
            PresignedUrlParam urlParam = new PresignedUrlParam();
            urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
            questionById.getFilesVos().forEach(filesVo -> {
                if (ObjectUtil.isNotEmpty(filesVo.getOssUrl())) {
                    urlParam.setOssKey(filesVo.getOssUrl());
                    PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                    if (presignedUrlVo != null && presignedUrlVo.getData() != null) {
                        question.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                    }
                }
            });
        }
        return specializedTrainingNewResultVoV2;
    }

    private SpecializedTrainingNewResultVoV2 buildSpecializedTrainingNewResultByIds(List<UUID> questions, PublisherEnum publisher,UUID knowledgePointId,Long personalExamId) {
        // 创建返回对象
        SpecializedTrainingNewResultVoV2 newResultVo = new SpecializedTrainingNewResultVoV2();
        Map<String,Integer> questionTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(questions)) {
            List<PersonalExamQuestion> personalExamQuestions = personalExamService.listQuestionByPersonalExamId(personalExamId);
            Map<UUID, PersonalExamQuestionResultEnum> questionResultMap = personalExamQuestions.stream().collect(Collectors.toMap(PersonalExamQuestion::getQuestionId, PersonalExamQuestion::getResult));

            List<QuestionAnalyzeResultDto> questionInfoList = personalExamMapper.questionAnalyzeResults(questions);
            List<QuestionAnalyzeResultDto> questionInfos = new ArrayList<>();
            for (UUID questionId : questions) {
                QuestionAnalyzeResultDto questionInfo = questionInfoList.stream().filter(q -> q.getQuestionId().equals(questionId)).findFirst().orElse(null);
                questionInfos.add(questionInfo);
            }
            QuestionSortNo finalSortNo = new QuestionSortNo();
            finalSortNo.setSortNo(1);
            List<SpecializedTrainingQuestionVo> questionList = questionInfos.stream()
                    .map(question -> {
                        Integer sortNo = finalSortNo.getSortNo();
                        SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                        BeanUtil.copyProperties(question, specializedTrainingQuestionVo);
                        specializedTrainingQuestionVo.setQuestionContent(eduKnowledgeHubService.decodeContentV2(question.getQuestionContent()));
                        specializedTrainingQuestionVo.setAnalyzeContent(eduKnowledgeHubService.decodeContentV2(question.getAnalyzeContent()));
                        specializedTrainingQuestionVo.setAnswer(eduKnowledgeHubService.decodeContentV2(question.getAnswer()));
                        specializedTrainingQuestionVo.setSortNo(++sortNo);
                        specializedTrainingQuestionVo.setQuestionResult(questionResultMap.getOrDefault(question.getQuestionId(), PersonalExamQuestionResultEnum.correct));
                        if (question.getQuestionType().contains("选择")){
                            specializedTrainingQuestionVo.setChoiceContent(DataUtil.formatChoiceContent(specializedTrainingQuestionVo.getQuestionContent()));
                        }
                        if (questionTypeMap.get(question.getQuestionType()) !=null){
                            questionTypeMap.put(question.getQuestionType(),questionTypeMap.get(question.getQuestionType())+1);
                        } else {
                            questionTypeMap.put(question.getQuestionType(),1);
                        }
                        finalSortNo.setSortNo(sortNo);
                        // 查询知识点信息
                        List<MathKnowledgePointVO> knowledgePoints = new ArrayList<>();
                        if(StringUtils.isNotBlank(question.getKnowledgePointIds())){
                            String  publisherName =null;
                            if(publisher != null){
                                publisherName = publisher.getDescription().replace("版", "");
                            }
                            String[] knowledgePointIds = question.getKnowledgePointIds().split(",");
                            List<KnowledgePointDto> knowledgePointFromViews =
                                    questionKnowledgePointService.getKnowledgePointFromView(null,publisherName, null, Arrays.stream(knowledgePointIds)
                                            .map(UUID::fromString)
                                            .collect(Collectors.toList()));
                            for(KnowledgePointDto knowledgePointFromView : knowledgePointFromViews) {
                                // 获取知识点信息TODO
                                MathKnowledgePointVO knowledgePoint = new MathKnowledgePointVO();
                                knowledgePoint.setId(knowledgePointFromView.getId());
                                knowledgePoint.setName(knowledgePointFromView.getName());
                                knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointFromView.getId()));
                                knowledgePoints.add(knowledgePoint);
                                if(specializedTrainingQuestionVo.getKnowledgePointName()==null){
                                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointFromView.getName());
                                } else {
                                    specializedTrainingQuestionVo.setKnowledgePointName(specializedTrainingQuestionVo.getKnowledgePointName()+","+knowledgePointFromView.getName());
                                }
                            }
                            specializedTrainingQuestionVo.setKnowledgePoints(knowledgePoints);
                        }
                        return specializedTrainingQuestionVo;
                    }).collect(Collectors.toList());
            //collect 根据questionType分组
            List<SpecializedTrainingQuestionDataVo> typeQuestionDataVos = questionList.stream()
                    .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                    .entrySet().stream()
                    .map(entry -> {
                        SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                        specializedTrainingQuestionDataVo.setType(entry.getKey());
                        specializedTrainingQuestionDataVo.setList(entry.getValue());
                        return specializedTrainingQuestionDataVo;
                    }).collect(Collectors.toList());
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePoint = new ArrayList<>();
            CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO();
            knowledgePointsDTO.setKnowledgePointId(knowledgePointId);
            knowledgePointsDTO.setQuestionList(typeQuestionDataVos);
            knowledgePoint.add(knowledgePointsDTO);
            newResultVo.setTotalQuestions(questions.size());
            newResultVo.setTotalDuration(newResultVo.getTotalQuestions() * 6);
            newResultVo.setTotalKnowledgePoints(1);
            newResultVo.setTotalQuestionTypes(questionTypeMap.size());
            newResultVo.setKnowledgePoint(knowledgePoint);
            List<SpecializedTrainingNewResultVoV2.QuestionTypeDistribution> questionTypeDistributions = new ArrayList<>();
            questionTypeMap.keySet().forEach(questionType -> {
                SpecializedTrainingNewResultVoV2.QuestionTypeDistribution questionTypeDistribution = new SpecializedTrainingNewResultVoV2.QuestionTypeDistribution();
                questionTypeDistribution.setQuestionType(QuestionTypeEnum.getEnumByDesc(questionType).name());
                questionTypeDistribution.setQuestionTypeName(questionType);
                questionTypeDistribution.setQuestionCount(questionTypeMap.get(questionType));
                questionTypeDistributions.add(questionTypeDistribution);
            });
            newResultVo.setQuestionTypeDistributions(questionTypeDistributions);
//            questionList.sort(Comparator.comparingInt(SpecializedTrainingQuestionVo::getSortNo));
            newResultVo.setQuestionList(questionList);
        }
        return newResultVo;
    }
}
