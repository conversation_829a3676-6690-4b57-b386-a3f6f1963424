package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.EnglishExamReportQuestionTypeMapper;
import com.joinus.study.model.entity.EnglishExamReportQuestionType;
import com.joinus.study.service.EnglishExamReportQuestionTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/3 15:48
 */
@Service
@Slf4j
public class EnglishExamReportQuestionTypeServiceImpl extends ServiceImpl<EnglishExamReportQuestionTypeMapper, EnglishExamReportQuestionType> implements EnglishExamReportQuestionTypeService {
    @Override
    public List<EnglishExamReportQuestionType> listByReportId(Long reportId) {
        return lambdaQuery()
                .eq(EnglishExamReportQuestionType::getReportId, reportId)
                .isNotNull(EnglishExamReportQuestionType::getQuestionType)
                .orderByAsc(EnglishExamReportQuestionType::getAccuracyRate)
                .list();
    }
}
