package com.joinus.study.service;

import com.joinus.study.model.entity.ReadingPersonalPassagesQuestions;

import java.math.BigDecimal;
import java.util.List;

public interface ReadingPersonalPassageQuestionsService {
    /**
     * 设置是否纠错标记
     *
     * @param personalPassageQuestionId
     * @param isErrorCorrection
     */
    void setIsErrorCorrection(Long personalPassageQuestionId, int isErrorCorrection, BigDecimal result);

    /**
     * 设置是否反馈标记
     *
     * @param id
     * @param isFeedback
     */
    void setIsFeedback(Long id, int isFeedback);

    List<ReadingPersonalPassagesQuestions> getByPersonalPassageIdAndPersonalQuestionId(Long personalPassageId , Long personalPassageQuestionId);
}
