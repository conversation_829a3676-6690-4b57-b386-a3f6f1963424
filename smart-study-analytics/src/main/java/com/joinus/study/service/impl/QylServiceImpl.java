package com.joinus.study.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.common.GlobalConstants;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.constant.Constant;
import com.joinus.study.model.bo.SmsRequestBo;
import com.joinus.study.model.dto.BusinessSubscriptionDto;
import com.joinus.study.model.enums.SmsCenterAppCodeEnum;
import com.joinus.study.model.enums.SmsCenterH5SceneEnum;
import com.joinus.study.model.enums.SmsCenterYzmTypeEnum;
import com.joinus.study.model.param.SmsLoginParam;
import com.joinus.study.model.param.SmsParam;
import com.joinus.study.model.result.MathH5TokenResult;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.service.QylService;
import com.joinus.study.utils.JWTUtil;
import com.joinus.study.utils.RedisUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class QylServiceImpl implements QylService {

    @Value("${server.domain.qyl-gateway:https://gateway.joinuscn.com}")
    private String qylGatewayDomain;

    @Value("${qyl.validate-code.template-id:585840}")
    private String validateCodeTemplateId;

    @Resource
    private BasicBusinessService basicBusinessService;

    @Resource
    private RedisUtil redisUtil;

    @Value("${server.domain.qyl-web.url:https://api.uat.qingyulan.net}")
    private String qylDomain;
    @Override
    public String sendSms(SmsParam smsParam, HttpServletRequest  request) {
        if(StrUtil.isBlank(smsParam.getPhone())) {
            throw new BaseException("手机号不能为空");
        }
        CommonResponse.ERROR.assertIsTrue(Validator.isMobile(smsParam.getPhone()), "手机号格式不正确");
        SmsRequestBo smsRequestBo = SmsRequestBo.builder()
                .appCode(SmsCenterAppCodeEnum.QYL.name())
                .phoneNum(smsParam.getPhone())
                .templateId(validateCodeTemplateId)
                .isH5Scene(SmsCenterH5SceneEnum.H5.getH5Scene())
                .yzmType(SmsCenterYzmTypeEnum.H5_LOGIN.getCode())
                .extraData("5分钟")
                .clientIp(ServletUtil.getClientIP(request))
                .sessionId(smsParam.getSessionId())
                .sig(smsParam.getSig())
                .token(smsParam.getToken())
                .scene(smsParam.getScene())
                .build();
        String url = qylGatewayDomain + "/sms/sendYzmSms";
        try {
            log.info("调用短信中心发送短信 request {}", JSONUtil.toJsonStr(smsRequestBo));
            HttpResponse response = HttpUtil.createPost(url)
                    .body(JSONUtil.toJsonStr(smsRequestBo))
                    .timeout(3 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                log.info("调用短信中心发送短信 responseBody {}", response.body());
                if (null != jsonObject.get("code") && jsonObject.get("code").toString().equals("200")) {
                    return "短信验证码发送成功";
                }else {
                    throw new BaseException("调用短信中心发送短信失败：" +  jsonObject.get("msg"));
                }
            } else {
                log.warn("调用短信中心发送短信失败 {}", JSONUtil.toJsonStr(response));
                throw new BaseException("调用短信中心发送短信失败：" + JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BaseException("验证码校验时发生错误：" + e.getMessage());
		}
    }

    @Override
    public MathH5TokenResult login(SmsLoginParam smsLoginParam, HttpServletResponse response) {
       //校验短信验证码
        try {
            boolean verifyCode = this.verifyCodeBySmsCenter(smsLoginParam);
            if (!verifyCode) {
                throw new BaseException("请输入正确的验证码!");
            }
        } catch (BaseException e) {
            log.warn("验证码校验失败 {}, 错误信息:{}", smsLoginParam, e.getMessage());
            throw new BaseException(e.getMessage());
        }
        Long userId = basicBusinessService.addParent(smsLoginParam.getUsername());
        if (null == userId) {
            throw new BaseException("登录失败，用户注册失败");
        }
        MathH5TokenResult mathH5TokenResult = new MathH5TokenResult();
       // 生成token 并返回
        try {
            Map<String, String> map = new HashMap<String, String>();
            LocalDateTime time = LocalDateTime.now().plusSeconds(Constant.TOKEN_TIME);
            map.put("endTime", time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            map.put("userId", Convert.toStr(userId));
            map.put("phone", smsLoginParam.getUsername());
            String token = JWTUtil.createJWT(map);
            mathH5TokenResult.setPhone(smsLoginParam.getUsername());
            mathH5TokenResult.setToken(token);
            mathH5TokenResult.setUserId(userId);
            redisUtil.set(Constant.QYL_LOGIN_REDIS_CACHE_NAME + userId, token, Constant.TOKEN_TIME);
        } catch (Exception e) {
            throw new BaseException("登录失败：" + e.getMessage());
        }
        return mathH5TokenResult;
    }

    @Override
    public String logout(HttpServletRequest request) {
        String token = JWTUtil.getToken(request);
        if (StrUtil.isBlank(token)) {
            throw new BaseException("退出登录失败,token不存在");
        }
        Claims claims = JWTUtil.parseJWT(token);
        Long userId = Convert.toLong(claims.get("userId"));
        redisUtil.delete(Constant.QYL_LOGIN_REDIS_CACHE_NAME + userId);
        return "退出登录成功";
    }


    private boolean verifyCodeBySmsCenter(SmsLoginParam smsLoginParam) {
        SmsRequestBo smsRequestBo = SmsRequestBo.builder()
                .appCode(SmsCenterAppCodeEnum.QYL.name())
                .phoneNum(smsLoginParam.getUsername())
                .yzmType(SmsCenterYzmTypeEnum.H5_LOGIN.getCode())
                .toBeVerifiedYzm(smsLoginParam.getValidateCode())
                .build();
        String url = qylGatewayDomain + "/sms/verifyYzm";
        try {
            log.info("调用短信中心校验短信 request {}", JSONUtil.toJsonStr(smsRequestBo));
            HttpResponse response = HttpUtil.createPost(url)
                    .body(JSONUtil.toJsonStr(smsRequestBo))
                    .timeout(3 * 1000)
                    .execute();
            if (null != response && response.isOk()) {
                log.info("调用短信中心校验短信 responseBody {}", response.body());
                JSONObject jsonObject = JSONUtil.parseObj(response.body());
                if (null != jsonObject.get("code")
                        && jsonObject.get("code").toString().equals("200")
                        && jsonObject.get("data").toString().equals("true")) {
                    return true;
                }
            } else {
                log.warn("调用短信中心校验短信失败 {}", JSONUtil.toJsonStr(response));
            }
        } catch (Exception e) {
            throw new BaseException("验证码校验时发生错误：" + e.getMessage());
        }
        return false;
    }

    @Override
    public boolean queryStudentMathMembership(Long studentId, Long parentId) {
        String url = StrUtil.format("{}/my/subjectMember/checkStudentIsOpen?studentId={}&subjectType={}", qylDomain, studentId, 6);
        HttpResponse response = HttpUtil.createGet(url)
                .setReadTimeout(GlobalConstants.REQUEST_READ_TIMEMOUT_SECONDS * 1000)
                .setConnectionTimeout(GlobalConstants.REQUEST_CONNECT_TIMEMOUT_SECONDS * 1000)
                .execute();
        if (response.isOk()) {
            log.info("请求青于蓝查询学科会员接口 url {} responseBody {}", url, response.body());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if ("200".equals(jsonObject.getStr("code"))) {
                Integer data = jsonObject.getInt("data");
                if (null != data) {
                    return data == 1;
                }
            }
        }
        throw new BaseException("查询学科会员状态接口异常");
    }

    @Override
    public boolean studentOpenProduct(Long studentId, String telNum, Integer businessDays) {
        String url = StrUtil.format("{}/my/life/subjectServe/activation", qylDomain);
        JSONObject param = new JSONObject();
        param.put("studentId", studentId);
        param.put("parentTelNum", telNum);

        param.put("productType", 6);
        param.put("operatorTelNum", telNum);
        param.put("source", "FORCE_OPEN");
        param.put("businessDays", businessDays);
        String returnBody = HttpUtil.post(url, param.toString(), GlobalConstants.REQUEST_READ_TIMEMOUT_SECONDS * 1000);
        log.info("请求青于蓝开通学科会员接口 url {} requestBody {} responseBody {}", url, param, returnBody);
        if (StringUtils.isNotBlank(returnBody)) {
            JSONObject jsonObject = JSONUtil.parseObj(returnBody);
            if (jsonObject.getInt("code") == 200) {
                log.info("activation 调用青于蓝开通会员接口成功");
                return true;
            } else {
                log.info("青于蓝开通会员接口错误返回: code={}, msg={}",
                        jsonObject.getInt("code"),
                        jsonObject.getStr("msg"));
            }
        }
        return false;
    }

    @Override
    public BusinessSubscriptionDto getStudentOpenProductInfo(Integer subjectType, Long studentId, Long parentId) {
        String url = StrUtil.format("{}/my/getStudentOpenProductInfo?studentId={}&parentId={}&subjectType={}",
                qylDomain, studentId, parentId, subjectType);
        HttpResponse response = HttpUtil.createGet(url)
                .setReadTimeout(GlobalConstants.REQUEST_READ_TIMEMOUT_SECONDS * 1000)
                .setConnectionTimeout(GlobalConstants.REQUEST_CONNECT_TIMEMOUT_SECONDS * 1000)
                .execute();
        if (response.isOk()) {
            log.info("请求青于蓝查询学科会员接口 url {} responseBody {}", url, response.body());
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            if ("200".equals(jsonObject.getStr("code"))) {
                String data = jsonObject.getStr("data");
                if (null != data) {
                    return com.alibaba.fastjson2.JSONObject.parseObject(data, BusinessSubscriptionDto.class);
                }
            }
            log.info("青于蓝查询学科会员信息接口错误返回: code={}, msg={}",
                    jsonObject.getInt("code"),
                    jsonObject.getStr("msg"));
            return null;
        }
        throw new BaseException("查询学科会员信息接口异常");
    }
}




