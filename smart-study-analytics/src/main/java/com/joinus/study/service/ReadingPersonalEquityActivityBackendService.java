package com.joinus.study.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingPersonalEquityActivity;
import com.joinus.study.model.param.ReadingPersonalEquityActivityOperateParam;
import com.joinus.study.excel.ReadingPersonalEquityActivityCodeExcel;
import com.joinus.study.model.param.ReadingPersonalEquityActivityPageParam;
import com.joinus.study.model.param.ReadingPersonalEquityActivityUsedRecordPageParam;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityDetailVo;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityPageItemVO;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityUsedRecordVo;

import com.joinus.common.exception.BaseException;
import com.joinus.study.model.param.ReadingPersonalEquityActivityParam;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityVO;

import java.util.List;

public interface ReadingPersonalEquityActivityBackendService extends IService<ReadingPersonalEquityActivity> {


    Page<ReadingPersonalEquityActivityUsedRecordVo> usedRecordPages(ReadingPersonalEquityActivityUsedRecordPageParam pageParam);

    void delete(Long id);

    void invalid(Long id);

    void invalidCode(Long id);

    /**
     * 权益活动会员标识
     *
     * @param studentId
     * @return
     */
    ReadingPersonalEquityActivityVO equityMemberFlag(Long studentId);

    Integer exchange(ReadingPersonalEquityActivityParam param) throws BaseException;

    Integer createEquityActivity(ReadingPersonalEquityActivityOperateParam param) throws BaseException;

    Integer editEquityActivity(ReadingPersonalEquityActivityOperateParam param) throws BaseException;

    ReadingPersonalEquityActivityDetailVo detailEquityActivity(Long id) throws BaseException;

    /**
     * 邀请码下载列表
     *
     * @param id
     * @return
     */
    List<ReadingPersonalEquityActivityCodeExcel> downloadListInvitationCode(Long id);

    /**
     * 邀请码使用须知
     *
     * @param code
     * @return
     */
    String notice(String code);

    Page<ReadingPersonalEquityActivityPageItemVO> pages(ReadingPersonalEquityActivityPageParam pageParam);
}
