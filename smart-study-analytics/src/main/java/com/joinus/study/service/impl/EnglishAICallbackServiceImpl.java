package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO;
import com.joinus.study.model.dto.EnglishExamDiagnoseRecordCallbackDTO;
import com.joinus.study.model.dto.EnglishWeakKnowledgePointsAnalysisDTO;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.param.EnglishExamQuestionNumberParamV2;
import com.joinus.study.model.vo.EnglishExamReportQuestionTypeVO;
import com.joinus.study.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnglishAICallbackServiceImpl implements EnglishAICallbackService {

    @Resource
    private EnglishExamDiagnoseRecordService englishExamDiagnoseRecordService;
    @Resource
    private EnglishExamDiagnoseReportService englishExamDiagnoseReportService;
    @Resource
    private EnglishExamDiagnoseQuestionService englishExamDiagnoseQuestionService;
    @Resource
    private EnglishExamReportQuestionTypeService englishExamReportQuestionTypeService;
    @Resource
    private EnglishExamReportKnowledgePointService englishExamReportKnowledgePointService;
    @Resource
    private EnglishPersonalWeakKnowledgePointService englishPersonalWeakKnowledgePointService;
    @Resource
    private EnglishFowDeputyQuestionService englishFowDeputyQuestionService;
    @Resource
    private EnglishExamService englishExamService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void examDiagnoseRecordCallback(EnglishExamDiagnoseRecordCallbackDTO param) {
        EnglishExamDiagnoseRecord record = englishExamDiagnoseRecordService.getById(param.getRequestId());
        if (ObjectUtil.isNotNull(record)) {
            //更新诊断记录问题表
            List<EnglishExamDiagnoseQuestion> englishExamDiagnoseQuestions = updateDiagnoseQuestion(record, param.getFalseQuestionNum());
            if (CollectionUtil.isNotEmpty(englishExamDiagnoseQuestions) && record.getStatus() == 0) {
                //更新记录得分
                record.setQuestionNumber(englishExamDiagnoseQuestions.size());
                record.setScore(englishExamDiagnoseQuestions.stream()
                        .filter(t -> !t.getIsWrong()).map(EnglishExamDiagnoseQuestion::getScore).reduce(BigDecimal.ZERO, BigDecimal::add));
                record.setStatus(1);
                // 更新AI解析出来的实际的题目数量（首次返回题号的数量和解析试卷后的题目数量可能不一致，以实际解析出来的试题数量为准）
                EnglishExam exam = englishExamService.getById(record.getExamId());
                record.setQuestionNumber(exam.getSmallQuestionsNum());
                englishExamDiagnoseRecordService.updateById(record);
                //生成报告
                EnglishExamDiagnoseReport englishExamDiagnoseReport = saveReport(record, englishExamDiagnoseQuestions, param);
                //生成题目类型分析
                saveQuestionType(record, englishExamDiagnoseReport);
                //保存薄弱知识点
                savePoint(record, englishExamDiagnoseReport, param);
                englishExamDiagnoseRecordService.sendMessage(record.getId(), record.getStudentId());
            }
        }
    }

    private List<EnglishExamDiagnoseQuestion> updateDiagnoseQuestion(EnglishExamDiagnoseRecord record, List<EnglishExamQuestionNumberParamV2> falseQuestionNumber) {
        // 查询试卷所有题目
        List<EnglishFowDeputyQuestion> englishFowDeputyQuestions = englishFowDeputyQuestionService.listByExamId(record.getExamId());
        if (CollectionUtil.isNotEmpty(englishFowDeputyQuestions)) {
            List<EnglishExamDiagnoseQuestion> englishExamDiagnoseQuestions = englishFowDeputyQuestions.stream().map(question -> {
                EnglishExamDiagnoseQuestion englishExamDiagnoseQuestion = EnglishExamDiagnoseQuestion.builder()
                        .recordId(record.getId())
                        .examId(record.getExamId())
                        .questionId(question.getId())
                        .questionNumber(question.getOrderNo())
                        .bigQuestionNumber(question.getQuestionNum())
                        .isWrong(false)
                        .questionType(question.getType())
                        .score(question.getScore())
                        .build();
                if (CollectionUtil.isNotEmpty(falseQuestionNumber)) {
                    falseQuestionNumber.stream().filter(t -> t.getNum().equals(englishExamDiagnoseQuestion.getBigQuestionNumber()))
                            .filter(t -> t.getChild().contains(englishExamDiagnoseQuestion.getQuestionNumber()))
                            .findAny().ifPresent(t -> englishExamDiagnoseQuestion.setIsWrong(true));
                }
                return englishExamDiagnoseQuestion;
            }).collect(Collectors.toList());
            englishExamDiagnoseQuestionService.saveBatch(englishExamDiagnoseQuestions);
            return englishExamDiagnoseQuestions;
        }
        return new ArrayList<>();
    }

    private EnglishExamDiagnoseReport saveReport(EnglishExamDiagnoseRecord record, List<EnglishExamDiagnoseQuestion> englishExamDiagnoseQuestions, EnglishExamDiagnoseRecordCallbackDTO param) {
        EnglishExamDiagnoseReport englishExamDiagnoseReport = new EnglishExamDiagnoseReport();
        englishExamDiagnoseReport.setRecordId(record.getId());
        englishExamDiagnoseReport.setExamId(record.getExamId());
        englishExamDiagnoseReport.setSummary(param.getSummary());
        String code = "YY%06d";
        englishExamDiagnoseReport.setCode(String.format(code, record.getId()));
        englishExamDiagnoseReport.setQuestionNumber(englishExamDiagnoseQuestions.size());
        List<EnglishExamDiagnoseQuestion> wrongQuestion = englishExamDiagnoseQuestions.stream().filter(EnglishExamDiagnoseQuestion::getIsWrong).collect(Collectors.toList());
        englishExamDiagnoseReport.setWrongQuestionNumber(wrongQuestion.size());
        Integer questionNumber = englishExamDiagnoseReport.getQuestionNumber();
        englishExamDiagnoseReport.setRightQuestionNumber(questionNumber - englishExamDiagnoseReport.getWrongQuestionNumber());
        if (questionNumber > 0) {
            BigDecimal rate = BigDecimal.valueOf(englishExamDiagnoseReport.getRightQuestionNumber()).divide(BigDecimal.valueOf(questionNumber), 4, RoundingMode.HALF_UP);
            englishExamDiagnoseReport.setAccuracyRate(rate.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.UP));
        }
        englishExamDiagnoseReport.setKnowledgePointNumber(englishExamDiagnoseQuestionService.queryKnowledgePointNumber(record.getId()));
        englishExamDiagnoseReport.setWeakKnowledgePointNumber(englishExamDiagnoseQuestionService.queryWeakKnowledgeNumber(record.getId()));
        englishExamDiagnoseReport.setMasterKnowledgePointNumber(englishExamDiagnoseReport.getKnowledgePointNumber() - englishExamDiagnoseReport.getWeakKnowledgePointNumber());
        englishExamDiagnoseReportService.save(englishExamDiagnoseReport);
        return englishExamDiagnoseReport;
    }

    private void saveQuestionType(EnglishExamDiagnoseRecord record, EnglishExamDiagnoseReport englishExamDiagnoseReport) {
        List<EnglishExamReportQuestionType> questionTypes = new ArrayList<>();
        List<EnglishExamReportQuestionTypeVO> examReportQuestionTypeVOS = englishExamDiagnoseQuestionService.listQuestionType(record.getId());
        examReportQuestionTypeVOS.forEach(questionTypeVO -> {
            int right = questionTypeVO.getQuestionNumber() - questionTypeVO.getWrongQuestionNumber();
            if (questionTypeVO.getQuestionNumber() > 0) {
                BigDecimal divided = BigDecimal.valueOf(right).divide(BigDecimal.valueOf(questionTypeVO.getQuestionNumber()), 4, RoundingMode.HALF_UP);
                questionTypeVO.setAccuracyRate(divided.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.UP));
            }
            EnglishExamReportQuestionType reportQuestionType = EnglishExamReportQuestionType.builder()
                    .recordId(record.getId())
                    .reportId(englishExamDiagnoseReport.getId())
                    .questionType(questionTypeVO.getQuestionType())
                    .questionNumber(questionTypeVO.getQuestionNumber())
                    .wrongQuestionNumber(questionTypeVO.getWrongQuestionNumber())
                    .rightQuestionNumber(right)
                    .accuracyRate(questionTypeVO.getAccuracyRate())
                    .build();
            questionTypes.add(reportQuestionType);
        });
        englishExamReportQuestionTypeService.saveBatch(questionTypes);
    }

    private void savePoint(EnglishExamDiagnoseRecord record, EnglishExamDiagnoseReport report, EnglishExamDiagnoseRecordCallbackDTO param) {
        List<EnglishWeakKnowledgePointsAnalysisDTO> knowledgePointsAnalysis = param.getWeakKnowledgePointsAnalysis();
        List<EnglishExamReportKnowledgePoint> knowledgePoints = new ArrayList<>();
        List<EnglishPersonalWeakKnowledgePoint> insertList = new ArrayList<>();
        List<EnglishPersonalWeakKnowledgePoint> updateList = new ArrayList<>();
        knowledgePointsAnalysis.forEach(dto -> {
            List<EnglishExamDiagnoseQuestionDTO> examDiagnoseQuestionList = englishExamDiagnoseQuestionService.listByRecordIdAndKnowledgePointId(record.getId(), dto.getKnowledgePointId());
            Integer totalCount = englishExamDiagnoseQuestionService.queryCountByPointId(record.getId(), dto.getKnowledgePointId());
            Integer wrongCount = englishExamDiagnoseQuestionService.queryWrongCountByPointId(record.getId(), dto.getKnowledgePointId());
            EnglishPersonalWeakKnowledgePoint personalWeakKnowledgePoint = englishPersonalWeakKnowledgePointService.selectByPointIdAndStudentId(dto.getKnowledgePointId(), record.getStudentId());
            //不存在新增，已经存在更新
            List<Integer> questionNumbers = dto.getQuestionNumbers();
            String questionTypeStr = examDiagnoseQuestionList.stream().filter(p -> questionNumbers.contains(p.getQuestionNumber())).map(q -> q.getQuestionType()).collect(Collectors.joining(","));
            if (personalWeakKnowledgePoint == null) {
                personalWeakKnowledgePoint = new EnglishPersonalWeakKnowledgePoint();
                personalWeakKnowledgePoint.setKnowledgePointId(dto.getKnowledgePointId());
                personalWeakKnowledgePoint.setKnowledgePointName(dto.getKnowledgePointName());
                personalWeakKnowledgePoint.setDiagnoseRecordId(String.valueOf(record.getId()));
                personalWeakKnowledgePoint.setQuestionType(questionTypeStr);
                personalWeakKnowledgePoint.setAnsweredQuestionCount(totalCount);
                personalWeakKnowledgePoint.setWrongAnswerCount(wrongCount);
                if (totalCount > 0) {
                    BigDecimal errorPercentage = BigDecimal.valueOf(wrongCount).divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
                    personalWeakKnowledgePoint.setErrorPercentage(errorPercentage);
                }
                personalWeakKnowledgePoint.setStatus(1);
                personalWeakKnowledgePoint.setStudentId(record.getStudentId());
                personalWeakKnowledgePoint.setCreatedAt(new Date());
                personalWeakKnowledgePoint.setUpdatedAt(new Date());
                insertList.add(personalWeakKnowledgePoint);
            } else {
                String diagnoseRecordId = personalWeakKnowledgePoint.getDiagnoseRecordId();
                //逗号拼接id串
                String diagnoseRecordIds = diagnoseRecordId + ",";
                personalWeakKnowledgePoint.setDiagnoseRecordId(diagnoseRecordIds + record.getId());
                personalWeakKnowledgePoint.setUpdatedAt(new Date());
                personalWeakKnowledgePoint.setAnsweredQuestionCount(personalWeakKnowledgePoint.getAnsweredQuestionCount() + totalCount);
                personalWeakKnowledgePoint.setWrongAnswerCount(personalWeakKnowledgePoint.getWrongAnswerCount() + wrongCount);
                String questionType = personalWeakKnowledgePoint.getQuestionType() + "," + questionTypeStr;
                String distinctQuestionType = String.join(",", Arrays.stream(questionType.split(",")).collect(Collectors.toSet()));
                personalWeakKnowledgePoint.setQuestionType(distinctQuestionType);
                Integer answeredQuestionCount = personalWeakKnowledgePoint.getAnsweredQuestionCount();
                Integer wrongAnswerCount = personalWeakKnowledgePoint.getWrongAnswerCount();
                if (answeredQuestionCount > 0) {
                    BigDecimal errorPercentage = BigDecimal.valueOf(wrongAnswerCount).divide(BigDecimal.valueOf(answeredQuestionCount), 2, RoundingMode.HALF_UP);
                    personalWeakKnowledgePoint.setErrorPercentage(errorPercentage);
                }
                personalWeakKnowledgePoint.setStatus(1);
                updateList.add(personalWeakKnowledgePoint);
            }
            EnglishExamReportKnowledgePoint knowledgePoint = EnglishExamReportKnowledgePoint.builder()
                    .recordId(record.getId())
                    .reportId(report.getId())
                    .knowledgePointsId(dto.getKnowledgePointId())
                    .knowledgePointsName(dto.getKnowledgePointName())
                    .questionNumber(dto.getQuestionNumbers().stream().map(String::valueOf).collect(Collectors.joining(",")))
                    .questionType(questionTypeStr)
                    .errorReason(dto.getReason())
                    .overcomeSecret(dto.getSolution())
                    .build();
            knowledgePoints.add(knowledgePoint);
        });
        //更新薄弱知识点表
        englishPersonalWeakKnowledgePointService.saveBatch(insertList);
        englishPersonalWeakKnowledgePointService.updateBatchById(updateList);
        englishExamReportKnowledgePointService.saveBatch(knowledgePoints);
    }
}
