package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.EnglishExamDiagnoseRecord;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.EnglishExamAnalysisVO;
import com.joinus.study.model.vo.EnglishExamDiagnoseDetailVO;
import com.joinus.study.model.vo.EnglishExamDiagnoseRecordVO;
import com.joinus.study.model.vo.OssTokenVo;

import javax.validation.Valid;
import java.util.List;

public interface EnglishExamDiagnoseRecordService extends IService<EnglishExamDiagnoseRecord> {

    Page<EnglishExamDiagnoseRecordVO> pages(EnglishDiagnoseReportPageParam param);

    EnglishExamDiagnoseDetailVO query(Long id);

    void sendMessage(Long id, Long studentId);

    OssTokenVo ossToken(@Valid OssTokenParam param);

    EnglishExamAnalysisVO analysisExam(EnglishExamAnalysisParam param);

    void submitRecord(@Valid EnglishExamAnalysisSubmitReportParam param);

    List<String> queryOssUrl(@Valid EnglishExamQuryOssUrlParam param);

}
