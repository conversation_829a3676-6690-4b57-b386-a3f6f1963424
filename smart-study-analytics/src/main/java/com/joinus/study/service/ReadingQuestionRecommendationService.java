package com.joinus.study.service;

import com.joinus.common.exception.BaseException;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;

/**
 * <AUTHOR>
 * @description 薄弱知识点服务
 * @date 2025/5/9
 */
public interface ReadingQuestionRecommendationService {

    /**
     * @Description 【定向爆破】获取薄弱知识点定向爆破题目
     * <AUTHOR>
     * @date 2025/5/6
     */
    ReadingPersonalPassagesVo getDirectionalBlastingQuestions(ReadingStudentPassagesQueryParam param) throws BaseException;

    /**
     * @description: 【定向爆破】获取定向爆破题目v2策略
     * @author: lifengxu
     * @date: 2025/6/9
     */
    ReadingPersonalPassagesVo getDirectionalBlastingQuestionsTwo(ReadingStudentPassagesQueryParam param) throws BaseException;

    /**
     * @Description 获取练习题目（阅读训练营或练习计划）
     * <AUTHOR>
     * @date 2025/5/13
     */
    ReadingPersonalPassagesVo getPracticeQuestions(ReadingStudentPassagesQueryParam param) throws BaseException;

    /**
     * @description: 获取假期练习题目
     * @author: lifengxu
     * @date: 2025/5/28
     */
    ReadingPersonalPassagesVo getActivityPracticeQuestions(ReadingStudentPassagesQueryParam param) throws BaseException;
}
