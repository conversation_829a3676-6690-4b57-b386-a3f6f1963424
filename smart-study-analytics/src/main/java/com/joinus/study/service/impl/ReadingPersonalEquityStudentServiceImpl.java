package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.study.mapper.ReadingPersonalEquityStudentMapper;
import com.joinus.study.model.entity.ReadingPersonalEquityStudent;
import com.joinus.study.service.ReadingPersonalEquityStudentService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@AllArgsConstructor
@Service("readingPersonalEquityStudentService")
@Slf4j
public class ReadingPersonalEquityStudentServiceImpl extends BaseServiceImpl<ReadingPersonalEquityStudentMapper, ReadingPersonalEquityStudent> implements ReadingPersonalEquityStudentService {

    @Override
    public List<ReadingPersonalEquityStudent> list(ReadingPersonalEquityStudent queryEntity) {
        LambdaQueryWrapper<ReadingPersonalEquityStudent> wrapper = Wrappers.lambdaQuery(ReadingPersonalEquityStudent.class)
                .eq(queryEntity != null && queryEntity.getStudentId() != null, ReadingPersonalEquityStudent::getStudentId, queryEntity.getStudentId())
                .eq(queryEntity != null && queryEntity.getStatus() != null, ReadingPersonalEquityStudent::getStatus, queryEntity.getStatus())
                .isNull(ReadingPersonalEquityStudent::getDeletedAt)
                .apply("CURRENT_TIMESTAMP BETWEEN start_date AND end_date");

        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void expired() {
        this.update(Wrappers.lambdaUpdate(ReadingPersonalEquityStudent.class)
                .set(ReadingPersonalEquityStudent::getStatus, 2)
                .apply("CURRENT_TIMESTAMP > end_date")
                .isNull(ReadingPersonalEquityStudent::getDeletedAt));
    }
}
