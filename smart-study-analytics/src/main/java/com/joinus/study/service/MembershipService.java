package com.joinus.study.service;

import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.BusinessSubscriptionDto;
import com.joinus.study.model.param.MathMembershipNoticeParam;

/**
 * 会员服务接口
 * 用于检查学生是否拥有会员权限
 */
public interface MembershipService {
    
    /**
     * 检查指定学生是否是有效会员
     *
     * @param studentId 学生ID
     * @return true表示是有效会员，false表示非会员或会员已过期
     */
    boolean isMathHolidayActivityMember(Long studentId, Long parentId);
    
    /**
     * 获取当前登录学生的会员状态
     * 
     * @return true表示当前用户是有效会员，false表示非会员或会员已过期
     */
    boolean isCurrentUserMember();

    boolean queryStudentMathMembership(Long studentId, Long parentId);

    void updateMathMembershipStatus(MathMembershipNoticeParam param) throws BaseException;

    //赠送会员
    boolean giftMathMember(Long studentId, Long parentId, Integer businessDays) throws BaseException;

    BusinessSubscriptionDto getStudentMathMemberInfo(Long studentId, Long parentId);
}
