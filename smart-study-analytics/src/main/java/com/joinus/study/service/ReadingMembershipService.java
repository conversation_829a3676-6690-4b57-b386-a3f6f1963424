package com.joinus.study.service;

import com.joinus.common.exception.BaseException;

/**
 * 会员服务接口
 * 用于检查学生是否拥有会员权限
 */
public interface ReadingMembershipService {


    /**
     *  检查学生会员权限
     * @param studentId
     * @param parentId
     * @param subjectType 学科
     */
    void checkStudentMembership(Long studentId, Long parentId,Integer subjectType) throws BaseException;

    Boolean checkStudentIsOpenMember(Long studentId, Long parentId ,Integer subjectType) throws BaseException;
}
