package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.ReadingPersonalEquityStudent;

import java.util.List;

public interface ReadingPersonalEquityStudentService extends IService<ReadingPersonalEquityStudent> {
    /**
     * 查询生效中的记录列表
     *
     * @param queryEntity
     * @return
     */
    List<ReadingPersonalEquityStudent> list(ReadingPersonalEquityStudent queryEntity);

    void expired();
}
