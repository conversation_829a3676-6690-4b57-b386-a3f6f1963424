package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.entity.MathInvitation;

/**
* <AUTHOR>
* @description 针对表【math_invitation(数学邀请记录表)】的数据库操作Service
* @createDate 2025-06-25 17:29:13
*/
public interface MathInvitationService extends IService<MathInvitation> {

    void mathInvitationAdd(MathInvitation param) throws BaseException;
}
