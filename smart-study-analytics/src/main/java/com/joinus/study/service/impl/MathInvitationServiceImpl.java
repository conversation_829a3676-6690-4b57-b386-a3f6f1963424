package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.study.mapper.MathInvitationMapper;
import com.joinus.study.model.entity.MathInvitation;
import com.joinus.study.service.MathInvitationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_invitation(数学邀请记录表)】的数据库操作Service实现
* @createDate 2025-06-25 17:29:13
*/
@Service
public class MathInvitationServiceImpl extends ServiceImpl<MathInvitationMapper, MathInvitation> implements MathInvitationService {

    @Autowired
    private MathInvitationMapper mathInvitationMapper;

    @Override
    public void mathInvitationAdd(MathInvitation param) throws BaseException {
        QueryWrapper<MathInvitation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inviter_student_id", param.getInviterStudentId());
        queryWrapper.eq("inviter_phone", param.getInviterPhone());
        queryWrapper.eq("invitee_student_id", param.getInviteeStudentId());
        queryWrapper.eq("invitee_phone", param.getInviteePhone());
        queryWrapper.eq("type", "HOLIDAY_TRAINING");
        MathInvitation mathInvitation = mathInvitationMapper.selectOne(queryWrapper);
        if (mathInvitation != null) {
            throw new BaseException("邀请人和被邀请人关系已存在");
        }
        //获取邀请人的路径
        queryWrapper.clear();
        queryWrapper.eq("invitee_student_id", param.getInviterStudentId());
        queryWrapper.eq("invitee_phone", param.getInviterPhone());
        queryWrapper.eq("type", "HOLIDAY_TRAINING");
        List<MathInvitation> mathInvitations = mathInvitationMapper.selectList(queryWrapper);

        // 构建新路径
        String newPath = (CollectionUtil.isEmpty(mathInvitations))
                ? param.getInviterStudentId()+"_"+param.getInviterPhone() + "." + param.getInviteeStudentId()+"_"+param.getInviteePhone()
                : mathInvitations.get(0).getPath() + "." + param.getInviteeStudentId()+"_"+param.getInviteePhone();

        // 创建邀请记录
        param.setPath(newPath);
        param.setType("HOLIDAY_TRAINING");
        mathInvitationMapper.mathInvitationAdd(param);
    }
}




