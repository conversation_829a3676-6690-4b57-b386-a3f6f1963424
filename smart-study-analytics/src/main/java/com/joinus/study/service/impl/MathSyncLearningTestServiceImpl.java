package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.exception.BaseException;
import com.joinus.study.mapper.MathStudentSyncLearningTestMapper;
import com.joinus.study.mapper.PersonalExamMapper;
import com.joinus.study.mapper.StudyRecordMapper;
import com.joinus.study.model.bo.MathCatalogNodeStatisticsDto;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.MathStudentKnowledgePoint;
import com.joinus.study.model.entity.MathStudentSyncLearningTest;
import com.joinus.study.model.enums.*;
import com.joinus.study.model.param.MistakeSetQuestionsParam;
import com.joinus.study.model.param.PresignedUrlParam;
import com.joinus.study.model.param.QuestionSortNo;
import com.joinus.study.model.param.SpecializedTrainingUpdateExamParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.utils.DataUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sound.midi.Soundbank;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MathSyncLearningTestServiceImpl implements MathSyncLearningTestService {

    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private QuestionKnowledgePointService questionKnowledgePointService;
    @Autowired
    private MathStudentSyncLearningTestMapper mathStudentSyncLearningTestMapper;
    @Resource
    private AiRequestService aiRequestService;
    @Resource
    private MathStudentKnowledgePointService mathStudentKnowledgePointService;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private StudyRecordMapper studyRecordMapper;
    @Resource
    private EduKnowLedgeHubBusinessService businessService;
    @Override
    public List<MathCatalogNodeVo> listCatalogRootNodes(Integer grade, PublisherEnum publisher, Integer semester, Long studentId) {
        List<MathCatalogNodeDto> mathNodeDtos = eduKnowledgeHubService.listCatalogNodes(grade, publisher, semester);
        if (CollUtil.isEmpty(mathNodeDtos)) {
            return List.of();
        }

        UUID bookId = mathNodeDtos.get(0).getBookId();

        mathNodeDtos.stream().forEach(mathNodeDto -> {mathNodeDto.setSubNodes(List.of());});

        List<MathCatalogNodeVo> results = mathNodeDtos.stream().map(MathCatalogNodeVo::ofMathCatalogNodeDto).collect(Collectors.toList());

        results.stream().forEach(mathCatalogNodeVo -> {
            mathCatalogNodeVo.setNodeType(mathCatalogNodeVo.getIsLeaf() ? MathSyncLearningTestNodeTypeEnum.SECTION_TEST : MathSyncLearningTestNodeTypeEnum.CATALOG_NODE);
            mathCatalogNodeVo.setNamePrefix(grade < 7 ? StrUtil.toString(mathCatalogNodeVo.getSortNo()) : StrUtil.format("第{}章",  NumberChineseFormatter.format(mathCatalogNodeVo.getSortNo(), false)));
        });

        List<MathCatalogNodeVo> leafNodes = results.stream().filter(MathCatalogNodeVo::getIsLeaf).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(leafNodes)) {
            //查询节点下的知识点熟练、题型数量、掌握度
            List<MathCatalogNodeStatisticsDto> nodeStatistics = eduKnowledgeHubService.listKnowledgePointAndQuestionTypeCountByNodeIds(leafNodes.stream().map(MathCatalogNodeVo::getId).collect(Collectors.toList()));
            Map<UUID, MathCatalogNodeStatisticsDto> nodeStatisticsMap = nodeStatistics.stream().collect(Collectors.toMap(MathCatalogNodeStatisticsDto::getCatalogNodeId, v -> v));
            //获取叶子节点的知识点数量、题型数量、掌握度
            leafNodes.stream().forEach(node -> {
                node.setKnowledgePointCount(nodeStatisticsMap.get(node.getId()).getKnowledgePointCount());
                node.setQuestionTypeCount(nodeStatisticsMap.get(node.getId()).getQuestionTypeCount());
                node.setMasteryDegree(selectNodesMasteryDegree(node.getId(), studentId, MathSyncLearningTestNodeTypeEnum.SECTION_TEST));
            });
        }

        results.stream().sorted(Comparator.comparing(MathCatalogNodeVo::getSortNo));
        Double comprehensiveTestMasteryDegree = selectNodesMasteryDegree(bookId, studentId, MathSyncLearningTestNodeTypeEnum.COMPREHENSIVE_TEST);
        results.add(MathCatalogNodeVo.builder().id(bookId)
                .name("综合测试")
                .nodeType(MathSyncLearningTestNodeTypeEnum.COMPREHENSIVE_TEST)
                .sortNo(results.get(results.size()-1).getSortNo() + 1)
                .isLeaf(true)
                .masteryDegree(comprehensiveTestMasteryDegree)
                .build());
        return results;
    }

    @Override
    public List<MathCatalogNodeVo> listCatalogSubNodes(UUID rootNodeId, Long studentId) {
        List<MathCatalogNodeDto> mathNodeDtos = eduKnowledgeHubService.listCatalogSubNodes(rootNodeId);
        if (CollUtil.isEmpty(mathNodeDtos)) {
            return List.of();
        }
        MathCatalogNodeDto catalogNodeByid = eduKnowledgeHubService.getCatalogNodeByid(rootNodeId);

        List<MathCatalogNodeVo> mathCatalogNodeVos = buildTree(mathNodeDtos, mathNodeDtos.get(0).getGrade(),  MathCatalogNodeVo.ofMathCatalogNodeDto(catalogNodeByid));

        if (mathCatalogNodeVos.size() > 1) {
            MathCatalogNodeVo lastNode = mathCatalogNodeVos.get(mathCatalogNodeVos.size() - 1);
            mathCatalogNodeVos.add(MathCatalogNodeVo.builder()
                    .id(lastNode.getParentId())
                    .parentId(lastNode.getParentId())
                    .name("章末测试")
                    .level(lastNode.getLevel())
                    .nodeType(MathSyncLearningTestNodeTypeEnum.CHAPTER_TEST)
                    .sortNo(lastNode.getSortNo() + 1)
                    .subNodes(List.of())
                    .isLeaf(true)
                    .build());
        }

        //过滤出所有的小节测试节点
        List<MathCatalogNodeVo> sectionNodes = mathCatalogNodeVos.stream()
                .flatMap(MathCatalogNodeVo::flattenNodes)
                .filter(node -> node.getNodeType() != null && node.getNodeType() == MathSyncLearningTestNodeTypeEnum.SECTION_TEST)
                .collect(Collectors.toList());
        //过滤出所有的章节测试节点
        List<MathCatalogNodeVo> chapterNodes = mathCatalogNodeVos.stream()
                .flatMap(MathCatalogNodeVo::flattenNodes)
                .filter(node -> node.getNodeType() != null && node.getNodeType() == MathSyncLearningTestNodeTypeEnum.CHAPTER_TEST)
                .collect(Collectors.toList());

        List<MathCatalogNodeStatisticsDto> nodeStatistics = eduKnowledgeHubService.listKnowledgePointAndQuestionTypeCountByNodeIds(sectionNodes.stream().map(MathCatalogNodeVo::getId).collect(Collectors.toList()));
        Map<UUID, MathCatalogNodeStatisticsDto> nodeStatisticsMap = nodeStatistics.stream().collect(Collectors.toMap(MathCatalogNodeStatisticsDto::getCatalogNodeId, v -> v));

        //获取叶子节点的知识点数量、题型数量、掌握度
        sectionNodes.stream().forEach(node -> {
            node.setKnowledgePointCount(nodeStatisticsMap.get(node.getId()).getKnowledgePointCount());
            node.setQuestionTypeCount(nodeStatisticsMap.get(node.getId()).getQuestionTypeCount());
            node.setMasteryDegree(selectNodesMasteryDegree(node.getId(), studentId, MathSyncLearningTestNodeTypeEnum.SECTION_TEST));
        });
        //获取章末测试的掌握度
        chapterNodes.stream().forEach(node -> node.setMasteryDegree(selectNodesMasteryDegree(node.getId(), studentId, MathSyncLearningTestNodeTypeEnum.CHAPTER_TEST)));

        return mathCatalogNodeVos;
    }

    @Override
    public MathSectionPointsVo getSectionDetailInfo(UUID sectionId,Long studentId) {
        MathSectionPointsVo sectionPointsVo = new MathSectionPointsVo();
        List<UUID> sectionIdList = List.of(sectionId);

        List<MathKnowledgePointVO> points = questionKnowledgePointService.getKnowledgePointBySectionId(sectionId,studentId);
        if (CollUtil.isNotEmpty(points)) {
            List<MathStudentKnowledgePoint> studentKnowledgePoints = mathStudentKnowledgePointService.listByStudentId(studentId, points.stream().map(MathKnowledgePointVO::getId).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(studentKnowledgePoints)) {
                Map<UUID, MathStudentKnowledgePoint> studentKnowledgePointMap = studentKnowledgePoints.stream().collect(Collectors.toMap(MathStudentKnowledgePoint::getKnowledgePointId, v -> v));
                points.stream().forEach(point -> {
                    point.setPptHtmlsCompletedState(studentKnowledgePointMap.get(point.getId()) == null ? KnowledgePointPptHtmlsStateEnum.NOT_COMPLETED : studentKnowledgePointMap.get(point.getId()).getPptHtmlsCompleted());
                });
            }
        }

        sectionPointsVo.setPoints(points);

        List<QuerySectionVideoDto> videoDtos = eduKnowledgeHubService.querySectionVideos(sectionIdList);
        sectionPointsVo.setSectionVideos(videoDtos);

        return sectionPointsVo;
    }

    /**
     * 获取节点的掌握度
     * @param nodeId 节点id
     * @param studentId 必传
     * @param testType 节点类型MathSyncLearningTestNodeTypeEnum
     * @return
     */
    @Override
    public Double selectNodesMasteryDegree(UUID nodeId, Long studentId, MathSyncLearningTestNodeTypeEnum testType) {
        if (MathSyncLearningTestNodeTypeEnum.SECTION_TEST == testType) {
            return mathStudentSyncLearningTestMapper.selectNodesMasteryDegree(nodeId, studentId, null);
        } else {
            //查询章节测试和综合测试节点的最新考情分析报告id
            Long lastExamAnalyzeResultId = mathStudentSyncLearningTestMapper.getLastExamAnalyzeResultId(nodeId, studentId);
            if (lastExamAnalyzeResultId == null) {
                return null;
            }
            return mathStudentSyncLearningTestMapper.selectNodesMasteryDegree(null, studentId, lastExamAnalyzeResultId);
        }
    }

    @Override
    public void saveSyncLearningTestRecord(SpecializedTrainingUpdateExamParam param) {
        log.info("保存同步学习测试记录:{}", param);
        if (param.getGrade() != null && param.getSemester() != null && param.getTestType() != null) {
            QueryWrapper<MathStudentSyncLearningTest> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("grade", param.getGrade());
            queryWrapper.eq("semester", param.getSemester());
            queryWrapper.eq("test_type", param.getTestType());
            queryWrapper.eq("student_id", param.getStudentId());
            queryWrapper.eq("publisher", param.getPublisher());
            queryWrapper.eq("catalog_node_id", param.getCatalogNodeId());
            queryWrapper.isNull("deleted_at");
            MathStudentSyncLearningTest mathStudentSyncLearningTest = mathStudentSyncLearningTestMapper.selectOne(queryWrapper);
            if (mathStudentSyncLearningTest == null) {
                MathStudentSyncLearningTest build = MathStudentSyncLearningTest.builder().studentId(param.getStudentId())
                        .publisher(param.getPublisher().getValue())
                        .grade(param.getGrade())
                        .semester(param.getSemester())
                        .testType(param.getTestType())
                        .catalogNodeId(param.getCatalogNodeId())
                        .examIdHistory(new UUID[]{param.getExamId()})
                        .build();
                mathStudentSyncLearningTestMapper.insert(build);
            } else {
                mathStudentSyncLearningTest.setExamIdHistory(ArrayUtils.add((UUID[]) mathStudentSyncLearningTest.getExamIdHistory(), param.getExamId()));
                mathStudentSyncLearningTest.setUpdatedAt(new Date());
                mathStudentSyncLearningTestMapper.updateById(mathStudentSyncLearningTest);
            }
        }
    }

    @Override
    public MathKnowledgePointHandoutVo getMathKnowledgePointVOById(UUID knowledgePointId, String publisher) {
        List<KnowledgePointChapterDto> chapterDtos = aiRequestService
                .mathKnowledgePointsChapter(null, null, null, knowledgePointId.toString());
        if (CollUtil.isNotEmpty(chapterDtos)) {
            MathKnowledgePointHandoutVo explanation = questionKnowledgePointService.getExplanationByKnowledgePointId(knowledgePointId);
            explanation.setKnowledgePointId(knowledgePointId);
            explanation.setKnowledgePointName(chapterDtos.get(0).getKnowledgePointName());
            explanation.setPublisher(PublisherEnum.valueOf(chapterDtos.get(0).getPublisher()).getDescription());
            List<MathCatalogNodeDto> catalogNodes = chapterDtos.get(0).getCatalogNodes();
            catalogNodes.sort(Comparator.comparing(MathCatalogNodeDto::getLevel));
            explanation.setCatalogNodes(catalogNodes);
            explanation.setGrade(GradeEnum.ofValue(chapterDtos.get(0).getGrade()).getDesc());
            explanation.setSemester(BookVolumeEnum.getBookVolumeDesc(chapterDtos.get(0).getSemester()));
            return explanation;
        }
        return null;
    }

    @Override
    public SpecializedTrainingNewResultVoV2 createQuestionsByKnowledgePoint(UUID knowledgePointId,PublisherEnum  publisher) {
        List<UUID> questions = eduKnowledgeHubService.createQuestionsByKnowledgePoint(knowledgePointId);
        return this.buildSpecializedTrainingNewResultByIds(questions, publisher,knowledgePointId);
    }

    @Override
    public SpecializedTrainingNewResultVoV2 createMistakeSetQuestions(MistakeSetQuestionsParam param) {
        SpecializedTrainingNewResultVoV2 specializedTrainingNewResultVoV2 = this.buildSpecializedTrainingNewResultByIds(param.getQuestionIds(), param.getPublisher(), null);
        for (SpecializedTrainingQuestionVo question : specializedTrainingNewResultVoV2.getQuestionList()) {
            question.setIsAddMistakesBook(true);
            //查询题目详情和文件详情
            QuestionVo questionById = studyRecordMapper.getQuestionById(question.getQuestionId());
            PresignedUrlParam urlParam = new PresignedUrlParam();
            urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
            questionById.getFilesVos().forEach(filesVo -> {
                if (ObjectUtil.isNotEmpty(filesVo.getOssUrl())) {
                    urlParam.setOssKey(filesVo.getOssUrl());
                    PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                    if (presignedUrlVo != null && presignedUrlVo.getData() != null) {
                        question.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                    }
                }
            });
        }
        return specializedTrainingNewResultVoV2;
    }

    private SpecializedTrainingNewResultVoV2 buildSpecializedTrainingNewResultByIds(List<UUID> questions, PublisherEnum publisher, UUID knowledgePointId) {
        // 创建返回对象
        SpecializedTrainingNewResultVoV2 newResultVo = new SpecializedTrainingNewResultVoV2();
        Map<String,Integer> questionTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(questions)) {
            List<QuestionAnalyzeResultDto> questionInfos = personalExamMapper.questionAnalyzeResults(questions);
            QuestionSortNo finalSortNo = new QuestionSortNo();
            finalSortNo.setSortNo(1);
            Set<UUID> points = new HashSet<>();
            List<SpecializedTrainingQuestionVo> questionList = questionInfos.stream()
                    .map(question -> {
                        Integer sortNo = finalSortNo.getSortNo();
                        SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                        BeanUtil.copyProperties(question, specializedTrainingQuestionVo);
                        specializedTrainingQuestionVo.setQuestionContent(eduKnowledgeHubService.decodeContentV2(question.getQuestionContent()));
                        specializedTrainingQuestionVo.setAnalyzeContent(eduKnowledgeHubService.decodeContentV2(question.getAnalyzeContent()));
                        specializedTrainingQuestionVo.setAnswer(eduKnowledgeHubService.decodeContentV2(question.getAnswer()));
                        specializedTrainingQuestionVo.setSortNo(++sortNo);
                        if (StringUtils.isNotBlank(question.getStructuredContent())) {
                            specializedTrainingQuestionVo.setChoiceContent(JSONUtil.toBean(eduKnowledgeHubService.decodeContentV2(question.getStructuredContent()), ChoiceContentVo.class));
                        } else if (question.getQuestionType().contains("选择")) {
                            specializedTrainingQuestionVo.setChoiceContent(DataUtil.formatChoiceContent(specializedTrainingQuestionVo.getQuestionContent()));
                        }
                        if (questionTypeMap.get(question.getQuestionType()) !=null){
                            questionTypeMap.put(question.getQuestionType(), questionTypeMap.get(question.getQuestionType()) + 1);
                        } else {
                            questionTypeMap.put(question.getQuestionType(),1);
                        }
                        finalSortNo.setSortNo(sortNo);
                        // 查询知识点信息
                        List<MathKnowledgePointVO> knowledgePoints = new ArrayList<>();
                        if(StringUtils.isNotBlank(question.getKnowledgePointIds())){
                            String  publisherName =null;
                            if(publisher != null){
                                publisherName = publisher.getDescription().replace("版", "");
                            }
                            String[] knowledgePointIds = question.getKnowledgePointIds().split(",");
                            List<KnowledgePointDto> knowledgePointFromViews =
                                    questionKnowledgePointService.getKnowledgePointFromView(null,publisherName, null, Arrays.stream(knowledgePointIds)
                                            .map(UUID::fromString)
                                            .collect(Collectors.toList()));
                            for(KnowledgePointDto knowledgePointFromView : knowledgePointFromViews) {
                                // 获取知识点信息TODO
                                MathKnowledgePointVO knowledgePoint = new MathKnowledgePointVO();
                                knowledgePoint.setId(knowledgePointFromView.getId());
                                knowledgePoint.setName(knowledgePointFromView.getName());
                                knowledgePoint.setHasExplanation(questionKnowledgePointService.chenckHasExplanation(knowledgePointFromView.getId()));
                                knowledgePoints.add(knowledgePoint);
                                if(specializedTrainingQuestionVo.getKnowledgePointName()==null){
                                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointFromView.getName());
                                } else {
                                    specializedTrainingQuestionVo.setKnowledgePointName(specializedTrainingQuestionVo.getKnowledgePointName()+","+knowledgePointFromView.getName());
                                }
                                points.add(knowledgePointFromView.getId());
                            }
                            specializedTrainingQuestionVo.setKnowledgePoints(knowledgePoints);

                        }
                        return specializedTrainingQuestionVo;
                    }).collect(Collectors.toList());
            //collect 根据questionType分组
            List<SpecializedTrainingQuestionDataVo> typeQuestionDataVos = questionList.stream()
                    .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                    .entrySet().stream()
                    .map(entry -> {
                        SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                        specializedTrainingQuestionDataVo.setType(entry.getKey());
                        specializedTrainingQuestionDataVo.setList(entry.getValue());
                        return specializedTrainingQuestionDataVo;
                    }).collect(Collectors.toList());
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePoint = new ArrayList<>();
            CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO();
            knowledgePointsDTO.setKnowledgePointId(knowledgePointId);
            knowledgePointsDTO.setQuestionList(typeQuestionDataVos);
            knowledgePoint.add(knowledgePointsDTO);
            newResultVo.setTotalQuestions(questions.size());
            newResultVo.setTotalDuration(newResultVo.getTotalQuestions() * 6);
            newResultVo.setTotalKnowledgePoints(points.size());
            newResultVo.setTotalQuestionTypes(questionTypeMap.size());
            newResultVo.setKnowledgePoint(knowledgePoint);
            List<SpecializedTrainingNewResultVoV2.QuestionTypeDistribution> questionTypeDistributions = new ArrayList<>();
            questionTypeMap.keySet().forEach(questionType -> {
                SpecializedTrainingNewResultVoV2.QuestionTypeDistribution questionTypeDistribution = new SpecializedTrainingNewResultVoV2.QuestionTypeDistribution();
                questionTypeDistribution.setQuestionType(QuestionTypeEnum.getEnumByDesc(questionType).name());
                questionTypeDistribution.setQuestionTypeName(questionType);
                questionTypeDistribution.setQuestionCount(questionTypeMap.get(questionType));
                questionTypeDistributions.add(questionTypeDistribution);
            });
            newResultVo.setQuestionTypeDistributions(questionTypeDistributions);
            questionList.sort(Comparator.comparingInt(SpecializedTrainingQuestionVo::getSortNo));
            newResultVo.setQuestionList(questionList);
        }
        return newResultVo;
    }


    private List<MathCatalogNodeVo> buildTree(List<MathCatalogNodeDto> nodes, Integer grade, MathCatalogNodeVo parentNode) {
        return nodes.stream()
                .map(node -> {
                    // 递归设置子节点
                    MathCatalogNodeVo vo = MathCatalogNodeVo.ofMathCatalogNodeDto(node);
                    //设置名称前缀
                    if (node.getLevel() == 2){
                        vo.setNamePrefix(StrUtil.format("{}.{}", parentNode.getSortNo(), vo.getSortNo()));
                    } else {
                        vo.setNamePrefix(StrUtil.format("{}.{}", parentNode.getNamePrefix(),  vo.getSortNo()));
                    }
                    if (CollUtil.isNotEmpty(node.getSubNodes())) {
                        List<MathCatalogNodeVo> children = buildTree(node.getSubNodes(), grade, vo);
                        vo.setSubNodes(children);
                    } else {
                        vo.setSubNodes(new ArrayList<>());
                    }
                    vo.setNodeType(vo.getIsLeaf() ? MathSyncLearningTestNodeTypeEnum.SECTION_TEST : MathSyncLearningTestNodeTypeEnum.CATALOG_NODE);

                    return vo;
                })
                .collect(Collectors.toList());
    }
}
