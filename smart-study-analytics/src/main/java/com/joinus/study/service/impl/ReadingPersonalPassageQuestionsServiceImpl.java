package com.joinus.study.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.ReadingPersonalPassagesQuestionMapper;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.entity.ReadingPersonalPassagesQuestions;
import com.joinus.study.service.ReadingPersonalAnalysisReportService;
import com.joinus.study.service.ReadingPersonalPassageQuestionsService;
import com.joinus.study.service.ReadingPersonalPassagesService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@AllArgsConstructor
@Service("readingPersonalPassageQuestionsService")
@Slf4j
public class ReadingPersonalPassageQuestionsServiceImpl extends ServiceImpl<ReadingPersonalPassagesQuestionMapper, ReadingPersonalPassagesQuestions>
        implements ReadingPersonalPassageQuestionsService {

    @Resource
    private ReadingPersonalAnalysisReportService  readingPersonalAnalysisReportService;

    @Resource
    private ReadingPersonalPassagesService readingPersonalPassagesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setIsErrorCorrection(Long id, int isErrorCorrection, BigDecimal result) {
        ReadingPersonalPassagesQuestions personalPassagesQuestions = this.getById(id);
        Assert.isTrue(Objects.nonNull(personalPassagesQuestions), "未查询到阅读文章题目信息");
        ReadingPersonalPassagesQuestions updateEntity = new ReadingPersonalPassagesQuestions();
        updateEntity.setId(id);
        updateEntity.setIsErrorCorrection(isErrorCorrection);
        updateEntity.setIsBlocked(isErrorCorrection);
        updateEntity.setResult(result);
        this.updateById(updateEntity);
        //检查答案是否改变 如果改变同时更新训练报告
        if(!Objects.equals(personalPassagesQuestions.getResult(), result)){
            //查训练报告信息
            ReadingPersonalPassages passages = readingPersonalPassagesService.getById(personalPassagesQuestions.getPersonalPassageId());
            //更新报告
            if(passages != null && passages.getEntryType() != 2){
                readingPersonalAnalysisReportService.generateTrainingReport(personalPassagesQuestions.getPersonalPassageId());
            }
        }
    }

    @Override
    public void setIsFeedback(Long id, int isFeedback) {

        ReadingPersonalPassagesQuestions personalPassagesQuestions = this.getById(id);
        Assert.isTrue(Objects.nonNull(personalPassagesQuestions), "未查询到阅读文章题目信息");
        ReadingPersonalPassagesQuestions updateEntity = new ReadingPersonalPassagesQuestions();
        updateEntity.setId(id);
        updateEntity.setIsFeedback(isFeedback);
        this.updateById(updateEntity);
    }

    @Override
    public List<ReadingPersonalPassagesQuestions> getByPersonalPassageIdAndPersonalQuestionId(Long personalPassageId, Long personalPassageQuestionId) {
        LambdaQueryWrapper<ReadingPersonalPassagesQuestions> wrapper = new LambdaQueryWrapper<ReadingPersonalPassagesQuestions>()
                .eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, personalPassageId)
                .eq(ReadingPersonalPassagesQuestions::getId, personalPassageQuestionId)
                .isNull(ReadingPersonalPassagesQuestions::getDeletedAt);
        return this.list(wrapper);
    }
}
