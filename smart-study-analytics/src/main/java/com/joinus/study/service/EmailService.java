package com.joinus.study.service;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/7 17:53
 **/
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.param.MailParam;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.OssResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;

@Slf4j
@Component
public class EmailService {
    @Resource
    private JavaMailSender emailSender;
    @Resource
    private AliOssUtils aliOssUtils;
    @Value("${spring.mail.username:lian<PERSON>@967111.com}")
    private String mainFrom;
    public void sendSimpleMessage(MailParam mail, File attachment, String filename) throws BaseException {
        MimeMessage message = emailSender.createMimeMessage();
        MimeMessageHelper helper = null;
        try {
            helper = new MimeMessageHelper(message, true);
            helper.setSubject(mail.getSubject());
            helper.setText(mail.getContent());
            helper.setTo(mail.getTo());
            helper.setFrom(mail.getFrom());
            if (null != attachment && attachment.exists()) {
                helper.addAttachment(filename, attachment);
            }
            emailSender.send(message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            throw new BaseException("发送邮件失败"+e.getMessage());
        }
    }

    public void sendOssPdf(MailParam mail, String objectKey, String filename) throws BaseException {
        MimeMessage message = emailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setSubject(mail.getSubject());
            helper.setText(mail.getContent(), true); // 第二个参数表示是HTML内容
            helper.setTo(mail.getTo());
            helper.setFrom(mainFrom);

            // 使用自定义资源类
            OssResource ossResource = aliOssUtils.getOssResource(objectKey);
            helper.addAttachment(filename, ossResource, "application/pdf");
            emailSender.send(message);
        } catch (Exception e) {
            handleException(e);
        }
    }


    private void handleException(Exception e) throws BaseException {
        String errorMsg = "邮件发送失败: " + e.getMessage();
        log.error(errorMsg, e);
        if (e instanceof IOException) {
            throw new BaseException("OSS文件读取失败: " + e.getMessage());
        }
        throw new BaseException(errorMsg);
    }
}
