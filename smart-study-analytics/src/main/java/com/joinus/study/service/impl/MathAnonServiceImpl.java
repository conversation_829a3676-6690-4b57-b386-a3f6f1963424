package com.joinus.study.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.MathExamsMapper;
import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.dto.ExamHasKnowledgeDto;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.MathExamsEntity;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.enums.AppTypeEnum;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.ExamHasKnowledgeParam;
import com.joinus.study.model.vo.CheckExamExsitenceVo;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.ExamAnalyzeResultService;
import com.joinus.study.service.MathAnonService;
import com.joinus.study.service.PersonalExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.UUID;

@Service
@Slf4j
public class MathAnonServiceImpl implements MathAnonService {


    @Autowired
    private MathExamsMapper mathExamsMapper;
    @Autowired
    private PersonalExamService personalExamService;
    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private ExamAnalyzeResultService examAnalyzeResultService;

    @Value("${ijx.ink.q.math:https://ijx.ink/q/math/}")
    private String inkQMath;
    //青于蓝app端
    //扫二维码跳转用户上传试卷
    @Value("${ijx.ink.q.math.qr.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=scanBtn}")
    private String mathQrJumpUrlExamPapers;
    //扫二维码跳转专项训练
    @Value("${ijx.ink.q.math.qr.url.special_training:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/specialPaperMask?params={}}")
    private String mathQrJumpUrlSpecialTraing;
    //扫二维码跳转常规考试卷
    @Value("${ijx.ink.q.math.qr.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathQrJumpUrlRegularExamPapers;
    //扫二维码跳转知识点(或错题集)专项训练
    @Value("${ijx.ink.q.math.photos.url.knowledge-point-or-mistake.papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/knowledgePaper?params={}}")
    private String mathQrJumpUrlKnowledgePointOrMistakePapers;
    //拍照跳转用户上传试卷
    @Value("${ijx.ink.q.math.photos.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=popBtn}")
    private String mathPhotosJumpUrlExamPapers;
    //拍照跳转常规考试卷
    @Value("${ijx.ink.q.math.photos.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathPhotosJumpUrlRegularExamPapers;



    //青于蓝学科教育pad端
    //扫二维码跳转用户上传试卷
    @Value("${ijx.ink.q.math.pad.qr.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=scanBtn}")
    private String mathPadQrJumpUrlExamPapers;
    //扫二维码跳转专项训练
    @Value("${ijx.ink.q.math.pad.qr.url.special_training:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/specialPaperMask?params={}}")
    private String mathPadQrJumpUrlSpecialTraing;
    //扫二维码跳转常规考试卷
    @Value("${ijx.ink.q.math.pad.qr.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathPadQrJumpUrlRegularExamPapers;
    //扫二维码跳转知识点(或错题集)专项训练
    @Value("${ijx.ink.q.math.pad.qr.url.knowledge-point-or-mistake.papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/mathClass/localPaperRecommend/knowledgePaper?params={}}")
    private String mathPadQrJumpUrlKnowledgePointOrMistakePapers;
    //拍照跳转常规考试卷
    @Value("${ijx.ink.q.math.pad.photos.url.exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?buttonType=popBtn}")
    private String mathPadPhotosJumpUrlExamPapers;
    //拍照跳转常规考试卷
    @Value("${ijx.ink.q.math.pad.photos.url.regular_exam_papers:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/localPaperRecommend/testPreview?examId={}}")
    private String mathPadPhotosJumpUrlRegularExamPapers;

    @Value("${ijx.ink.q.chinese.qr.url:https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/chineseClass/questionLabeling?qrStr={}&keyboard={}}")
    private String chineseJumpUrl;
    @Value("${ijx.ink.q.chinese.pad.qr.url:https://cdn-ali-static.ijx.ink/pad-h5-learning-tools-dev/index.html#/chineseClass/questionLabeling?qrStr={}&keyboard={}}")
    private String chinesePadJumpUrl;

    @Override
    public JumpUrlBo queryJumpUrlForMath(String qrStr, Long studentId, AppTypeEnum appType) {
        CommonResponse.ERROR.assertNotEmpty(qrStr, "请输入试卷id");
        UUID examId = null;
        if (!qrStr.startsWith(inkQMath)) {
            CommonResponse.assertError("二维码不符合规则");
        }
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        MathExamsEntity mathExamsEntity = null;
            String printId = qrStr.replace(inkQMath, "");


        // 判断printId是数字还是UUID
        if (StrUtil.isNumeric(printId)) {
            // 如果是数字，转换为Long类型并查询
            Long id = Long.parseLong(printId);
            PersonalExam personalExam = personalExamService.getById(id);
            if (null == personalExam) {
                CommonResponse.assertError("个人考试不存在");
            }
            if (null == personalExam.getExamId()) {
                CommonResponse.assertError("个人考试的试卷id不存在");
            }
            mathExamsEntity = mathExamsMapper.selectById(personalExam.getExamId());
        } else {
            // 如果不是数字，尝试作为UUID处理
            try {
                UUID uuidId = UUID.fromString(printId);
                mathExamsEntity = mathExamsMapper.selectById(uuidId);

            } catch (IllegalArgumentException uuidException) {
                log.error("printId既不是有效的数字也不是有效的UUID: {}", printId, uuidException);
            }
        }

        CommonResponse.ERROR.assertNotNull(mathExamsEntity, "试卷不存在");
        examId = mathExamsEntity.getId();
        examData.setPublisher(mathExamsEntity.getPublisher() != null ? PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : null);
        examData.setExamSource(mathExamsEntity.getSource());

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);

        JumpUrlBo jumpUrlBo = new JumpUrlBo();
        jumpUrlBo.setParams(JSONUtil.parseObj(examData));

        String url = "";
        switch (mathExamsEntity.getSource()) {

            case PAST_EXAM_PAPER:
            case REGULAR_EXAM_PAPER:
                url = AppTypeEnum.QYL_JY_PAD == appType ? StrUtil.format(mathPadQrJumpUrlRegularExamPapers, examId) : StrUtil.format(mathQrJumpUrlRegularExamPapers, examId);
                break;
            case USER_UPLOAD:
                url = AppTypeEnum.QYL_JY_PAD == appType ? mathPadQrJumpUrlExamPapers : mathQrJumpUrlExamPapers;
                break;

            case SPECIAL_TRAINING:
            case EXAM_BLIND_SPOTS_TRAINING:
            case HOLIDAY_TRAINING:
                url = getMathQrJumpurlForHolidayTraining(examId, studentId, appType);
                break;
            case KNOWLEDGE_POINTS_TRAINING:
                url = getMathQrJumpurlForKnowledgePointOrMistake(examId, studentId, appType, ExamSourceType.KNOWLEDGE_POINTS_TRAINING);
                break;
            case MISTAKE_SET_TRAINING:
                url = getMathQrJumpurlForKnowledgePointOrMistake(examId, studentId, appType, ExamSourceType.MISTAKE_SET_TRAINING);
                break;
            default:
                url = getMathQrJumpurlForHolidayTraining(examId, studentId, appType);
                break;
        }
        jumpUrlBo.setUrl(url);
        return jumpUrlBo;
    }

    @Override
    public JumpUrlBo queryJumpUrlForChinese(String qrStr, String keyboard, AppTypeEnum appType) {
        // 提取公共逻辑，减少重复代码
        String urlTemplate = AppTypeEnum.QYL_JY_PAD.equals(appType) ? chinesePadJumpUrl : chineseJumpUrl;
        String url = StrUtil.format(urlTemplate, qrStr, keyboard);
        return JumpUrlBo.builder().url(url).build();
    }

    @Override
    public String getJumpUrlByExamSource(ExamSourceType examSource, UUID examId, AppTypeEnum appType) {
        String url = "";
        if (null == examSource) {
            return url;
        }
        switch (examSource) {

            case PAST_EXAM_PAPER:
            case REGULAR_EXAM_PAPER:
                url = AppTypeEnum.QYL_JY_PAD == appType ? StrUtil.format(mathPadPhotosJumpUrlRegularExamPapers, examId) : StrUtil.format(mathPhotosJumpUrlRegularExamPapers, examId);;
                break;
            case USER_UPLOAD:
                url = AppTypeEnum.QYL_JY_PAD == appType ? mathPadPhotosJumpUrlExamPapers : mathPhotosJumpUrlExamPapers;
                break;
            case SPECIAL_TRAINING:
            case EXAM_BLIND_SPOTS_TRAINING:
            case HOLIDAY_TRAINING:
            default:
                url = AppTypeEnum.QYL_JY_PAD == appType ? mathPadPhotosJumpUrlExamPapers : mathPhotosJumpUrlExamPapers;
                break;
        }
        return url;
    }

    private String getMathQrJumpurlForHolidayTraining(UUID examId, Long studentId, AppTypeEnum appType) {
        MathActivityWeekUnit mathActivityWeekUnit = examAnalyzeResultService.getMathActivityWeekUnitByTrainingExamId(examId);
        MathActivityWeekUnitTypeEnum weekUnitType = null != mathActivityWeekUnit ? mathActivityWeekUnit.getType() : null;
        PersonalExam personalExam = personalExamService.getLatestNoAnalyzedPersonalExam(examId, studentId);
        JSONObject params = new JSONObject();
        params.set("examId", examId);
        params.set("personalExamId", null == personalExam ? null : personalExam.getId());
        params.set("weekUnitType", null == weekUnitType ? null : weekUnitType.name());
        String paramsEncoded = "";
        try {
            paramsEncoded = URLEncoder.encode(JSONUtil.toJsonStr(params), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("Failed to encode params: {}", params, e);
            throw new RuntimeException(e);
        }
        String format = AppTypeEnum.QYL_JY_PAD == appType ? StrUtil.format(mathPadQrJumpUrlSpecialTraing, paramsEncoded) :
                StrUtil.format(mathQrJumpUrlSpecialTraing, paramsEncoded);

        return format;
    }

    private String getMathQrJumpurlForKnowledgePointOrMistake(UUID examId, Long studentId, AppTypeEnum appType, ExamSourceType examSource) {
        PersonalExam personalExam = personalExamService.getLatestNoAnalyzedPersonalExam(examId, studentId);
        JSONObject params = new JSONObject();
        params.set("examId", examId);
        params.set("personalExamId", null == personalExam ? null : personalExam.getId());
        params.set("type", examSource == ExamSourceType.KNOWLEDGE_POINTS_TRAINING ? "knowledgeTest" : "errorTest");
        String paramsEncoded = "";
        try {
            paramsEncoded = URLEncoder.encode(JSONUtil.toJsonStr(params), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("Failed to encode params: {}", params, e);
            throw new RuntimeException(e);
        }
        String format = AppTypeEnum.QYL_JY_PAD == appType ? StrUtil.format(mathPadQrJumpUrlKnowledgePointOrMistakePapers, paramsEncoded) :
                StrUtil.format(mathQrJumpUrlKnowledgePointOrMistakePapers, paramsEncoded);

        return format;
    }


}
