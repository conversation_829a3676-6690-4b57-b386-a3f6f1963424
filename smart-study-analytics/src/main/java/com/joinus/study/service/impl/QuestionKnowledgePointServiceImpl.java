package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.MathKnowledgePointMapper;
import com.joinus.study.mapper.QuestionKnowledgePointMapper;
import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.QuestionKnowledgePointService;
import com.joinus.study.utils.AliOssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【question_knowledge_point】的数据库操作Service实现
* @createDate 2025-03-11 09:48:14
*/
@Slf4j
@Service
public class QuestionKnowledgePointServiceImpl extends ServiceImpl<QuestionKnowledgePointMapper, QuestionKnowledgePoint>
    implements QuestionKnowledgePointService{

    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private AliOssUtils aliOssUtils;
    @Autowired
    private MathKnowledgePointMapper mathKnowledgePointMapper;
    @Override
    public List<QuestionKnowledgePoint> getQuestionKnowledgePointList(UUID questionId) {
        LambdaQueryWrapper<QuestionKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionId);
        List<QuestionKnowledgePoint> list = this.list(queryWrapper);
        return list ;
    }

    @Override
    public List<KnowledgePointStatisticsVo> selectKnowledgePointStatisticsByExamId(Long id) {
        List<KnowledgePointStatisticsVo> knowledgePointStatisticsVoList = baseMapper.selectKnowledgePointStatisticsByExamId(id);
        return knowledgePointStatisticsVoList;
    }

    @Override
    public List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getListByExamIdAndQuestionId(UUID examId, UUID questionId, PublisherEnum publisher) {
        LambdaQueryWrapper<QuestionKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionId);
        queryWrapper.eq(QuestionKnowledgePoint::getExamId, examId);
        queryWrapper.eq(QuestionKnowledgePoint::getPublisher, publisher);
        List<QuestionKnowledgePoint> list = this.list(queryWrapper);
        //把list 转换为knowledgePointsDTOList
        List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsDTOList = list.stream().map(questionKnowledgePoint -> {
            KnowledgePointsVO.DataDTO.KnowledgePointsDTO knowledgePointsDTO = new KnowledgePointsVO.DataDTO.KnowledgePointsDTO();
            knowledgePointsDTO.setId(questionKnowledgePoint.getKnowledgePointId());
            knowledgePointsDTO.setName(questionKnowledgePoint.getKnowledgePointName());
            knowledgePointsDTO.setHasExplanation(this.chenckHasExplanation(questionKnowledgePoint.getKnowledgePointId()));
            return knowledgePointsDTO;
        }).collect(Collectors.toList());
        return knowledgePointsDTOList ;
    }

    @Override
    public List<StudentInfoVo> getStudentIds(Long studentId) {
        return baseMapper.getStudentIds(studentId);
    }

    @Override
    public List<KnowledgePointStatisticsVo> getClassKnowledgePointStatics(List<Long> studentIds,List<Long> classIds, UUID examId) {
        return baseMapper.getClassKnowledgePointStatics(studentIds,classIds,examId);
    }

    @Override
    public List<QuestionKnowledgePoint> listKnowledgePointsByExamIdAndPublisher(UUID examId, PublisherEnum publisher) {
        //查询业务库中试卷关联的知识点列表
        return lambdaQuery().eq(QuestionKnowledgePoint::getExamId, examId)
                .eq(QuestionKnowledgePoint::getPublisher, publisher)
                .list();
    }

    @Override
    public void rebuildExamQuestionPublisher() {
        // 分页大小
        int pageSize = 100;
        // 当前页码
        int currentPage = 1;
        // 是否还有数据
        boolean hasMore = true;

        while (hasMore) {
            // 1. 分页查询没有publisher的记录
            Page<QuestionKnowledgePoint> page = new Page<>(currentPage, pageSize);
            LambdaQueryWrapper<QuestionKnowledgePoint> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNull(QuestionKnowledgePoint::getPublisher);
            Page<QuestionKnowledgePoint> resultPage = page(page, queryWrapper);

            List<QuestionKnowledgePoint> records = resultPage.getRecords();
            if (CollUtil.isEmpty(records)) {
                hasMore = false;
                continue;
            }

            List<UUID> kpIds = records.stream().map(QuestionKnowledgePoint::getKnowledgePointId).collect(Collectors.toList());
            List<KnowledgePointDto> knowledgePointDtos = eduKnowledgeHubService.listKnowledgePoints(kpIds);
            Map<UUID, PublisherEnum> kpPublisherMap = knowledgePointDtos.stream().collect(Collectors.toMap(KnowledgePointDto::getId, KnowledgePointDto::getPublisher, (v1,v2)-> v1));
            // 2. 批量处理记录
            for (QuestionKnowledgePoint record : records) {
                try {
                    PublisherEnum publisherEnum = kpPublisherMap.get(record.getKnowledgePointId());
                    if (null != publisherEnum) {
                        record.setPublisher(publisherEnum);
                        updateById(record);
                    }
                } catch (Exception e) {
                    log.error("处理知识点ID:{} 失败: {}", record.getKnowledgePointId(), e.getMessage(), e);
                }
            }

            // 5. 判断是否还有下一页
            if (currentPage >= resultPage.getPages()) {
                hasMore = false;
            } else {
                currentPage++;
            }
        }
    }

    @Override
    public List<MathKnowledgePointVO> listByExamIdAndQuestionIdAndPublisher(UUID examId, UUID questionId, PublisherEnum publisher) {
        List<QuestionKnowledgePoint> knowledgePoints = lambdaQuery().eq(QuestionKnowledgePoint::getExamId, examId)
                .eq(QuestionKnowledgePoint::getQuestionId, questionId)
                .eq(null != publisher, QuestionKnowledgePoint::getPublisher, publisher)
                .list();
        return knowledgePoints.stream().map(item -> {
            return MathKnowledgePointVO.builder()
                    .id(item.getKnowledgePointId())
                    .name(item.getKnowledgePointName())
                    .build();
        }).collect(Collectors.toList());
    }


    @Override
    public Boolean chenckHasExplanation(UUID knowledgePointId) {
        return eduKnowledgeHubService.checkExplanationExistByKnowledgePointId(knowledgePointId);
    }
    @Override
    public MathKnowledgePointHandoutVo getExplanationByKnowledgePointId(UUID knowledgePointId) {
        return eduKnowledgeHubService.getExplanationByKnowledgePointId(knowledgePointId);
    }

    @Override
    public List<MathKnowledgePointVO> getKnowledgePointBySectionId(UUID sectionId,Long studentId) {
        List<MathKnowledgePointVO> knowledgePointVOS = baseMapper.selectKnowledgePointBySectionId(sectionId);
        knowledgePointVOS.forEach(item -> {
            if(studentId != null){
                item.setMasteryDegree(baseMapper.selectPointMasteryDegree(studentId,item.getId()));
            }
            item.setHasExplanation(this.chenckHasExplanation(item.getId()));
        });
        return knowledgePointVOS.stream()
                .sorted(Comparator.comparing(MathKnowledgePointVO::getSortNo,  Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    @Override
    public List<KnowledgePointDto> getKnowledgePointFromView(UUID sectionId,String publisherName,UUID knowledgePointId,List<UUID> knowledgePointIds) {
        return baseMapper.getKnowledgePointFromView(sectionId, publisherName, knowledgePointId, knowledgePointIds);
    }
    @Override
    public List<KnowledgePointDto> getKnowledgePointFromViewByIds(List<UUID> ids,PublisherEnum  publisher) {
        List<KnowledgePointDto> viewByIds = mathKnowledgePointMapper.getKnowledgePointFromViewByIds(ids);
        if(publisher !=null && CollectionUtils.isNotEmpty(viewByIds)){
            return viewByIds.stream().filter(item -> item.getPublisher().equals(publisher)).collect(Collectors.toList());
        }
        return viewByIds;
    }

    @Override
    public List<MathKnowledgePointVO> getMathKnowledgePointVOByIds(List<UUID> ids, PublisherEnum publisher) {
        List<MathKnowledgePointVO> mathKnowledgePointVOS = new ArrayList<>();
        List<KnowledgePointDto> knowledgePointDtos = this.getKnowledgePointFromViewByIds(ids, publisher);
        for (KnowledgePointDto knowledgePointFromView : knowledgePointDtos) {
            MathKnowledgePointVO knowledgePoint = new MathKnowledgePointVO();
            knowledgePoint.setId(knowledgePointFromView.getId());
            knowledgePoint.setName(knowledgePointFromView.getName());
            knowledgePoint.setHasExplanation(this.chenckHasExplanation(knowledgePointFromView.getId()));
            mathKnowledgePointVOS.add(knowledgePoint);
        }
        return mathKnowledgePointVOS;
    }

    @Override
    public KnowledgePointDto getKnowledgePointFromViewById(UUID id) {
        return mathKnowledgePointMapper.getKnowledgePointFromViewById(id);
    }
}




