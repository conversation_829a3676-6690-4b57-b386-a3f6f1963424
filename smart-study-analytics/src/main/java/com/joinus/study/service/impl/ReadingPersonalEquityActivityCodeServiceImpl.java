package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.BaseServiceImpl;
import com.joinus.study.mapper.ReadingPersonalEquityActivityCodeMapper;
import com.joinus.study.model.entity.ReadingPersonalEquityActivityCode;
import com.joinus.study.service.ReadingPersonalEquityActivityCodeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@AllArgsConstructor
@Service("readingPersonalEquityActivityCodeService")
@Slf4j
public class ReadingPersonalEquityActivityCodeServiceImpl extends BaseServiceImpl<ReadingPersonalEquityActivityCodeMapper, ReadingPersonalEquityActivityCode>
        implements ReadingPersonalEquityActivityCodeService {

    @Override
    public ReadingPersonalEquityActivityCode getOneByCode(String code) {
        return this.baseMapper.selectOne(Wrappers.lambdaQuery(ReadingPersonalEquityActivityCode.class)
                .eq(ReadingPersonalEquityActivityCode::getCode, code));
    }

    @Override
    public ReadingPersonalEquityActivityCode getOneByUserPhone(String phone) {
        return this.baseMapper.selectOne(Wrappers.lambdaQuery(ReadingPersonalEquityActivityCode.class)
                .eq(ReadingPersonalEquityActivityCode::getUserPhone, phone)
                .eq(ReadingPersonalEquityActivityCode::getStatus, 2));
    }
}
