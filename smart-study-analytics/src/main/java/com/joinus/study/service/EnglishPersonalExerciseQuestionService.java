package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import com.joinus.study.model.entity.EnglishPersonalExerciseQuestion;
import com.joinus.study.model.vo.EnglishPersonalExerciseQuestionVO;

import java.util.List;

public interface EnglishPersonalExerciseQuestionService extends IService<EnglishPersonalExerciseQuestion> {
    List<EnglishPersonalExerciseQuestionDTO> listByPersonalExerciseId(Long personalExerciseId);
}
