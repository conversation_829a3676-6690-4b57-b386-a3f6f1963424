package com.joinus.study.utils;

import cn.hutool.core.util.StrUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @version V2.0
 * @Author: maweihua
 * @Description:
 * @date 2025-7-3 15:50:20
 */
@Slf4j
public class JWTUtil {
    private static String stringKey = "7786df7fc3a34e26a61c034d5ec8245d";

    /**
     * 加密接口 
     *   prjLable:设置jti(JWT ID)：是JWT的唯一标识，根据业务需要，这个可以设置为一个不重复的值，主要用来作为一次性token,从而回避重放攻击。
     *   userId: 家长id
     *   startTime：签发时间
     *   endTime: 生效终止时间
     *   minutes: 生效时长
     * @Notes
     *   标准声明
     *   iss: jwt签发者;
     *   sub: jwt所面向的用户;
     *   aud: 接收jwt的一方;
     *   exp: jwt的过期时间，这个过期时间必须要大于签发时间;
     *   nbf: 定义在什么时间之前，该jwt都是不可用的;
     *   iat: jwt的签发时间;
     *   jti: jwt的唯一身份标识，主要用来作为一次性token,从而回避重放攻击;
     * @return Map<String, Object>
     */
    public static String createJWT(Map<String,String> data)throws ParseException{
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startDate = new Date();
        Date endDate = new Date();
        if (data.get("startTime") != null && data.get("startTime").length() != 0) {
            startDate = simpleDateFormat.parse(data.get("startTime"));
        }
        if (data.get("endTime") != null && data.get("endTime").length() != 0) {
            endDate = simpleDateFormat.parse(data.get("endTime"));
        }
        long ttlMillis = endDate.getTime() - startDate.getTime();
        if (data.get("minutes") != null && data.get("minutes").length() != 0) {
            ttlMillis = Long.parseLong(data.get("minutes")) * 1000 * 60;
        }
        //判断时间长度大小
        ttlMillis = (endDate.getTime() - (new Date()).getTime() - ttlMillis) > 0L ? (endDate.getTime() - startDate.getTime()) : ttlMillis;
        String userId = data.get("userId") != null && !"".equals(data.get("userId")) ? data.get("userId") : null;
        String prjLable = data.get("prjLable") != null && !"".equals(data.get("prjLable")) ? data.get("prjLable") : "jysd";
        //指定签名的时候使用的签名算法，也就是header那部分，jjwt已经将这部分内容封装好了。
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        long nowMillis = System.currentTimeMillis();//生成JWT的时间(当前计算机元年毫秒数)
        Date now = new Date(nowMillis);
        SecretKey key = generalKey();//生成签名的时候使用的秘钥secret,这个方法本地封装了的，一般可以从本地配置文件中读取，切记这个秘钥不能外露哦。它就是你服务端的私钥，在任何场景都不应该流露出去。一旦客户端得知这个secret, 那就意味着客户端是可以自我签发jwt了。
        Map<String, Object> claims = new HashMap<String, Object>();//创建payload的私有声明（根据特定的业务需要添加，如果要拿这个做验证，一般是需要和jwt的接收方提前沟通好验证方式的）
        List<String> listCla = new ArrayList<String>(data.keySet());
        for (int i = 0; i < listCla.size(); i++) {
            claims.put(listCla.get(i), data.get(listCla.get(i)));
        }
        //下面就是在为payload添加各种标准声明和私有声明了
        JwtBuilder builder = Jwts.builder()
                .setClaims(claims)          //如果有私有声明，一定要先设置这个自己创建的私有的声明，这个是给builder的claim赋值，一旦写在标准的声明赋值之后，就是覆盖了那些标准的声明的
                .setId(prjLable)
                .setIssuedAt(now)//iat: jwt的签发时间
                .setIssuer(userId)
                .signWith(signatureAlgorithm, key);//设置签名使用的签名算法和签名使用的秘钥
        //如果过期时间不为0  则设置过期时间
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);     //设置过期时间
        }
        return builder.compact();
    }

    /**
     * 解密jwt
     * @param jwt
     * @return
     * @throws  IllegalArgumentException
     */
    public static Claims parseJWT(String jwt) {
        SecretKey key = generalKey();  //签名秘钥，和生成的签名的秘钥一模一样
        Claims claims = Jwts.parser()  //得到DefaultJwtParser
                .setSigningKey(key)         //设置签名的秘钥
                .parseClaimsJws(jwt).getBody();//设置需要解析的jwt
        return claims;
    }

    /**
     * 由字符串生成加密key
     * @return
     */
    public static SecretKey generalKey(){
        byte[] encodedKey = Base64.decodeBase64(stringKey);//本地的密码解码
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");// 根据给定的字节数组使用AES加密算法构造一个密钥，使用 encodedKey中的始于且包含 0 到前 leng 个字节这是当然是所有。（后面的文章中马上回推出讲解Java加密和解密的一些算法）
        return key;
    }

    //获取token
    public static String getToken(HttpServletRequest request){
        //1.从请求头获取token
        //获取token
        String token = request.getHeader("token");
        //判断token是否为空
        if (StrUtil.isEmpty(token)){
            //2.尝试从url获取token
            token = request.getParameter("token");
        }
        if (StrUtil.isBlank(token)){
            log.info("token not exist");
        }
        return token;
    }

}
