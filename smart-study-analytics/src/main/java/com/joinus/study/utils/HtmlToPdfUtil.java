package com.joinus.study.utils;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.util.XRLog;
import lombok.extern.slf4j.Slf4j;

import java.io.*;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31 15:13
 **/
@Slf4j
public class HtmlToPdfUtil {
    public static void htmlToPdf(String htmlContent, String outputPath) throws IOException {
        try (OutputStream os = new FileOutputStream(outputPath)) {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withHtmlContent(htmlContent, null);
            // 1. 动态判断字体加载路径（优先文件系统路径，兼容本地开发）
            String fontPath = "/app/fonts/STFANGSO.TTF"; // Docker容器中的绝对路径
            InputStream fontStream;
            if (new File(fontPath).exists()) {
                // 生产环境：从文件系统加载
                fontStream = new FileInputStream(fontPath);
                if (fontStream == null) {
                    log.error("Font file not found in classpath: /app/fonts/STFANGSO.TTF");
                }
            } else {
                // 本地开发：从类路径加载
                fontStream = HtmlToPdfUtil.class.getResourceAsStream("/static/fonts/STFANGSO.TTF");
                if (fontStream == null) {
                    throw new IOException("Font file not found in classpath: /static/fonts/STFANGSO.TTF");
                }
            }
            builder.withHtmlContent(htmlContent, null);
            // 加载字体（必须）
            builder.useFont(() -> fontStream, "STFangsong");
            builder.toStream(os);
            builder.run();
            System.out.println("PDF creat over ");
        } catch (Exception e) {
            System.err.println("PDF chanhge fail ");
            e.printStackTrace();
        }
    }
}