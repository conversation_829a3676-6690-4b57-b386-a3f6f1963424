package com.joinus.study.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * <AUTHOR>
 * @date 2025/4/27 10:37
 * description：pdf转图片工具
 */
public class PDFToImageUtils {
    public static void convertPDFToImage(String pdfFilePath, String outputFolderPath, String imageFormat) throws IOException {
        // 加载PDF文档
        try (PDDocument document = PDDocument.load(new File(pdfFilePath))) {
            PDFRenderer renderer = new PDFRenderer(document);
            File outputFolder = new File(outputFolderPath);
            if (!outputFolder.exists()) {
                outputFolder.mkdirs();
            }
            // 遍历PDF的每一页并转换为图片
            for (int i = 0; i < document.getNumberOfPages(); ++i) {
                BufferedImage image = renderer.renderImageWithDPI(i, 300, ImageType.RGB);

                File outputFile = new File(outputFolder, "page-" + i + "." + imageFormat);
                ImageIO.write(image, imageFormat, outputFile);
            }
        }
    }

    public static byte[][] convertPDFToImageBytes(String pdfFilePath, String imageFormat) throws IOException {
        try {
        // 创建 URL 对象
        URL url = new URL(pdfFilePath);
            try (InputStream inputStream = url.openStream();
             // 从 InputStream 加载 PDF 文档
            PDDocument document = PDDocument.load(inputStream)) {
                PDFRenderer renderer = new PDFRenderer(document);
                int numPages = document.getNumberOfPages();
                byte[][] imageBytesArray = new byte[numPages][];

                for (int i = 0; i < numPages; ++i) {
                    BufferedImage image = renderer.renderImageWithDPI(i, 300, ImageType.RGB);
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    ImageIO.write(image, imageFormat, byteArrayOutputStream);
                    imageBytesArray[i] = byteArrayOutputStream.toByteArray();
                }
                return imageBytesArray;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        try {
            String pdfFilePath = "output/01.pdf";
            String outputFolderPath = "output/folder/";
            convertPDFToImage(pdfFilePath, outputFolderPath, "jpg");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
