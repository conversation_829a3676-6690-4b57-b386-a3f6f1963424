package com.joinus.study.utils;


import lombok.extern.slf4j.Slf4j;
import java.util.Date;

@Slf4j
public class DateUtil {
    // 最大可表示的秒数（约68年）
    private static final int MAX_SAFE_SECONDS = Integer.MAX_VALUE;

    /**
     * 计算两个时间的秒数差值（绝对值）
     * @return 非负秒数（超过Integer最大值时返回Integer.MAX_VALUE）
     */
    public static int getDiffInSeconds(Date start, Date end) {
        if (start == null || end == null) {
            return 0;
        }

        long diffMillis = Math.abs(end.getTime() - start.getTime());
        long seconds = diffMillis / 1000;

        return safeConvert(seconds);
    }

    /**
     * 获取带符号的秒数差
     * @return 正数表示end晚于start，负数反之（溢出时返回Integer边界值）
     */
    public static int getSignedDiffInSeconds(Date start, Date end) {
        if (start == null || end == null) return 0;

        long diffMillis = end.getTime() - start.getTime();
        long seconds = diffMillis / 1000;

        return safeConvert(seconds);
    }

    /**
     * 安全转换long到int（防止溢出）
     */
    private static int safeConvert(long seconds) {
        if (seconds > MAX_SAFE_SECONDS) {
            log.warn("秒数超过int最大值，已截断为Integer.MAX_VALUE");
            return Integer.MAX_VALUE;
        }
        if (seconds < Integer.MIN_VALUE) {
            log.warn("秒数小于int最小值，已截断为Integer.MIN_VALUE");
            return Integer.MIN_VALUE;
        }
        return (int) seconds;
    }
}
