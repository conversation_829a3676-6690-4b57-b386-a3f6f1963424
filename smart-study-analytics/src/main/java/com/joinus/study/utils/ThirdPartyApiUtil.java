package com.joinus.study.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 通用第三方接口调用工具类
 * 提供统一的HTTP请求方法，并自动打印请求参数和返回参数
 * 
 * <AUTHOR>
 * @date 2025-09-22
 */
@Slf4j
@Component
public class ThirdPartyApiUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int DEFAULT_TIMEOUT = 30000; // 默认超时时间30秒

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public String get(String url) {
        return get(url, null, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送GET请求
     *
     * @param url     请求URL
     * @param params  请求参数
     * @return 响应结果
     */
    public String get(String url, Map<String, Object> params) {
        return get(url, params, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送GET请求
     *
     * @param url     请求URL
     * @param params  请求参数
     * @param headers 请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public String get(String url, Map<String, Object> params, Map<String, String> headers, int timeout) {
        return executeRequest(Method.GET, url, params, headers, timeout, false);
    }

    /**
     * 发送POST请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应结果
     */
    public String postJson(String url, Object body) {
        return postJson(url, body, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送POST请求（JSON格式）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public String postJson(String url, Object body, Map<String, String> headers, int timeout) {
        return executeJsonRequest(Method.POST, url, body, headers, timeout);
    }

    /**
     * 发送POST请求（表单格式）
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public String postForm(String url, Map<String, Object> params) {
        return postForm(url, params, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送POST请求（表单格式）
     *
     * @param url     请求URL
     * @param params  请求参数
     * @param headers 请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public String postForm(String url, Map<String, Object> params, Map<String, String> headers, int timeout) {
        return executeRequest(Method.POST, url, params, headers, timeout, true);
    }

    /**
     * 发送PUT请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应结果
     */
    public String putJson(String url, Object body) {
        return putJson(url, body, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送PUT请求（JSON格式）
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public String putJson(String url, Object body, Map<String, String> headers, int timeout) {
        return executeJsonRequest(Method.PUT, url, body, headers, timeout);
    }

    /**
     * 发送DELETE请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public String delete(String url) {
        return delete(url, null, null, DEFAULT_TIMEOUT);
    }

    /**
     * 发送DELETE请求
     *
     * @param url     请求URL
     * @param params  请求参数
     * @param headers 请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应结果
     */
    public String delete(String url, Map<String, Object> params, Map<String, String> headers, int timeout) {
        return executeRequest(Method.DELETE, url, params, headers, timeout, false);
    }

    /**
     * 执行HTTP请求（表单或查询参数）
     *
     * @param method   HTTP方法
     * @param url      请求URL
     * @param params   请求参数
     * @param headers  请求头
     * @param timeout  超时时间
     * @param isForm   是否为表单请求
     * @return 响应结果
     */
    private String executeRequest(Method method, String url, Map<String, Object> params, 
                                 Map<String, String> headers, int timeout, boolean isForm) {
        long startTime = System.currentTimeMillis();
        String requestId = generateRequestId();
        
        try {
            // 创建HTTP请求
            HttpRequest request = HttpRequest.of(url).method(method);
            
            // 设置超时时间
            request.timeout(timeout);
            
            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(request::header);
            }
            
            // 处理参数
            if (params != null && !params.isEmpty()) {
                if (method == Method.GET || method == Method.DELETE) {
                    // GET和DELETE请求将参数添加到URL
                    params.forEach((key, value) -> request.form(key, value));
                } else if (isForm) {
                    // POST/PUT表单请求
                    request.contentType("application/x-www-form-urlencoded");
                    params.forEach((key, value) -> request.form(key, value));
                }
            }
            
            // 打印请求日志
            logRequest(requestId, method.name(), url, params, headers);
            
            // 执行请求
            HttpResponse response = request.execute();
            
            // 获取响应结果
            String responseBody = response.body();
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应日志
            logResponse(requestId, response.getStatus(), responseBody, executionTime);
            
            // 检查响应状态
            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
            return responseBody;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logError(requestId, url, e, executionTime);
            throw new RuntimeException("调用第三方接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行HTTP请求（JSON格式）
     *
     * @param method  HTTP方法
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @param timeout 超时时间
     * @return 响应结果
     */
    private String executeJsonRequest(Method method, String url, Object body, 
                                     Map<String, String> headers, int timeout) {
        long startTime = System.currentTimeMillis();
        String requestId = generateRequestId();
        
        try {
            // 创建HTTP请求
            HttpRequest request = HttpRequest.of(url).method(method);
            
            // 设置超时时间
            request.timeout(timeout);
            
            // 设置JSON内容类型
            request.contentType("application/json;charset=UTF-8");
            
            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                headers.forEach(request::header);
            }
            
            // 设置请求体
            String requestBody = null;
            if (body != null) {
                requestBody = JSONUtil.toJsonStr(body);
                request.body(requestBody);
            }
            
            // 打印请求日志
            logJsonRequest(requestId, method.name(), url, requestBody, headers);
            
            // 执行请求
            HttpResponse response = request.execute();
            
            // 获取响应结果
            String responseBody = response.body();
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应日志
            logResponse(requestId, response.getStatus(), responseBody, executionTime);
            
            // 检查响应状态
            if (!response.isOk()) {
                throw new RuntimeException("HTTP请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
            return responseBody;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logError(requestId, url, e, executionTime);
            throw new RuntimeException("调用第三方接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 打印请求日志（表单或查询参数）
     */
    private void logRequest(String requestId, String method, String url, 
                           Map<String, Object> params, Map<String, String> headers) {
        try {
            log.info("========= 第三方接口请求开始 =========");
            log.info("请求ID: {}", requestId);
            log.info("请求方法: {}", method);
            log.info("请求URL: {}", url);
            
            if (headers != null && !headers.isEmpty()) {
                log.info("请求头: {}", formatJson(headers));
            }
            
            if (params != null && !params.isEmpty()) {
                log.info("请求参数: {}", formatJson(params));
            }
            
        } catch (Exception e) {
            log.warn("打印请求日志失败", e);
        }
    }

    /**
     * 打印请求日志（JSON格式）
     */
    private void logJsonRequest(String requestId, String method, String url, 
                               String requestBody, Map<String, String> headers) {
        try {
            log.info("========= 第三方接口请求开始 =========");
            log.info("请求ID: {}", requestId);
            log.info("请求方法: {}", method);
            log.info("请求URL: {}", url);
            
            if (headers != null && !headers.isEmpty()) {
                log.info("请求头: {}", formatJson(headers));
            }
            
            if (requestBody != null) {
                log.info("请求体: {}", formatJsonString(requestBody));
            }
            
        } catch (Exception e) {
            log.warn("打印请求日志失败", e);
        }
    }

    /**
     * 打印响应日志
     */
    private void logResponse(String requestId, int statusCode, String responseBody, long executionTime) {
        try {
            log.info("========= 第三方接口响应结束 =========");
            log.info("请求ID: {}", requestId);
            log.info("响应状态码: {}", statusCode);
            log.info("执行时间: {}ms", executionTime);
            
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 2000 ? 
                    responseBody.substring(0, 2000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应体: {}", formatJsonString(logBody));
            }
            
        } catch (Exception e) {
            log.warn("打印响应日志失败", e);
        }
    }

    /**
     * 打印错误日志
     */
    private void logError(String requestId, String url, Exception e, long executionTime) {
        log.error("========= 第三方接口调用失败 =========");
        log.error("请求ID: {}", requestId);
        log.error("请求URL: {}", url);
        log.error("执行时间: {}ms", executionTime);
        log.error("错误信息: {}", e.getMessage(), e);
    }

    /**
     * 格式化对象为JSON字符串
     */
    private String formatJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            return String.valueOf(obj);
        }
    }

    /**
     * 格式化JSON字符串
     */
    private String formatJsonString(String json) {
        try {
            if (json != null && (json.startsWith("{") || json.startsWith("["))) {
                Object obj = objectMapper.readValue(json, Object.class);
                return objectMapper.writeValueAsString(obj);
            }
            return json;
        } catch (Exception e) {
            return json;
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
