package com.joinus.study.utils;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import java.io.*;

public class HtmlToPdfConverter {

    // 字体文件路径（需根据实际位置修改）
    private static final String FONT_PATH = "D:\\downLoad\\simsun\\simsun.ttf";
    private static final String FONT_FAMILY = "simsun";

    public static void convertHtmlToPdf(String htmlContent, String outputPath) {
        try (OutputStream os = new FileOutputStream(outputPath)) {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            // 配置基本信息
            builder.useFastMode()           // 启用快速渲染模式
                    .useFont(() -> {
                        try {
                            return new FileInputStream("D:\\downLoad\\simsun\\simsun.ttf");
                        } catch (FileNotFoundException e) {
                            throw new RuntimeException("字体文件未找到: ", e);
                        }
                    }, FONT_FAMILY)  // 中文字体
                    .withHtmlContent(htmlContent, getBaseUri())  // 设置基础URI
                    .toStream(os);

            // 执行转换
            builder.run();

        } catch (Exception e) {  // 捕获所有异常类型
            handleError(e);
            throw new RuntimeException("PDF生成失败", e);
        }
    }

    private static String getBaseUri() {
        // 返回当前工作目录作为基础URI
        return new File("").toURI().toString();
    }

    private static void handleError(Exception e) {
        System.out.println("错误详情：");
        e.printStackTrace();

        // 特殊错误处理
        if (e.getMessage().contains("font")) {
            System.out.println("错误详情：" + "font");
        }
    }

    public static void main(String[] args) {
        // 完整HTML模板（添加CSS样式）
        String html = "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <style>\n" +
                "        body { font-family: simsun, sans-serif; }\n" +
                "        .exam-title {\n" +
                "            font-size: 24px;\n" +
                "            text-align: center;\n" +
                "            margin: 20px 0;\n" +
                "        }\n" +
                "        .code-container {\n" +
                "            border: 1px solid #000;\n" +
                "            padding: 10px;\n" +
                "            margin: 10px 0;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"exam-title\">阅读训练</div>\n" +
                "    <div class=\"code-container\">QR_code</div>\n" +
                "    <div class=\"passages-type\">这里是文章内容</div>\n" +
                "    <div class=\"question-type\">这里是问题内容</div>\n" +
                "</body>\n" +
                "</html>";

        try {
            convertHtmlToPdf(html, "E:\\temp\\output1.pdf");
            System.out.println("PDF生成成功！");
        } catch (Exception e) {
            System.out.println("PDF生成失败，请检查以下内容：");
            System.out.println("1. 输出目录是否存在（E:\\temp\\）");
            System.out.println("2. 字体文件路径是否正确");
            System.out.println("3. 文件是否被其他程序占用");
        }
    }
}