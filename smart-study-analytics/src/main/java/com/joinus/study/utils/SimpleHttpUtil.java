package com.joinus.study.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 简单的HTTP工具类
 * 使用hutool进行HTTP调用，打印完整的请求和响应信息
 * 
 * <AUTHOR>
 * @date 2025-09-22
 */
@Slf4j
public class SimpleHttpUtil {

    /**
     * GET请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String get(String url) {
        return get(url, null);
    }

    /**
     * GET请求
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public static String get(String url, Map<String, Object> params) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 打印请求信息
            log.info("=== HTTP GET 请求开始 ===");
            log.info("请求URL: {}", url);
            if (params != null && !params.isEmpty()) {
                log.info("请求参数: {}", JSONUtil.toJsonStr(params));
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.get(url);
            if (params != null && !params.isEmpty()) {
                params.forEach((key, value) -> request.form(key, value));
            }
            
            // 执行请求
            HttpResponse response = request.timeout(30000).execute();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应信息
            log.info("=== HTTP GET 响应结束 ===");
            log.info("响应状态码: {}", response.getStatus());
            log.info("执行时间: {}ms", executionTime);
            
            String responseBody = response.body();
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应内容: {}", logBody);
            }
            
            // 检查响应状态
            if (response.isOk()) {
                return responseBody;
            } else {
                log.error("GET请求失败 - URL: {}, 状态码: {}, 响应内容: {}", url, response.getStatus(), responseBody);
                throw new RuntimeException("GET请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("GET请求异常 - URL: {}, 执行时间: {}ms, 错误: {}", url, executionTime, e.getMessage(), e);
            throw new RuntimeException("GET请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应结果
     */
    public static String postJson(String url, Object body) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 打印请求信息
            log.info("=== HTTP POST JSON 请求开始 ===");
            log.info("请求URL: {}", url);
            
            String requestBody = null;
            if (body != null) {
                requestBody = JSONUtil.toJsonStr(body);
                log.info("请求体: {}", requestBody);
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.post(url)
                    .contentType("application/json;charset=UTF-8");
            
            if (requestBody != null) {
                request.body(requestBody);
            }
            
            // 执行请求
            HttpResponse response = request.timeout(30000).execute();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应信息
            log.info("=== HTTP POST JSON 响应结束 ===");
            log.info("响应状态码: {}", response.getStatus());
            log.info("执行时间: {}ms", executionTime);
            
            String responseBody = response.body();
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应内容: {}", logBody);
            }
            
            // 检查响应状态
            if (response.isOk()) {
                return responseBody;
            } else {
                log.error("POST JSON请求失败 - URL: {}, 状态码: {}, 响应内容: {}", url, response.getStatus(), responseBody);
                throw new RuntimeException("POST JSON请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("POST JSON请求异常 - URL: {}, 执行时间: {}ms, 错误: {}", url, executionTime, e.getMessage(), e);
            throw new RuntimeException("POST JSON请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * POST请求（表单格式）
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public static String postForm(String url, Map<String, Object> params) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 打印请求信息
            log.info("=== HTTP POST FORM 请求开始 ===");
            log.info("请求URL: {}", url);
            if (params != null && !params.isEmpty()) {
                log.info("请求参数: {}", JSONUtil.toJsonStr(params));
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.post(url)
                    .contentType("application/x-www-form-urlencoded");
            
            if (params != null && !params.isEmpty()) {
                params.forEach((key, value) -> request.form(key, value));
            }
            
            // 执行请求
            HttpResponse response = request.timeout(30000).execute();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应信息
            log.info("=== HTTP POST FORM 响应结束 ===");
            log.info("响应状态码: {}", response.getStatus());
            log.info("执行时间: {}ms", executionTime);
            
            String responseBody = response.body();
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应内容: {}", logBody);
            }
            
            // 检查响应状态
            if (response.isOk()) {
                return responseBody;
            } else {
                log.error("POST FORM请求失败 - URL: {}, 状态码: {}, 响应内容: {}", url, response.getStatus(), responseBody);
                throw new RuntimeException("POST FORM请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("POST FORM请求异常 - URL: {}, 执行时间: {}ms, 错误: {}", url, executionTime, e.getMessage(), e);
            throw new RuntimeException("POST FORM请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * PUT请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应结果
     */
    public static String putJson(String url, Object body) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 打印请求信息
            log.info("=== HTTP PUT JSON 请求开始 ===");
            log.info("请求URL: {}", url);
            
            String requestBody = null;
            if (body != null) {
                requestBody = JSONUtil.toJsonStr(body);
                log.info("请求体: {}", requestBody);
            }
            
            // 构建请求
            HttpRequest request = HttpRequest.put(url)
                    .contentType("application/json;charset=UTF-8");
            
            if (requestBody != null) {
                request.body(requestBody);
            }
            
            // 执行请求
            HttpResponse response = request.timeout(30000).execute();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应信息
            log.info("=== HTTP PUT JSON 响应结束 ===");
            log.info("响应状态码: {}", response.getStatus());
            log.info("执行时间: {}ms", executionTime);
            
            String responseBody = response.body();
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应内容: {}", logBody);
            }
            
            // 检查响应状态
            if (response.isOk()) {
                return responseBody;
            } else {
                log.error("PUT JSON请求失败 - URL: {}, 状态码: {}, 响应内容: {}", url, response.getStatus(), responseBody);
                throw new RuntimeException("PUT JSON请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("PUT JSON请求异常 - URL: {}, 执行时间: {}ms, 错误: {}", url, executionTime, e.getMessage(), e);
            throw new RuntimeException("PUT JSON请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * DELETE请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String delete(String url) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 打印请求信息
            log.info("=== HTTP DELETE 请求开始 ===");
            log.info("请求URL: {}", url);
            
            // 构建请求
            HttpRequest request = HttpRequest.delete(url);
            
            // 执行请求
            HttpResponse response = request.timeout(30000).execute();
            
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 打印响应信息
            log.info("=== HTTP DELETE 响应结束 ===");
            log.info("响应状态码: {}", response.getStatus());
            log.info("执行时间: {}ms", executionTime);
            
            String responseBody = response.body();
            if (responseBody != null) {
                // 限制响应体长度，避免日志过长
                String logBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "...[响应体过长，已截断]" : responseBody;
                log.info("响应内容: {}", logBody);
            }
            
            // 检查响应状态
            if (response.isOk()) {
                return responseBody;
            } else {
                log.error("DELETE请求失败 - URL: {}, 状态码: {}, 响应内容: {}", url, response.getStatus(), responseBody);
                throw new RuntimeException("DELETE请求失败，状态码: " + response.getStatus() + ", 响应: " + responseBody);
            }
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("DELETE请求异常 - URL: {}, 执行时间: {}ms, 错误: {}", url, executionTime, e.getMessage(), e);
            throw new RuntimeException("DELETE请求失败: " + e.getMessage(), e);
        }
    }
}
