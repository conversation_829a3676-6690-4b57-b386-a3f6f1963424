package com.joinus.study.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import org.springframework.core.io.InputStreamSource;
import java.io.IOException;
import java.io.InputStream;

public class OssResource implements InputStreamSource {
    private final OSS ossClient;
    private final String bucketName;
    private final String objectKey;

    public OssResource(OSS ossClient, String bucketName, String objectKey) {
        this.ossClient = ossClient;
        this.bucketName = bucketName;
        this.objectKey = objectKey;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        // 每次调用都创建新对象
        OSSObject ossObject = ossClient.getObject(bucketName, objectKey);
        return new ClosingWrapperStream(ossObject);
    }

    // 包装流并自动关闭OSSObject
    private static class ClosingWrapperStream extends InputStream {
        private final OSSObject ossObject;
        private final InputStream delegate;

        ClosingWrapperStream(OSSObject ossObject) {
            this.ossObject = ossObject;
            this.delegate = ossObject.getObjectContent();
        }

        @Override
        public int read() throws IOException {
            return delegate.read();
        }

        @Override
        public void close() throws IOException {
            try {
                delegate.close();
            } finally {
                ossObject.close();
            }
        }

        // 重写其他InputStream方法...
    }
}
