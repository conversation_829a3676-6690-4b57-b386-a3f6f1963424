package com.joinus.study.utils;

import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/21 16:55
 * description：发送短信工具类
 */
@Slf4j
public class SendSmsUtils {

    public static void sendSms(Long parentId, Long reportId, String smsType, String phone) {
        log.info("发送短信开始 phone:{} smsType:{}", phone, smsType);
        //您反馈的试卷错误已修正，可继续生成考试分析报告，立即标注错题，开启薄弱知识精准练习[短链]
        String templateId = "3027748";
        String url = "https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/questionMarking?reportId=" + reportId + "-fromType=reportList-reAnalysis=false";
        String templateContentParam = creatSmsUrl(parentId, url, null);
        String templateContent = "您反馈的试卷错误已修正，可继续生成考试分析报告，立即去标注错题，开启薄弱知识精准练习\uD83D\uDC49{1}";

        //判断smsType=invalid 无效纠错短信
        if("invalid".equals(smsType)){
            templateId = "3052533";//无效纠错的短信模版
            url = "https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/mathEnter?type=notice";
            templateContentParam = creatSmsUrl(parentId, url, null);
            templateContent = "您上传的试卷未识别到完整试题，请重新上传清晰的试卷照片\uD83D\uDC49{1}";
        }
        if("analysis".equals(smsType)){
            templateId = "2973079";//考试分析报告生成
            url = "https://cdn-ali-static.ijx.ink/h5-learning-tools-dev/index.html#/reportList/reportDetail?reportId=" + reportId;
            templateContentParam = creatSmsUrl(parentId, url, "分析报告详情");
            templateContent = "考试分析报告已生成，快去查看失分知识点，开启薄弱知识精准练习，立即查看报告{1}\uD83D\uDC49{1}";
        }

        sendMsgByMid(phone, templateContentParam, templateContent, templateId);
    }

    public static void sendMsgByMid(String phoneNum, String templateContentParam,String templateContent, String templateId) {
        String gatewayURL = "https://gateway.joinuscn.com";
        String url = gatewayURL + "/sms/sendSms";
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("appCode", "QYL");
        jsonParam.put("templateContentParam", templateContentParam);
        jsonParam.put("templateId", templateId);
        jsonParam.put("templateContent", templateContent);
        jsonParam.put("phoneNum", phoneNum);
        String post = HttpRequest.post(url)
                .body(JSONUtil.toJsonStr(jsonParam))
                .header("Content-Type", "application/json")
                .timeout(10000)
                .execute().body();
        JSONObject httpPost = JSONUtil.parseObj(post);
        log.info("利用佳音中间件发送短信 {}", JSONUtil.toJsonStr(jsonParam));
        if (!"200".equals(httpPost.getStr("code"))) {
            log.error("利用佳音中间件发送短信失败 {}", JSONUtil.toJsonStr(jsonParam)+httpPost.getStr("msg"));
        }
    }

    private static String creatSmsUrl(Long parentId, String url, String title){
        JSONObject options = new JSONObject();
        options.put("url", url);
        if (title != null) {
            options.put("title", title);
        }
        JSONObject jsonMap = new JSONObject();
        jsonMap.put("qylRouteName", "PublicWebView");
        jsonMap.put("parentId", parentId.toString());
        jsonMap.put("options", options);

        StringBuffer sb = new StringBuffer("https://cdn-ali-static.ijx.ink/wx-open-app-dev/index.html");
        sb.append("?publicParam=").append(URLUtil.encode(JSONUtil.toJsonStr(jsonMap)));
        return createShortUrl(sb.toString());
    }

    public static String createShortUrl(String longUrl) {
        String params = "{\"Url\":\""+ longUrl + "\",\"TermOfValidity\":\"\",\"prePath\":\"q\"}";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("TOKEN", "ijxJoinusShortUrlToken");// 设置发送数据的格式
        headers.put("Content-Type", "application/json");// 设置发送数据的格式
        String res = HttpUtil.createPost("https://ijx.ink/create/shortUrl").addHeaders(headers).body(params).execute().body();
        // 抽取生成短网址
        JSONObject jsonResult = JSONUtil.parseObj(res);
        if ("0".equals(jsonResult.get("code").toString())) {
            return jsonResult.getStr("shortUrl");
        } else {
            log.debug("errMsg: {}", jsonResult.getStr("errMsg"));
        }
        return "";
    }
}
