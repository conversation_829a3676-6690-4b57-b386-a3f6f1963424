package com.joinus.study.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 流式响应收集器
 * 用于在流式响应的同时收集完整内容，并在流结束时执行回调操作（如存入数据库）
 */
@Slf4j
public class StreamResponseCollector {

    /**
     * 非阻塞地收集普通Flux流的内容，同时立即返回原始流
     * 此方法不会阻塞原始流的传输，而是在后台异步收集内容并在流结束时执行处理
     * 
     * @param originalFlux 原始流
     * @param apiName API名称（用于日志记录）
     * @param asyncProcessor 流结束时的异步处理函数，接收完整收集的内容并返回Mono
     * @param <T> 流中的数据类型
     * @param <R> 异步处理返回的类型
     * @return 原始流（不会被阻塞）
     */
    public static <T, R> Flux<T> collectFluxNonBlocking(
            Flux<T> originalFlux, 
            String apiName, 
            Function<String, Mono<R>> asyncProcessor) {
        
        final long startTime = System.currentTimeMillis();
        final AtomicLong messageCount = new AtomicLong(0);
        final StringBuilder fullContent = new StringBuilder();

        log.info("===== 非阻塞流式响应开始 - {} =====", apiName);
        
        // 创建一个副本流用于内容收集，不影响原始流的传输
        Flux<T> collectorFlux = originalFlux
            .doOnNext(item -> {
                messageCount.incrementAndGet();
                // 收集完整内容
                fullContent.append(item);
            })
            .doOnComplete(() -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info("===== 非阻塞流式响应完成 - {} =====", apiName);
                log.info("总消息数: {}, 耗时: {}ms", messageCount.get(), duration);
                
                // 在单独的线程中执行异步处理，确保不阻塞主流程
                Mono.fromCallable(() -> fullContent.toString())
                    .flatMap(asyncProcessor)
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(
                        result -> log.info("流式响应内容已成功异步处理 - {}", apiName),
                        error -> log.error("异步处理流式响应内容时发生错误 - " + apiName, error)
                    );
            })
            .doOnError(error -> {
                log.error("非阻塞流式响应发生错误 - " + apiName, error);
            });
        
        // 订阅副本流进行内容收集，但返回原始流
        collectorFlux.subscribe();
        return originalFlux;
    }

    /**
     * 解析流式响应数据，提取所有JSON对象中的content字段并拼接
     * 
     * @param fluxText 包含多个JSON对象的字符串，如{"content":"A","reasoningContent":"嗯","finish":false}{"content":"B","reasoningContent":"，","finish":false}
     * @param text 日志标识符，用于记录日志
     * @return 拼接后的content内容，如"AB"
     */
    public static String fluxToText(String fluxText) {
        if (fluxText == null || fluxText.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        ObjectMapper objectMapper = new ObjectMapper();
        int jsonCount = 0;
        
        try {
            // 使用 Jackson 的 JsonParser 逐个解析 JSON 对象
            com.fasterxml.jackson.core.JsonFactory factory = objectMapper.getFactory();
            com.fasterxml.jackson.core.JsonParser parser = factory.createParser(fluxText);
            
            // 尝试解析每个独立的 JSON 对象
            while (parser.nextToken() != null) {
                try {
                    // 读取当前位置的完整 JSON 对象
                    JsonNode jsonNode = objectMapper.readTree(parser);
                    jsonCount++;
                    
                    // 提取 content 字段
                    if (jsonNode != null && jsonNode.has("content")) {
                        String content = jsonNode.get("content").asText();
                        result.append(content);
                    }
                } catch (Exception e) {
                    // 解析单个 JSON 对象失败，继续处理下一
                }
            }

            return result.toString();
        } catch (Exception e) {
            return "";
        }
    }

    public static void main(String[] args) {
        String text = "{\"content\":\"\",\"reasoningContent\":\"嗯\",\"finish\":false}{\"content\":\"\",\"reasoningContent\":\"嗯\",\"finish\":false}{\"content\":\"\",\"reasoningContent\":\"嗯\",\"finish\":false}{\"content\":\"A\",\"reasoningContent\":\"嗯\",\"finish\":false}{\"content\":\"B\",\"reasoningContent\":\",\",\"finish\":false}";
        String result = fluxToText(text);
        System.out.printf("结果: %s\n", result);
    }
}
