package com.joinus.study.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.study.model.vo.ChoiceContentVo;
import com.joinus.study.model.vo.SpecializedTrainingQuestionVo;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class DataUtil {
    /**
     * 计算正确率
     */
    public static BigDecimal calculateAccuracyRate(BigDecimal correctCount, BigDecimal totalQuestions) {
        // 计算正确率（处理除零异常）
        BigDecimal accuracyRate;
        if (totalQuestions.compareTo(BigDecimal.ZERO) == 0) {
            accuracyRate = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        } else {
            accuracyRate = correctCount
                    .divide(totalQuestions, 2, RoundingMode.HALF_UP);// 先保留4位中间精度
//                    .multiply(new BigDecimal("100")) // 转换为百分比
//                    .setScale(2, RoundingMode.HALF_UP); // 最终保留两位小数
        }
        return accuracyRate;
    }

    /**
     * 答案解析
     */
    public static String parseAnswer(String answerJson) {
        try {
            JSONObject json = JSONUtil.parseObj(answerJson);
            Object answerValue = json.get("答案");

            // 情况1：直接是字符串
            if (answerValue instanceof String) {
                return (String) answerValue;
            }

            // 情况2：是数组结构
            if (answerValue instanceof JSONArray) {
                JSONArray answerArray = (JSONArray) answerValue;
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < answerArray.size(); i++) {
                    JSONObject item = answerArray.getJSONObject(i);
                    String subAnswer = item.getStr("答案");
                    if (i > 0) sb.append("，");
                    sb.append(subAnswer);
                }
                return sb.toString();
            }

            // 其他未定义结构返回空
            return "";
        } catch (Exception e) {
            // 异常处理（如JSON格式错误）
            return null;
        }
    }

    public static Map<String, Object> processQuestionContent(String questionContent) {
        Map<String, Object> resMap = new HashMap<>();
        if (StrUtil.contains(questionContent, "问题")) {
            // 使用Hutool的JSONUtil解析
            JSONObject jsonObject = JSONUtil.parseObj(questionContent);

            // 处理选项（使用Hutool的JSONArray）
            if (jsonObject.containsKey("选项")) {
                resMap.put("options", jsonObject.getJSONArray("选项"));
            }

            // 获取问题内容
            resMap.put("questionContent", jsonObject.getStr("问题"));
        }
        return resMap;
    }

    //格式化题干
    public static String formatQuestionContent(String questionContent, JSONArray options) {
        JSONObject jsonContent = new JSONObject();
        if (ObjectUtil.isNotEmpty(options)) {
            jsonContent.set("选项", options);
        }
        jsonContent.set("问题", questionContent);
        return jsonContent.toString();
    }

    //格式化答案
    public static String formatAnswer(String answer) {
        return formatAnswer(answer, null);
    }

    public static String formatAnswer(String answer,String prettyAnswer) {
        JSONObject jsonAnswer = new JSONObject();
        jsonAnswer.set("答案", answer);
        if (StrUtil.isNotBlank(prettyAnswer)) {
            jsonAnswer.set("prettyAnswer", prettyAnswer);
        }
        return jsonAnswer.toString();
    }

    //年级转换格式
    public static Integer formatGrade(String grade) {
        // 处理特殊初中情况
        Map<String, Integer> juniorMap = new HashMap<>();
        juniorMap.put("初一", 7);
        juniorMap.put("初二", 8);
        juniorMap.put("初三", 9);
        juniorMap.put("初1", 7);
        juniorMap.put("初2", 8);
        juniorMap.put("初3", 9);
        juniorMap.put("高一", 10);
        juniorMap.put("高二", 11);
        juniorMap.put("高三", 12);
        juniorMap.put("高1", 10);
        juniorMap.put("高2", 11);
        juniorMap.put("高3", 12);
        if (juniorMap.containsKey(grade)) {
            return juniorMap.get(grade);
        }

        // 使用正则表达式提取数字部分
        Pattern pattern = Pattern.compile("^(?:([一二三四五六七八九])|(\\d))年级?$");
        Matcher matcher = pattern.matcher(grade);
        if (matcher.find()) {
            // 处理中文数字
            if (matcher.group(1) != null) {
                Map<Character, Integer> chineseNumMap = new HashMap<>();
                chineseNumMap.put('一', 1);
                chineseNumMap.put('二', 2);
                chineseNumMap.put('三', 3);
                chineseNumMap.put('四', 4);
                chineseNumMap.put('五', 5);
                chineseNumMap.put('六', 6);
                chineseNumMap.put('七', 7);
                chineseNumMap.put('八', 8);
                chineseNumMap.put('九', 9);
                return chineseNumMap.get(matcher.group(1).charAt(0));
            }
            // 处理阿拉伯数字
            if (matcher.group(2) != null) {
                return Integer.parseInt(matcher.group(2));
            }
        }

        // 无法解析时返回0或抛出异常（根据业务需求选择） --解析不出来先当作六年级处理
        return 6;
        // throw new IllegalArgumentException("无效的年级格式: " + grade);
    }

    //获取大写字母+数字组合随机数
    public static List<String> getRandomStringList(int length, int count) {
        if (length <= 0 || count <= 0) {
            return Collections.emptyList();
        }

        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return Stream.generate(() -> {
            // 使用Hutool的RandomUtil优化随机生成
            return RandomUtil.randomString(characters, length);
        }).limit(count).collect(Collectors.toList());
    }
    public static String convertDigitsToChinese(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 创建字符映射表：阿拉伯数字 -> 中文数字
        final char[] chineseDigits = {'零', '一', '二', '三', '四', '五', '六', '七', '八', '九'};

        StringBuilder result = new StringBuilder();

        for (char c : input.toCharArray()) {
            if (c >= '0' && c <= '9') {
                // 数字字符转换：'0'->索引0, '1'->索引1, 依此类推
                result.append(chineseDigits[c - '0']);
            } else {
                // 非数字字符保留原样
                result.append(c);
            }
        }

        return result.toString();
    }

    public static ChoiceContentVo formatChoiceContent(String questionContent) {
        if (StrUtil.isBlank(questionContent)){
            return null;
        }
        Map<String, Object> resMap = new HashMap<>();
        List<Map<String, String>>options = new ArrayList<>();
        if (StrUtil.contains(questionContent, "A.") && StrUtil.contains(questionContent, "B.")) {
            int ofA = questionContent.indexOf("A.");
            ChoiceContentVo choiceContent =
                    ChoiceContentVo.builder().questionContent(questionContent.substring(0, ofA)).build();
            Map<String, String> optionA = new HashMap<>();
            optionA.put("key", "A");
            optionA.put("value", questionContent.substring(ofA + 2, questionContent.indexOf("B.")).replaceAll("\n", ""));
            options.add(optionA);
            if(StrUtil.contains(questionContent, "C.")){
                Map<String, String> optionB = new HashMap<>();
                optionB.put("key", "B");
                optionB.put("value", questionContent.substring(questionContent.indexOf("B.")+2, questionContent.indexOf("C.")).replaceAll("\n", ""));
                options.add(optionB);
                if(StrUtil.contains(questionContent, "D.")){
                    Map<String, String> optionC = new HashMap<>();
                    optionC.put("key", "C");
                    optionC.put("value", questionContent.substring(questionContent.indexOf("C.")+2, questionContent.indexOf("D.")).replaceAll("\n", ""));
                    options.add(optionC);
                    Map<String, String> optionD = new HashMap<>();
                    optionD.put("key", "D");
                    optionD.put("value", questionContent.substring(questionContent.indexOf("D.")+2).replaceAll("\n", ""));
                    options.add(optionD);
                } else {
                    Map<String, String> optionC = new HashMap<>();
                    optionC.put("key", "C");
                    optionC.put("value", questionContent.substring(questionContent.indexOf("C.")+2));
                    options.add(optionC);
                }
            } else {
                Map<String, String> optionB = new HashMap<>();
                optionB.put("key", "B");
                optionB.put("value", questionContent.substring(questionContent.indexOf("B.")+2).replaceAll("\n", ""));
                options.add(optionB);
            }
            choiceContent.setOptions(options);
            return choiceContent;
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println("******************");
        String questionContent = "如图, 在 $5 \\times 5$ 的方格纸中, 将图(1) 中的三角形甲平移到图(2) 中所示的位置, 与三角形乙拼成一个长方形, 下面的平移方式中, 正确的是( )\n" +
                "\n" +
                " A. 1233333 1 \n" +
                "\n" +
                "B. 1233333 1\n" +
                "\n" +
                "C. 1233333 1\n" +
                "\n" +
                "D. 1233333 1\n" +
                "\n" +
                "<img data-s3-key=\"93a45a05-3465-4f51-a761-c0a73d330e53/43/56baf639-2f6a-4898-a340-36c58eecf200/4420d8a4-2bb2-45c2-b924-d2928c1d3ebe.png\" src=\"\"/>\n";
        ChoiceContentVo contentVo = formatChoiceContent(questionContent);
        System.out.println(contentVo);
    }
}
