package com.joinus.study.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.study.model.entity.ActiveStudentsCacheEntity;
import com.joinus.study.model.entity.ReadingActivityStudent;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.service.ActiveStudentsCacheService;
import com.joinus.study.service.ReadingActivityStudentService;
import com.joinus.study.service.ReadingPersonalUserService;
import com.joinus.study.utils.DataUtil;
import com.joinus.study.utils.RedisUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("readingStudentSyncTask")
@JobHandler(value = "readingStudentSyncTaskHandler")
@Slf4j
public class ReadingStudentSyncTask extends IJobHandler {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ActiveStudentsCacheService activeStudentsCacheService;
    @Resource
    private ReadingPersonalUserService readingPersonalUserService;
    @Resource
    private ReadingActivityStudentService readingActivityStudentService;

    private final static String readingStudentSyncLastAtKey = "reading:student:sync:lastAt";
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("AI语文-同步学生信息-开始-{}",OffsetDateTime.now());
        String readingStudentSyncLastAtStr = redisUtil.get(readingStudentSyncLastAtKey) != null ? redisUtil.get(readingStudentSyncLastAtKey).toString() : "";
        log.info("AI语文-同步学生信息-从redis中获取上次同步时间-{}",readingStudentSyncLastAtStr);
        LambdaQueryWrapper<ActiveStudentsCacheEntity> queryWrapper = Wrappers.<ActiveStudentsCacheEntity>lambdaQuery()
                .orderByAsc(ActiveStudentsCacheEntity::getLastSyncedAt)
                .orderByAsc(ActiveStudentsCacheEntity::getStudentId);
        if (StrUtil.isNotBlank(readingStudentSyncLastAtStr)) {
            queryWrapper.gt(ActiveStudentsCacheEntity::getLastSyncedAt, OffsetDateTime.parse(readingStudentSyncLastAtStr));
        }
        List<ActiveStudentsCacheEntity> studentList = activeStudentsCacheService.list(queryWrapper);
        log.info("AI语文-同步学生信息-本次同步的学生数量-{}",studentList.size());

        if (CollUtil.isNotEmpty(studentList)) { //有需要处理的数据
            Map<Long, List<ActiveStudentsCacheEntity>> studentMap =
                    studentList.stream().collect(Collectors.groupingBy(ActiveStudentsCacheEntity::getStudentId));
            List<Long> studentIdList = studentList.stream().map(ActiveStudentsCacheEntity::getStudentId).collect(Collectors.toList());
            log.info("AI语文-同步学生信息-本次同步的学生ID-{}",studentIdList);

            List<ReadingPersonalUser> userList = new ArrayList<>();
            List<ReadingActivityStudent> activityStudentList = new ArrayList<>();

            CollUtil.split(studentIdList,1000).forEach(itemList -> {
                userList.addAll(readingPersonalUserService.list(Wrappers.<ReadingPersonalUser>lambdaQuery()
                        .in(ReadingPersonalUser::getStudentId,itemList)));

                activityStudentList.addAll(readingActivityStudentService.list(Wrappers.<ReadingActivityStudent>lambdaQuery()
                        .in(ReadingActivityStudent::getStudentId,itemList)));

            });

            userList.forEach(user -> {
                ActiveStudentsCacheEntity student = studentMap.get(user.getStudentId()).get(0);
                user.setStudentName(student.getStudentName());
                user.setGrade(DataUtil.formatGrade(student.getGradeName()));
                user.setUpdatedAt(new Date());
            });
            log.info("AI语文-同步学生信息-本次同步ReadingPersonalUser表数据-{}条",userList.size());
            CollUtil.split(userList,1000).forEach(itemList -> {
                readingPersonalUserService.updateBatchById(itemList);
            });

            activityStudentList.forEach(activityStudent -> {
                ActiveStudentsCacheEntity student = studentMap.get(activityStudent.getStudentId()).get(0);
                activityStudent.setUpdatedAt(new Date());
                activityStudent.setGrade(DataUtil.formatGrade(student.getGradeName()));
            });
            CollUtil.split(activityStudentList,1000).forEach(itemList -> {
                readingActivityStudentService.updateBatchById(itemList);
            });
            log.info("AI语文-同步学生信息-本次同步ReadingActivityStudent表数据-{}条",activityStudentList.size());
        }

        redisUtil.set(readingStudentSyncLastAtKey,OffsetDateTime.now().toString());
        log.info("AI语文-同步学生信息-结束-{}",OffsetDateTime.now());

        return  ReturnT.SUCCESS;
    }
}
