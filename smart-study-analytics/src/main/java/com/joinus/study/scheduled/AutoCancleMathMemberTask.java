package com.joinus.study.scheduled;

import com.joinus.study.service.MathActivityStudentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 查询已开数学会员的学生的会员状态，如果学生已取消会员，则关闭其对应的业务权限
 */
@JobHandler(value = "autoCancleMathMemberTask")
@Service
@Slf4j
public class AutoCancleMathMemberTask extends IJobHandler {

	@Autowired
	private MathActivityStudentService mathActivityStudentService;

	@Override
	public ReturnT<String> execute(String param) throws Exception {
		log.debug("自动取消会员权益开始执行");
		try {
			mathActivityStudentService.autoCancleMathMemberRightsTask();
		} catch (Exception e) {
			log.info(e.getMessage());
		}
		return new ReturnT<String>(IJobHandler.SUCCESS.getCode(), "自动取消会员权益执行完成");
	}
}
