/*
package com.joinus.study.scheduled;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.ActivityRefundHistory;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathActivityFinishEnum;
import com.joinus.study.model.param.ActivityRefundParam;
import com.joinus.study.service.MathActivityStudentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


*/
/**
 * 描述 : 数学活动自动退款任务
 *
 * @date: 2025/6/16 11:50
 *//*

@JobHandler(value = "mathActivityAutoRefundTask")
@Service
@Slf4j
public class MathActivityAutoRefundTask extends IJobHandler {

    @Resource
    private MathActivityStudentService mathActivityStudentService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.debug("开始执行数学活动自动退款任务");
        try {
            // 获取mathActivityStudent finish_result状态为FINISHED 的列表
            QueryWrapper<MathActivityStudent> queryWrapper = new QueryWrapper<>();
            queryWrapper.isNull("deleted_at");
            queryWrapper.isNotNull("order_no");
            queryWrapper.gt("update_at", DateUtils.addHours(new Date(), -25));
            queryWrapper.like("finish_result", "FINISHED");
            List<MathActivityStudent> mathActivityStudentList = mathActivityStudentService.list(queryWrapper);
            mathActivityStudentList.forEach(mathActivityStudent -> {
                MathActivityFinishEnum finishResult = mathActivityStudent.getFinishResult();
                String refundHistory = mathActivityStudent.getRefundHistory();
                ActivityRefundHistory ActivityRefundHistory = null;
                if (refundHistory == null) {
                    ActivityRefundHistory = JSONObject.parseObject(refundHistory, ActivityRefundHistory.class);
                }
                ActivityRefundParam refundParam = ActivityRefundParam.builder()
                        .activityId(mathActivityStudent.getActivityId())
                        .studentId(mathActivityStudent.getStudentId())
                        .activitystudentId(mathActivityStudent.getId())
                        .build();
                try {
                    if (finishResult.equals(MathActivityFinishEnum.ALL_FINISHIED.getValue()) || finishResult.equals(MathActivityFinishEnum.REVIEW_FINISHIED.getValue())) { // 复习完成
                        if (ActivityRefundHistory == null || ActivityRefundHistory.getReviewRefundOrderNo() == null) {
                            refundParam.setType("REVIEW");
                            mathActivityStudentService.activityRefund(refundParam);
                        }
                    }
                    if (finishResult.equals(MathActivityFinishEnum.ALL_FINISHIED.getValue()) || finishResult.equals(MathActivityFinishEnum.PREVIEW_FINISHIED.getValue())) { // 复习完成
                        if (ActivityRefundHistory == null || ActivityRefundHistory.getPreviewRefundOrderNo() == null) {
                            refundParam.setType("PREVIEW");
                            mathActivityStudentService.activityRefund(refundParam);
                        }
                    }
                } catch (BaseException e) {
                    log.info("自动退款失败 : 学生活动 {} 错误 {} ", mathActivityStudent.getId(), e.getMessage());
                }
            });

        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return new ReturnT<String>(IJobHandler.SUCCESS.getCode(), "开始执行数学活动自动退款任务");
    }
}
*/
