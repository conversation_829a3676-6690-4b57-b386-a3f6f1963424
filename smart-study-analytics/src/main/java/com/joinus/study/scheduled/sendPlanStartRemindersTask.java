package com.joinus.study.scheduled;

import cn.hutool.core.util.ObjectUtil;
import com.joinus.study.kafka.ReadingPersonalPlanStartRemindersSender;
import com.joinus.study.mapper.ReadingPersonalPlanMapper;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p> Title: sendPlanStartRemindersTask </p>
 * <p> Description:定时任务执行计划开始提醒 执行任务</p>
 * <AUTHOR>
 * @date 创建日期：2025-04-02
 */
@JobHandler(value = "sendPlanStartRemindersTask")
@Service
@Slf4j
public class sendPlanStartRemindersTask extends IJobHandler {

	@Resource
	private ReadingPersonalPlanMapper personalPlanMapper;

	@Resource
	private ReadingPersonalPlanStartRemindersSender ReadingPersonalPlanStartRemindersSender;

	@Override
	public ReturnT<String> execute(String param) throws Exception {
		log.info("开始执行计划开始提醒任务，参数: {}", param); // 记录参数
		try {
			sendPlanStartReminders();
			return new ReturnT<>(SUCCESS.getCode(), "任务执行成功");
		} catch (Exception e) {
			log.error("定时任务执行失败", e); // 打印完整异常堆栈
			return new ReturnT<>(FAIL.getCode(), "任务执行失败: " + e.getMessage());
		}
	}

	void sendPlanStartReminders(){
		//获取已到期的计划 每天晚上7：30推送 （获取今天在计划周期内的所有学生遍历推送消息）
		List<ReadingPersonalPlan> personalPlans = personalPlanMapper.queryPlanStartRemindersList();
		log.info("sendPlanStartReminders已开始计划：{}", personalPlans);
		if (ObjectUtil.isEmpty(personalPlans)) {
			log.info("无待提醒计划");
			return;
		}
		personalPlans.stream()
				.filter(plan -> plan.getStudentId() != null && plan.getId() != null)
				.forEach(plan -> {
					String content = String.format("你制定的阅读计划【%s】已开始，快去练习吧！",
							Optional.ofNullable(plan.getPlanName()).orElse("未命名计划"));

					Map<String, String> message = Map.of(
							"senderType", "system",
							"studentId", plan.getStudentId().toString(),
							"messageType", "35",
							"messageTitle", "计划开始提醒",
							"content", content,
							"bizId", plan.getId().toString(),
							"isJPush", "1"
					);

					ReadingPersonalPlanStartRemindersSender.sendMessageToQylMsg(message);
				});
	}
}
