package com.joinus.study.scheduled;

import com.joinus.study.service.ReadingPersonalAnalysisPeriodicReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p> Title: autoGeneratePeriodicReportsTask </p>
 * <p> Description:自动创建周期性报告 执行任务</p>
 * <AUTHOR>
 * @date 创建日期：2025-04-02
 */
@JobHandler(value = "autoGeneratePeriodicReportsTask")
@Service
@Slf4j
public class autoGeneratePeriodicReportsTask extends IJobHandler {

	@Resource
	private ReadingPersonalAnalysisPeriodicReportService periodicReportService;

	@Override
	public ReturnT<String> execute(String param) throws Exception {
		log.debug("开始执行自动创建周期性报告任务");
		try {
			periodicReportService.autoGeneratePeriodicReports();
		} catch (Exception e) {
			log.info(e.getMessage());
		}
		return new ReturnT<String>(IJobHandler.SUCCESS.getCode(), "自动创建周期性报告任务完成");
	}
}
