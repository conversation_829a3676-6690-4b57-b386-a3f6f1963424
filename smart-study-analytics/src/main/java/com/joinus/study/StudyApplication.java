package com.joinus.study;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan(basePackages = {"com.joinus.study.mapper","com.joinus.common.mapper","com.joinus.dao.mapper"})
@ComponentScan(basePackages = {"com.joinus.study","com.joinus.common"})
@EnableScheduling
@EnableConfigurationProperties
public class StudyApplication {
    public static void main(String[] args) {
        SpringApplication.run(StudyApplication.class);
    }

}