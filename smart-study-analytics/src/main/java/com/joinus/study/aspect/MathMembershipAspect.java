package com.joinus.study.aspect;

import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.exception.MembershipRequiredException;
import com.joinus.study.service.MembershipService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 会员权限AOP切面
 * 拦截带有@RequiresMembership注解的方法，检查用户是否有会员权限
 */
@Slf4j
@Aspect
@Component
public class MathMembershipAspect {

    @Autowired
    private MembershipService membershipService;
    
    /**
     * 拦截带有@RequiresMembership注解的方法
     * 
     * @param joinPoint 连接点
     */
    @Before("@annotation(com.joinus.study.annotation.RequiresMathMembership)")
    public void checkMembership(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取方法上的注解
        RequiresMathMembership requiresMathMembership = method.getAnnotation(RequiresMathMembership.class);
        if (requiresMathMembership == null || !requiresMathMembership.value()) {
            // 未启用会员检查，直接放行
            return;
        }
        
        // 检查当前用户是否有会员权限
        if (!membershipService.isCurrentUserMember()) {
            String description = requiresMathMembership.description();
            log.warn("拒绝访问: 用户没有会员权限访问 {}.{}", 
                    method.getDeclaringClass().getSimpleName(), method.getName());
            
            // 抛出会员权限异常
            throw new MembershipRequiredException(description);
        }
        
        log.debug("会员权限校验通过: {}.{}", 
                method.getDeclaringClass().getSimpleName(), method.getName());
    }
}
