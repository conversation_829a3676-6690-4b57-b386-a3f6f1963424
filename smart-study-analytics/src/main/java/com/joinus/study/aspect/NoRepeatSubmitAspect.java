package com.joinus.study.aspect;

import com.joinus.study.annotation.NoRepeatSubmit;
import com.joinus.study.utils.RedisUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * @Description 防重复提交切面
 * <AUTHOR>
 * @Date 2025/7/1 9:34
 **/
@Aspect
@Component
public class NoRepeatSubmitAspect {
    @Autowired
    private RedisUtil redisUtil;

    @Before("@annotation(noRepeatSubmit)")
    public void around(JoinPoint pjp, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        // 1. 获取方法签名和参数信息
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        String[] paramNames = signature.getParameterNames();
        Object[] args = pjp.getArgs();
        // 2. 构建锁定的参数值组合
        String paramKey = buildParamKey(noRepeatSubmit.lockParams(), paramNames, args);
        //String className = method.getDeclaringClass().getSimpleName();
        String methodName = method.getName();

        // 3. 构建Redis Key
        String key = String.format("lock:%s:%s", methodName, paramKey);

        // 4. 尝试加锁
        if (lock(key, "1", noRepeatSubmit.lockTime())) {
            throw new RuntimeException("操作过于频繁，请稍后再试");
        }
    }

    // 构建参数键值组合
    private String buildParamKey(String[] lockParams, String[] paramNames, Object[] args) {
        if (lockParams.length == 0) {
            return "all";
        }
        StringBuilder keyBuilder = new StringBuilder();
        for (String paramPath : lockParams) {
            // 处理嵌套属性 (如 "obj.field")
            String[] pathParts = paramPath.split("\\.");
            String paramName = pathParts[0];
            for (int i = 0; i < paramNames.length; i++) {
                if (paramName.equals(paramNames[i])) {
                    Object value = args[i];
                    // 递归获取嵌套属性值
                    for (int j = 1; j < pathParts.length && value != null; j++) {
                        value = getFieldValue(value, pathParts[j]);
                    }
                    keyBuilder.append(paramPath).append("=");
                    keyBuilder.append(valueToString(value));
                    break;
                }
            }
        }
        return keyBuilder.toString();
    }


    // 反射获取字段值
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            return null;
        }
    }

    // 值转字符串
    private String valueToString(Object value) {
        if (value == null) return "null";
        if (value instanceof Number || value instanceof Boolean) {
            return value.toString();
        }
        return Integer.toHexString(value.hashCode());
    }

    // 获取锁（key: 业务唯一标识，如userId+接口名）
    private boolean lock(String key, String value, long expireSeconds) {
        if (redisUtil.hasKey(key)) {
            return true;
        }
        redisUtil.set(key, value, expireSeconds);
        return false;
    }

    // 释放锁
    private void unlock(String key) {
        redisUtil.delete(key);
    }

}
