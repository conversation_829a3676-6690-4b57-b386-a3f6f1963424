package com.joinus.study.aspect;

import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.annotation.RequiresReadingMembership;
import com.joinus.study.exception.MembershipRequiredException;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.service.MembershipService;
import com.joinus.study.service.ReadingMembershipService;
import com.joinus.study.util.CurrentUserHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 会员权限AOP切面
 * 拦截带有@RequiresMembership注解的方法，检查用户是否有会员权限
 */
@Slf4j
@Aspect
@Component
public class ReadingMembershipAspect {

    @Autowired
    private ReadingMembershipService membershipService;

    // SpEL 解析器
    private final ExpressionParser parser = new SpelExpressionParser();
    /**
     * 拦截带有@RequiresMembership注解的方法
     * 
     * @param joinPoint 连接点
     */
    @Before("@annotation(com.joinus.study.annotation.RequiresReadingMembership)")
    public void checkMembership(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取方法上的注解
        RequiresReadingMembership requiresReadingMembership = method.getAnnotation(RequiresReadingMembership.class);
        if (requiresReadingMembership != null && requiresReadingMembership.value()) {
            String description = requiresReadingMembership.description();
            String rule = requiresReadingMembership.rule();
            CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
            if (null == currentUser) {
                log.warn("拒绝访问: 用户没有会员权限访问 {}.{}",
                        method.getDeclaringClass().getSimpleName(), method.getName());
                throw new MembershipRequiredException(description);
            }
            if(StringUtils.isNoneEmpty( rule)){
                // 1. 获取方法参数和参数名
                Parameter[] parameters = method.getParameters(); // 参数元信息（含名称）
                Object[] args = joinPoint.getArgs(); // 参数值

                // 2. 构建 SpEL 评估上下文（绑定参数名和参数值）
                EvaluationContext context = new StandardEvaluationContext();
                for (int i = 0; i < parameters.length; i++) {
                    // 将参数名和参数值放入上下文，如参数名为"order"，则可在SpEL中用#order引用
                    context.setVariable(parameters[i].getName(), args[i]);
                }

                // 3. 解析注解中的 SpEL 表达式
                Expression expression = parser.parseExpression(rule);

                // 4. 执行表达式并判断结果（表达式需返回布尔值）
                Boolean result = expression.getValue(context, Boolean.class);
                if (result != null && result) {
                    membershipService.checkStudentMembership(currentUser.getStudentId(), currentUser.getUserId(), 5);
                }
            }else {
                membershipService.checkStudentMembership(currentUser.getStudentId(), currentUser.getUserId(), 5);

            }

        }

        log.debug("会员权限校验通过: {}.{}", 
                method.getDeclaringClass().getSimpleName(), method.getName());
    }
}
