package com.joinus.study.exception;

/**
 * 会员权限异常
 * 当非会员用户尝试访问需要会员权限的功能时抛出
 */
public class MembershipRequiredException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 构造函数
     * 
     * @param message 异常信息
     */
    public MembershipRequiredException(String message) {
        this(403, message);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 异常信息
     */
    public MembershipRequiredException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public int getCode() {
        return code;
    }
}
