package com.joinus.study.exception;

import com.joinus.common.exception.BaseException;
import com.joinus.common.exception.IResponseException;
import com.joinus.common.model.response.ApiResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

/**
 * @Description: 通用异常处理类
 * @Author:  anpy
 * @date:  2025/3/17 13:58
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(value = BaseException.class)
    public ApiResult bizExceptionHandler(BaseException e) {
        log.error(e.getMessage(), e);
        IResponseException responseException = e.getResponseException();
        if (responseException != null) {
            return ApiResult.failed(responseException.getCode(), responseException.getMsg());
        }
        return ApiResult.failed(e.getMessage());
    }

    /**
     * 处理会员权限异常
     */
    @ExceptionHandler(value = MembershipRequiredException.class)
    public ApiResult membershipExceptionHandler(MembershipRequiredException e) {
        log.warn("会员权限异常: {}", e.getMessage());
        return ApiResult.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(value = Exception.class)
    public ApiResult exceptionHandler(Exception e) {
        if (e instanceof BindException) {
            e.printStackTrace();//将异常打印出来
            BindException ex = (BindException) e;
            List<ObjectError> allErrors = ex.getAllErrors();
            ObjectError objectError = allErrors.get(0);
            String ms = objectError.getDefaultMessage();
            return ApiResult.failed(ms);
        } else if (e instanceof HttpMessageConversionException || e instanceof javax.validation.ConstraintViolationException) {
            e.printStackTrace();//将异常打印出来
            return ApiResult.failed(e.getMessage());
        } else if (e instanceof org.springframework.web.bind.MissingServletRequestParameterException) {
            e.printStackTrace();//将异常打印出来
            return ApiResult.failed("缺少请求参数" + ((MissingServletRequestParameterException) e).getParameterName());
        } else if (e instanceof IllegalArgumentException) {
            e.printStackTrace();//将异常打印出来
            return ApiResult.failed(e.getMessage());
        }
        log.error(e.getMessage(), e);
        return ApiResult.failed("服务器异常,请联系管理员");

    }
}
