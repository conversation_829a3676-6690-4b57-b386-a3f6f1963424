package com.joinus.study.config.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.UUID;

/**
 * EasyExcel UUID转换器
 * 用于处理Excel中UUID字段的读取和写入
 */
public class UUIDConverter implements Converter<UUID> {

    @Override
    public Class<UUID> supportJavaTypeKey() {
        return UUID.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 从Excel读取时，将字符串转换为UUID
     */
    @Override
    public UUID convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                  GlobalConfiguration globalConfiguration) {
        String stringValue = cellData.getStringValue();
        if (stringValue == null || stringValue.trim().isEmpty()) {
            return null;
        }
        try {
            return UUID.fromString(stringValue.trim());
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("无法将字符串 '" + stringValue + "' 转换为UUID: " + e.getMessage(), e);
        }
    }

    /**
     * 写入Excel时，将UUID转换为字符串
     */
    @Override
    public WriteCellData<String> convertToExcelData(UUID value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>("");
        }
        return new WriteCellData<>(value.toString());
    }
}
