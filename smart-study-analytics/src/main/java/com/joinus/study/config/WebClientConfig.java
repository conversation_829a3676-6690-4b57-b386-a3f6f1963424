package com.joinus.study.config;

import com.joinus.study.config.logging.RequestResponseLoggingFilter;
import com.joinus.study.config.logging.RequestResponseLoggingInterceptor;
import com.joinus.study.interceptor.MathActivityH5Interceptor;
import com.joinus.study.interceptor.TokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WebClient配置类
 * 提供统一的WebClient实例，可被所有服务实现类共享使用
 */
@Configuration
public class WebClientConfig implements WebMvcConfigurer {

    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
    private String eduKnowledgeHubHostUrl;

    @Value("${cors.allowed-headers:Content-Type,Accept}")
    private String[] allowedHeaders;

    // 响应头配置
    @Value("${cors.exposed-headers:Authorization}")
    private String[] exposedHeaders;

    @Autowired
    private RequestResponseLoggingInterceptor requestResponseLoggingInterceptor;

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Autowired
    private MathActivityH5Interceptor mathActivityH5Interceptor;

    /**
     * 创建针对edu-knowledge-hub服务的WebClient Bean
     * @return WebClient实例
     */
    @Bean
    public WebClient eduKnowledgeHubWebClient() {
        return WebClient.builder()
                .baseUrl(eduKnowledgeHubHostUrl)
                .build();
    }

    @Bean
    public FilterRegistrationBean<RequestResponseLoggingFilter> loggingFilter(
            RequestResponseLoggingFilter filter) {
        FilterRegistrationBean<RequestResponseLoggingFilter> registrationBean =
                new FilterRegistrationBean<>(filter);
        registrationBean.setOrder(1);
        return registrationBean;
    }
    /**
     * 创建通用的WebClient.Builder Bean
     * 可用于创建其他特定服务的WebClient实例
     * @return WebClient.Builder实例
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 或指定具体域名
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                //允许浏览器在发送请求时可以携带的请求头
                .allowedHeaders(allowedHeaders)
                //允许浏览器在接收响应时可以访问的响应头
                .exposedHeaders(exposedHeaders)
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加请求响应日志拦截器，应用于所有请求
        registry.addInterceptor(requestResponseLoggingInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/error"); // 排除错误页面
        // 添加拦截器，并排除本地IP地址的请求
        registry.addInterceptor(tokenInterceptor)
                // Swagger相关路径
                .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**")
                // Knife4j相关路径
                .excludePathPatterns("/doc.html/**", "/v3/**", "/swagger-ui/**", "/api-docs/**", "/api-docs-ext/**")
                // 其他需要排除的路径
                .excludePathPatterns("/local/**", "/error")
                //排除数学暑假活动H5 相关url
                .excludePathPatterns("/math/activity/h5/**")
                .excludePathPatterns("/pad/**");
        //  添加数学暑假活动H5拦截器
        registry.addInterceptor(mathActivityH5Interceptor).addPathPatterns("/math/activity/h5/**");
//        // 添加pad拦截器
//        registry.addInterceptor(padTokenInterceptor)
//                .addPathPatterns("/pad/**")
//                .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**")
//                .excludePathPatterns("/doc.html/**", "/v3/**", "/swagger-ui/**", "/api-docs/**", "/api-docs-ext/**")
//                .excludePathPatterns("/local/**", "/error");
    }

}
