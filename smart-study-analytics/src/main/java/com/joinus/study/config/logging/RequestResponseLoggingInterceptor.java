package com.joinus.study.config.logging;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求和响应日志拦截器
 * 记录请求参数、请求体、响应体等信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RequestResponseLoggingInterceptor implements HandlerInterceptor {

    private final ObjectMapper objectMapper;
    private final LoggingUtils loggingUtils;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查是否应该跳过日志记录
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        String path = requestURI.substring(contextPath.length());

        if (loggingUtils.shouldSkipLogging(path)) {
            // 设置属性，标记为跳过日志记录
            request.setAttribute("skipLogging", true);
            return true;
        }

        // 记录请求开始时间
        request.setAttribute("skipLogging", false);
        request.setAttribute("startTime", System.currentTimeMillis());
        return true;
    }



    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 检查是否应该跳过日志记录
        if (Boolean.TRUE.equals(request.getAttribute("skipLogging"))) {
            // 即使跳过日志记录，也要确保响应体被正确写入
            if (response instanceof ContentCachingResponseWrapper) {
                ((ContentCachingResponseWrapper) response).copyBodyToResponse();
            }
            return;
        }

        // 获取请求路径信息
        String requestURI = request.getRequestURI();
        String contextPath = request.getContextPath();
        String path = requestURI.substring(contextPath.length());

        // 检查是否应该完全跳过日志记录（排除路径或文件下载）
        if (loggingUtils.shouldSkipCompleteLogging(request, response, path)) {
            return;
        }

        // 检查是否为流式接口（只记录请求信息，不记录响应）
        if (loggingUtils.shouldLogRequestOnly(request, response, path)) {
            String detectionMethod = loggingUtils.getStreamingDetectionMethod(request, response, path);
            logStreamingRequest(request, response, detectionMethod);
            return;
        }

        try {
            // 获取执行时间
            long startTime = (Long) request.getAttribute("startTime");
            long executionTime = System.currentTimeMillis() - startTime;

            // 日志记录基本信息
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("\n========= 请求响应日志 =========\n");
            logMessage.append("请求路径: ").append(request.getMethod()).append(" ").append(request.getRequestURI());

            // 记录查询参数
            if (StringUtils.hasLength(request.getQueryString())) {
                logMessage.append("?").append(request.getQueryString());
            }
            logMessage.append("\n");

            // 记录请求头
            logMessage.append("请求头信息:\n");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                logMessage.append("  ").append(headerName).append(": ").append(request.getHeader(headerName)).append("\n");
            }

            // 记录请求参数
            logMessage.append("请求参数:\n");
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (!parameterMap.isEmpty()) {
                for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                    logMessage.append("  ").append(entry.getKey()).append(": ");
                    for (String value : entry.getValue()) {
                        logMessage.append(value).append(", ");
                    }
                    logMessage.setLength(logMessage.length() - 2); // 删除最后的", "
                    logMessage.append("\n");
                }
            } else {
                logMessage.append("  无参数\n");
            }

            // 记录请求体
            String requestBody = "";
            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
                byte[] content = requestWrapper.getContentAsByteArray();
                if (content.length > 0) {
                    // 检查是否为文件上传请求
                    String contentType = request.getContentType();
                    if (contentType != null && contentType.toLowerCase().contains("multipart/form-data")) {
                        // 对于文件上传请求，不记录详细内容，只记录大小
                        logMessage.append("请求体: [文件上传] 大小: ").append(content.length).append(" 字节\n");
                        requestBody = "[文件上传] 大小: " + content.length + " 字节";
                    } else {
                        // 对于非文件上传请求，记录详细内容
                        // 明确指定使用UTF-8编码读取请求内容
                        String charset = "UTF-8";

                        // 从Content-Type中提取字符集
                        if (contentType != null && contentType.contains("charset=")) {
                            charset = contentType.substring(contentType.indexOf("charset=") + 8).split(";")[0].trim();
                        }

                        try {
                            String contentStr = new String(content, charset);
                            // 对于日志打印，保留格式化的JSON
                            logMessage.append("请求体:\n").append(formatJson(contentStr)).append("\n");
                            // 对于存储，去掉回车符
                            requestBody = removeNewlines(contentStr);
                        } catch (Exception e) {
                            // 如果指定编码失败，尝试使用UTF-8
                            String contentStr = new String(content, "UTF-8");
                            logMessage.append("请求体:\n").append(formatJson(contentStr)).append("\n");
                            requestBody = removeNewlines(contentStr);
                        }
                    }
                }
            }

            // 记录响应状态和头信息
            logMessage.append("响应状态: ").append(response.getStatus()).append("\n");
            logMessage.append("响应头信息:\n");
            Collection<String> responseHeaderNames = response.getHeaderNames();
            for (String headerName : responseHeaderNames) {
                logMessage.append("  ").append(headerName).append(": ").append(response.getHeader(headerName)).append("\n");
            }

            // 记录响应体
            String responseBody = "";
            if (response instanceof ContentCachingResponseWrapper) {
                ContentCachingResponseWrapper responseWrapper = (ContentCachingResponseWrapper) response;
                byte[] content = responseWrapper.getContentAsByteArray();

                // 检查响应类型
                String contentType = response.getContentType();
                // 对于非流式响应，记录详细内容
                // 明确指定使用UTF-8编码读取响应内容
                String charset = "UTF-8";

                // 从Content-Type中提取字符集
                if (contentType != null && contentType.contains("charset=")) {
                    charset = contentType.substring(contentType.indexOf("charset=") + 8).split(";")[0].trim();
                }

                try {
                    String contentStr = new String(content, charset);
                    // 对于日志打印，保留格式化的JSON
                    logMessage.append("响应体:\n").append(formatJson(contentStr)).append("\n");
                    // 对于存储，去掉回车符
                    responseBody = removeNewlines(contentStr);
                } catch (Exception e) {
                    // 如果指定编码失败，尝试使用UTF-8
                    String contentStr = new String(content, "UTF-8");
                    logMessage.append("响应体:\n").append(formatJson(contentStr)).append("\n");
                    responseBody = removeNewlines(contentStr);
                }

                // 复制内容到原始流 - 对于流式响应需要特殊处理
                responseWrapper.copyBodyToResponse();
            }

            // 记录执行时间
            logMessage.append("执行时间: ").append(executionTime).append("ms\n");
            logMessage.append("========= 日志结束 =========");

            // 输出日志
            log.debug(logMessage.toString());

            // 如果不是GET请求，记录到math_user_logs表
            String method = request.getMethod();
            try {
                // 创建用于存储的JSON对象
                Map<String, Object> logContentMap = new HashMap<>();

                // 添加请求信息
                Map<String, Object> requestInfo = new HashMap<>();
                requestInfo.put("method", method);
                requestInfo.put("uri", request.getRequestURI());
                if (StringUtils.hasLength(request.getQueryString())) {
                    requestInfo.put("queryString", request.getQueryString());
                }

                // 尝试将请求体解析为JSON对象
                if (StringUtils.hasLength(requestBody)) {
                    try {
                        // 尝试解析为JSON对象或数组
                        Object jsonBody = objectMapper.readValue(requestBody, Object.class);
                        requestInfo.put("body", jsonBody);
                    } catch (Exception e) {
                        // 如果解析失败，则保留原始字符串
                        requestInfo.put("body", requestBody);
                        log.debug("请求体不是有效的JSON格式: {}", requestBody);
                    }
                } else {
                    requestInfo.put("body", requestBody);
                }

                logContentMap.put("request", requestInfo);

                // 添加响应信息
                Map<String, Object> responseInfo = new HashMap<>();
                responseInfo.put("status", response.getStatus());

                // 尝试将响应体解析为JSON对象
                if (StringUtils.hasLength(responseBody)) {
                    try {
                        // 尝试解析为JSON对象或数组
                        Object jsonBody = objectMapper.readValue(responseBody, Object.class);
                        responseInfo.put("body", jsonBody);
                    } catch (Exception e) {
                        // 如果解析失败，则保留原始字符串
                        responseInfo.put("body", responseBody);
                        log.debug("响应体不是有效的JSON格式: {}", responseBody);
                    }
                } else {
                    responseInfo.put("body", responseBody);
                }

                logContentMap.put("response", responseInfo);

                // 添加执行时间
                logContentMap.put("executionTime", executionTime);

                // 将Map转换为JSON字符串，并转义为PostgreSQL的JSONB类型格式
                String jsonContent = objectMapper.writeValueAsString(logContentMap);
                // 移除换行符，确保JSON格式紧凑
                jsonContent = removeNewlines(jsonContent);

                log.info("log {}", jsonContent);
            } catch (Exception e) {
                log.error("记录用户日志到数据库时发生错误", e);
            }
        } catch (Exception e) {
            log.error("记录请求响应日志时发生错误", e);
        }
    }

    /**
     * 尝试格式化JSON字符串
     */
    private String formatJson(String json) {
        try {
            if (json.startsWith("{") || json.startsWith("[")) {
                Object obj = objectMapper.readValue(json, Object.class);
                return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
            }
            return json;
        } catch (Exception e) {
            return json;
        }
    }

    /**
     * 专门为流式接口记录日志（只记录请求信息，不记录响应）
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param detectionMethod 检测方式（"路径配置" 或 "Content-Type"）
     */
    private void logStreamingRequest(HttpServletRequest request, HttpServletResponse response, String detectionMethod) {
        try {
            // 获取执行时间
            long startTime = (Long) request.getAttribute("startTime");
            long executionTime = System.currentTimeMillis() - startTime;

            // 日志记录基本信息
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("\n========= 流式接口请求日志 =========\n");
            logMessage.append("检测方式: ").append(detectionMethod).append("\n");
            logMessage.append("请求路径: ").append(request.getMethod()).append(" ").append(request.getRequestURI());

            // 记录查询参数
            if (StringUtils.hasLength(request.getQueryString())) {
                logMessage.append("?").append(request.getQueryString());
            }
            logMessage.append("\n");

            // 记录请求头
            logMessage.append("请求头信息:\n");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                logMessage.append("  ").append(headerName).append(": ").append(request.getHeader(headerName)).append("\n");
            }

            // 记录请求参数
            logMessage.append("请求参数:\n");
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (!parameterMap.isEmpty()) {
                for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                    logMessage.append("  ").append(entry.getKey()).append(": ");
                    for (String value : entry.getValue()) {
                        logMessage.append(value).append(", ");
                    }
                    logMessage.setLength(logMessage.length() - 2); // 删除最后的", "
                    logMessage.append("\n");
                }
            } else {
                logMessage.append("  无参数\n");
            }

            // 记录请求体
            String requestBody = "";
            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper requestWrapper = (ContentCachingRequestWrapper) request;
                byte[] content = requestWrapper.getContentAsByteArray();
                if (content.length > 0) {
                    String contentType = request.getContentType();
                    if (contentType != null && contentType.toLowerCase().contains("multipart/form-data")) {
                        logMessage.append("请求体: [文件上传] 大小: ").append(content.length).append(" 字节\n");
                        requestBody = "[文件上传] 大小: " + content.length + " 字节";
                    } else {
                        String charset = "UTF-8";
                        if (contentType != null && contentType.contains("charset=")) {
                            charset = contentType.substring(contentType.indexOf("charset=") + 8).split(";")[0].trim();
                        }

                        try {
                            String contentStr = new String(content, charset);
                            logMessage.append("请求体:\n").append(formatJson(contentStr)).append("\n");
                            requestBody = removeNewlines(contentStr);
                        } catch (Exception e) {
                            String contentStr = new String(content, "UTF-8");
                            logMessage.append("请求体:\n").append(formatJson(contentStr)).append("\n");
                            requestBody = removeNewlines(contentStr);
                        }
                    }
                }
            }

            // 记录响应状态（但不记录响应体）
            logMessage.append("响应状态: ").append(response.getStatus()).append("\n");
            logMessage.append("响应类型: 流式数据 (不记录响应体)\n");

            // 记录执行时间
            logMessage.append("执行时间: ").append(executionTime).append("ms\n");
            logMessage.append("========= 流式接口日志结束 =========");

            // 输出日志
            log.debug(logMessage.toString());

            // 记录到数据库（只记录请求信息）
            String method = request.getMethod();
            try {
                Map<String, Object> logContentMap = new HashMap<>();

                // 添加请求信息
                Map<String, Object> requestInfo = new HashMap<>();
                requestInfo.put("method", method);
                requestInfo.put("uri", request.getRequestURI());
                if (StringUtils.hasLength(request.getQueryString())) {
                    requestInfo.put("queryString", request.getQueryString());
                }

                if (StringUtils.hasLength(requestBody)) {
                    try {
                        Object jsonBody = objectMapper.readValue(requestBody, Object.class);
                        requestInfo.put("body", jsonBody);
                    } catch (Exception e) {
                        requestInfo.put("body", requestBody);
                    }
                } else {
                    requestInfo.put("body", requestBody);
                }

                logContentMap.put("request", requestInfo);

                // 添加响应信息（简化版）
                Map<String, Object> responseInfo = new HashMap<>();
                responseInfo.put("status", response.getStatus());
                responseInfo.put("type", "streaming");
                responseInfo.put("body", "[流式数据 - 不记录响应体]");

                logContentMap.put("response", responseInfo);
                logContentMap.put("executionTime", executionTime);

                String jsonContent = objectMapper.writeValueAsString(logContentMap);
                jsonContent = removeNewlines(jsonContent);

                log.info("streaming log {}", jsonContent);
            } catch (Exception e) {
                log.error("记录流式接口日志到数据库时发生错误", e);
            }
        } catch (Exception e) {
            log.error("记录流式接口请求日志时发生错误", e);
        }
    }

    /**
     * 移除字符串中的回车换行符
     */
    private String removeNewlines(String input) {
        if (input == null) {
            return "";
        }

        // 尝试解析为JSON并紧凑输出
        try {
            if (input.startsWith("{") || input.startsWith("[")) {
                Object obj = objectMapper.readValue(input, Object.class);
                return objectMapper.writeValueAsString(obj); // 使用默认输出，不格式化，没有空格和换行
            }
        } catch (Exception ignored) {
            // 忽略错误，如果不是有效的JSON，使用下面的方法处理
        }

        // 如果不是JSON或者JSON解析失败，直接替换所有回车换行
        return input.replaceAll("\\r\\n|\\r|\\n", " ").trim();
    }
}
