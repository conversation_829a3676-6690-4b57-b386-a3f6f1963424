package com.joinus.study.config.logging;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 请求响应日志过滤器
 * 用于包装请求和响应对象，以便可以多次读取请求体和响应体
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RequestResponseLoggingFilter implements Filter {

    private final LoggingUtils loggingUtils;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 获取请求URI和上下文路径
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        String path = requestURI.substring(contextPath.length());

        // 使用工具类进行综合判断
        if (loggingUtils.shouldSkipWrapping(httpRequest, path)) {
            log.debug("跳过包装的接口: {}, 不包装请求/响应体", requestURI);
            // 直接传递，不包装
            chain.doFilter(request, response);
        } else {
            // 对于普通API，包装请求和响应
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(httpRequest);
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(httpResponse);

            chain.doFilter(requestWrapper, responseWrapper);

            // 重要：将缓存的响应内容复制回原始响应流
            responseWrapper.copyBodyToResponse();
        }
    }


}
