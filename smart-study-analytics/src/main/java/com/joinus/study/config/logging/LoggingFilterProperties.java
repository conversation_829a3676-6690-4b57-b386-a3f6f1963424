package com.joinus.study.config.logging;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Getter
@Component
public class LoggingFilterProperties {

    @Value("${logging.filter.enabled:true}")
    private boolean enabled;

    @Value("#{'${logging.filter.exclude-url-patterns:/actuator/**,/swagger-ui/**,/v3/api-docs/**,/webjars/**,/error}'.split(',')}")
    private List<String> excludeUrlPatterns;

    @Value("#{'${logging.filter.streaming-url-patterns:/edu-knowledge-hub/question-single-analysis-results,/edu-knowledge-hub/ai-question-single-analysis-results,/edu-knowledge-hub/question-multi-analysis-results,/edu-knowledge-hub/flexibly-generating,/edu-knowledge-hub/text/stream/**}'.split(',')}")
    private List<String> streamingUrlPatterns;
}