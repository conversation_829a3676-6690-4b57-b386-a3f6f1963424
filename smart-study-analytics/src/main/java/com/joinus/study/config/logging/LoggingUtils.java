package com.joinus.study.config.logging;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 日志记录工具类
 * 统一管理日志记录相关的判断逻辑，避免在Filter和Interceptor中重复代码
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoggingUtils {

    private final LoggingFilterProperties loggingFilterProperties;
    private final PathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 判断是否应该跳过日志记录
     * @param path 请求路径
     * @return 如果应该跳过日志记录返回true，否则返回false
     */
    public boolean shouldSkipLogging(String path) {
        if (!loggingFilterProperties.isEnabled()) {
            return true;
        }

        return loggingFilterProperties.getExcludeUrlPatterns().stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    /**
     * 判断是否为流式接口（基于URL路径配置）
     * @param path 请求路径
     * @return 如果是流式接口返回true，否则返回false
     */
    public boolean isStreamingEndpointByPath(String path) {
        if (path == null) {
            return false;
        }

        return loggingFilterProperties.getStreamingUrlPatterns().stream()
                .anyMatch(pattern -> pathMatcher.match(pattern, path));
    }

    /**
     * 判断是否为流式接口（基于Content-Type）
     * @param response HTTP响应对象
     * @return 如果是流式接口返回true，否则返回false
     */
    public boolean isStreamingEndpointByContentType(HttpServletResponse response) {
        String contentType = response.getContentType();
        return contentType != null && (
            contentType.contains("text/event-stream") ||
            contentType.contains("application/octet-stream") ||
            contentType.contains("application/stream+json")
        );
    }

    /**
     * 判断是否为文件下载响应（基于Content-Type）
     * @param response HTTP响应对象
     * @return 如果是文件下载返回true，否则返回false
     */
    public boolean isFileDownloadResponse(HttpServletResponse response) {
        String contentType = response.getContentType();
        return contentType != null && (
            contentType.contains("application/vnd.openxmlformats") || // Excel文件
            contentType.contains("application/vnd.ms-excel") ||        // Excel文件
            contentType.contains("application/pdf") ||                 // PDF文件
            contentType.contains("application/zip") ||                 // ZIP文件
            contentType.contains("application/octet-stream") ||        // 通用二进制文件
            contentType.contains("image/") ||                          // 图片文件
            contentType.contains("video/") ||                          // 视频文件
            contentType.contains("audio/")                             // 音频文件
        );
    }

    /**
     * 判断是否为文件下载请求（基于路径和请求特征）
     * @param request HTTP请求对象
     * @param path 请求路径
     * @return 如果是文件下载请求返回true，否则返回false
     */
    public boolean isFileDownloadRequest(HttpServletRequest request, String path) {
        if (path == null) {
            return false;
        }

        // 1. 基于路径模式判断
        boolean isDownloadPath = path.contains("/download") ||
                                path.contains("/export") ||
                                path.contains("/file/") ||
                                path.endsWith(".xlsx") ||
                                path.endsWith(".xls") ||
                                path.endsWith(".pdf") ||
                                path.endsWith(".zip") ||
                                path.endsWith(".doc") ||
                                path.endsWith(".docx") ||
                                path.endsWith(".csv");

        // 2. 基于Accept头判断（客户端期望的响应类型）
        String acceptHeader = request.getHeader("Accept");
        boolean isFileAccept = acceptHeader != null && (
            acceptHeader.contains("application/vnd.ms-excel") ||
            acceptHeader.contains("application/vnd.openxmlformats") ||
            acceptHeader.contains("application/pdf") ||
            acceptHeader.contains("application/zip") ||
            acceptHeader.contains("application/octet-stream")
        );

        return isDownloadPath || isFileAccept;
    }

    /**
     * 判断是否为文件上传请求
     * @param request HTTP请求对象
     * @return 如果是文件上传请求返回true，否则返回false
     */
    public boolean isFileUploadRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType == null) {
            return false;
        }

        // 转换为小写进行比较
        contentType = contentType.toLowerCase();

        // 检查常见的文件上传Content-Type
        return contentType.contains("multipart/form-data") ||           // 表单文件上传
               contentType.contains("application/octet-stream") ||       // 二进制文件流
               contentType.contains("application/x-www-form-urlencoded") &&
               isLargeRequest(request);                                  // 大型表单数据（可能包含文件）
    }

    /**
     * 判断是否为大型请求（可能包含文件数据）
     * @param request HTTP请求对象
     * @return 如果请求体较大返回true，否则返回false
     */
    public boolean isLargeRequest(HttpServletRequest request) {
        // 获取Content-Length头
        String contentLengthStr = request.getHeader("Content-Length");
        if (contentLengthStr != null) {
            try {
                long contentLength = Long.parseLong(contentLengthStr);
                // 如果请求体大于1MB，认为可能是文件上传
                return contentLength > 1024 * 1024;
            } catch (NumberFormatException e) {
                log.debug("无法解析Content-Length: {}", contentLengthStr);
            }
        }
        return false;
    }

    /**
     * 综合判断是否应该跳过包装（用于Filter）
     * @param request HTTP请求对象
     * @param path 请求路径
     * @return 如果应该跳过包装返回true，否则返回false
     */
    public boolean shouldSkipWrapping(HttpServletRequest request, String path) {
        return shouldSkipLogging(path) ||
               isStreamingEndpointByPath(path) ||
               isFileUploadRequest(request) ||
               isFileDownloadRequest(request, path);
    }

    /**
     * 判断是否为流式接口（综合判断）
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param path 请求路径
     * @return 如果是流式接口返回true，否则返回false
     */
    public boolean isStreamingEndpoint(HttpServletRequest request, HttpServletResponse response, String path) {
        return isStreamingEndpointByPath(path) || isStreamingEndpointByContentType(response);
    }

    /**
     * 综合判断是否应该跳过完整日志记录（用于Interceptor）
     * 对于流式接口，只记录请求信息，不记录响应
     * 对于文件下载和排除路径，完全跳过日志记录
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param path 请求路径
     * @return 如果应该跳过完整日志记录返回true，否则返回false
     */
    public boolean shouldSkipCompleteLogging(HttpServletRequest request, HttpServletResponse response, String path) {
        // 完全跳过的情况：排除路径或文件下载
        return shouldSkipLogging(path) || isFileDownloadResponse(response);
    }

    /**
     * 判断是否应该只记录请求信息（流式接口）
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param path 请求路径
     * @return 如果应该只记录请求信息返回true，否则返回false
     */
    public boolean shouldLogRequestOnly(HttpServletRequest request, HttpServletResponse response, String path) {
        return isStreamingEndpoint(request, response, path);
    }

    /**
     * 获取流式接口检测方式描述
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param path 请求路径
     * @return 检测方式描述
     */
    public String getStreamingDetectionMethod(HttpServletRequest request, HttpServletResponse response, String path) {
        if (isStreamingEndpointByPath(path)) {
            return "路径配置";
        } else if (isStreamingEndpointByContentType(response)) {
            return "Content-Type";
        }
        return "未知";
    }
}
