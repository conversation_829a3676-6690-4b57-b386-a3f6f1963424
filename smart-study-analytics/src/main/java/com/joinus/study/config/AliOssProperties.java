package com.joinus.study.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description ali.oss 阿里云配置类
 * <AUTHOR>
 * @Date 2025/3/31 9:00
 **/
@Data
@Component
@ConfigurationProperties(prefix = "ali.oss")
public class AliOssProperties {

    /** 阿里OSS配置参数 **/
    private String region;
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    /** 阿里OSS配置参数 **/
    private String baseDir;

    private Sts sts;  // 对应ali.oss.sts前缀的配置

    @Data
    public static class Sts {
        private String roleArn;  // 对应ali.oss.sts.role-arn
    }

}
