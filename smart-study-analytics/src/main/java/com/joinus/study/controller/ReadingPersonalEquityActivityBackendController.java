package com.joinus.study.controller;


import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.excel.ReadingPersonalEquityActivityCodeExcel;
import com.joinus.study.model.param.ReadingPersonalEquityActivityOperateParam;
import com.joinus.study.model.param.ReadingPersonalEquityActivityPageParam;
import com.joinus.study.model.param.ReadingPersonalEquityActivityParam;
import com.joinus.study.model.param.ReadingPersonalEquityActivityUsedRecordPageParam;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityDetailVo;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityPageItemVO;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityUsedRecordVo;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityVO;
import com.joinus.study.service.ReadingPersonalEquityActivityBackendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/backend/reading-personal-equity-activity")
@Api(tags = "管理后台-邀请码活动")
public class ReadingPersonalEquityActivityBackendController {

    private ReadingPersonalEquityActivityBackendService equityActivityBackendService;


    @Operation(summary = "后台活动使用记录")
    @PostMapping("/usedRecordPages")
    public ApiResult<Page<ReadingPersonalEquityActivityUsedRecordVo>> usedRecordPages(@Validated @RequestBody ReadingPersonalEquityActivityUsedRecordPageParam pageParam) {
        return ApiResult.success(equityActivityBackendService.usedRecordPages(pageParam));
    }

    @Operation(summary = "活动删除")
    @DeleteMapping("/delete")
    @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query", dataType = "Long")
    public ApiResult delete(Long id) {
        equityActivityBackendService.delete(id);
        return ApiResult.success();
    }

    @Operation(summary = "活动失效")
    @PutMapping("/invalid")
    @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query", dataType = "Long")
    public ApiResult invalid(Long id) {
        equityActivityBackendService.invalid(id);
        return ApiResult.success();
    }

    @Operation(summary = "邀请码作废")
    @PutMapping("/invalidCode")
    @ApiImplicitParam(name = "id", value = "邀请码id", required = true, paramType = "query", dataType = "Long")
    public ApiResult invalidCode(Long id) {
        equityActivityBackendService.invalidCode(id);
        return ApiResult.success();
    }

    @GetMapping("/equity-member/flag")
    @ApiOperation(value = "首页权益活动会员标识", notes = "首页权益活动会员标识", response = ApiResult.class)
    public ApiResult<ReadingPersonalEquityActivityVO> equityMemberFlag(@RequestParam Long studentId) {
        return ApiResult.success(equityActivityBackendService.equityMemberFlag(studentId));
    }

    @PostMapping("/exchange")
    @ApiOperation(value = "兑换码兑换", notes = "邀请码-兑换码兑换", response = ApiResult.class)
    public ApiResult<Integer> exchange(@RequestBody @Valid ReadingPersonalEquityActivityParam param) {
        return ApiResult.success(equityActivityBackendService.exchange(param));
    }

    @PostMapping("/create")
    @ApiOperation(value = "添加邀请码", notes = "添加邀请码", response = ApiResult.class)
    public ApiResult<Object> createEquityActivity(@RequestBody @Valid ReadingPersonalEquityActivityOperateParam param) {
        equityActivityBackendService.createEquityActivity(param);
        return ApiResult.success();
    }

    @PostMapping("/edit")
    @ApiOperation(value = "编辑邀请码", notes = "编辑邀请码", response = ApiResult.class)
    public ApiResult<Object> editEquityActivity(@RequestBody ReadingPersonalEquityActivityOperateParam param) {
        equityActivityBackendService.editEquityActivity(param);
        return ApiResult.success();
    }

    @GetMapping("/detail")
    @ApiOperation(value = "邀请码详情", notes = "邀请码详情", response = ApiResult.class)
    public ApiResult<ReadingPersonalEquityActivityDetailVo> detailEquityActivity(Long id) {
        return ApiResult.success(equityActivityBackendService.detailEquityActivity(id));
    }

    @Operation(summary = "邀请码下载")
    @GetMapping("/{id}/invitation-code/download")
    public void downloadInvitationCode(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String timestamp = LocalDateTime.now().format(formatter);
        String fileName = URLEncoder.encode("邀请码-" + timestamp, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        List<ReadingPersonalEquityActivityCodeExcel> invitationCodeList = equityActivityBackendService.downloadListInvitationCode(id);
        EasyExcel.write(response.getOutputStream(), ReadingPersonalEquityActivityCodeExcel.class)
                .inMemory(Boolean.TRUE)
                .sheet("Sheet1")
                .doWrite(invitationCodeList);
    }

    @GetMapping("/notice/{code}")
    @ApiOperation(value = "邀请码使用须知", notes = "邀请码使用须知", response = ApiResult.class)
    public ApiResult notice(@PathVariable("code") String code) {
        return ApiResult.success(equityActivityBackendService.notice(code));
    }

    @Operation(summary = "后台邀请码分页列表")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPersonalEquityActivityPageItemVO>> pages(@Validated @RequestBody ReadingPersonalEquityActivityPageParam pageParam) {
        return ApiResult.success(equityActivityBackendService.pages(pageParam));
    }

}
