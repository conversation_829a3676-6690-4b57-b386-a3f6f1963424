package com.joinus.study.controller;

import com.joinus.study.service.SpecializedTrainingService;
import com.joinus.study.model.dto.ExamChapterQuestionDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * 性能测试控制器
 * 用于测试selectSpecializedTrainingExamDetail方法的性能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/performance/test")
@Slf4j
public class PerformanceTestController {

    @Autowired
    private SpecializedTrainingService specializedTrainingService;

    /**
     * 测试selectSpecializedTrainingExamDetail方法性能
     * 访问: GET /api/smart-study/performance/test/exam-detail?examId=xxx&personalExamId=xxx
     */
    @GetMapping("/exam-detail")
    public String testExamDetail(@RequestParam String examId, 
                                @RequestParam(required = false) Long personalExamId) {
        try {
            UUID examUuid = UUID.fromString(examId);
            log.info("开始性能测试 - ExamId: {}, PersonalExamId: {}", examId, personalExamId);
            
            long startTime = System.currentTimeMillis();
            ExamChapterQuestionDetailDto result = specializedTrainingService.selectSpecializedTrainingExamDetail(examUuid, personalExamId);
            long endTime = System.currentTimeMillis();
            
            String summary = String.format(
                "性能测试完成 - ExamId: %s, PersonalExamId: %s, 总耗时: %dms, " +
                "总题目数: %d, 总知识点数: %d, 总题型数: %d, 书籍数量: %d",
                examId, personalExamId, (endTime - startTime),
                result.getTotalQuestions(), result.getTotalKnowledgePoints(), 
                result.getTotalQuestionTypes(), result.getBookList().size()
            );
            
            log.info(summary);
            return summary;
            
        } catch (Exception e) {
            String error = String.format("性能测试失败 - ExamId: %s, PersonalExamId: %s, 错误: %s", 
                                        examId, personalExamId, e.getMessage());
            log.error(error, e);
            return error;
        }
    }

    /**
     * 批量性能测试
     * 访问: POST /api/smart-study/performance/test/batch-exam-detail
     */
    @PostMapping("/batch-exam-detail")
    public String batchTestExamDetail(@RequestBody BatchTestRequest request) {
        StringBuilder result = new StringBuilder();
        result.append("批量性能测试开始\n");
        
        for (int i = 0; i < request.getTestCount(); i++) {
            try {
                UUID examUuid = UUID.fromString(request.getExamId());
                log.info("批量测试第{}次 - ExamId: {}, PersonalExamId: {}", i + 1, request.getExamId(), request.getPersonalExamId());
                
                long startTime = System.currentTimeMillis();
                ExamChapterQuestionDetailDto testResult = specializedTrainingService.selectSpecializedTrainingExamDetail(examUuid, request.getPersonalExamId());
                long endTime = System.currentTimeMillis();
                
                String testSummary = String.format("第%d次测试: 耗时%dms, 题目数%d\n", 
                                                  i + 1, (endTime - startTime), testResult.getTotalQuestions());
                result.append(testSummary);
                
                // 避免过于频繁的调用
                if (i < request.getTestCount() - 1) {
                    Thread.sleep(request.getIntervalMs());
                }
                
            } catch (Exception e) {
                String error = String.format("第%d次测试失败: %s\n", i + 1, e.getMessage());
                result.append(error);
                log.error("批量测试第{}次失败", i + 1, e);
            }
        }
        
        result.append("批量性能测试完成");
        return result.toString();
    }

    /**
     * 批量测试请求参数
     */
    public static class BatchTestRequest {
        private String examId;
        private Long personalExamId;
        private int testCount = 3;
        private long intervalMs = 1000;

        public String getExamId() {
            return examId;
        }

        public void setExamId(String examId) {
            this.examId = examId;
        }

        public Long getPersonalExamId() {
            return personalExamId;
        }

        public void setPersonalExamId(Long personalExamId) {
            this.personalExamId = personalExamId;
        }

        public int getTestCount() {
            return testCount;
        }

        public void setTestCount(int testCount) {
            this.testCount = testCount;
        }

        public long getIntervalMs() {
            return intervalMs;
        }

        public void setIntervalMs(long intervalMs) {
            this.intervalMs = intervalMs;
        }
    }
}
