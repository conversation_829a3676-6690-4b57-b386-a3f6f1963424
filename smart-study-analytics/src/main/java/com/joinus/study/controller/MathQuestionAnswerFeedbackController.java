package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.QuestionAnswerFeedbackParam;
import com.joinus.study.service.QuestionAnswerFeedbackService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/3/17 16:37
 */
@RestController
@RequestMapping("/question-answer-feedback")
@Api(value = "/question-answer-feedback", tags = "question-answer-feedback")
public class MathQuestionAnswerFeedbackController {

    @Resource
    private QuestionAnswerFeedbackService questionAnswerFeedbackService;
     @PostMapping("/add")
        public ApiResult add(@RequestBody@Valid QuestionAnswerFeedbackParam param) {
            questionAnswerFeedbackService.add(param);
            return ApiResult.success();
        }
    @PostMapping("/cancelUpvote")
    public ApiResult cancelUpvote(@RequestBody@Valid QuestionAnswerFeedbackParam param) {
        questionAnswerFeedbackService.cancelUpvote(param);
        return ApiResult.success();
    }


}
