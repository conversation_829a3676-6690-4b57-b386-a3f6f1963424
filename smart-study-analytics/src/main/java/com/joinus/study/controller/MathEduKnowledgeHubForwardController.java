
package com.joinus.study.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.service.impl.AiRequestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/4/09 14:17
 */
@Slf4j
@RestController
@RequestMapping("/reading-personal/forward")
@Api(value = "/forward", tags = "AI接口转发")
public class MathEduKnowledgeHubForwardController {

    @Resource
    private AiRequestService requestService;
    @GetMapping("/ai/ability/ocr/hand-writing")
    @ApiOperation(value = "手写文字识别", notes = "手写文字识别 - 参数见教育知识平台文档", response = ApiResult.class)
    @ApiImplicitParam(name = "url", value = "图片地址", required = true, dataType = "String")
    public ApiResult<List<JSONObject>> ocrHandWriting(@RequestParam String url) {
        try {
            JSONObject param = new JSONObject();
            param.put("url", url);
            param.put("probability","");
            param.put("detectDirection","false");
            String data = requestService.postRequest("/ai/ability/ocr/hand-writing", param);
            if (StringUtils.isNotBlank(data)){
                List<JSONObject> objects = JSONArray.parseArray(data, JSONObject.class);
                return ApiResult.success(objects);
            } else {
                return ApiResult.failed("未检测到文字");
            }
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @PutMapping("/ai/ability/baidu/speech-asr")
    @ApiOperation(value = "百度语音识别转文字", notes = "AI能力接口 - 百度语音识别转文字", response = ApiResult.class)
    public ApiResult<String> baiduSpeechAsr(@RequestParam String path) {
        try {
            JSONObject param = new JSONObject();
            param.put("path", path);
            String data = requestService.putRequest("/ai/ability/baidu/speech-asr",param,null);
            return ApiResult.success(data);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/ai/ability/oss/presigned-url")
    @ApiOperation(value = "获取oss临时链接", notes = "AI能力接口 - 获取oss临时链接", response = ApiResult.class)
    public ApiResult<JSONObject> ossPresignedUrl(@RequestParam String ossEnum, @RequestParam String ossKey) {
        try {
            JSONObject param = new JSONObject();
            param.put("ossEnum", ossEnum);
            param.put("ossKey", ossKey);
            String data = requestService.getRequest("/ai/ability/oss/presigned-url",param);
            return ApiResult.success(JSONObject.parseObject(data));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
}

