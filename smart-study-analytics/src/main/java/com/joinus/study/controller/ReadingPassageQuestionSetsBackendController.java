package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.ReadingPassageQuestionSetsService;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

@RestController
@AllArgsConstructor
@RequestMapping("/backend/reading-personal-question-sets")
@Api(tags = "管理后台-套题列表")
public class ReadingPassageQuestionSetsBackendController {

    private ReadingPassageQuestionSetsService questionSetsService;


    //1根据文章id获取套题列表
    @Operation(summary = "获取套题列表")
    @GetMapping("/list")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "passageId", value = "文章ID", required = true, paramType = "query", dataType = "String")
    })
    public ApiResult<Page<ReadingPassageQuestionSetsVo>> queryQuestionSetsList(ReadingPassageQuestionSetsParam param) {
        return ApiResult.success(questionSetsService.queryQuestionSetsList(param));
    }

    @Operation(summary = "编辑套题")
    @PostMapping("/edit")
    public ApiResult<Boolean> questionSetsEdit(@RequestBody ReadingQuestionSetEditParam param) {
        return ApiResult.success(questionSetsService.questionSetsEdit(param));
    }

    @Operation(summary = "启用/挂起套题")
    @PostMapping("/operateEnabled")
    public ApiResult<Boolean> operateEnabled(@RequestBody ReadingPassageQuestionSetsEnabledParam param) {
        return ApiResult.success(questionSetsService.operateEnabled(param));
    }

    @Operation(summary = "审核套题")
    @PostMapping("/operateAudit")
    public ApiResult<Boolean> operateAudit(@RequestBody ReadingPassageQuestionSetsAuditParam param) {
        return ApiResult.success(questionSetsService.operateAudit(param));
    }

    @Operation(summary = "删除套题")
    @GetMapping("/delete")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "questionSetId", value = "套题ID", required = true, paramType = "query", dataType = "String")
    })
    public ApiResult<Boolean> questionSetsDelete(ReadingPassageQuestionSetsParam param) {
        return ApiResult.success(questionSetsService.questionSetsDelete(param));
    }

    @Operation(summary = "预览套题")
    @GetMapping("/view")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "questionSetId", value = "套题ID", required = true, paramType = "query", dataType = "String")
    })
    public ApiResult<ReadingQuestionSetsViewVo> questionSetsView(ReadingPassageQuestionSetsParam param) {
        return ApiResult.success(questionSetsService.questionSetsView(param));
    }

    @GetMapping(value = "/view-html")
    @ApiOperation(value = "套题生成html预览写静态页面版", notes = "套题生成html预览写静态页面版", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "questionSetId", value = "套题id",paramType = "query", dataType = "String"),
    })
    public ApiResult<ReadingPersonalPrintParam> generateQuestionSetHtml(HttpServletRequest request, ReadingPassageQuestionSetsParam param,Integer printRange) {
        try {
                String html = questionSetsService.generateQuestionSetHtml(param,printRange);
                ReadingPersonalPrintParam result = new ReadingPersonalPrintParam();
                result.setHtmlContent(html);
                return ApiResult.success(result);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping(value = "/view-htmlNew")
    @ApiOperation(value = "套题生成html预览", notes = "套题生成html预览", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "questionSetId", value = "套题id",paramType = "query", dataType = "String"),
    })
    public ApiResult<String> generateQuestionSetHtmlNew(HttpServletRequest request, @RequestParam UUID questionSetId) {
        try {
            String html = questionSetsService.generateQuestionSetHtmlNew(questionSetId);
            return ApiResult.success(html);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
    @GetMapping(value = "/getQuestionAuditResult")
    @ApiOperation(value = "获取题目审核结果列表", notes = "获取题目审核结果列表", response = ApiResult.class)

    public ApiResult<List<QuestionAuditResultVo>> getQuestionAuditResult(@RequestParam UUID questionId) {
        try {
            List<QuestionAuditResultVo> auditResult = questionSetsService.getQuestionAuditResult(questionId);
            return ApiResult.success(auditResult);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

}
