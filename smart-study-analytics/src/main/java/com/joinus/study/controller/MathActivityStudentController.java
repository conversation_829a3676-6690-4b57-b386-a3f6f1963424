package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.enums.MathActivityWeekTypeEnum;
import com.joinus.study.model.param.ActivityAutoGenerateParam;
import com.joinus.study.model.param.IpayPayBackParam;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.MathActivityJoinusParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.model.dto.ExamSummerPlanListDto;
import com.joinus.study.service.MathActivityStudentService;
import com.joinus.study.service.MembershipService;
import com.joinus.study.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.http.HttpRequest;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * 数学暑期学生活动相关controller
 */
@RestController
@RequestMapping("/math/activity/student")
@Api(value = "数学-暑期训练营活动", tags = "数学-暑期训练营活动")
@Slf4j
public class MathActivityStudentController {

    @Value("${math.activity.gift.endDate:2025-07-31}")
    private String giftEndDate;
    @Value("${math.activity.gift.no.students:123456}")
    private String giftNoStudents;
    @Resource
    private MathActivityStudentService mathActivityStudentService;
    @Resource
    private MembershipService membershipService;
    @GetMapping("/{activityId}/{studentId}")
    @ApiOperation(value = "学生参与活动报名信息", notes = "学生参与活动报名信息", response = ApiResult.class,position = 1)
    public ApiResult<MathActivityStudentVo> mathActivityStudentDto(HttpServletRequest request,@PathVariable Long activityId, @PathVariable Long studentId) {
        MathActivityStudent mathActivityStudent = new MathActivityStudent();
        mathActivityStudent.setStudentId(studentId);
        mathActivityStudent.setActivityId(activityId);
        List<MathActivityStudentVo> studentVos = mathActivityStudentService.getMathActivityStudentVo(mathActivityStudent);
        if(studentVos.size() > 0){
            int daysBetween = (int) ChronoUnit.DAYS.between(LocalDate.now(), LocalDate.parse(giftEndDate));
            if(daysBetween > 0){
                if(! giftNoStudents.contains(studentId.toString())){ //不赠送会员
                    Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
                    //领取会员
                    if(mathActivityStudentService.checkAndGiftMember(studentId, parentId, daysBetween)){
                        //更新会员权益
                        mathActivityStudentService.updateMembershipLevel(studentId, MathMemberLevelEnum.PAID);
                    }
                }
            }
            return ApiResult.success(studentVos.get(0));
        }
        return ApiResult.success(null);
    }
    @PostMapping("/join")
    @ApiOperation(value = "活动立即报名接口", notes = "选择活动内容后立即报名", response = ApiResult.class,position = 2)
    public ApiResult<Long> joinActivity(HttpServletRequest request,@RequestBody MathActivityJoinusParam mathActivityJoinusParam) {
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getActivityId(), "活动id不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getStudentId(), "学生id不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getPublisher(), "教材版本不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getGrade(), "学生年级不可为空");
        mathActivityJoinusParam.setParentId((Long) request.getAttribute("CURRENT_USER_ID"));
        Long activityStudentId = mathActivityStudentService.studentJoinActivity(mathActivityJoinusParam);
        if(activityStudentId != null){
            int daysBetween = (int) ChronoUnit.DAYS.between(LocalDate.now(), LocalDate.parse(giftEndDate));
            if(daysBetween > 0){
                if(! giftNoStudents.contains(mathActivityJoinusParam.getStudentId().toString())){ //不赠送会员
                    Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
                    //领取会员
                    if(mathActivityStudentService.checkAndGiftMember(mathActivityJoinusParam.getStudentId(), parentId, daysBetween)){
                        //更新会员权益
                        mathActivityStudentService.updateMembershipLevel(mathActivityJoinusParam.getStudentId(), MathMemberLevelEnum.PAID);
                    }
                }
            }
        }
        return ApiResult.success(activityStudentId);
    }

    @GetMapping("/member/{studentId}")
    @ApiOperation(value = "学生当前数学会员状态", notes = "学生当前数学会员状态", response = ApiResult.class)
    @ApiImplicitParam(name = "studentId", value = "学生id", required = true, dataType = "Long", paramType = "path")
    public ApiResult<Boolean> studentStudyRecords(HttpServletRequest request, @PathVariable Long studentId) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        boolean member = membershipService.isMathHolidayActivityMember(studentId, parentId);
        if (member) {
            return ApiResult.success(true);
        }
        int daysBetween = (int) ChronoUnit.DAYS.between(LocalDate.now(), LocalDate.parse(giftEndDate));
        if (daysBetween > 0) {
            MathActivityStudent currentActivity = mathActivityStudentService.getStudentCurrentActivity(studentId);
            if (currentActivity != null) {
                if (!giftNoStudents.contains(studentId.toString())) { //不赠送会员
                    //领取会员
                    membershipService.giftMathMember(studentId, parentId, daysBetween);
                    //更新会员权益
                    mathActivityStudentService.updateMembershipLevel(studentId, MathMemberLevelEnum.PAID);
                    return ApiResult.success(true);
                }
            }
        }
        return ApiResult.success(false);
    }

    @PostMapping("/pay")
    @ApiOperation(value = "支付", notes = "支付", response = ApiResult.class,  hidden = true)
    public ApiResult<String> pay(@RequestBody IpayPayRequest param) {
        String payStr = mathActivityStudentService.activityPay(param);
        return ApiResult.success(payStr);
    }

    @PostMapping("/back")
    @ApiOperation(value = "支付回调", notes = "支付成功回调", response = ApiResult.class,  hidden = true)
    public ApiResult<String> payBack(@RequestBody IpayPayBackParam param) {
        log.info("支付回调-{}", param);
        String payStr = mathActivityStudentService.activityPayBack(param);
        return ApiResult.success(payStr);
    }
    @GetMapping("/study-record/{activityStudentId}")
    @ApiOperation(value = "学生当前活动学习战绩", notes = "学习战绩", response = ApiResult.class)
    @ApiImplicitParam(name = "activityStudentId", value = "活动学生id", required = true, dataType = "Long", paramType = "path")
    public ApiResult<MathActivityStudentStudyRecord> studentStudyRecords(@PathVariable Long activityStudentId){
        MathActivityStudentStudyRecord studyRecord = mathActivityStudentService.getActivityStudentStudyRecord(activityStudentId);
        return ApiResult.success(studyRecord);
    }
    @GetMapping("/statistics")
    @ApiOperation(value = "暑期全部学习计划总览", notes = "暑期全部学习计划总览", response = ApiResult.class)
    public ApiResult<List<Map<String, Object>>> selectSummerPlansStatistics(Long studentId, Long activityId) {
        List<Map<String, Object>> maps = mathActivityStudentService.selectSummerPlansStatistics(studentId,activityId);
        return ApiResult.success(maps);
    }

    @GetMapping("/list")
    @ApiOperation(value = "暑期全部学习计划列表", notes = "暑期全部学习计划列表", response = ApiResult.class)
    public ApiResult<List<ExamSummerPlanListDto>> selectStudentSummerPlans(Long studentId, Integer weekSort, Long activityId) {
        try {
            mathActivityStudentService.updateStudentSummerPlanStatus(studentId, activityId);
        } catch (Exception e) {
            log.error("更新学生暑期计划完成状态失败", e);
        }
        List<ExamSummerPlanListDto> examSummerPlanListDtos = mathActivityStudentService.selectStudentSummerPlans(studentId, weekSort, activityId);
        return ApiResult.success(examSummerPlanListDtos);
    }

    @GetMapping("/summer-plan-status/update")
    @ApiOperation(value = "更新学生暑期计划完成状态", notes = "更新学生暑期计划完成状态", response = ApiResult.class)
    public ApiResult updateStudentSummerPlanStatus(Long studentId, Long activityId) {
        mathActivityStudentService.updateStudentSummerPlanStatus(studentId, activityId);
        return ApiResult.success("更新成功");
    }

    @Operation(summary = "暑期活动-查询是否弹窗")
    @Parameters({
            @Parameter(schema = @Schema(implementation = ActivityAutoGenerateParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/personal-planer/toast/{studentId}")
    public ApiResult<MathActivityLearningPlanerToastVo> getPersonalPlanerToast(@PathVariable("studentId") Long studentId) {
        MathActivityLearningPlanerToastVo toastVo = mathActivityStudentService.getPersonalPlanerToast(studentId);
        return ApiResult.success(toastVo);
    }

    @Operation(summary = "暑期活动-修改学生弹窗状态")
    @Parameters({
            @Parameter(schema = @Schema(implementation = MathActivityLearningPlanerToastParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/personal-planer/toast/{studentId}")
    public ApiResult<MathActivityLearningPlanerToastVo> updatePersonalPlanerToast(@PathVariable("studentId") Long studentId,
                                                                                  @RequestBody MathActivityLearningPlanerToastParam param) {
        MathActivityLearningPlanerToastVo toastVo = mathActivityStudentService.updatePersonalPlanerToast(studentId, param);
        return ApiResult.success(toastVo);
    }

    @GetMapping("/update-Memberlevel")
    @ApiOperation(value = "更新活动学生参与活动会员信息", notes = "更新活动学生参与活动会员信息", response = ApiResult.class,position = 1)
    public ApiResult<Object> mathActivityStudentDto(HttpServletRequest request) {
        String studentId = request.getParameter("studentId");
        String level = request.getParameter("membershipLevel");
        CommonResponse.ERROR.assertIsTrue(studentId !=null && level != null, "参数错误");
        mathActivityStudentService.updateMembershipLevel(Long.parseLong(studentId), MathMemberLevelEnum.valueOf(level));
        return ApiResult.success();
    }

    @GetMapping("/current-week/{studentId}")
    @ApiOperation(value = "本周规划内容", notes = "本周规划内容", response = ApiResult.class,position = 1)
    @ApiImplicitParam(name = "studentId", value = "学生id", required = true, dataType = "Long", paramType = "path")
    public ApiResult<Map<String, Object>> mathActivityCurrentWeek(HttpServletRequest request,@PathVariable("studentId") Long studentId) {
        return ApiResult.success(mathActivityStudentService.studentCurrentWeekInfo(studentId));
    }
    @PostMapping("/change-select")
    @ApiOperation(value = "更改活动学生选择书本信息", notes = "更改活动学生选择书本信息", response = ApiResult.class,position = 1)
    public ApiResult<Object> changeJoinActivity(HttpServletRequest request,@RequestBody MathActivityJoinusParam mathActivityJoinusParam) {
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getActivityId(), "活动id不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getStudentId(), "学生id不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getPublisher(), "教材版本不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getGrade(), "学生年级不可为空");

        mathActivityJoinusParam.setParentId ((Long)request.getAttribute("CURRENT_USER_ID"));
        Long activityStudentId = mathActivityStudentService.changeJoinActivity(mathActivityJoinusParam);
        return ApiResult.success(activityStudentId);
    }

    @GetMapping("/delete/{studentId}")
    @ApiImplicitParam(name = "studentId", value = "学生id", required = true, dataType = "Long", paramType = "path")
    public ApiResult<Long> deleteActivityStudent(@PathVariable("studentId") Long studentId) {
        Long activityStudentId =mathActivityStudentService.deleteActivityStudent(studentId);
        CommonResponse.ERROR.assertNotNull(activityStudentId, "删除失败,没有对应参与活动");
        return ApiResult.success(activityStudentId);
    }

    @Operation(summary = "暑期活动-查询电子教材")
    @Parameters({
            @Parameter(name = "studentId", schema = @Schema(implementation = Long.class)),
            @Parameter(name = "weekType", schema = @Schema(implementation = MathActivityWeekTypeEnum.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathElecttronicTextbookVo.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{studentId}/electronic-textbook")
    public ApiResult<MathElecttronicTextbookVo> getTextbook(HttpServletRequest request,
                                         @PathVariable("studentId") Long studentId,
                                         @RequestParam("weekType")MathActivityWeekTypeEnum weekType) {
        MathElecttronicTextbookVo result = mathActivityStudentService.getElectronicTextbook(studentId, weekType);
        return ApiResult.success(result);
    }
}
