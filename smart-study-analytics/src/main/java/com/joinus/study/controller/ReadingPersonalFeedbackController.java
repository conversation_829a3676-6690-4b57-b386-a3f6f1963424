package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingPersonalFeedbackPageParam;
import com.joinus.study.model.param.ReadingPersonalFeedbackParam;
import com.joinus.study.model.vo.ReadingPersonalFeedbackDetailsVo;
import com.joinus.study.model.vo.ReadingPersonalFeedbackVO;
import com.joinus.study.service.ReadingPersonalFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/6 14:02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/reading-personal-feedback")
@Api(tags = "题目反馈相关")
public class ReadingPersonalFeedbackController {


    private ReadingPersonalFeedbackService readingPersonalFeedbackService;

    @Operation(summary = "后台分页查询")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPersonalFeedbackVO>> pages(@Validated @RequestBody ReadingPersonalFeedbackPageParam pageParam) {
        return ApiResult.success(readingPersonalFeedbackService.pages(pageParam));
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "反馈问题题详情", notes = "反馈问题题详情", response = ApiResult.class)
    public ApiResult<ReadingPersonalFeedbackDetailsVo> detail(@PathVariable Long id) throws JsonProcessingException {
        ReadingPersonalFeedbackDetailsVo detailsVo = readingPersonalFeedbackService.getDetails(id);
        return ApiResult.success(detailsVo);
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改反馈", notes = "修改反馈", response = ApiResult.class)
    public ApiResult update(@Validated @RequestBody ReadingPersonalFeedbackDetailsVo vo) {
        readingPersonalFeedbackService.update(vo);
        return ApiResult.success();
    }
    @PostMapping
    @ApiOperation(value = "反馈")
    public ApiResult add(@Validated @RequestBody ReadingPersonalFeedbackParam param) {
        readingPersonalFeedbackService.add(param);
        return ApiResult.success();
    }

}
