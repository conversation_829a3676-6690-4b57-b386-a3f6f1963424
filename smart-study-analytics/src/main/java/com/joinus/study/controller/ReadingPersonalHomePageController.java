package com.joinus.study.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.ReadingPlanPageParam;
import com.joinus.study.model.vo.ReadingPersonalPlanVo;
import com.joinus.study.service.ReadingPersonalHomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/reading-personal")
@Api(tags = "阅读提分训练营首页相关")
@Slf4j
public class ReadingPersonalHomePageController {

    @Resource
    private ReadingPersonalHomePageService readingPersonalHomePageService;

    @Value("${reading.exclusive.customer.service:[{\"name\":\"专属语文规划师\",\"url\":\"https://cdn-ali-static.ijx.ink/math/ai_reading_customer_service_qrcode.png\"}]}")
    private String exclusiveCustomerService;
    @Value("${reading.pad.home.page.banner:[{\"url\":\"https://qyl-res.fangxiao.top/home/<USER>/yuwen_banner_1.png\"}]}")
    private String padHomePageBanners;

    @PostMapping("/user/textbook/add")
    @ApiOperation(value = "学生年级学期教材信息保存", notes = "学生年级学期教材信息保存", response = ApiResult.class)
    public ApiResult textbookAdd(@RequestBody ReadingPersonalUser vo) throws Exception {
        readingPersonalHomePageService.textbookAdd(vo);
        return ApiResult.success("成功");
    }

    @GetMapping("/user/textbook/info/{studentId}")
    @ApiOperation(value = "学生年级学期教材信息查询", notes = "学生年级学期教材信息查询", response = ApiResult.class)
    public ApiResult<ReadingPersonalUser> userTextbookInfo(@PathVariable("studentId") Long studentId) throws Exception {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不存在！");
        ReadingPersonalUser readingPersonalUser = readingPersonalHomePageService.getTextbookInfo(studentId);
        return ApiResult.success(readingPersonalUser);
    }

    @PostMapping("/plan/add")
    @ApiOperation(value = "创建阅读计划", notes = "创建阅读计划", response = ApiResult.class)
    public ApiResult planAdd(@RequestBody ReadingPersonalPlan vo) {
        try {
            readingPersonalHomePageService.planAdd(vo);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("成功");
    }

    @GetMapping("/plan/info/{planId}")
    @ApiOperation(value = "阅读计划详情", notes = "阅读计划详情", response = ApiResult.class)
    public ApiResult<ReadingPersonalPlan> planInfo(@PathVariable("planId") Long planId) throws Exception {
        CommonResponse.ERROR.assertNotNull(planId, "计划id不存在！");
        ReadingPersonalPlanVo readingPersonalPlan = readingPersonalHomePageService.getPlanInfoById(planId);
        return ApiResult.success(readingPersonalPlan);
    }

    @PostMapping("/plan/pages")
    @ApiOperation(value = "阅读计划分页", notes = "阅读计划分页", response = ApiResult.class)
    public ApiResult<Page<ReadingPersonalPlanVo>> planPages(@Validated @RequestBody ReadingPlanPageParam pageParam) {
        return ApiResult.success(readingPersonalHomePageService.planPages(pageParam));
    }

    @GetMapping("/plan/underWay/list/{studentId}")
    @ApiOperation(value = "首页阅读计划三条", notes = "首页阅读计划三条", response = ApiResult.class)
    public ApiResult<List<ReadingPersonalPlanVo>> planUnderWayList(@PathVariable("studentId") Long studentId) throws Exception {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不存在！");
        List<ReadingPersonalPlanVo> list = readingPersonalHomePageService.planUnderWayList(studentId);
        return ApiResult.success(list);
    }


    @GetMapping("/knowledge_points/info")
    @ApiOperation(value = "薄弱点列表", notes = "薄弱点列表", response = ApiResult.class)
    public ApiResult<Map<String, List<Map<String, Object>>>> knowledgePointsInfo(Long studentId) throws Exception {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不存在！");
        Map<String, List<Map<String, Object>>> pointsInfo = readingPersonalHomePageService.getKnowledgePointsInfo(studentId);
        return ApiResult.success(pointsInfo);
    }

    /**
     * @description: 专属客服
     * @author: lifengxu
     * @date: 2025/6/26 16:28
     */
    @GetMapping("/exclusive/customer/service")
    @ApiOperation(value = "专属客服信息", notes = "专属客服信息", response = ApiResult.class)
    public ApiResult<Map<String, String>> getExclusiveCustomerService() throws BaseException {
        if (ObjectUtil.isNotEmpty(exclusiveCustomerService)) {
            List<JSONObject> customerService = JSONUtil.toList(exclusiveCustomerService, JSONObject.class);
            // 当有多条记录时，随机返回一条
            if (CollUtil.isNotEmpty(customerService)) {
                int randomIndex = new Random().nextInt(customerService.size());
                log.info("从{}条专属客服数据中随机选择第{}条返回", customerService.size(), randomIndex + 1);

                JSONObject jsonObject = customerService.get(randomIndex);
                Map<String, String> result = new HashMap<>();
                result.put("name", jsonObject.getStr("name"));
                result.put("url", jsonObject.getStr("url"));
                return ApiResult.success(result);
            }
        }
        return ApiResult.success();
    }

    /**
     * @description: PAD端banner图
     * @author: lifengxu
     * @date: 2025/6/26 16:28
     */
    @GetMapping("/home/<USER>/pad/banners")
    @ApiOperation(value = "PAD端banner图", notes = "PAD端banner图", response = ApiResult.class)
    public ApiResult<List<JSONObject>> getPadHomePageBanners() throws BaseException {
        if (ObjectUtil.isNotEmpty(padHomePageBanners)) {
            List<JSONObject> bannerList = JSONUtil.toList(padHomePageBanners, JSONObject.class);
            return ApiResult.success(bannerList);
        }
        return ApiResult.success();
    }

    /**
     * @description: 某月计划日历
     * @author: lifengxu
     * @date: 2025/8/21 17:55
     */
    @GetMapping("/plan/calendar")
    @ApiOperation(value = "某月计划日历", notes = "某月计划日历", response = ApiResult.class)
    public ApiResult<Map<String, ReadingPersonalPlanVo>> planCalendar(Long studentId, Integer year, Integer month) throws BaseException {
        return ApiResult.success(readingPersonalHomePageService.getPlanCalendar(studentId, year, month));
    }
}
