package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ExampleQuestionParam;
import com.joinus.study.model.param.QueryStudyRecordParam;
import com.joinus.study.model.param.QuestionDetailParam;
import com.joinus.study.model.param.StudyRecordSaveParam;
import com.joinus.study.model.vo.ExampleQuestionVo;
import com.joinus.study.model.vo.QuestionVo;
import com.joinus.study.model.vo.StudyRecordQuestionDetailsVo;
import com.joinus.study.service.StudyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description: 学习记录
 * @date: 2025/3/12 13:57
 */
@RestController
@RequestMapping("/study-record")
@Api(value = "/study-record", tags = "study-record")
public class MathStudyRecordController {

    @Resource
    private StudyRecordService studyRecordService;
    @GetMapping("/pages")
    @ApiOperation(value = "学习记录分页列表", notes = "学习记录分页列表", response = ApiResult.class)
    public ApiResult<Page> pages(QueryStudyRecordParam param) {
        Page pages = studyRecordService.pages(param);
        return ApiResult.success(pages);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "查看解析结果页", notes = "查看解析结果页", response = ApiResult.class)
    public ApiResult<List<StudyRecordQuestionDetailsVo>> detail(@PathVariable Long id) {
        List<StudyRecordQuestionDetailsVo> studyDetailsVos = studyRecordService.detail(id);
        return ApiResult.success(studyDetailsVos);
    }

    /*
     * 学习记录单题查询
     * */
    @Deprecated
    @ApiOperation(value = "学习记录单题查询", notes = "学习记录单题查询", response = ApiResult.class)
    @GetMapping("/question/detail")
    public Flux<String> questionDetail(Long studyId, String questionId, Long studentId) {
        return studyRecordService.questionDetail(studyId, questionId, studentId);
    }


    /*
     * 学习记录单题查询
     * */
    @Deprecated
    @ApiOperation(value = "学习记录多题查询", notes = "学习记录多题查询", response = ApiResult.class)
    @GetMapping("/questions/detail")
    public Flux<String> questionsDetail(@RequestBody QuestionDetailParam param) {
        return studyRecordService.questionsDetail(param);
    }

}
