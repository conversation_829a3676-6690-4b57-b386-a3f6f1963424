package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.KnowledgePointQuestionTypesParam;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.model.vo.ExamAnalyzeResultVo;
import com.joinus.study.model.vo.MathExamQuestionDetailVo;
import com.joinus.study.model.vo.MathExamQuestionVO;
import com.joinus.study.model.vo.PersonalExamVo;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.service.GradeService;
import com.joinus.study.service.PersonalExamService;
import com.joinus.study.service.impl.AiRequestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/exam")
@Api(tags = "数学-试卷")
public class MathExamController {
    /**
     * 拍照生成试卷数量限制
     */
    @Value("${exam_image_size:10}")
    private Integer examImageSize;
    @Resource
    private PersonalExamService personalExamService;
    @Resource
    private AiRequestService aiRequestService;
    @Resource
    private GradeService gradeService;
    @Resource
    private BasicBusinessService basicBusinessService;
    @GetMapping
    @ApiOperation(value = "根据id获取试卷信息", notes = "根据id获取试卷信息,用户上传-标注错题时使用", response = ApiResult.class)
    public ApiResult<ExamQuestionInfoListData> examById(Long studentId, String id, boolean createExam,Long personalExamId, PublisherEnum publisher) {
        return ApiResult.success(personalExamService.examById(studentId, UUID.fromString(id), createExam,personalExamId, publisher));
    }

    @GetMapping("/image-size")
    @ApiOperation(value = "拍照生成试卷时照片数量", notes = "拍照生成试卷时照片数量", response = ApiResult.class)
    public ApiResult<Integer> getExamImageSize() {
        return ApiResult.success(examImageSize);
    }


    @PostMapping("/knowledge-point/question-types")
    @ApiOperation(value = "专项训练获取知识点下题型", notes = "专项训练获取知识点下题型", response = ApiResult.class)
    public ApiResult<KnowledgePointQuestionTypesDtoV2New.DataDTO> weakKnowledgePoints(@RequestBody List<KnowledgePointQuestionTypesParam> param) {
        KnowledgePointQuestionTypesDtoV2New.DataDTO typesDtos = aiRequestService.mathSpecialTrainingQuestionTypes(param);
        if (typesDtos != null) {
            return ApiResult.success(typesDtos);
        }
        return ApiResult.success();
    }

    @PostMapping("/personal-exam/save")
    @ApiOperation(value = "试卷入库", notes = "试卷入库", response = ApiResult.class)
    public ApiResult saveExam(@RequestBody PersonalExamVo personalExamVo) {
        try {
            String examId = personalExamService.saveExam(personalExamVo);
            return ApiResult.success(examId);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/student-region/{studentId}")
    @ApiOperation(value = "获取学生所在区域", notes = "获取学生所在区域", response = ApiResult.class)
    public ApiResult<String> getStudentRegion(@PathVariable Long studentId) {
        StudentBasicInfoDTO regionIdInfo = basicBusinessService.getStudentRegionIdInfo(studentId);
        CommonResponse.ERROR.assertNotNull(regionIdInfo, "学生信息无效");
        String region = String.format("%s.%s.%s", regionIdInfo.getProvince(), regionIdInfo.getCity(), regionIdInfo.getDistrict(), regionIdInfo.getRegionId());
        return ApiResult.success(region);
    }

    @GetMapping("/grades")
    @ApiOperation(value = "获取年级下拉列表", notes = "获取年级下拉列表", response = ApiResult.class)
    public ApiResult<List<Integer>> getGradeList(HttpServletRequest request) {
        List<Integer> list = new ArrayList<>();
        list.add(7);
        list.add(8);
        list.add(9);
        return ApiResult.success(list);
    }

    @GetMapping("/region")
    @ApiOperation(value = "获取区域下级区域列表", notes = "获取区域下级区域列表", response = ApiResult.class)
    @ApiImplicitParam(name = "region", value = "待查询区域", required = true, dataType = "String")
    public ApiResult<List<String>> getLowerRegionList(HttpServletRequest request) {
        String region = request.getParameter("region");
        return ApiResult.success(basicBusinessService.getLowerRegionList(region));
    }
    @GetMapping("/region/exams")
    @ApiOperation(value = "获取区域下的试题列表", notes = "获取区域下的试题列表", response = ApiResult.class)
    public ApiResult<Page<PersonalExamDto>> getRegionExamList(StudentAndTimePageParam param) {
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "请选择学生");
        CommonResponse.ERROR.assertNotNull(param.getRegion(), "请选择学生区域");
        CommonResponse.ERROR.assertNotNull(param.getSchoolId(), "请选择学校");
/*        GradeInfo grade = gradeService.getGradeByStudentId(param.getStudentId());
        CommonResponse.ERROR.assertNotNull(grade, "学生年级无效");
        param.setGrade(grade.getCurrentGradeLevel());
        param.setSemester(grade.getCurrentSemester());*/
        Page<PersonalExamDto> page = personalExamService.getRegionExamList(param);
        return ApiResult.success(page);
    }
    @GetMapping("/region/exams/{examId}")
    @ApiOperation(value = "获取区域下的试题详情", notes = "获取区域下的试题详情", response = ApiResult.class)
    @ApiImplicitParam(name = "examId", value = "试题id", required = true, dataType = "UUID")
    public ApiResult<ExamGroupQuestionTypeInfoDto> getRegionExamDetail(@PathVariable("examId") UUID examId,
                                                                       @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        ExamGroupQuestionTypeInfoDto examDetail = personalExamService.getPersonalExamExamDetail(examId, personalExamId);
        return ApiResult.success(examDetail);
    }

    @GetMapping("/personal-exam/exams/{examId}")
    @ApiOperation(value = "获取个人试卷的做题结果", notes = "获取个人试卷的做题结果", response = ApiResult.class)
    @ApiImplicitParam(name = "examId", value = "试题id", required = true, dataType = "UUID")
    public ApiResult<ExamGroupQuestionTypeInfoDto> getPersonalExamExamDetail(@PathVariable("examId") UUID examId,
                                                                       @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        ExamGroupQuestionTypeInfoDto examDetail = personalExamService.getPersonalExamExamDetail(examId, personalExamId);
        return ApiResult.success(examDetail);
    }

    @GetMapping("/getExamImages")
    @ApiOperation(value = "根据examId和examSource获取试卷原图或抹除笔记图", notes = "根据examId和examSource获取试卷原图或抹除笔记图", response = ApiResult.class)
    public ApiResult<List<String>> getExamImages(@Param("examId") String examId,@Param("examSource") String examSource) {
        List<String> images = personalExamService.getExamImages(examId, examSource);
        return ApiResult.success(images);
    }

    @GetMapping("/{examId}/questions/{questionId}")
    @ApiOperation(value = "查询个人试卷题目详情", notes = "查询个人试卷题目详情", response = ApiResult.class)
    @ApiImplicitParam(name = "examId", value = "试题id", required = true, dataType = "UUID")
    public ApiResult<MathExamQuestionDetailVo> getExamQuestionDetail(@PathVariable("examId") UUID examId,
                                                                     @PathVariable("questionId") UUID questionId,
                                                                     @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        MathExamQuestionDetailVo examQuestionDetailVo = personalExamService.getExamQuestionDetail(examId, questionId, personalExamId);
        return ApiResult.success(examQuestionDetailVo);
    }
}
