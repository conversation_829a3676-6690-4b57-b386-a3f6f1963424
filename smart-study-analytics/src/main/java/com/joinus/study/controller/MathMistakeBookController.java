package com.joinus.study.controller;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.MistakeBookDetailsVo;
import com.joinus.study.service.MistakeBookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/3/13 16:49
 */
@RestController
@RequestMapping("/mistake-book")
@Api(value = "/错题本相关", tags = "错题本")
public class MathMistakeBookController {
    @Resource
    private MistakeBookService mistakeBookService;

    @RequiresMathMembership
    @PostMapping("/add")
    @ApiOperation(value = "拍照新增错题", notes = "拍照新增错题", response = ApiResult.class)
    public ApiResult add(@RequestBody @Valid List<MistakeBookAddParam> mistakeBooks) {
        mistakeBookService.add(mistakeBooks);
        return ApiResult.success();
    }
    @PostMapping("/add-one")
    @ApiOperation(value = "新增单的错题", notes = "新增单的错题", response = ApiResult.class)
    public ApiResult add(@RequestBody MistakeBookAddParam mistakeBookAddParam) {
        mistakeBookService.addOne(mistakeBookAddParam);
        return ApiResult.success();
    }
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除错题", notes = "删除错题", response = ApiResult.class)
    public ApiResult delete(@PathVariable Long id) {
        mistakeBookService.deleteById(id);
        return ApiResult.success();
    }

    @GetMapping("/pages")
    @ApiOperation(value = "错题分页查询", notes = "错题分页查询", response = ApiResult.class)
    public ApiResult<Page> pages(QueryMistakeBookParam param) {
        Page pages = mistakeBookService.pages(param);
        return ApiResult.success(pages);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "错题详情", notes = "错题详情", response = ApiResult.class)
    public ApiResult<MistakeBookDetailsVo> detail(QueryMistakeBookParam param) {
        MistakeBookDetailsVo detailsVo = mistakeBookService.geDetails(param);
        return ApiResult.success(detailsVo);
    }

    @ApiOperation(value = "问题答案数据流展示", notes = "问题答案数据流展示", response = ApiResult.class)
    @GetMapping("/questionAnswer/{id}")
    public Flux<String> getCompleteQuestionAnswerDto(@PathVariable Long id) {
        return mistakeBookService.getCompleteQuestionAnswerDto(id);
    }
    @PostMapping("/examMistake/save")
    @ApiOperation(value = "试卷错题批量增加", notes = "试卷错题批量增加", response = ApiResult.class)
    public ApiResult examMistakeSave(@RequestBody ExamMistakeSaveParam param) {
        mistakeBookService.examMistakeSave(param);
        return ApiResult.success();
    }

    @PostMapping("/batch/update")
    @ApiOperation(value = "批量修改错题本", notes = "批量修改错题本", response = ApiResult.class)
    public ApiResult batchUpdate(@RequestBody ExamMistakeUpdateParam param) {
        mistakeBookService.batchUpdate(param);
        return ApiResult.success();
    }

}
