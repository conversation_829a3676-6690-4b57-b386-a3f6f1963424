package com.joinus.study.controller.miniapp;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.param.ActivityAutoGenerateParam;
import com.joinus.study.model.vo.ActivityChapterVo;
import com.joinus.study.service.MathActivityService;
import com.joinus.study.service.WechatMaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/mini-app/login")
@Api(tags = "青于蓝智习室小程序-登录")
@Slf4j
public class WechatMaLoginController {

    @Autowired
    private WechatMaService wechatMaService;

    @Operation(summary = "登录")
    @GetMapping("/openid")
    public ApiResult listChapters(@RequestParam("code") String code) {
        String openid = wechatMaService.getOpenId(code);
        return ApiResult.success();
    }


}
