package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingErrorCorrectingParam;
import com.joinus.study.model.param.ReadingHistoryReportPageParam;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.vo.ReadingHistoryReportVo;
import com.joinus.study.model.vo.ReadingPeriodicReportViewDetailVo;
import com.joinus.study.model.vo.ReadingReportDetailVo;
import com.joinus.study.service.ReadingPersonalAnalysisPeriodicReportService;
import com.joinus.study.service.ReadingPersonalAnalysisReportService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

@RestController
@RequestMapping("/reading-personal-analysis-report")
@Api(tags = "语文阅读提分学情分析报告")
public class ReadingPersonalAnalysisReportController {

    @Resource
    private ReadingPersonalAnalysisReportService reportService;
    @Resource
    private ReadingPersonalAnalysisPeriodicReportService periodicReportService;
    @Resource(name = "taskExecutor")
    private Executor threadPoolTaskExecutor;

    @GetMapping(value = "/queryReadingHistoryReportList")
    @ApiOperation(value = "历次学情分析报告列表", notes = "历次学情分析报告列表", response = ApiResult.class)
    public ApiResult<Page<ReadingHistoryReportVo>> queryReadingHistoryReportList(ReadingHistoryReportPageParam pageParam) {
        return ApiResult.success(reportService.queryReadingHistoryReportList(pageParam));
    }

    @GetMapping(value = "/generateTrainingReport")
    @ApiOperation(value = "阅读提分训练生成学情分析报告", notes = "阅读提分训练生成学情分析报告", response = ApiResult.class)
    public ApiResult generateTrainingReport(Long personalPassageId) {
        return ApiResult.success(reportService.generateTrainingReport(personalPassageId));
//        periodicReportService.autoGeneratePeriodicReports();
//        return ApiResult.success();
    }

    @GetMapping(value = "/reportDetail")
    @ApiOperation(value = "阅读提分训练学情分析报告详情", notes = "阅读提分训练学情分析报告详情", response = ApiResult.class)
    public ApiResult<ReadingReportDetailVo> reportDetail(
            @ApiParam(name = "reportId", value = "报告ID", required = true) @RequestParam Long reportId) {
        return ApiResult.success(reportService.reportDetail(reportId, 1));
    }

    @GetMapping(value = "/periodicReportDetail")
    @ApiOperation(value = "阅读提分训练学情分析周报和月报报告详情", notes = "阅读提分训练学情分析周报和月报报告详情", response = ApiResult.class)
    public ApiResult<ReadingPeriodicReportViewDetailVo> periodicReportDetail(ReadingPeriodicReportDetailParam param) {
        return ApiResult.success(periodicReportService.periodicReportDetail(param));
    }

    @GetMapping(value = "/getReportIdByPersonalPassageId")
    @ApiOperation(value = "阅读提分训练根据用户答题训练id获取报告id", notes = "阅读提分训练根据用户答题训练id获取报告id", response = ApiResult.class)
    public ApiResult<Long> getReportIdByPersonalPassageId(Long personalPassageId) {
        return ApiResult.success(reportService.getReportIdByPersonalPassageId(personalPassageId));
    }

    @PostMapping(value = "/error-correcting")
    @ApiOperation(value = "纠错")
    public ApiResult errorCorrecting(@Validated @RequestBody ReadingErrorCorrectingParam param) {
        reportService.errorCorrecting(param);
        return ApiResult.success();
    }

    @GetMapping(value = "/getReportNotViewCountByStudentId")
    @ApiOperation(value = "获取学生未查看的报告数量", notes = "获取学生未查看的报告数量", response = ApiResult.class)
    @ApiImplicitParams(value = {
        @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<Integer> getReportNotViewCountByStudentId(Long studentId) {
        return ApiResult.success(reportService.getReportNotViewCountByStudentId(studentId));
    }

    @GetMapping(value = "/againGenerateSingleReport")
    @ApiOperation(value = "生成单次报告", notes = "阅读提分训练生成学情分析报告", response = ApiResult.class)
    public ApiResult againGenerateSingleReport() {
        threadPoolTaskExecutor.execute(() -> {
            reportService.againGenerateSingleReport();
        });
        return ApiResult.success();
    }

    @GetMapping(value = "/againGeneratePeriodicReport")
    @ApiOperation(value = "生成周期报告", notes = "阅读提分训练生成学情分析报告", response = ApiResult.class)
    public ApiResult againGeneratePeriodicReport() {
        threadPoolTaskExecutor.execute(() -> {
            periodicReportService.againGeneratePeriodicReport();
        });
        return ApiResult.success();
    }
}
