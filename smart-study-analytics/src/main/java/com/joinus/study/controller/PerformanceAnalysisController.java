package com.joinus.study.controller;

import com.joinus.study.util.PerformanceAnalyzer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 性能分析控制器
 * 提供性能数据查看和分析接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/performance/analysis")
@Slf4j
public class PerformanceAnalysisController {

    /**
     * 获取指定方法的性能报告
     * 访问: GET /api/smart-study/performance/analysis/report?methodName=selectSpecializedTrainingExamDetail
     */
    @GetMapping("/report")
    public String getPerformanceReport(@RequestParam(defaultValue = "selectSpecializedTrainingExamDetail") String methodName) {
        try {
            String report = PerformanceAnalyzer.getPerformanceReport(methodName);
            log.info("生成性能报告 - 方法: {}", methodName);
            return report;
        } catch (Exception e) {
            log.error("生成性能报告失败 - 方法: {}", methodName, e);
            return "生成性能报告失败: " + e.getMessage();
        }
    }

    /**
     * 获取所有方法的性能概览
     * 访问: GET /api/smart-study/performance/analysis/overview
     */
    @GetMapping("/overview")
    public String getAllPerformanceOverview() {
        try {
            String overview = PerformanceAnalyzer.getAllPerformanceOverview();
            log.info("生成全局性能概览");
            return overview;
        } catch (Exception e) {
            log.error("生成全局性能概览失败", e);
            return "生成全局性能概览失败: " + e.getMessage();
        }
    }

    /**
     * 获取指定方法的性能统计数据（JSON格式）
     * 访问: GET /api/smart-study/performance/analysis/stats?methodName=selectSpecializedTrainingExamDetail
     */
    @GetMapping("/stats")
    public Map<String, Object> getPerformanceStats(@RequestParam(defaultValue = "selectSpecializedTrainingExamDetail") String methodName) {
        try {
            Map<String, Object> stats = PerformanceAnalyzer.getPerformanceStats(methodName);
            log.info("获取性能统计数据 - 方法: {}", methodName);
            return stats;
        } catch (Exception e) {
            log.error("获取性能统计数据失败 - 方法: {}", methodName, e);
            return Map.of("error", "获取性能统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 清除所有性能数据
     * 访问: DELETE /api/smart-study/performance/analysis/clear
     */
    @DeleteMapping("/clear")
    public String clearAllPerformanceData() {
        try {
            PerformanceAnalyzer.clearPerformanceData();
            log.info("已清除所有性能数据");
            return "已清除所有性能数据";
        } catch (Exception e) {
            log.error("清除性能数据失败", e);
            return "清除性能数据失败: " + e.getMessage();
        }
    }

    /**
     * 清除指定方法的性能数据
     * 访问: DELETE /api/smart-study/performance/analysis/clear?methodName=selectSpecializedTrainingExamDetail
     */
    @DeleteMapping("/clear-method")
    public String clearMethodPerformanceData(@RequestParam(defaultValue = "selectSpecializedTrainingExamDetail") String methodName) {
        try {
            PerformanceAnalyzer.clearMethodPerformanceData(methodName);
            log.info("已清除方法 {} 的性能数据", methodName);
            return "已清除方法 " + methodName + " 的性能数据";
        } catch (Exception e) {
            log.error("清除方法性能数据失败 - 方法: {}", methodName, e);
            return "清除方法性能数据失败: " + e.getMessage();
        }
    }

    /**
     * 获取性能分析建议
     * 访问: GET /api/smart-study/performance/analysis/suggestions?methodName=selectSpecializedTrainingExamDetail
     */
    @GetMapping("/suggestions")
    public String getPerformanceSuggestions(@RequestParam(defaultValue = "selectSpecializedTrainingExamDetail") String methodName) {
        try {
            Map<String, Object> stats = PerformanceAnalyzer.getPerformanceStats(methodName);
            StringBuilder suggestions = new StringBuilder();
            suggestions.append("=== 性能优化建议 - ").append(methodName).append(" ===\n\n");

            if (stats.containsKey("message")) {
                suggestions.append("暂无性能数据，请先执行方法以收集数据。\n");
                return suggestions.toString();
            }

            @SuppressWarnings("unchecked")
            Map<String, Map<String, Object>> stepStats = (Map<String, Map<String, Object>>) stats.get("stepStats");
            
            if (stepStats == null || stepStats.isEmpty()) {
                suggestions.append("暂无步骤性能数据。\n");
                return suggestions.toString();
            }

            // 找出最耗时的步骤
            String slowestStep = null;
            double maxAvgDuration = 0;
            
            for (Map.Entry<String, Map<String, Object>> entry : stepStats.entrySet()) {
                double avgDuration = (Double) entry.getValue().get("avgDuration");
                if (avgDuration > maxAvgDuration) {
                    maxAvgDuration = avgDuration;
                    slowestStep = entry.getKey();
                }
            }

            if (slowestStep != null) {
                suggestions.append("🔍 性能瓶颈分析:\n");
                suggestions.append(String.format("最耗时步骤: %s (平均%.2fms)\n\n", slowestStep, maxAvgDuration));

                // 根据步骤名称给出具体建议
                if (slowestStep.contains("DB查询") || slowestStep.contains("getQuestionInfoById")) {
                    suggestions.append("💡 数据库查询优化建议:\n");
                    suggestions.append("- 考虑批量查询替代单个查询\n");
                    suggestions.append("- 检查数据库索引是否合适\n");
                    suggestions.append("- 考虑添加查询缓存\n");
                    suggestions.append("- 使用连接池优化数据库连接\n\n");
                }

                if (slowestStep.contains("集合操作") || slowestStep.contains("过滤")) {
                    suggestions.append("💡 集合操作优化建议:\n");
                    suggestions.append("- 考虑使用Map替代多次stream过滤\n");
                    suggestions.append("- 预先构建索引Map提高查找效率\n");
                    suggestions.append("- 减少不必要的对象创建\n");
                    suggestions.append("- 考虑并行流处理大数据集\n\n");
                }

                if (slowestStep.contains("解码") || slowestStep.contains("decodeContentV2")) {
                    suggestions.append("💡 内容解码优化建议:\n");
                    suggestions.append("- 考虑缓存解码结果\n");
                    suggestions.append("- 批量解码替代单个解码\n");
                    suggestions.append("- 检查解码算法效率\n\n");
                }

                if (slowestStep.contains("外部调用") || slowestStep.contains("eduKnowledgeHubService")) {
                    suggestions.append("💡 外部服务调用优化建议:\n");
                    suggestions.append("- 考虑添加结果缓存\n");
                    suggestions.append("- 检查网络延迟和超时设置\n");
                    suggestions.append("- 考虑异步调用\n");
                    suggestions.append("- 实现服务降级策略\n\n");
                }
            }

            // 通用优化建议
            suggestions.append("🚀 通用优化建议:\n");
            suggestions.append("- 监控内存使用，避免内存泄漏\n");
            suggestions.append("- 考虑分页处理大数据集\n");
            suggestions.append("- 使用异步处理非关键路径\n");
            suggestions.append("- 定期清理不必要的临时对象\n");
            suggestions.append("- 考虑使用缓存减少重复计算\n");

            return suggestions.toString();

        } catch (Exception e) {
            log.error("生成性能建议失败 - 方法: {}", methodName, e);
            return "生成性能建议失败: " + e.getMessage();
        }
    }
}
