package com.joinus.study.controller;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.bo.QueryElectronicTextbookBo;
import com.joinus.study.model.enums.BookVolumeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.CreateQuestionByKnowledgeParamV2;
import com.joinus.study.model.param.MathStudentStudyVideoParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.util.CurrentUserHolder;
import com.joinus.study.utils.AliOssUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/math/sync-learning-test")
@Api(tags = "数学-同步学练测")
@Slf4j
public class MathSyncLearningTestController {

    @Resource
    private MathSyncLearningTestService mathSyncLearningTestService;

    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private SpecializedTrainingService specializedTrainingService;
    @Resource
    private PersonalExamService personalExamService;
    @Resource
    private MathStudentKnowledgePointService mathStudentKnowledgePointService;

    @Resource
    private MathStudentStudyVideoHistoryService mathStudentStudyVideoHistoryService;
    @GetMapping("/catalogs/root-nodes")
    @ApiOperation(value = "查询根节点", notes = "查询根节点", response = ApiResult.class)
    public ApiResult<List<MathCatalogNodeVo>> listCatalogRootNodes(@RequestParam(value = "grade") Integer grade,
                                                                   @RequestParam(value = "publisher") PublisherEnum publisher,
                                                                   @RequestParam(value = "semester", required = false) Integer semester,
                                                                   @RequestParam(value = "bookVolume", required = false) BookVolumeEnum bookVolume) {
        Long studentId = CurrentUserHolder.getCurrentStudentId();
        semester = null != bookVolume ? bookVolume.getVolumeNum() : semester;
        List<MathCatalogNodeVo> rootNodes = mathSyncLearningTestService.listCatalogRootNodes(grade, publisher, semester, studentId);
        return ApiResult.success(rootNodes);
    }

    @GetMapping("/catalogs/sub-nodes")
    @ApiOperation(value = "查询子节点", notes = "查询子节点", response = ApiResult.class)
    public ApiResult<List<MathCatalogNodeVo>> listCatalogSubNodes(@RequestParam(value = "rootNodeId") UUID rootNodeId) {
        CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
        List<MathCatalogNodeVo> subNodes = mathSyncLearningTestService.listCatalogSubNodes(rootNodeId, currentUser.getStudentId());
        return ApiResult.success(subNodes);
    }


    @GetMapping("/electronic-textbook")
    @ApiOperation(value = "根据教材年级查询电子教材下载", notes = "根据教材年级查询电子教材下载", response = ApiResult.class)
    public ApiResult<String> getTextbook(HttpServletRequest request, QueryElectronicTextbookBo queryElectronicTextbookBo) {
        MathElecttronicTextbookVo result = eduKnowledgeHubService.getElectronicTextbook(queryElectronicTextbookBo);
        if(result == null){
            return ApiResult.success(null);
        }
        String url = aliOssUtils.generatePresignedUrl(result.getOssKey());
        return ApiResult.success(url);
    }
    @GetMapping("/section-detail")
    @ApiOperation(value = "查询小节知识点等信息详情", notes = "不返回章节名称信息", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "sectionId", value = "小节ID", required = true, dataType = "UUID"),
            @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, dataType = "Long"),
            @ApiImplicitParam(name = "sectionName", value = "小节名称", required = true, dataType = "String")
    })
    public ApiResult<MathSectionPointsVo> getSectionDetailInfo(HttpServletRequest request,
                                                               @RequestParam UUID sectionId,
                                                               @RequestParam(value = "studentId") Long studentId) {

        MathSectionPointsVo  sectionPointsVo = mathSyncLearningTestService.getSectionDetailInfo(sectionId,studentId);
        sectionPointsVo.setSectionName(request.getParameter("sectionName"));
        sectionPointsVo.setSectionId(sectionId);
        return ApiResult.success(sectionPointsVo);
    }

    @GetMapping("/creat/questions/{knowledgeId}")
    @ApiOperation(value = "生成练习题", notes = "生成练习题", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVoV2> createQuestions(HttpServletRequest request, @PathVariable UUID knowledgeId) {
        String questionsCount = request.getParameter("questionsCount");
        CreateQuestionByKnowledgeParamV2 param = CreateQuestionByKnowledgeParamV2.builder()
                .knowledgePointIds(java.util.Arrays.asList(knowledgeId))
                .questionCount(StringUtils.isAllBlank(questionsCount)? 2 : Integer.parseInt(questionsCount))
                .build();
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV2(param));
    }
    @GetMapping("/knowledgePoint/{knowledgePointId}")
    @ApiOperation(value = "查询知识点包含精讲详情", notes = "查询知识点包含精讲详情", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "knowledgePointId", value = "知识点ID", required = true, dataType = "UUID"),
            @ApiImplicitParam(name = "publisher", value = "教材版本", required = true, dataType = "String")
    })
    public ApiResult<MathKnowledgePointHandoutVo> getKnowledgePointVoById(HttpServletRequest request, @RequestParam UUID knowledgePointId) {
        String publisher = request.getParameter("publisher");
        CommonResponse.ERROR.assertNotNull(publisher, "参数 publisher 不能为空");
        MathKnowledgePointHandoutVo vo = mathSyncLearningTestService.getMathKnowledgePointVOById(knowledgePointId,publisher);
        CommonResponse.ERROR.assertNotNull(vo, "暂无章节信息数据");
        return ApiResult.success(vo);
    }

    @GetMapping("/error-question-pages")
    @ApiOperation(value = "根据知识点查询错题", notes = "根据知识点查询错题", response = ApiResult.class)
    public ApiResult<Page<PersonalExamQuestionVo>> page(HttpServletRequest request, @RequestParam Long studentId, @RequestParam UUID knowledgePointId,
                                                        Integer current, Integer size) {
        return ApiResult.success(personalExamService.getErrorQuestionPages(knowledgePointId, studentId, current, size));
    }

    @PostMapping("/knowledgePoint/study")
    @ApiOperation(value = "知识点加入已学习", notes = "视频播放完成加入知识点已学习", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "knowledgePointId", value = "知识点ID", required = true, dataType = "UUID"),
            @ApiImplicitParam(name = "studentId",value = "学生ID",required = true,dataType = "Long")
    })
    public ApiResult knowledgePointAddStudy(@RequestBody MathStudentStudyVideoParam studentKnowledgePoint) {
        CommonResponse.ERROR.assertNotNull(studentKnowledgePoint.getStudentId(), "参数 studentId 不能为空");
        CommonResponse.ERROR.assertNotNull(studentKnowledgePoint.getKnowledgePointId(), "参数 knowledgePointId 不能为空");
        mathStudentKnowledgePointService.addOrUpdatePptHtmlsCompleted(studentKnowledgePoint.getStudentId(),
                studentKnowledgePoint.getKnowledgePointId());
        return ApiResult.success();
    }
    @GetMapping("/knowledgePoint/questions")
    @ApiOperation(value = "根据知识点获取练习", notes = "根据知识点获取练习", response = ApiResult.class)
    @ApiImplicitParams(
            value = {
                    @ApiImplicitParam(name = "knowledgePointId", value = "知识点ID", required = true, dataType = "UUID"),
                    @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, dataType = "Long"),
                    @ApiImplicitParam(name = "publisher", value = "出版社", required = true, dataType = "String"),
            }
    )
    public ApiResult getQuestions(@RequestParam Long studentId, @RequestParam UUID knowledgePointId,PublisherEnum publisher) {
        return ApiResult.success(mathSyncLearningTestService.createQuestionsByKnowledgePoint(knowledgePointId,publisher));
    }

    @PostMapping("/study-video-length")
    @ApiOperation(value = "记录学习视频时长", notes = "视频播放记录学习视频时长", response = ApiResult.class)
    public ApiResult videoLengthStudy(@RequestBody MathStudentStudyVideoParam mathQueryParam) {
        CommonResponse.ERROR.assertNotNull(mathQueryParam.getStudentId(), "参数 studentId 不能为空");
        CommonResponse.ERROR.assertIsTrue(mathQueryParam.getKnowledgePointId() !=null || mathQueryParam.getSectionId() != null, "参数不能全部为空");
        mathStudentStudyVideoHistoryService.addStudyVideoHistory(mathQueryParam);
        return ApiResult.success();
    }
}
