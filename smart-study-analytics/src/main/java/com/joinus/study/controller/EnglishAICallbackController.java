package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.dto.EnglishExamDiagnoseRecordCallbackDTO;
import com.joinus.study.service.EnglishAICallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/9/1 11:25
 */
@RestController
@RequestMapping("/english/ai/callback")
@Api(tags = "英语-AI回调相关接口")
@Slf4j
public class EnglishAICallbackController {

    @Resource
    private EnglishAICallbackService englishAICallbackService;

    @PostMapping("/exam/diagnose/record")
    @ApiOperation(value = "试卷诊断-考情分析报告回调", notes = "试卷诊断-考情分析报告回调", response = ApiResult.class)
    public ApiResult<String> examDiagnoseRecordCallback(@Valid @RequestBody EnglishExamDiagnoseRecordCallbackDTO param) {
        log.info("试卷诊断-考情分析报告回调:{}", param);
        englishAICallbackService.examDiagnoseRecordCallback(param);
        return ApiResult.success();
    }

}
