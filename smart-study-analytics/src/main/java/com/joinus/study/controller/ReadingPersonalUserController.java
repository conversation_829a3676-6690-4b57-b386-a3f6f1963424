package com.joinus.study.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingPersonalUserPageParam;
import com.joinus.study.model.vo.ReadingPersonalUserVO;
import com.joinus.study.model.vo.ReadingUserDataOverviewVO;
import com.joinus.study.service.ReadingPersonalUserService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/7/11 17:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/reading-personal-user")
@Api(tags = "语文用户管理")
public class ReadingPersonalUserController {

    private  ReadingPersonalUserService readingPersonalUserService;
    @Operation(summary = "后台分页查询")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPersonalUserVO>> pages(@Validated @RequestBody ReadingPersonalUserPageParam pageParam) {
        return ApiResult.success(readingPersonalUserService.pages(pageParam));
    }
    //分页导出
    @Operation(summary = "分页导出")
    @PostMapping("/export")
    public void export(@Validated @RequestBody ReadingPersonalUserPageParam pageParam,HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("用户汇总统计", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 查询数据参数
        List<ReadingPersonalUserVO> allData = new ArrayList<>();
        Page<ReadingPersonalUserVO> data;
        int currentPage = 1;
        pageParam.setSize(1000);
        // 循环查询数据
        do {
            pageParam.setCurrent(currentPage);
            data =readingPersonalUserService.pages(pageParam);;
            allData.addAll(data.getRecords());
            currentPage++;
        } while (data.hasNext());
        EasyExcel.write(response.getOutputStream(), ReadingPersonalUserVO.class)
                .inMemory(Boolean.TRUE)
                .sheet("Sheet1")
                .doWrite(allData);
    }

    @Operation(summary = "数据总统计")
    @PostMapping("/dataOverview")
    public ApiResult<ReadingUserDataOverviewVO> dataOverview() {
        return ApiResult.success(readingPersonalUserService.dataOverview());
    }
}
