package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingPersonalCorrectionPageParam;
import com.joinus.study.model.vo.ReadingPersonalCorrectionVO;
import com.joinus.study.service.ReadingPersonalCorrectionService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/backend/reading-personal-correction")
@Api(tags = "管理后台-纠错列表")
public class ReadingPersonalCorrectionBackendController {

    private ReadingPersonalCorrectionService readingPersonalCorrectionService;

    @Operation(summary = "分页")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPersonalCorrectionVO>> pages(@Validated @RequestBody ReadingPersonalCorrectionPageParam pageParam) {
        return ApiResult.success(readingPersonalCorrectionService.pages(pageParam));
    }
}
