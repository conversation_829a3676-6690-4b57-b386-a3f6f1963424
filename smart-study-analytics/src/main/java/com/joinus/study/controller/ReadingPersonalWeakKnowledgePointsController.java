package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingStudentWeakKnowledgePointParam;
import com.joinus.study.model.vo.ReadingWeekKnowledgePointVo;
import com.joinus.study.service.ReadingPersonalPassagesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@AllArgsConstructor
@RequestMapping("/weak-knowledge-points")
@Api(tags = "语文阅读-ai爆破薄弱知识点")
public class ReadingPersonalWeakKnowledgePointsController {

    @Resource
    private ReadingPersonalPassagesService readingPersonalPassagesService;
    @GetMapping("/list")
    @ApiOperation(value = "薄弱知识点列表", notes = "薄弱知识点列表", response = ApiResult.class)
    public ApiResult<Page<ReadingWeekKnowledgePointVo>> weakKnowledgePointList(HttpServletRequest request, ReadingStudentWeakKnowledgePointParam pageParam) {
        try {
            Page<ReadingWeekKnowledgePointVo> page = readingPersonalPassagesService.weakKnowledgePointList(pageParam);
            return ApiResult.success(page);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
}
