package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.dto.ParentDTO;
import com.joinus.study.service.BasicBusinessService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Description: 学生关联接口
 * @Author: anpy
 * @date: 2025/3/12 10:41
 */
@RestController
@RequestMapping("/parent")
@Slf4j
public class ParentController {
    @Autowired
    private BasicBusinessService basicBusinessService;

    @GetMapping("/{parentId}")
    @ApiOperation(value = "根据家长ID查询家长信息", notes = "根据家长ID查询家长信息", response = ApiResult.class)
    public ApiResult<ParentDTO> getParentByParentId(@PathVariable("parentId") Long parentId) {
        return ApiResult.success(basicBusinessService.getParentByParentId(parentId));
    }

}
