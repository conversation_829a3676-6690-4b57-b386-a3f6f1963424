
package com.joinus.study.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.entity.ReadingUnits;
import com.joinus.study.model.enums.ReadingEntryTypeEnum;
import com.joinus.study.model.param.ReadingPersonalPassagesQueryParam;
import com.joinus.study.model.param.ReadingPersonalPassagesQuestionsParam;
import com.joinus.study.model.param.ReadingPersonalPrintParam;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
import com.joinus.study.model.vo.StudentReadingPersonalPassagesVo;
import com.joinus.study.service.ReadingPersonalPassagesService;
import com.joinus.study.service.ReadingQuestionRecommendationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/3/28 9:37
 */
@Slf4j
@RestController
@RequestMapping("/reading-personal")
@Api(value = "/reading-personal", tags = "阅读提分训练营")
public class ReadingPersonalController {
    @Resource
    private ReadingPersonalPassagesService readingPersonalPassagesService;
    @Resource
    private ReadingQuestionRecommendationService readingQuestionRecommendationService;

    @GetMapping("/get-units")
    @ApiOperation(value = "获取单元列表", notes = "阅读提分训练营-获取单元列表", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "textbook", value = "教材", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "grade", value = "年级", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "semester", value = "学期", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "parentId", value = "家长ID", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<List<ReadingUnits>> units(HttpServletRequest request, @RequestParam(required = false) String textbook,
                                               @RequestParam Long grade,
                                               @RequestParam Integer semester,
                                               @RequestParam(required = false) Long studentId,
                                               @RequestParam(required = false) Long parentId) {
        CurrentUser currentUser = JSONUtil.toBean(JSONUtil.toJsonStr(request.getAttribute("CURRENT_USER").toString()), CurrentUser.class);
        CommonResponse.ERROR.assertNotNull(currentUser, "家长id不能为空");
        try {
            List<ReadingUnits> units = readingPersonalPassagesService.getUnits(textbook, grade,semester,studentId,currentUser.getUserId());
            return ApiResult.success(units);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    /**
     * @Description 【废弃】获取文章及问题，使用题目推荐中接口
     * <AUTHOR>
     * @date 2025/5/13
     */
    @GetMapping("/get-passage")
    @ApiOperation(value = "获取生成练习文章", notes = "阅读提分训练营-获取练习文章", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id",required = true,paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "passageId", value = "文章id",paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "planId", value = "计划id",paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "unitId", value = "单元id",paramType = "query", dataType = "String")
    })
    public ApiResult<ReadingPersonalPassagesVo> personalPassages(HttpServletRequest request,
                                                                 ReadingStudentPassagesQueryParam param) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        try {
            if(param.getPassageId()==null && param.getUnitId()==null){
                return ApiResult.failed("参数错误");
            }
            ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.getAndSavePersonalPassage(param);
            return ApiResult.success(passagesVo);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
    @GetMapping("/passage/{id}")
    @ApiOperation(value = "根据练习id获取练习详情", notes = "阅读提分训练营-获取练习文章", response = ApiResult.class)
    public ApiResult<ReadingPersonalPassagesVo> personalPassagesById(HttpServletRequest request, @PathVariable Long id) {
        try {
            ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.getpersonalPassagesById(id);
            return ApiResult.success(passagesVo);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
    @GetMapping("/get-unit-passage")
    @ApiOperation(value = "获取单元下正在练习文章", notes = "阅读提分训练营-获取单元下正在练习文章", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id",paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "unitId", value = "单元id",paramType = "query", dataType = "String")
    })
    public ApiResult<ReadingPersonalPassagesVo> personalUnitPassages(HttpServletRequest request, ReadingPersonalPassagesQueryParam param) {
        try {
            if(param.getStudentId() == null || param.getUnitId() == null){
                return ApiResult.failed("参数错误");
            }
            ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.getStudentPersonalVoDoing(param);
            if(passagesVo != null){
                return ApiResult.success(passagesVo);
            }
            return ApiResult.success();
        } catch (Exception e){
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/passage-again/{id}")
    @ApiOperation(value = "再次练习-获取题目", notes = "阅读提分训练营-错题本再次练习", response = ApiResult.class)
    public ApiResult<ReadingPersonalPassagesVo> doPersonaldoAgain(HttpServletRequest request, @PathVariable Long id) {
        try {
            ReadingPersonalPassagesVo passagesVo = readingPersonalPassagesService.doPersonaldoAgain(id);
            if(passagesVo != null){
                return ApiResult.success(passagesVo);
            }
            return ApiResult.success();
        } catch (Exception e){
            return ApiResult.failed(e.getMessage());
        }
    }
    @PostMapping("/questions-pause")
    @ApiOperation(value = "练习暂停保存进度", notes = "阅读提分训练营-练习暂停保存进度", response = ApiResult.class)
    public ApiResult pause(@RequestBody ReadingPersonalPassagesQuestionsParam param) {
        readingPersonalPassagesService.pausePersonalPassagesQuestions(param);
        return ApiResult.success();
    }

    @PostMapping("/questions-submit")
    @ApiOperation(value = "提交练习答案", notes = "阅读提分训练营-提交练习答案", response = ApiResult.class)
    public ApiResult submit(@RequestBody @Valid ReadingPersonalPassagesQuestionsParam param) {
        return ApiResult.success(readingPersonalPassagesService.submitPersonalPassagesQuestions(param));
    }

    /**
     * @description: 【废弃】v1.1.2.1 拍照识别后改为提交页面
     * @author: lifengxu
     * @date: 2025/7/1 10:01
     */
    @PostMapping("/questions-wrongmark")
    @ApiOperation(value = "拍照识别阅读标注错题", notes = "阅读提分训练营-提交练习答案", response = ApiResult.class)
    public ApiResult annotationWrong(@RequestBody ReadingPersonalPassagesQuestionsParam param) {
        readingPersonalPassagesService.annotationWrongPersonalPassagesQuestions(param);
        return ApiResult.success();
    }

    @GetMapping("/passage-deprecate/{id}")
    @ApiOperation(value = "弃用练习", notes = "阅读提分训练营-弃用练习", response = ApiResult.class)
    @ApiImplicitParam(name = "id", value = "文章id", required = true, paramType = "path", dataType = "Long")
    public ApiResult deprecate(@PathVariable Long id) {
        readingPersonalPassagesService.deprecatePersonalPassagesQuestions(id);
        return ApiResult.success();
    }

    @GetMapping("/questions-delete")
    @ApiOperation(value = "删除练习", notes = "阅读提分训练营-删除练习", response = ApiResult.class)
    @ApiImplicitParam(name = "id", value = "文章id", required = true, paramType = "query", dataType = "Long")
    public ApiResult delete(@RequestParam Long id) {
        readingPersonalPassagesService.deletePersonalPassage(id);
        return ApiResult.success();
    }

    //获取练习列表
        @GetMapping("/passage-list")
    @ApiOperation(value = "训练记录列表", notes = "阅读提分训练营-训练记录列表", response = ApiResult.class)
    public ApiResult<Page<StudentReadingPersonalPassagesVo>> personalList(HttpServletRequest request, ReadingPersonalPassagesQueryParam param) {
        try {
            Page<StudentReadingPersonalPassagesVo> passagesVoPage = readingPersonalPassagesService.studentPersonalList(param);
            return ApiResult.success(passagesVoPage);
        } catch (Exception e){
            return ApiResult.failed(e.getMessage());
        }
    }

    //获取练习详情
    @GetMapping("/passage-detail")
    @ApiOperation(value = "训练记录详情", notes = "阅读提分训练营-训练记录详情", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "训练id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<ReadingPersonalPassagesVo> personalDetail(HttpServletRequest request, Long id) {
        try {
            return ApiResult.success(readingPersonalPassagesService.studentPersonalDetail(id));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping(value = "/passage-html")
    @ApiOperation(value = "生成html打印预览", notes = "阅读提分训练营-生成html", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "训练id",paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "passageId", value = "文章id",paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "studentId", value = "学生id",paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "unitId", value = "单元id",paramType = "query", dataType = "String")
    })
    public ApiResult<ReadingPersonalPrintParam> generatePassagesHtml(HttpServletRequest request, ReadingStudentPassagesQueryParam param) {
        try {
            if((param.getId() != null && param.getPassageId() != null) || (param.getUnitId() != null && param.getStudentId() != null)){
                Long planId = null;
                UUID setsId = null;
                UUID passageId =null;
                List<UUID> questionIds = null;
                if(param.getId() != null){
                    ReadingPersonalPassages personalPassages = readingPersonalPassagesService.getById(param.getId());
                    CommonResponse.ERROR.assertNotNull(personalPassages, "练习id无效");

                    planId = personalPassages.getPlanId();
                    setsId = personalPassages.getSetsId();
                    passageId = personalPassages.getPassageId();
                    questionIds = readingPersonalPassagesService.getQuestionIdsByPersonalPassagesId(param.getId());
                } else {
                    CommonResponse.ERROR.assertNotNull(param.getEntryType(), "答题入口类型不能为空");
                    ReadingPersonalPassagesVo passagesVo = null;
                    if (param.getEntryType() == ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode()) {
                        passagesVo = readingQuestionRecommendationService.getDirectionalBlastingQuestionsTwo(param);
                    } else if (param.getEntryType() == ReadingEntryTypeEnum.READING_TRAINING.getCode() ||
                            param.getEntryType() == ReadingEntryTypeEnum.EXERCISE_PLAN.getCode()) {
                        passagesVo = readingQuestionRecommendationService.getPracticeQuestions(param);
                    } else {
                        // 假期活动练习可能是4 也可能是5，所以只能放在else里
                        passagesVo = readingQuestionRecommendationService.getActivityPracticeQuestions(param);
                    }
                    planId = passagesVo.getPlanId();
                    setsId = passagesVo.getSetsId();
                    passageId = passagesVo.getPassageId();
                    questionIds = passagesVo.getQuestions().stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
                }
                String html = readingPersonalPassagesService.generatePassageHtml(passageId, questionIds, 0);
                ReadingPersonalPrintParam result = new ReadingPersonalPrintParam();
                result.setPassageId(passageId);
                result.setHtmlContent(html);
                result.setQuestionIds(questionIds);
                result.setEntryType(param.getEntryType());
                if (ObjectUtil.isNotEmpty(setsId)) {
                    result.setSetsId(setsId);
                }
                if (ObjectUtil.isNotEmpty(planId)) {
                    result.setPlanId(planId);
                }
                return ApiResult.success(result);
            }
            return ApiResult.failed("参数错误");
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @PostMapping("/passages-pdf")
    @ApiOperation(value = "生成pdf文件", notes = "阅读提分训练营-生成pdf文件", response = ApiResult.class)
    public ApiResult<Map<String,String>> generatePassagesPdf(HttpServletRequest request,@RequestBody ReadingPersonalPrintParam param) {
        try {
            CurrentUser currentUser = JSONUtil.toBean(request.getAttribute("CURRENT_USER").toString(),CurrentUser.class);
            CommonResponse.ERROR.assertNotNull(currentUser, "家长id不能为空");
            param.setParentId(currentUser.getUserId());
            ReadingPassages readingPassages = readingPersonalPassagesService.getReadingPassagesByid(param.getPassageId());
            if(readingPassages != null){
                Map<String,String> resultMap = readingPersonalPassagesService.generatePassagePdf(param);
                return ApiResult.success(resultMap);
            }
            return ApiResult.failed("文章id无效");
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @PostMapping("/passages-pdf-byte")
    public ResponseEntity<byte[]> generatePdf(@RequestBody ReadingPersonalPrintParam param) {
        try {
            byte[] pdfBytes = readingPersonalPassagesService.generatePassagePdfByte(param.getId());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=reading_report.pdf")
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(pdfBytes);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/identify-QR")
    @ApiOperation(value = "识别图片中二维码", notes = "识别图片中二维码返回二维码练习详情", response = ApiResult.class)
    @ApiImplicitParam(name = "objectKey", value = "阿里云OSS objectKey", required = true, paramType = "query", dataType = "String")
    public ApiResult<ReadingPersonalPassagesVo> identifyQRcode(@RequestParam String objectKey) {
        try {
            ReadingPersonalPassagesVo passagePdf = readingPersonalPassagesService.identifyQRcode(objectKey);
            return ApiResult.success(passagePdf);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/QR-data")
    @ApiOperation(value = "根据扫描的二维码获取文章详情", notes = "阅读提分训练营-获取练习文章", response = ApiResult.class)
    @ApiImplicitParam(name = "qrStr", value = "扫描二维码内容", required = true, paramType = "query", dataType = "String")
    public ApiResult<ReadingPersonalPassagesVo> personalPassagesByPrintId(HttpServletRequest request, @RequestParam String qrStr, Long studentId){
        try {
            if (ObjectUtil.isEmpty(studentId)) {
                studentId = Long.parseLong(request.getHeader("studentid"));
            }
            return ApiResult.success(readingPersonalPassagesService.getQrDataAnalysis(qrStr, studentId));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/send-email")
    @ApiOperation(value = "分享邮箱", notes = "发送pdf到邮件", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "recipient", value = "收件人邮箱", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "personalPrintId", value = "练习id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult sendEmail(@RequestParam String recipient, @RequestParam Long personalPrintId) {
        try {
            readingPersonalPassagesService.sendPassagePdfEmail(recipient, personalPrintId);
            return ApiResult.success();
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/remaining-print-times")
    @ApiOperation(value = "查询剩余打印次数", notes = "查询剩余打印次数", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id",paramType = "query", dataType = "Long")
    })
    public ApiResult<Integer> remainingPrintTimes(Long studentId) {
        try {
            Integer remainingPrintTimes = readingPersonalPassagesService.getRemainingPrintTimes(studentId);
            return ApiResult.success(remainingPrintTimes);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
}

