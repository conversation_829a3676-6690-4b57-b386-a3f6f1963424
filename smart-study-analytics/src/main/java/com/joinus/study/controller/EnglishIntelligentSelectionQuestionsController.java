package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.EnglishIntelligentSelectionQuestionQueryParam;
import com.joinus.study.model.vo.EnglishPersonalExerciseVO;
import com.joinus.study.service.EnglishIntelligentSelectionQuestionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@AllArgsConstructor
@RequestMapping("/english/intelligent-selection-questions")
@Api(tags = "英语-智能选题相关接口")
public class EnglishIntelligentSelectionQuestionsController {

    private EnglishIntelligentSelectionQuestionsService englishIntelligentSelectionQuestionsService;


    @PostMapping("/directional-blasting")
    @ApiOperation(value = "定向爆破练习题", notes = "薄弱知识点定向爆破练习题（单题）", response = ApiResult.class)
    public ApiResult<EnglishPersonalExerciseVO> directionalBlastingExerciseQuestion(HttpServletRequest request, @RequestBody @Valid EnglishIntelligentSelectionQuestionQueryParam queryParam) {
        try {
            return ApiResult.success(englishIntelligentSelectionQuestionsService.directionalBlastingExerciseQuestion(queryParam));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

}
