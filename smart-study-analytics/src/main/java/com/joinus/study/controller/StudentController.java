package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.dto.StudentBasicInfoDTO;
import com.joinus.study.model.param.AddStudentForPadParam;
import com.joinus.study.model.param.AddStudentParam;
import com.joinus.study.service.BasicBusinessService;
import com.joinus.study.util.CurrentUserHolder;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Description: 学生关联接口
 * @Author: anpy
 * @date: 2025/3/12 10:41
 */
@RestController
@RequestMapping("/student")
@Slf4j
public class StudentController {
    @Autowired
    private BasicBusinessService basicBusinessService;

    @GetMapping("/by-parent-phone/{parentPhone}")
    @ApiOperation(value = "根据家长手机号查询学生信息", notes = "根据家长手机号查询关联的学生信息列表", response = ApiResult.class)
    public ApiResult<List<StudentBasicInfoDTO>> getStudentsByParentPhone(@PathVariable("parentPhone") String parentPhone) {
        return ApiResult.success(basicBusinessService.getStudentsByParentPhone(parentPhone));
    }

    @PostMapping
    @ApiOperation(value = "新增学生信息", notes = "新增学生", response = ApiResult.class)
    public ApiResult<Long> addStudentBasicData( @RequestBody AddStudentParam addStudentParam) {
        return ApiResult.success(basicBusinessService.addStudentBasicData(addStudentParam));
    }

    @PostMapping("/pad")
    @ApiOperation(value = "新增学生信息pad", notes = "新增学生pad", response = ApiResult.class)
    public ApiResult<Long> addStudentBasicDataForPad(@Valid @RequestBody AddStudentForPadParam param) {
        return ApiResult.success(basicBusinessService.addStudentBasicDataForPad(param));
    }


}
