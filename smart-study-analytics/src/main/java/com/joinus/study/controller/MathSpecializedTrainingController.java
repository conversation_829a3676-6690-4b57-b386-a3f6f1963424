package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.PersonalExam;
import com.joinus.study.model.enums.BookVolumeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.enums.QuestionTypeEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.ExamAnalysisReportService;
import com.joinus.study.service.MathSyncLearningTestService;
import com.joinus.study.service.MistakeBookService;
import com.joinus.study.service.SpecializedTrainingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 专项训练
 * @Author: anpy
 * @date: 2025/4/28 17:33
 */
@RestController
@RequestMapping("/specialized-training")
@Api(tags = "数学专项训练")
public class MathSpecializedTrainingController {

    @Resource
    private SpecializedTrainingService specializedTrainingService;
    @Resource
    private MistakeBookService mistakeBookService;
    @Resource
    private ExamAnalysisReportService examAnalysisReportService;
    @Resource
    private MathSyncLearningTestService mathSyncLearningTestService;
    @PostMapping("/questions")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVo> createSpecializedTrainingQuestions(@RequestBody CreateQuestionByKnowledgeParam param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionType(param));
    }

    @PostMapping("/questions/v2")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVoV2> createSpecializedTrainingQuestionsV2(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV2(param));
    }

    @RequiresMathMembership
    @PostMapping("/questions/v3")
    @ApiOperation(value = "专项训练-根据知识点和题型生成题目_V3", notes = "专项训练-根据知识点和题型生成题目", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> createSpecializedTrainingQuestionsV3(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createQuestionByKnowledgeAndQuestionTypeV3(param));
    }
    @PostMapping("/holiday-training/questions")
    @ApiOperation(value = "暑假学习训练-单元/章节-生成题目", notes = "暑假学习训练-单元/章节-生成题目", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> createHolidayTrainingSection(@RequestBody CreateQuestionByKnowledgeParamV2 param) {
        return ApiResult.success(specializedTrainingService.createHolidayTrainingQuestion(param));
    }
    @PostMapping("/holiday-training/textbook/questions")
    @ApiOperation(value = "暑假学习训练-综合训练-生成题目", notes = "暑假学习训练-综合训练-生成题目", response = ApiResult.class)
    public ApiResult<Map<String, Object>> createHolidayTrainingTextbook(@RequestBody CreateQuestionByKnowledgeParamV2 param) {

        Map<String, Object> result = new HashMap<>();
        List<ExamBookQuestionDetailDto> resultList = specializedTrainingService.createHolidayTrainingBookQuestion(param);
        List<MathQuestionTypeDistributionVo.QuestionTypeDistribution> questionTypeDistributions = new ArrayList<>();
        List<ExamBookQuestionDetailDto.QuestionInfoDto> questionList = new ArrayList<>();
        Set<UUID> knowledgePointIds = new HashSet<>();
        for (ExamBookQuestionDetailDto examBookQuestionDetailDto : resultList){
            MathQuestionTypeDistributionVo.QuestionTypeDistribution questionTypeDistribution
                    = new MathQuestionTypeDistributionVo.QuestionTypeDistribution();
            questionTypeDistribution.setQuestionType(examBookQuestionDetailDto.getQuestionType());
            questionTypeDistribution.setQuestionCount(examBookQuestionDetailDto.getQuestionInfoDtos().size());
            questionTypeDistribution.setQuestionType(QuestionTypeEnum.getEnumByDesc(examBookQuestionDetailDto.getQuestionType()).name());
            questionTypeDistribution.setQuestionTypeName(examBookQuestionDetailDto.getQuestionType());
            questionTypeDistributions.add(questionTypeDistribution);
            questionList.addAll(examBookQuestionDetailDto.getQuestionInfoDtos());
            examBookQuestionDetailDto.getQuestionInfoDtos().forEach(questionInfoDto -> {
                knowledgePointIds.addAll(questionInfoDto.getKnowledgePoints().stream().map(ExamBookQuestionDetailDto.KnowledgePointsDTO::getKnowledgePointId).collect(Collectors.toList()));
            });
        }
        result.put("resultList", resultList);
        result.put("questionList", questionList);
        result.put("questionTypeDistributions", questionTypeDistributions);
        result.put("totalQuestions", questionList.size());
        result.put("totalDuration", questionList.size()*6);
        result.put("totalQuestionTypes", questionTypeDistributions.size());
        result.put("totalKnowledgePoints", knowledgePointIds.size());
        return ApiResult.success(result);
    }
    @GetMapping("/questions/detail/{examId}")
    @ApiOperation(value = "专项训练-查询试卷详情", notes = "专项训练-查询试卷详情", response = ApiResult.class)
    public ApiResult<ExamChapterQuestionDetailDto> selectSpecializedTrainingExamDetail(@PathVariable UUID examId,
                                                                                       @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        return ApiResult.success(specializedTrainingService.selectSpecializedTrainingExamDetail(examId, personalExamId));
    }
    @GetMapping("/questions/textbook/detail/{examId}")
    @ApiOperation(value = "专项训练-查询综合试卷详情", notes = "专项训练-查询综合试卷详情", response = ApiResult.class)
    public ApiResult<List<ExamBookQuestionDetailDto>> selectSpecializedTextBookTrainingExamDetail(@PathVariable UUID examId,
                                                                                                  @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        return ApiResult.success(specializedTrainingService.selectSpecializedTextBookTrainingExamDetail(examId, personalExamId));
    }
    @GetMapping("/questions/online/detail/{examId}")
    @ApiOperation(value = "在线答题-查询试卷详情", notes = "在线答题-查询试卷详情", response = ApiResult.class)
    public ApiResult selectOnlineSpecializedTrainingExamDetail(@PathVariable UUID examId,
                                                                @RequestParam(value = "personalExamId", required = false) Long personalExamId) {
        return ApiResult.success(specializedTrainingService.selectOnlineSpecializedTrainingExamDetail(personalExamId, examId));
    }
    @GetMapping("/questions/analyze-result")
    @ApiOperation(value = "专项训练-根据题目解析结果", response = ApiResult.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", value = "题目ID", required = true, dataType = "UUID", paramType = "path"),
            @ApiImplicitParam(name = "publisher", value = "出版社", required = false, dataType = "String", paramType = "query")
    })
    public ApiResult<QuestionAnalyzeResultDto> questionAnalyzeResult(HttpServletRequest request,@RequestParam UUID questionId) {
        String publisher = request.getParameter("publisher");
        if(publisher == null){
            return ApiResult.success(specializedTrainingService.questionAnalyzeResult(questionId,null));
        }
        return ApiResult.success(specializedTrainingService.questionAnalyzeResult(questionId,PublisherEnum.valueOf(publisher)));
    }

    @GetMapping("/exam/exist")
    @ApiOperation(value = "专项训练-判断试卷是否存在", response = ApiResult.class)
    public CheckExamExsitenceVo examExist(@RequestParam String qrStr) {
        return specializedTrainingService.examExist(qrStr);
    }

    @GetMapping("/exam/exist/{personalExamId}")
    @ApiOperation(value = "专项训练-通过personalExamId判断试卷是否存在", response = ApiResult.class)
    public CheckExamExsitenceVo examExist(@PathVariable Long personalExamId) {
        return specializedTrainingService.examExistByPersonalExamId(personalExamId);
    }

    /**
     * 专项练习生成试卷
     * @param param
     * @return
     */
    @PostMapping("/exam/create")
    @ApiOperation(value = "试卷入库", notes = "试卷入库", response = ApiResult.class)
    public ApiResult<String> saveExam(@Valid @RequestBody SpecializedTrainingCreateExamParam param) {
        try {
            String examId = specializedTrainingService.saveExam(param);
            return ApiResult.success(examId);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    /**
     * 专项练习更新试卷
     * @param param
     * @return
     */
    @PostMapping("/exam/update")
    @ApiOperation(value = "更新试卷", notes = "更新试卷", response = ApiResult.class)
    public SpecializedTrainingUpdateExamDto updateExam(@RequestBody SpecializedTrainingUpdateExamParam param) {
        CommonResponse.ERROR.assertNotNull(param.getExamSource(), "examSource不能为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "studentId不能为空");
        CommonResponse.ERROR.assertNotNull(param.getGrade(), "年级不能为空");
        return specializedTrainingService.updateExam(param);
    }

    /**
     * 在线答题完成练习：生成试卷-并生成考情分析
     * @param param
     * @return
     */
    @PostMapping("/online/exam/create/analysis-report")
    @ApiOperation(value = "在线答题完成练习：生成试卷-并生成考情分析", notes = "在线答题完成练习：生成试卷-并生成考情分析", response = ApiResult.class)
    public ApiResult<Long> onlineCreateExam(@Valid @RequestBody SpecializedTrainingCreateExamParam param) {
        try {
            PersonalExam personalExam = specializedTrainingService.onlineCreateExam(param);
            CreateExamAnalysisReportParam analysisReportParam = CreateExamAnalysisReportParam.builder()
                    .examId(personalExam.getExamId())
                    .studentId(param.getStudentId())
                    .parentId(param.getParentId())
                    .personalExamId(personalExam.getId())
                    .publisher(param.getPublisher())
                    .grade(param.getGrade())
                    .bookVolume(BookVolumeEnum.fromVolumeNum(param.getSemester()))
                    .examDataList(param.getExamDataList())
                    .build();
            Long examAnalysisReportHasExam = examAnalysisReportService.createExamAnalysisReportHasExam(analysisReportParam);
            return ApiResult.success(examAnalysisReportHasExam);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/exam/textbook/catalogue")
    @ApiOperation(value = "教材列表", notes = "教材列表", response = ApiResult.class)
    public ApiResult<TextbookCatalogLabelTree> textbookCatalogue(Long analyzeReportId,String examId) {
        return ApiResult.success(specializedTrainingService.textbookCatalogue(analyzeReportId,UUID.fromString(examId)));
    }

    @PostMapping("/exam/scope/update")
    @ApiOperation(value = "更新考察范围", notes = "更新考察范围", response = ApiResult.class)
    public ApiResult<String> updateExamScope(@RequestBody ExamScopeParam param) {
        specializedTrainingService.updateExamScope(param);
        return ApiResult.success("更新成功");
    }

    @GetMapping("/exam/scope")
    @ApiOperation(value = "获取考察范围", notes = "获取考察范围", response = ApiResult.class)
    public ApiResult<TextbookCatalogLabelTree> getExamScope(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getExamScope(analyzeReportId));
    }

    @GetMapping("/exam/scope/name")
    @ApiOperation(value = "获取已选择的考察范围", notes = "获取已选择的考察范围", response = ApiResult.class)
    public ApiResult<List<String>> getManualExamScopeName(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getManualExamScopeName(analyzeReportId));
    }

    @GetMapping("/exam/scope/knowledge-point")
    @ApiOperation(value = "获取考察知识点", notes = "获取考察知识点", response = ApiResult.class)
    public ApiResult<KnowledgePointBySelectVo> getKnowledgePointBySection(Long analyzeReportId) {
        CommonResponse.ERROR.assertNotNull(analyzeReportId, "analyzeReportId不能为空");
        return ApiResult.success(specializedTrainingService.getKnowledgePointBySection(analyzeReportId));
    }

    //查询题目详情
    @GetMapping("/question/{questionId}")
    @ApiOperation(value = "专项训练-查询题目详情", notes = "专项训练-查询题目详情", response = ApiResult.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", value = "题目ID", required = true, dataType = "UUID", paramType = "path"),
            @ApiImplicitParam(name = "publisher", value = "出版社", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "studentId", value = "学生id", required = false, dataType = "Long", paramType = "query")
    })
    public ApiResult<SpecializedTrainingQuestionVo> questionDetails(HttpServletRequest request, @PathVariable  UUID questionId) {
        String publisher = request.getParameter("publisher");
        if(publisher == null){
            SpecializedTrainingQuestionVo questionDetail = specializedTrainingService.getMathQuestionDetailById(questionId,null);
            return ApiResult.success(questionDetail);
        }
        SpecializedTrainingQuestionVo questionDetail = specializedTrainingService.getMathQuestionDetailById(questionId,PublisherEnum.valueOf(publisher));
        if (questionDetail !=null && StringUtils.isNumeric(request.getParameter("studentId"))){
            questionDetail.setIsAddMistakesBook(mistakeBookService.getIsAddMistakesBook(Long.valueOf(request.getParameter("studentId")), questionId, null, null));
        }
        return ApiResult.success(questionDetail);
    }

    @PostMapping("/mistake-set/questions")
    @ApiOperation(value = "错题组卷根据试题id获取试卷", notes = "错题组卷根据试题id获取试卷", response = ApiResult.class)
    public ApiResult<SpecializedTrainingNewResultVoV2> getMistakeSetQuestions(@RequestBody MistakeSetQuestionsParam param) {
        return ApiResult.success(mathSyncLearningTestService.createMistakeSetQuestions(param));
    }
}
