package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.EnglishPersonalExerciseUpdateParam;
import com.joinus.study.model.vo.EnglishPersonalExerciseReportVO;
import com.joinus.study.service.EnglishPersonalExerciseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@AllArgsConstructor
@RequestMapping("/english/personal-exercise")
@Api(tags = "英语-练习做题相关接口")
public class EnglishPersonalExerciseController {

    private EnglishPersonalExerciseService englishPersonalExerciseService;

    @PostMapping("/question-answer-submit")
    @ApiOperation(value = "确认交卷", notes = "定向爆破-提交练习答案", response = ApiResult.class)
    public ApiResult questionAnswerSubmit(@RequestBody @Valid EnglishPersonalExerciseUpdateParam updateParam) {
        return ApiResult.success(englishPersonalExerciseService.questionAnswerSubmit(updateParam));
    }

    @GetMapping("/report-detail")
    @ApiOperation(value = "练习报告", notes = "AI定向爆破-练习报告详情", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "exerciseReportId", value = "练习报告id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<EnglishPersonalExerciseReportVO> reportDetail(HttpServletRequest request, Long exerciseReportId) {
        try {
            return ApiResult.success(englishPersonalExerciseService.reportDetail(exerciseReportId));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }
}
