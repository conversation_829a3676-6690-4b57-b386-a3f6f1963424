package com.joinus.study.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.EmptyResult;
import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.model.dto.ExamAnalysisReportExcelDTO;
import com.joinus.study.model.dto.ExamQuestionInfoDto;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.QuestionTypeEnum;
import com.joinus.study.model.param.CreateExamAnalysisReportParam;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.ClassStatisticsService;
import com.joinus.study.service.ExamAnalysisReportService;
import com.joinus.study.service.QuestionKnowledgePointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.joinus.study.model.enums.PersonalExamQuestionResultEnum;
import com.joinus.study.model.vo.QuestionVo;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/exam-analysis-report")
@Api(tags = "考情分析报告")
@Slf4j
public class MathExamAnalysisReportController {

    @Resource
    private ExamAnalysisReportService examAnalysisReportService;
    @Resource
    private ClassStatisticsService classStatisticsService;

    @RequiresMathMembership
    @PostMapping
    @ApiOperation(value = "生成考情分析报告", notes = "生成考情分析报告", response = ApiResult.class)
    public ApiResult<EmptyResult> createExamAnalysisReport(HttpServletRequest request, @RequestBody @Valid CreateExamAnalysisReportParam param) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        param.setParentId(parentId);
        CommonResponse.ERROR.assertCollNotNull(param.getExamDataList(), "试卷数据不能为空");
        CommonResponse.ERROR.assertNotNull(param.getGrade(), "年级不能为空");
        examAnalysisReportService.createExamAnalysisReport(param);
        return ApiResult.success();
    }

    /**
     * 标错题生成考情分析报告
     * @param request
     * @param param
     * @return
     */
    @PostMapping("/create/has-exam")
    @ApiOperation(value = "生成考情分析报告(已有试卷)", notes = "生成考情分析报告", response = ApiResult.class)
    public ApiResult<Long> createExamAnalysisReportHasExam(HttpServletRequest request,@RequestBody @Valid CreateExamAnalysisReportParam param) {
        CommonResponse.ERROR.assertCollNotNull(param.getExamDataList(), "试卷数据不能为空");
        Long id = examAnalysisReportService.createExamAnalysisReportHasExam(param);
        return ApiResult.success(id);
    }

    @PostMapping("/exist-exam/check/reanalyze")
    @ApiOperation(value = "已有试卷-校验是否需要重新分析", notes = "校验是否需要重新分析", response = ApiResult.class)
    public ApiResult<Boolean> checkExistExamShouldReanalyze(HttpServletRequest request,@RequestBody @Valid CreateExamAnalysisReportParam param) {
        Boolean shouldReanalyze = examAnalysisReportService.checkExistExamShouldReanalyze(param);
        return ApiResult.success(shouldReanalyze);
    }


    @PostMapping("/exam-question/rebuild-publisher")
    @ApiOperation(value = "试卷的题目知识点表根据题库补全教材字段", notes = "试卷的题目知识点表根据题库补全教材字段", response = ApiResult.class)
    public ApiResult<Boolean> rebuildExamQuestionPublisher(HttpServletRequest request) {
        examAnalysisReportService.rebuildExamQuestionPublisher();
        return ApiResult.success();
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "查看考情分析报告", notes = "查看考情分析报告", response = ApiResult.class)
    public ApiResult<ExamAnalysisReportVo> getExamAnalysisReport(@PathVariable Long id) {
        ExamAnalysisReportVo reportVo = examAnalysisReportService.getExamAnalysisReport(id);
        return ApiResult.success(reportVo);
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "统计", notes = "统计", response = ApiResult.class)
    @ResponseBody
    public ApiResult<EmptyResult> statistics( @RequestParam(required = false) Long classId,
                                              @RequestParam(required = false) Long gradeId,
                                              @RequestParam UUID examId) {
        if (classId == null && gradeId == null) {
            return ApiResult.failed("班级ID和年级ID不能同时为空");
        }
        if (examId == null) {
            return ApiResult.failed("试卷ID不能为空");
        }
        classStatisticsService.updateClassExamStatistics(classId, gradeId, examId);
        return ApiResult.success();
    }
    @GetMapping("/exam/{personalExamId}/{knowledgePointId}")
    @ApiOperation(value = "查看试卷薄弱点对应题目", notes = "查看试卷薄弱点对应题目", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "personalExamId", value = "学生试卷id", required = true, dataType = "Long"),
            @ApiImplicitParam(name = "knowledgePointId", value = "知识点id", required = true, dataType = "String")
    })
    public ApiResult<List<ExamQuestionFileVo>> getExamknowledgePointQuestions(@PathVariable Long personalExamId,@PathVariable String knowledgePointId) {
        List<ExamQuestionFileVo> list = examAnalysisReportService.getExamknowledgePointQuestions(personalExamId,knowledgePointId);
        return ApiResult.success(list);
    }

    @GetMapping("/pdf-info/{id}/{publisher}")
    @ApiOperation(value = "考试分析报告pdf包含的数据", notes = "考试分析报告pdf包含的数据", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "报告id", required = true, dataType = "Long"),
            @ApiImplicitParam(name = "publisher", value = "教材版本", required = true, dataType = "String")
    })
    public ApiResult<ExamAnalysisReportPdfVo> getExamAnalysisReportPdfInfo(@PathVariable("id") Long id, @PathVariable String publisher) {
        ExamAnalysisReportPdfVo reportVo = examAnalysisReportService.getExamAnalysisReportPdfInfo(id,publisher);
        return ApiResult.success(reportVo);
    }

    @GetMapping("/pdf-info/{id}/v2")
    @ApiOperation(value = "考试分析报告pdf包含的数据", notes = "考试分析报告pdf包含的数据", response = ApiResult.class)
    @ApiImplicitParam(name = "id", value = "报告id", required = true, dataType = "Long")
    public ApiResult<ExamAnalysisReportPdfVo2> getExamAnalysisReportPdfInfoV2(@PathVariable("id") Long id) {
        ExamAnalysisReportPdfVo2 reportVo = examAnalysisReportService.getExamAnalysisReportPdfInfoV2(id);
        return ApiResult.success(reportVo);
    }

    @PostMapping("/import/excel")
    @ApiOperation(value = "导入Excel文件生成考情分析报告", notes = "通过Excel文件批量导入考情分析报告数据，Excel每行包含学生ID、姓名和错误题目", response = ApiResult.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel文件，包含学生ID、姓名和错误题目", required = true, dataType = "MultipartFile"),
            @ApiImplicitParam(name = "examId", value = "试卷ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "ossEnum", value = "OSS类型", required = true, dataType = "String")
    })
    public ApiResult<List<Long>> importExamAnalysisReportFromExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam UUID examId) {

        try {
            // 1. 验证文件
            if (file.isEmpty()) {
                return ApiResult.failed("上传的文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
                return ApiResult.failed("请上传有效的Excel文件(.xls 或 .xlsx)");
            }

            // 2. 解析Excel文件
            List<ExamAnalysisReportExcelDTO> studentDataList = EasyExcel.read(file.getInputStream())
                    .head(ExamAnalysisReportExcelDTO.class)
                    .sheet()
                    .doReadSync();

            if (studentDataList == null || studentDataList.isEmpty()) {
                return ApiResult.failed("Excel文件中没有数据");
            }

            // 3. 批量处理学生数据并生成报告
            List<Long> reportIds = new ArrayList<>();

            List<ExamQuestionInfoDto> questions = examAnalysisReportService.listExamQuestions(examId);

            if (CollUtil.isEmpty(questions)) {
                return ApiResult.failed("该试卷没有题目数据");
            }
            for (ExamAnalysisReportExcelDTO studentData : studentDataList) {
                try {
                    // 3.1 准备参数
                    CreateExamAnalysisReportParam param = new CreateExamAnalysisReportParam();
                    param.setExamId(examId);
                    param.setStudentId(studentData.getStudentId());

                    // 3.2 获取试卷题目列表

                    if (questions == null || questions.isEmpty()) {
                        log.warn("试卷[{}]没有题目，跳过学生[{}]的处理", examId, studentData.getStudentId());
                        continue;
                    }

                    // 3.3 处理错误题目
                    List<Integer> mistakeNumbers = studentData.getMistakeQuestionNumbers();
                    Set<Integer> mistakeSet = new HashSet<>(mistakeNumbers);

                    // 3.4 构建题目数据
                    List<CreateExamAnalysisReportParam.QuestionData> questionDataList = new ArrayList<>();
                    for (ExamQuestionInfoDto question : questions) {
                        boolean isMistake = mistakeSet.contains(question.getSortNo());

                        CreateExamAnalysisReportParam.QuestionData questionData = CreateExamAnalysisReportParam.QuestionData.builder()
                                .questionId(question.getQuestionId())
                                .sortNo(question.getSortNo())
                                .questionType(QuestionTypeEnum.getEnumByDesc(question.getQuestionType()))
                                .result(isMistake ?
                                        PersonalExamQuestionResultEnum.mistake :
                                        PersonalExamQuestionResultEnum.correct)
                                .build();

                        questionDataList.add(questionData);
                    }

                    param.setExamDataList(questionDataList);

                    // 3.5 生成报告
                    Long reportId = examAnalysisReportService.createExamAnalysisReportHasExam(param);
                    reportIds.add(reportId);

                    log.info("成功为学生[{}]生成考情分析报告，报告ID: {}",
                            studentData.getStudentName(), reportId);

                } catch (Exception e) {
                    log.error("处理学生[{}]数据时发生错误: {}",
                            studentData.getStudentId(), e.getMessage(), e);
                    // 继续处理下一个学生
                }
            }

            if (reportIds.isEmpty()) {
                return ApiResult.failed("未能成功生成任何考情分析报告，请检查数据");
            }

            return ApiResult.success(reportIds);

        } catch (IllegalArgumentException e) {
            return ApiResult.failed("参数错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入Excel文件失败", e);
            return ApiResult.failed("导入Excel文件失败：" + e.getMessage());
        }
    }

    @PostMapping("/math/exam/knowledge-point/reanalyze")
    @ApiOperation(value = "已存在的母卷针对不同的教材版本重新分析知识点", notes = "已存在的母卷针对不同的教材版本重新分析知识点", response = ApiResult.class)
    public ApiResult<Boolean> reanalyzeExamKnowledgePoint(HttpServletRequest request,@RequestBody @Valid CreateExamAnalysisReportParam param) {
        CommonResponse.ERROR.assertNotNull(param.getGrade(), "年级不能为空");
        examAnalysisReportService.reanalyzeExamKnowledgePoint(param);
        return ApiResult.success();
    }


}
