package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingErrorBookPageParam;
import com.joinus.study.model.vo.ReadingErrorBookVO;
import com.joinus.study.model.vo.ReadingPeriodicReportPosterVO;
import com.joinus.study.model.vo.ReadingReportPosterVO;
import com.joinus.study.service.ReadingPersonalPassagesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@AllArgsConstructor
@RequestMapping("/reading-personal-passages")
@Api(tags = "阅读记录")
public class ReadingPersonalPassagesController {

    private ReadingPersonalPassagesService readingPersonalPassagesService;

    @Operation(summary = "错题本-分页")
    @PostMapping("/error-book/pages")
    public ApiResult<Page<ReadingErrorBookVO>> pagesOfErrorBook(@Validated @RequestBody ReadingErrorBookPageParam pageParam) {
        return ApiResult.success(readingPersonalPassagesService.pagesOfErrorBook(pageParam));
    }

    @GetMapping("/getReportPosterData")
    @ApiOperation(value = "获取训练报告分享海报数据", notes = "获取训练报告分享海报数据", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<ReadingReportPosterVO> getReportPosterData(Long studentId) {
        return ApiResult.success(readingPersonalPassagesService.getReportPosterData(studentId));
    }

    @GetMapping("/getPeriodicReportPosterData")
    @ApiOperation(value = "获取周/月报告分享海报数据", notes = "获取周/月报告分享海报数据", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "reportType", value = "报告类型 1周报 2月报", required = true, paramType = "query", dataType = "Integer")
    })
    public ApiResult<ReadingPeriodicReportPosterVO> getPeriodicReportPosterData(Long studentId, Integer reportType) {
        return ApiResult.success(readingPersonalPassagesService.getPeriodicReportPosterData(studentId, reportType));
    }
}
