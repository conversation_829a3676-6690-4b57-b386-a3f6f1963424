package com.joinus.study.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.dto.StudentBasicInfoDTO;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.entity.MathInvitation;
import com.joinus.study.model.entity.SchoolConfig;
import com.joinus.study.model.enums.GradeSemesterEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.result.MathH5TokenResult;
import com.joinus.study.model.vo.ActivityChapterVo;
import com.joinus.study.model.vo.MathActivityStudentVo;
import com.joinus.study.service.*;
import com.joinus.study.util.CurrentUserHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

@RestController
@RequestMapping("/math/activity/h5")
@Api(tags = "数学-暑期训练营推广h5")
@Slf4j
public class MathActivityH5Controller {
    @Resource
    private MathActivityService mathActivityService;
    @Resource
    private MathActivityStudentService mathActivityStudentService;
    @Resource
    private BasicBusinessService basicBusinessService;
    @Resource
    private QylService qylService;
    @Resource
    private SchoolConfigService schoolConfigService;
    @Resource
    private MathInvitationService mathInvitationService;
    @Autowired
    private MathPageViewTrackingService mathPageViewTrackingService;

    @Value("${math.activity.learning.planners:[{\"name\":\"李老师\",\"url\":\"https://img.967111.com/test/basic/learning_planer.png\"}]}")
    private String learningPlanners;

    @GetMapping("/detail")
    @ApiOperation(value = "获取活动信息", notes = "活动名称金额时间等信息", response = ApiResult.class)
    public ApiResult<MathActivity> activityDetail() {
        return ApiResult.success(mathActivityService.currentActivity(null));
    }
    @Operation(summary = "根据年级查询所有章节")
    @Parameters({
            @Parameter(name = "grade", description = "年级", required = true, schema = @Schema(implementation = Integer.class), example = "7")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/chapters")
    public ApiResult<List<ActivityChapterVo>> listChapters(@RequestParam("grade") Integer grade) {
        List<ActivityChapterVo> results = mathActivityService.listChapters(grade);
        return ApiResult.success(results);
    }
    @GetMapping("/{activityId}/{studentId}")
    @ApiOperation(value = "学生参与活动报名信息", notes = "学生参与活动报名信息", response = ApiResult.class,position = 1)
    public ApiResult<MathActivityStudentVo> mathActivityStudentDto(@PathVariable Long activityId, @PathVariable Long studentId) {
        MathActivityStudent mathActivityStudent = new MathActivityStudent();
        mathActivityStudent.setStudentId(studentId);
        mathActivityStudent.setActivityId(activityId);
        List<MathActivityStudentVo> studentVo = mathActivityStudentService.getMathActivityStudentVo(mathActivityStudent);
        if(studentVo.size() > 0){
            return ApiResult.success(studentVo.get(0));
        }
        return ApiResult.success();
    }
    @PostMapping("/join")
    @ApiOperation(value = "活动立即报名接口", notes = "选择活动内容后立即报名", response = ApiResult.class,position = 2)
    public ApiResult<MathActivityStudentVo> joinActivity(@RequestBody MathActivityJoinusParam mathActivityJoinusParam) {
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getActivityId(), "活动id不可为空");
        CommonResponse.ERROR.assertIsTrue(mathActivityJoinusParam.getStudentId() != null
                || mathActivityJoinusParam.getStudentName() != null, "学生选择不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getPublisher(), "教材不可为空");
        CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getGrade(), "年级不可为空");
        CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
        if(currentUser !=null){
            mathActivityJoinusParam.setParentId(currentUser.getUserId());
        }
        if (mathActivityJoinusParam.getStudentId() == null) { // H5游客新增创建学生
            CommonResponse.ERROR.assertNotNull(mathActivityJoinusParam.getParentPhone(), "家长手机号不可为空");
            AddStudentParam studentParam = AddStudentParam.builder()
                    .studentName(mathActivityJoinusParam.getStudentName())
                    .gradeName(GradeSemesterEnum.getByGrade(mathActivityJoinusParam.getGrade()))
                    .parentPhone(mathActivityJoinusParam.getParentPhone())
                    .build();
            Long studentId = basicBusinessService.addStudentBasicData(studentParam);
            CommonResponse.ERROR.assertNotNull(studentId, "创建学生失败");
            mathActivityJoinusParam.setStudentId(studentId);
        }
        Long activityStudentId = mathActivityStudentService.studentJoinActivity(mathActivityJoinusParam);
        if(activityStudentId != null) {
            MathActivityStudent mathActivityStudent = new MathActivityStudent();
            mathActivityStudent.setId(activityStudentId);
            return ApiResult.success(mathActivityStudentService.getMathActivityStudentVo(mathActivityStudent).get(0));
        }
        return ApiResult.failed("报名失败");
    }

    @GetMapping("/by-parent-phone/{parentPhone}")
    @ApiOperation(value = "根据家长手机号查询学生信息", notes = "根据家长手机号查询关联的学生信息列表", response = ApiResult.class)
    public ApiResult<List<StudentBasicInfoDTO>> getStudentsByParentPhone(@PathVariable("parentPhone") String parentPhone) {
        return ApiResult.success(basicBusinessService.getStudentsByParentPhone(parentPhone));
    }

    @PostMapping
    @ApiOperation(value = "新增学生信息", notes = "新增学生", response = ApiResult.class)
    public ApiResult<Long> addStudentBasicData( @RequestBody AddStudentParam addStudentParam) {
        return ApiResult.success(basicBusinessService.addStudentBasicData(addStudentParam));
    }

    @PostMapping("/sms")
    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码", response = ApiResult.class)
    public ApiResult<String> sendSms(HttpServletRequest request, @RequestBody SmsParam smsParam) {
        return ApiResult.success(qylService.sendSms(smsParam, request));
    }

    @PostMapping("/login")
    @ApiOperation(value = "通过验证码登录", notes = "通过验证码登录", response = ApiResult.class)
    public ApiResult<MathH5TokenResult> login(@Valid @RequestBody SmsLoginParam smsLoginParam , HttpServletResponse response) {
        return ApiResult.success(qylService.login(smsLoginParam, response));
    }

    @PostMapping("/logout")
    @ApiOperation(value = "退出登录", notes = "退出登录", response = ApiResult.class)
    public ApiResult<String> logout(HttpServletRequest request) {
        return ApiResult.success(qylService.logout(request));
    }


    @PostMapping("/school-config/add")
    @ApiOperation(value = "学校海报设置新增", notes = "学校海报设置新增", response = ApiResult.class)
    public ApiResult<String> schoolConfigAdd(@RequestParam Integer schoolId,@RequestParam List<String> mathHolidayH5Images) {
        try {
            SchoolConfig schoolConfig = new SchoolConfig();
            schoolConfig.setSchoolId(schoolId);
            schoolConfig.setMathHolidayH5Images(JSONUtil.toJsonStr(mathHolidayH5Images));
            schoolConfigService.schoolConfigAdd(schoolConfig);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("新增配置成功");
    }

    @GetMapping("/school-config/{schoolId}")
    @ApiOperation(value = "查询学校海报设置", notes = "查询学校海报设置", response = ApiResult.class)
    public ApiResult<SchoolConfig> schoolConfigBySchoolId(@PathVariable Long schoolId) {
        CommonResponse.ERROR.assertNotNull(schoolId, "学校id不可为空");
        QueryWrapper<SchoolConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("school_id", schoolId);
        queryWrapper.isNull("deleted_at");
        SchoolConfig schoolConfig = schoolConfigService.getOne(queryWrapper);
        return ApiResult.success(schoolConfig);
    }

    @GetMapping("/school-config/update")
    @ApiOperation(value = "学校海报设置更新", notes = "学校海报设置更新", response = ApiResult.class)
    public ApiResult<String> schoolConfigUpdate(@RequestParam Long schoolId,@RequestParam List<String> mathHolidayH5Images) {
        try {
            QueryWrapper<SchoolConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("school_id", schoolId);
            queryWrapper.isNull("deleted_at");
            SchoolConfig schoolConfig = schoolConfigService.getOne(queryWrapper);
            schoolConfig.setMathHolidayH5Images(JSONUtil.toJsonStr(mathHolidayH5Images));
            schoolConfigService.updateSchoolConfigById(schoolConfig);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("更新配置成功");
    }

    @PostMapping("/math-invitation/add")
    @ApiOperation(value = "记录被邀请人和邀请人关系", notes = "记录被邀请人和邀请人关系", response = ApiResult.class)
    public ApiResult<String> mathInvitationAdd(@RequestBody MathInvitation param) {
        try {
            CommonResponse.ERROR.assertNotNull(param.getInviterStudentId(), "inviterStudentId不可为空");
            CommonResponse.ERROR.assertNotNull(param.getInviterPhone(), "inviterPhone不可为空");
            CommonResponse.ERROR.assertNotNull(param.getInviteeStudentId(), "inviteeStudentId不可为空");
            CommonResponse.ERROR.assertNotNull(param.getInviteePhone(), "inviteePhone不可为空");
            mathInvitationService.mathInvitationAdd(param);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("新增成功");
    }

    @PostMapping("/ijx/shortUrl-forwarder")
    @ApiOperation(value = "ijx短链接转发", notes = "ijx短链接转发", response = ApiResult.class)
    public ApiResult<JSONObject> ijxShortUrl(@RequestBody JSONObject param) {
        try {
            String targetUrl = "https://ijx.ink/create/shortUrl";
            // 构建请求JSON体（注意Java字符串转义）
            String jsonBody = param.toJSONString();
            String result = HttpRequest.post(targetUrl)
                    .header("Content-Type", "application/json")
                    .header("token", "ijxJoinusShortUrlToken")
                    .body(jsonBody)
                    .timeout(60000)
                    .execute()
                    .body();
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                return ApiResult.success(jsonObject);
            }
        } catch (Exception e) {
            log.error("shortUrl-forwarder error: {}", e.getMessage());
            e.printStackTrace();
        }
        return ApiResult.failed("短链接转换失败");
    }

    @Operation(summary = "查询训练师分享链接")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/learning-planner")
    public ApiResult<Map<String, String>> getLearningPlanner() {
        List<cn.hutool.json.JSONObject> planners = JSONUtil.toList(learningPlanners, cn.hutool.json.JSONObject.class);
        // 当有多条记录时，随机返回一条
        int randomIndex = 0;
        if (planners.size() >= 1) {
            randomIndex = new Random().nextInt(planners.size());
            log.info("从{}条训练师数据中随机选择第{}条返回", planners.size(), randomIndex + 1);
        }
        cn.hutool.json.JSONObject jsonObject = planners.get(randomIndex);
        Map<String, String> result = new HashMap<>();
        result.put("name", jsonObject.getStr("name"));
        result.put("url", jsonObject.getStr("url"));
        return ApiResult.success(result);
    }



    @ApiOperation(value = "保存页面浏览数据")
    @PostMapping("/page-view-trackings")
    public ApiResult<String> savePageViewTracking(@RequestBody @Valid MathPageViewTrackingParam mathPageViewTrackingParam) {
        CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
        boolean save = mathPageViewTrackingService.addTracking(mathPageViewTrackingParam, currentUser);
        if (save) {
            return ApiResult.success("保存成功");
        }
        return ApiResult.failed("保存失败");
    }

}
