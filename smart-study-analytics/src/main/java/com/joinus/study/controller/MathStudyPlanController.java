package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.QueryStudyRecordParam;
import com.joinus.study.model.param.QuestionDetailParam;
import com.joinus.study.model.vo.MathStudentStudyPlanVo;
import com.joinus.study.model.vo.StudyRecordQuestionDetailsVo;
import com.joinus.study.service.MathStudentStudyPlanService;
import com.joinus.study.service.StudyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 学习计划
 * @date: 2025/9/17 13:37
 */
@RestController
@RequestMapping("/math/study-plan")
@Api(value = "/学习计划", tags = "学习计划")
public class MathStudyPlanController {
    @Resource
    private MathStudentStudyPlanService mathStudentStudyPlanService;

    @GetMapping("/current/{studentId}")
    @ApiOperation(value = "获取学生当前学习计划", notes = "获取学生当前学习计划", response = ApiResult.class)
    public ApiResult<MathStudentStudyPlanVo> getStudentCurrentPlan(@PathVariable Long studentId) {
        return ApiResult.success(mathStudentStudyPlanService.getStudentCurrentPlan(studentId));
    }
    @GetMapping("/list/{studentId}")
    @ApiOperation(value = "获取学生学习计划列表", notes = "获取学生学习计划列表", response = ApiResult.class)
    public ApiResult<List<MathStudentStudyPlanVo>> getStudentPlanlist(@PathVariable Long studentId) {
        return ApiResult.success(mathStudentStudyPlanService.getStudentCurrentPlanList(studentId));
    }

}
