package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingPassagesPageParam;
import com.joinus.study.model.param.ReadingPassagesUpdateBatchParam;
import com.joinus.study.model.param.ReadingPassagesUpdateParam;
import com.joinus.study.model.vo.ReadingGradeVO;
import com.joinus.study.model.vo.ReadingPassagesBackendVO;
import com.joinus.study.service.ReadingPassagesBackendService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@AllArgsConstructor
@RequestMapping("/backend/reading-passages")
@Api(tags = "管理后台-阅读题库")
public class ReadingPassagesBackendController {

    private ReadingPassagesBackendService readingPassagesBackendService;

    @Operation(summary = "分页")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPassagesBackendVO>> pages(@Validated @RequestBody ReadingPassagesPageParam pageParam) {
        return ApiResult.success(readingPassagesBackendService.pages(pageParam));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch/delete")
    public ApiResult batchDelete(@Validated @RequestBody ReadingPassagesUpdateBatchParam updateParam) {
        readingPassagesBackendService.batchDelete(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "挂起")
    @PutMapping("/disable")
    public ApiResult disable(@Validated @RequestBody ReadingPassagesUpdateBatchParam updateParam) {
        readingPassagesBackendService.disable(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "启用")
    @PutMapping("/enable")
    public ApiResult enable(@Validated @RequestBody ReadingPassagesUpdateBatchParam updateParam) {
        readingPassagesBackendService.enable(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "详情")
    @Parameters({
            @Parameter(name = "id", description = "主键id", required = true, in = ParameterIn.PATH, example = "5bc13a8e-4ef1-4798-9ee9-bda04c7b7895")
    })
    @GetMapping("/{id}")
    public ApiResult<ReadingPassagesBackendVO> query(@PathVariable("id") UUID id) {
        return ApiResult.success(readingPassagesBackendService.query(id));
    }

    @Operation(summary = "修改")
    @PutMapping
    public ApiResult update(@Validated @RequestBody ReadingPassagesUpdateParam updateParam) {
        readingPassagesBackendService.update(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "年级-学期-单元列表")
    @GetMapping("/grade-semester-unit/list")
    public ApiResult<List<ReadingGradeVO>> listGradeSemesterUnit() {
        return ApiResult.success(readingPassagesBackendService.listGradeSemesterUnit());
    }

    @Operation(summary = "审核通过")
    @PutMapping("/audit/pass")
    public ApiResult auditPass(@Validated @RequestBody ReadingPassagesUpdateBatchParam updateParam) {
        readingPassagesBackendService.auditPass(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "审核不通过")
    @PutMapping("/audit/no-pass")
    public ApiResult auditNoPass(@Validated @RequestBody ReadingPassagesUpdateBatchParam updateParam) {
        readingPassagesBackendService.auditNoPass(updateParam);
        return ApiResult.success();
    }
}
