package com.joinus.study.controller;

import cn.hutool.json.JSONObject;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.AiAnalyticsExamParam;
import com.joinus.study.service.ExamAnalysisReportService;
import com.joinus.study.service.ExamErrorCorrectionFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description: 生成考情分析报告回调
 * @Author:  anpy
 * @date:  2025/3/25 10:33
 */
@RestController
@RequestMapping("/callback")
@Api(tags = "AI回调")
@Slf4j
public class MathCallBackController {

    @Resource
    private ExamAnalysisReportService examAnalysisReportService;
    @Resource
    private ExamErrorCorrectionFeedbackService examErrorCorrectionFeedbackService;

    @PostMapping("/ai-analytics-result")
    @ApiOperation(value = "AI试卷分析完成回调", response = ApiResult.class)
    public void analyticsResult(@RequestBody AiAnalyticsExamParam param) {
        log.info("AI试卷分析完成回调 ai-analytics-result param:{}", param);
        examAnalysisReportService.createExamAnalysisReport(param);
    }

    @PostMapping("/ai-cut-questions-result")
    @ApiOperation(value = "AI试卷拆题完成回调", response = ApiResult.class)
    public void aiCutQuestionsResult(@RequestBody AiAnalyticsExamParam param) {
        log.info("AI试卷拆题完成回调 ai-cut-questions-result param:{}", param);
        examErrorCorrectionFeedbackService.aiCutQuestionsResult(param);
    }

    @PostMapping("/error-correction-analytics-result")
    @ApiOperation(value = "AI完成纠错试卷分析完成回调", response = ApiResult.class)
    public void errorCorrectionAnalyticsResult(@RequestBody AiAnalyticsExamParam param) {
        log.info("AI完成纠错试卷分析完成回调 error-correction-analytics-result param:{}", param);
        examErrorCorrectionFeedbackService.errorCorrectionAnalyticsResult(param);
    }

}
