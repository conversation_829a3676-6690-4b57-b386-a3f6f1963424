package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingPeriodicReportDetailParam;
import com.joinus.study.model.param.ReadingPersonalPassagesPageParam;
import com.joinus.study.model.param.ReadingPersonalPassagesUpdateParam;
import com.joinus.study.model.vo.ReadingGenreVO;
import com.joinus.study.model.vo.ReadingPeriodicReportViewDetailVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesBackendVO;
import com.joinus.study.model.vo.ReadingReportDetailVo;
import com.joinus.study.service.ReadingPersonalPassagesBackendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;


@RestController
@AllArgsConstructor
@RequestMapping("/backend/reading-personal-passages")
@Api(tags = "管理后台-阅读记录")
public class ReadingPersonalPassagesBackendController {

    private ReadingPersonalPassagesBackendService readingPersonalPassagesBackendService;

    @Operation(summary = "分页")
    @PostMapping("/pages")
    public ApiResult<Page<ReadingPersonalPassagesBackendVO>> pages(@Validated @RequestBody ReadingPersonalPassagesPageParam pageParam) {
        return ApiResult.success(readingPersonalPassagesBackendService.pages(pageParam));
    }

    @Operation(summary = "批量删除")
    @DeleteMapping("/batch/delete")
    public ApiResult batchDelete(@Validated @RequestBody ReadingPersonalPassagesUpdateParam updateParam) {
        readingPersonalPassagesBackendService.batchDelete(updateParam);
        return ApiResult.success();
    }

    @Operation(summary = "详情")
    @Parameters({
            @Parameter(name = "id", description = "主键id", required = true, in = ParameterIn.PATH, example = "123456")
    })
    @GetMapping("/{id}")
    public ApiResult<ReadingPersonalPassagesBackendVO> query(@PathVariable("id") Long id) {
        return ApiResult.success(readingPersonalPassagesBackendService.query(id));
    }

    @GetMapping(value = "/report-detail")
    @ApiOperation(value = "学情分析报告详情", notes = "学情分析报告详情", response = ApiResult.class)
    public ApiResult<ReadingReportDetailVo> reportDetail(@ApiParam(name = "id", value = "主键id", required = true) @RequestParam Long id) {
        return ApiResult.success(readingPersonalPassagesBackendService.reportDetail(id));
    }

    @GetMapping(value = "/periodic/report-detail")
    @ApiOperation(value = "学情分析周报和月报报告详情", notes = "学情分析周报和月报报告详情", response = ApiResult.class)
    public ApiResult<ReadingPeriodicReportViewDetailVo> periodicReportDetail(ReadingPeriodicReportDetailParam param) {
        return ApiResult.success(readingPersonalPassagesBackendService.periodicReportDetail(param));
    }

    @Operation(summary = "文体列表")
    @GetMapping("/genre/list")
    public ApiResult<List<ReadingGenreVO>> listGenre() {
        return ApiResult.success(readingPersonalPassagesBackendService.listGenre());
    }
}
