package com.joinus.study.controller;

import java.util.Random;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.entity.MathActivity;
import com.joinus.study.model.param.ActivityAutoGenerateParam;
import com.joinus.study.model.param.IpayPayBackParam;
import com.joinus.study.model.param.IpayPayRequest;
import com.joinus.study.model.param.MathActivityJoinusParam;
import com.joinus.study.model.vo.ActivityChapterVo;
import com.joinus.study.model.vo.MathActivityVo;
import com.joinus.study.service.MathActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/math/activity")
@Api(tags = "数学-暑期训练营活动")
@Slf4j
public class MathActivityController {
    @Resource
    private MathActivityService mathActivityService;
    
    @Value("${math.activity.learning.planners:[{\"name\":\"李老师\",\"url\":\"https://img.967111.com/test/basic/learning_planer.png\"}]}")
    private String learningPlanners;


    @GetMapping("/detail")
    @ApiOperation(value = "获取活动信息", notes = "活动名称金额时间等信息", response = ApiResult.class)
    public ApiResult<MathActivity> activityDetail() {
        return ApiResult.success(mathActivityService.currentActivity(null));
    }
    /**
     * 获取支付方式
     */
    @GetMapping("/pay-method")
    @ApiOperation(value = "获取支付方式", notes = "支付方式列表", response = ApiResult.class,hidden = true)
    @ApiImplicitParam(name = "schoolId", value = "学校id", required = true, dataType = "String")
    public ApiResult<List<Map<String, String>>> activityPayMethod(@RequestParam Long schoolId) {
        List<Map<String, String>> methodList= new ArrayList<>();
        Map<String, String> payMethodMap = new HashMap<>();
        payMethodMap.put("payMethod", "304");
        payMethodMap.put("name", "微信APP");
        methodList.add(payMethodMap);
        Map<String, String> payMethodMap2 = new HashMap<>();
        payMethodMap2.put("payMethod", "300");
        payMethodMap2.put("name", "支付宝");
        methodList.add(payMethodMap2);
        return ApiResult.success(methodList);
    }

    @Operation(summary = "自动生成统一的训练计划")
    @Parameters({
            @Parameter(schema = @Schema(implementation = ActivityAutoGenerateParam.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/generate/auto")
    public ApiResult<String> generateAuto(@RequestBody ActivityAutoGenerateParam param) {
        mathActivityService.generateAuto(param);
        return ApiResult.success("生成成功");
    }

    @Operation(summary = "查询训练师分享链接")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/learning-planner")
    public ApiResult<Map<String, String>> getLearningPlanner() {
        List<JSONObject> planners = JSONUtil.toList(learningPlanners, JSONObject.class);
        // 当有多条记录时，随机返回一条
        int randomIndex = 0;
        if (planners.size() >= 1) {
            randomIndex = new Random().nextInt(planners.size());
            log.info("从{}条训练师数据中随机选择第{}条返回", planners.size(), randomIndex + 1);
        }
        JSONObject jsonObject = planners.get(randomIndex);
        Map<String, String> result = new HashMap<>();
        result.put("name", jsonObject.getStr("name"));
        result.put("url", jsonObject.getStr("url"));
        return ApiResult.success(result);
    }

    @Operation(summary = "根据年级查询所有章节")
    @Parameters({
            @Parameter(name = "grade", description = "年级", required = true, schema = @Schema(implementation = Integer.class), example = "7")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/chapters")
    public ApiResult<List<ActivityChapterVo>> listChapters(@RequestParam("grade") Integer grade) {
        List<ActivityChapterVo> results = mathActivityService.listChapters(grade);
        return ApiResult.success(results);
    }


}
