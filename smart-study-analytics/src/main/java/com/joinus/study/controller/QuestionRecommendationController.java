package com.joinus.study.controller;

import cn.hutool.json.JSONUtil;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
import com.joinus.study.service.ReadingQuestionRecommendationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 题目推荐
 * @date 2025/5/12
 */

@RestController
@AllArgsConstructor
@RequestMapping("/question-recommendation")
@Api(tags = "题目推荐")
public class QuestionRecommendationController {

    private ReadingQuestionRecommendationService readingQuestionRecommendationService;

    @GetMapping("/directional-blasting")
    @ApiOperation(value = "薄弱知识点定向爆破", notes = "阅读提分训练营-薄弱知识点（单题）", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "entryType", value = "答题入口类型", required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "knowledgePointId", value = "知识点ID", required = true, paramType = "query", dataType = "String")
    })
//    @RequiresReadingMembership(rule = "#param.entryType == 2",value = true)
    public ApiResult<ReadingPersonalPassagesVo> getDirectionalBlastingQuestions(HttpServletRequest request, ReadingStudentPassagesQueryParam param) {
        CurrentUser currentUser = JSONUtil.toBean(JSONUtil.toJsonStr(request.getAttribute("CURRENT_USER").toString()), CurrentUser.class);
        CommonResponse.ERROR.assertNotNull(currentUser, "家长id不能为空");
        param.setParentId(currentUser.getUserId());
        try {
            return ApiResult.success(readingQuestionRecommendationService.getDirectionalBlastingQuestionsTwo(param));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/practice")
    @ApiOperation(value = "阅读训练", notes = "阅读提分训练营-阅读训练（套题）、自建练习计划-去练习（套题）", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "entryType", value = "答题入口类型", required = true, paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "planId", value = "计划id", paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "unitId", value = "单元id", paramType = "query", dataType = "String")
    })
    public ApiResult<ReadingPersonalPassagesVo> getPracticeQuestions(HttpServletRequest request, ReadingStudentPassagesQueryParam param) {
        try {
            return ApiResult.success(readingQuestionRecommendationService.getPracticeQuestions(param));
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
    }

    @GetMapping("/activity/practice")
    @ApiOperation(value = "假期活动练习", notes = "假期训练营-强化训练（套题）、假期训练营-巩固复习", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生ID", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "planId", value = "活动计划ID", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "personalPassageId", value = "练习记录ID", paramType = "query", dataType = "Long")
    })
    public ApiResult<ReadingPersonalPassagesVo> getActivityPracticeQuestions(HttpServletRequest request, ReadingStudentPassagesQueryParam param) {
        try {
            return ApiResult.success(readingQuestionRecommendationService.getActivityPracticeQuestions(param));
        }catch (Exception e){
            return ApiResult.failed(e.getMessage());
        }
    }

}
