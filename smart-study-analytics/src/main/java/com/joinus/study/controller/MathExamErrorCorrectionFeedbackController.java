package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.study.service.ExamErrorCorrectionFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * @Description: 试卷纠错反馈
 */
@RestController
@RequestMapping("/api/exam_error_correction_feedback")
@Tag(name = "试卷纠错反馈", description = "试卷纠错反馈")
@RequiredArgsConstructor
public class MathExamErrorCorrectionFeedbackController {

    @Autowired
    private ExamErrorCorrectionFeedbackService examErrorCorrectionFeedbackService;

    @Operation(summary = "试卷纠错反馈提交", description = "试卷纠错反馈提交")
    @PostMapping("/save")
    public ApiResult saveExamErrorCorrectionFeedback(HttpServletRequest request, @RequestBody ExamErrorCorrectionFeedbackParam param) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        param.setParentId(parentId);
        CommonResponse.ERROR.assertNotNull(param.getGrade(), "年级不能为空");
        try {
            examErrorCorrectionFeedbackService.saveExamErrorCorrectionFeedback(param);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("试卷问题反馈成功");
    }

    @Operation(summary = "试卷纠错反馈完成（并发送通知）", description = "试卷纠错反馈完成（并发送通知）")
    @PostMapping("/manager/finish")
    public ApiResult finishExamErrorCorrectionFeedback(@RequestBody ExamErrorCorrectionFeedbackParam param) {
        try {
            examErrorCorrectionFeedbackService.finishExamErrorCorrectionFeedback(param);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("试卷问题反馈完成");
    }

    @Operation(summary = "判断是否有正在AI分析中试题纠错", description = "判断是否有正在AI分析中试题纠错")
    @GetMapping("/manager/isCorrecting/{examId}")
    public ApiResult isCorrectingExamWithAI(@PathVariable UUID examId) {
        return ApiResult.success(examErrorCorrectionFeedbackService.isCorrectingExamWithAI(examId));
    }

    @Operation(summary = "试卷纠错标记试卷作废", description = "试卷纠错标记试卷作废")
    @PostMapping("/manager/invalid")
    public ApiResult invalidExamErrorCorrectionFeedback(@RequestBody ExamErrorCorrectionFeedbackParam param) {
        try {
            examErrorCorrectionFeedbackService.invalidExamErrorCorrectionFeedback(param);
        } catch (Exception e) {
            return ApiResult.failed(e.getMessage());
        }
        return ApiResult.success("试卷标记作废");
    }

}
