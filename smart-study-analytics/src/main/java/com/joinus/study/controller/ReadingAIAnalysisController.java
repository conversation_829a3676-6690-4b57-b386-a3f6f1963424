package com.joinus.study.controller;


import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.*;
import com.joinus.study.service.ReadingAiAnalysisService;
import com.joinus.study.service.ReadingPersonalPassagesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

@RestController
@RequestMapping("/reading-ai-analysis")
@Api(tags = "语文阅读提分ai交互")
public class ReadingAIAnalysisController {
    @Resource
    private ReadingPersonalPassagesService readingPersonalPassagesService;

    @Resource
    private ReadingAiAnalysisService readingAiAnalysisService;

    @Resource(name = "taskExecutor")
    private Executor threadPoolTaskExecutor;

    @PostMapping("/correcting-results")
    @ApiOperation(value = "获取批改结果", notes = "文阅读提分ai交互-获取批改结果", response = ApiResult.class)
    public ApiResult correctingResults(@RequestBody ReadingAIAbilityParam param) {
        //readingPersonalPassagesService.saveCorrectingResults(param);
        return ApiResult.success();
    }

    @PostMapping("/weak-knowledge-points-analysis-results")
    @ApiOperation(value = "获取薄弱知识点分析结果", notes = "文阅读提分ai交互-获取薄弱知识点分析结果", response = ApiResult.class)
    public ApiResult weakKnowledgePointsAnalysisResults(@RequestBody ReadingAIWeakKnowledgePointsParam param) {
        readingAiAnalysisService.weakKnowledgePointsAnalysisResults(param);
        return ApiResult.success();
    }

    @PostMapping("/suggestion-analyses-results")
    @ApiOperation(value = "获取综合训练建议结果", notes = "文阅读提分ai交互-获取综合训练建议结果", response = ApiResult.class)
    public ApiResult suggestionAnalysesResults(@RequestBody ReadingAISuggestionParam param) {
        readingAiAnalysisService.suggestionAnalysesResults(param);
        return ApiResult.success();
    }

    @PostMapping("/weak-question-type-analyses-results")
    @ApiOperation(value = "获取题型分析结果", notes = "文阅读提分ai交互-获取题型分析结果", response = ApiResult.class)
    public ApiResult weakQuestionTypeAnalysesResults(@RequestBody ReadingAIWeakQuestionTypeParam param) {
        readingAiAnalysisService.weakQuestionTypeAnalysesResults(param);
        return ApiResult.success();
    }

    @PostMapping("/analyses-results")
    @ApiOperation(value = "AI分析公共回调接口", notes = "文阅读提分ai交互-AI分析公共回调接口", response = ApiResult.class)
    public ApiResult analysesResults(@RequestBody ReadingAICallBackParam param) {
        threadPoolTaskExecutor.execute(() -> {
            readingAiAnalysisService.analysesResults(param);
        });
        return ApiResult.success();
    }

}
