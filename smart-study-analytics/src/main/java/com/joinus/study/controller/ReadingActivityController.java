package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.ReadingJoinActivityParam;
import com.joinus.study.model.vo.ReadingActivityVO;
import com.joinus.study.service.ReadingActivityService;
import com.joinus.study.service.ReadingActivityStudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/reading/activity")
@Api(tags = "语文阅读-暑期训练营活动")
public class ReadingActivityController {
    @Resource
    private ReadingActivityService activityService;

    @Resource
    private ReadingActivityStudentService activityStudentService;

    /**
     * 加入训练营
     */
    @PostMapping("/join")
    @ApiOperation(value = "立即加入暑期训练营", notes = "立即加入暑期训练营", response = ApiResult.class)
    public ApiResult<Object> joinActivity(@RequestBody @Valid ReadingJoinActivityParam param) {
        activityService.joinActivity(param);
        return ApiResult.success();
    }

    /**
     * 校验学生是否加入暑期训练营
     * @param studentId 学生id
     * @return
     */
    @GetMapping("/checkStudentIsJoinActivity")
    @ApiOperation(value = "校验学生是否加入暑期训练营", notes = "校验学生是否加入暑期训练营", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<Object> checkStudentIsJoinActivity(Long studentId) {
        return ApiResult.success(activityStudentService.checkStudentIsJoinActivity(studentId));
    }

    /**
     * 获取所有活动 -- 不分页
     */
    @GetMapping("/queryReadingActivityList")
    @ApiOperation(value = "查询活动列表", notes = "查询活动列表", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<List<ReadingActivityVO>> queryReadingActivityList(Long studentId) {
        return ApiResult.success(activityService.queryReadingActivityList(studentId));
    }
}
