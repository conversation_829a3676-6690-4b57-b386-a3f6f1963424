package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointCreateParam;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointPageParam;
import com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.joinus.study.service.EnglishPersonalWeakKnowledgePointService;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/english/personal-weak-knowledge-point")
@Api(tags = "英语-薄弱知识点相关接口")
public class EnglishPersonalWeakKnowledgePointController {

    private EnglishPersonalWeakKnowledgePointService englishPersonalWeakKnowledgePointService;

    @PostMapping("/pages")
    @ApiOperation(value = "分页", notes = "薄弱知识点-分页列表", response = ApiResult.class)
    public ApiResult<Page<EnglishPersonalWeakKnowledgePointVO>> pages(@Validated @RequestBody EnglishPersonalWeakKnowledgePointPageParam pageParam) {
        return ApiResult.success(englishPersonalWeakKnowledgePointService.pages(pageParam));
    }

    @GetMapping("/list")
    @ApiOperation(value = "列表", notes = "试卷诊断报告-薄弱知识点列表", response = ApiResult.class)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "diagnoseRecordId", value = "试卷诊断记录id", required = true, paramType = "query", dataType = "Long")
    })
    public ApiResult<List<EnglishPersonalWeakKnowledgePointVO>> list(Long studentId, Long diagnoseRecordId) {
        return ApiResult.success(englishPersonalWeakKnowledgePointService.list(studentId, diagnoseRecordId));
    }
}
