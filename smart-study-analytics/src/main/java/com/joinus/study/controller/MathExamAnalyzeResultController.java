package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.joinus.study.service.ExamAnalyzeResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/situation-analysis")
@Api(tags = "考情分析报告-1")
public class MathExamAnalyzeResultController {

    @Resource
    private ExamAnalyzeResultService examAnalyzeResultService;

    @GetMapping("/pages")
    @ApiOperation(value = "分页查询分析报告列表", notes = "分页查询分析报告列表", response = ApiResult.class)
    public ApiResult<Page> page(HttpServletRequest request,
                                            @RequestParam Long studentId, Integer current, Integer size) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        return ApiResult.success(examAnalyzeResultService.situationAnalysisPage(parentId, studentId, current, size));
    }

    @GetMapping("/info/{examAnalyzeResultId}")
    @ApiOperation(value = "根据报告id查看分析报告状态", notes = "根据报告id查看分析报告状态", response = ApiResult.class)
    public ApiResult<ExamAnalyzeResult> getExamAnalyzeResult(@PathVariable Long examAnalyzeResultId) {
        return ApiResult.success(examAnalyzeResultService.getExamAnalyzeResultById(examAnalyzeResultId));
    }

    @GetMapping("/specialized-record/pages")
    @ApiOperation(value = "分页查询专项训练记录", notes = "分页查询专项训练记录", response = ApiResult.class)
    public ApiResult<Page> specializedRecordPage(HttpServletRequest request, Long studentId, Integer current, Integer size) {
        Long parentId = (Long) request.getAttribute("CURRENT_USER_ID");
        return ApiResult.success(examAnalyzeResultService.specializedRecordPage(parentId, studentId, current, size));
    }

    @GetMapping("/pages/all")
    @ApiOperation(value = "分页查询全部记录", notes = "分页查询全部记录", response = ApiResult.class)
    public ApiResult<Page> pageAllRecords(HttpServletRequest request, Long studentId, Integer current, Integer size) {
        return ApiResult.success(examAnalyzeResultService.pageAllRecords(studentId, current, size));
    }

}
