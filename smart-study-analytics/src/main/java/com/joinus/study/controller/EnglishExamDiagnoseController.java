package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.EnglishExamAnalysisParam;
import com.joinus.study.model.param.EnglishExamAnalysisSubmitReportParam;
import com.joinus.study.model.param.EnglishExamQuryOssUrlParam;
import com.joinus.study.model.param.OssTokenParam;
import com.joinus.study.model.vo.EnglishExamAnalysisVO;
import com.joinus.study.model.vo.OssTokenVo;
import com.joinus.study.service.EnglishExamDiagnoseRecordService;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/1 11:25
 */
@RestController
@RequestMapping("/english/diagnose")
@Api(tags = "AI英语-试卷诊断相关接口")
@Slf4j
public class EnglishExamDiagnoseController {

    @Resource
    private EnglishExamDiagnoseRecordService englishExamDiagnoseRecordService;

    @GetMapping("/oss/token")
    @ApiOperation(value = "获取上传OSS token", notes = "获取上传OSS token", response = ApiResult.class)
    public OssTokenVo ossToken(@Valid OssTokenParam param) {
        return englishExamDiagnoseRecordService.ossToken(param);
    }

    @PostMapping("/exam/analysis")
    @ApiOperation(value = "试卷分析", notes = "试卷分析", response = ApiResult.class)
    public ApiResult<EnglishExamAnalysisVO> analysisExam(@Valid @RequestBody EnglishExamAnalysisParam param) {
        EnglishExamAnalysisVO analysisVO = englishExamDiagnoseRecordService.analysisExam(param);
        return ApiResult.success(analysisVO);
    }

    @PostMapping("/submit/record")
    @ApiOperation(value = "提交生成报告", notes = "提交生成报告", response = ApiResult.class)
    public ApiResult<String> submitRecord(@Valid @RequestBody EnglishExamAnalysisSubmitReportParam param) {
        englishExamDiagnoseRecordService.submitRecord(param);
        return ApiResult.success();
    }

    @PostMapping("/exam/image-url")
    @ApiOperation(value = "根据OSS key获取完整链接", notes = "根据OSS key获取完整链接", response = ApiResult.class)
    public ApiResult<List<String>> queryOssUrl(@Valid @RequestBody EnglishExamQuryOssUrlParam param) {
        List<String> urls = englishExamDiagnoseRecordService.queryOssUrl(param);
        return ApiResult.success(urls);
    }

}
