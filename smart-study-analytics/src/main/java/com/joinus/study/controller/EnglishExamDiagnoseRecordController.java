package com.joinus.study.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.param.EnglishDiagnoseReportPageParam;
import com.joinus.study.model.vo.EnglishExamDiagnoseDetailVO;
import com.joinus.study.model.vo.EnglishExamDiagnoseRecordVO;
import com.joinus.study.service.EnglishExamDiagnoseRecordService;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/9/1 11:25
 */
@RestController
@RequestMapping("/english/diagnose/report")
@Api(tags = "英语-诊断报告")
@Slf4j
public class EnglishExamDiagnoseRecordController {

    @Resource
    private EnglishExamDiagnoseRecordService englishExamDiagnoseRecordService;

    /**
     * 分页
     */
    @PostMapping("/page")
    @Operation(summary = "分页")
    @Parameters({
            @Parameter(schema = @Schema(implementation = EnglishDiagnoseReportPageParam.class))
    })
    @ApiOperation(value = "分页列表", notes = "分页列表", response = ApiResult.class)
    public ApiResult<Page<EnglishExamDiagnoseRecordVO>> pages(@RequestBody EnglishDiagnoseReportPageParam param) {
        Page<EnglishExamDiagnoseRecordVO> englishDiagnoseReportVOPage = englishExamDiagnoseRecordService.pages(param);
        return ApiResult.success(englishDiagnoseReportVOPage);
    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    @Operation(summary = "详情")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true, schema = @Schema(implementation = Long.class))
    })
    public ApiResult<EnglishExamDiagnoseDetailVO> query(@RequestParam Long id) {
        EnglishExamDiagnoseDetailVO detailVO = englishExamDiagnoseRecordService.query(id);
        return ApiResult.success(detailVO);
    }
}
