package com.joinus.study.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.service.MembershipService;
import com.joinus.study.util.CurrentUserHolder;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@RestController
@AllArgsConstructor
@RequestMapping("/math/membership")
@Slf4j
@Api(value = "数学-会员相关",  tags = "数学-会员相关")
public class MathMembershipController {

    @Autowired
    private MembershipService membershipService;

    @Operation(summary = "查询学生数学会员状态")
    @Parameters({
            @Parameter(name = "studentId", description = "请求参数", required = true, schema = @Schema(implementation = Long.class))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = String.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{studentId}/status")
    public ApiResult getStudentMathMemberShipStatus(HttpServletRequest request,
                                                    @PathVariable("studentId") Long studentId) {
        CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
        boolean isMember = membershipService.queryStudentMathMembership(studentId, currentUser.getUserId());
        return ApiResult.success(isMember);
    }

}
