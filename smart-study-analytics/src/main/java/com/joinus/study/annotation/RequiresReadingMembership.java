package com.joinus.study.annotation;

import java.lang.annotation.*;

/**
 * 会员权限校验注解
 * 标记在控制器方法上，表示该方法需要会员权限才能访问
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresReadingMembership {
    /**
     * 是否需要会员权限
     * 默认为true，表示需要会员权限
     */
    boolean value() default false;
    
    /**
     * 会员权限描述信息
     */
    String description() default "此功能需要开通数学会员";


    /**
     * 会员权限规则  存储 SpEL 表达式，如 "#amount > 1000" 或 "#order.status == 'PAID'"
     */
    String rule() default "";
}
