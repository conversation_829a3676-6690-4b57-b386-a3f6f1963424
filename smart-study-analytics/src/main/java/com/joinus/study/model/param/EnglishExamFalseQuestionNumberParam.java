package com.joinus.study.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "英语-试卷诊断-提交生成报告-错题题号 请求参数")
public class EnglishExamFalseQuestionNumberParam {

    @NotNull(message = "大题题号不能为空")
    @ApiModelProperty(value = "大题题号", required = true)
    private Integer num;

    @NotEmpty(message = "小题题号不能为空")
    @ApiModelProperty(value = "小题题号", required = true)
    private List<EnglishExamQuestionNumberParam> child;

}
