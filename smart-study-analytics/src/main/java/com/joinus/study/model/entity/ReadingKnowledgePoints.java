package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/7 15:14
 */
@TableName(value = "reading_knowledge_points")
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "知识点")
public class ReadingKnowledgePoints implements Serializable {
    @Schema(description = "主键id")
    private UUID id;
    @Schema(description = "知识点名称")
    private String name;
    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;
    private String content;

}
