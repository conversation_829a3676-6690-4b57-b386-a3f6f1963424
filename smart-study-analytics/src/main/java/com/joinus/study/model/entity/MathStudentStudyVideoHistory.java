package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.UUID;

import com.joinus.study.model.enums.MathVideoTypeEnum;
import lombok.Data;

/**
 * 学生视频观看记录
 * @TableName math_student_study_video_history
 */
@TableName(value ="math_student_study_video_history")
@Data
public class MathStudentStudyVideoHistory extends BaseEntity {

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 视频类型 SECTION 小节视频, KNOWLEDGE_POINT 知识点视频
     */
    private MathVideoTypeEnum videoType;

    /**
     * 视频ID，video_type=SECTION时小节视频id(file_id)， video_type=KNOWLEDGE_POINT时知识点对应知识点的id
     */
    private UUID videoId;

    /**
     * 观看时长 单位:秒
     */
    private Integer duration;

    /**
     * 是否完成观看
     */
    private Boolean completed;

}