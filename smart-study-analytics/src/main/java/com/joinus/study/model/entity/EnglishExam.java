package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.joinus.study.config.JsonbTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@TableName(value = "english_flow_exam")
@Data
public class EnglishExam {

    @TableId(value = "id", type = IdType.INPUT)
    private UUID id;

    /**
     * 试卷名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 所属年级
     */
    @TableField(value = "grade")
    private Integer grade;

    /**
     * 题目总数
     */
    @TableField(value = "small_questions_num")
    private Integer smallQuestionsNum;

    /**
     * 题号
     */
    @TableField(value = "questions_nums")
    private String questionsNums;

    /**
     * 总分数
     */
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 阿里云OSS key
     */
    @TableField(value = "oss_urls", typeHandler = JsonbTypeHandler.class)
    private String ossUrls;

}
