package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.QuestionAnswerFeedbackTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * 
 * @TableName question_answer_feedback
 */
@TableName(value ="question_answer_feedback")
@Data
@EqualsAndHashCode(callSuper=false)
public class QuestionAnswerFeedback extends BaseEntity {
    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 题目答案及解析的id
     */
    private UUID questionAnswerId;

    /**
     * 反馈类型：upvote_count-点赞，problem_feedback-问题反馈
     */
    private QuestionAnswerFeedbackTypeEnum type;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈建议
     */
    private String suggestion;

    private String questionAnswer;

    private String questionAnswerContent;
}