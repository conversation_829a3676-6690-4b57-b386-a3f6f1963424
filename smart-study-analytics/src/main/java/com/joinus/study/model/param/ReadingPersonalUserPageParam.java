package com.joinus.study.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.joinus.common.model.param.PageParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/7/11 17:18
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(description = "管理后台-用户管理分页查询参数")
public class ReadingPersonalUserPageParam extends PageParam implements Serializable {

    @ApiModelProperty(value = "姓名")
    private String studentName;
    @ApiModelProperty(value = "学校名称")
    private String schoolName;
    @ApiModelProperty(value = "年级1-9")
    private Integer grade;
    @ApiModelProperty(value = "用户来源 1B端用户 2C端用户")
    private Integer source;
    @ApiModelProperty(value = "加入暑假训练开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime joinSummerTrainingTimeStart;
    @ApiModelProperty(value = "加入暑假训练结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime joinSummerTrainingTimeEnd;
    @ApiModelProperty(value = "手机号")
    private String telNum;
}
