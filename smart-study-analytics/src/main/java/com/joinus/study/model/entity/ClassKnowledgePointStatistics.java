package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * @Description: 班级知识点正确率统计表
 * @Author: anpy
 * @date: 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_knowledge_point_statistics")
public class ClassKnowledgePointStatistics {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 试卷ID
     */
    private UUID examId;
    
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 考试场次
     * 用于区分同一班级同一试卷的不同考试场次
     */
    @TableField(exist = false)
    private String examSession;
    
    /**
     * 知识点ID
     */
    private UUID knowledgePointId;
    
    /**
     * 知识点名称
     */
    @TableField(exist = false)
    private String knowledgePointName;
    
    /**
     * 班级知识点正确率
     */
    private BigDecimal correctRate;
    
    /**
     * u6b63u786eu4ebau6570
     */
    private Integer correctCount;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 删除时间
     */
    private Date deletedAt;
    
    /**
     * 课程考试ID
     */
    private Long classExamId;
    private BigDecimal gradeCorrectRate;
}
