package com.joinus.study.model.param;

import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.OssEnum;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * @Description: AI-更新专项训练试卷参数
 * @Author:  anpy
 * @date:  2025/5/8 09:55
 */
@Builder
@Data
public class AISpecializedTrainingUpdateExamParam {

    private UUID examId;
    private List<ImagesDTO> images;
    private ExamSourceType examSource;

    @Builder
    @Data
    public static class ImagesDTO {
        private OssEnum ossEnum;
        private String ossKey;
        private int type;
        private int sortNo;
    }
}
