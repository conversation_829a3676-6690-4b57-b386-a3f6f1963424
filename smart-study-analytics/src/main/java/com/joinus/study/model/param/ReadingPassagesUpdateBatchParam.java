package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台-阅读题库,题目批量处理参数")
public class ReadingPassagesUpdateBatchParam {

    @Schema(description = "主键id", type = "array", example = "[\"550e8400-e29b-41d4-a716-************\"]")
    @NotEmpty(message = "id不能为空")
    private List<UUID> ids;
}
