package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Map;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathStudentStudyVideoParam implements Serializable {
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "知识点ID")
    private UUID knowledgePointId;
    @Schema(description = "小节ID")
    private UUID sectionId;
    @Schema(description = "视频内容")
    private Map<String,String> videoInfo;
    @Schema(description = "视频时长/秒")
    private Integer duration;
    @Schema(description = "是否看完")
    private Boolean completed;


}
