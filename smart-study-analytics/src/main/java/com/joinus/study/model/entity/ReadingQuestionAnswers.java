package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

@TableName(value = "reading_question_answers")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingQuestionAnswers {
    private UUID id;
    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;
    /**
     * 题目id
     */
    private UUID questionId;
    /**
     * 答案
     */
    private String answer;
    /**
     * 答案解析
     */
    private String content;
    /**
     * 公式ID
     */
    private UUID answeringFormulaId;
}
