package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/9/3 15:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("english_exam_report_question_type")
public class EnglishExamReportQuestionType {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //生成表字段
    @TableField(value = "record_id")
    private Long recordId;
    @TableField(value = "question_type")
    private String questionType;
    @TableField(value = "question_number")
    private Integer questionNumber;
    @TableField(value = "wrong_question_number")
    private Integer wrongQuestionNumber;
    @TableField(value = "right_question_number")
    private Integer rightQuestionNumber;
    @TableField(value = "accuracy_rate")
    private BigDecimal accuracyRate;
    @TableField(value = "report_id")
    private Long reportId;
}
