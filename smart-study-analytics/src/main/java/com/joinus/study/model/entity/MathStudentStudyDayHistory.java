package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 学生学习天数记录
 * @TableName math_student_study_day_history
 */
@TableName(value ="math_student_study_day_history")
@Data
public class MathStudentStudyDayHistory implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private Long studentId;

    /**
     * 学习的日期
     */
    private Date studyDay;

    /**
     * 
     */
    private Date createdAt;

}