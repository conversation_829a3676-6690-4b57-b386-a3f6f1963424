package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@TableName(value = "english_personal_exercise_report")
@Data
@EqualsAndHashCode(callSuper = false)
public class EnglishPersonalExerciseReport extends BaseEntity {
    /**
     * 练习表主键
     */
    private Long personalExerciseId;
    /**
     * 练习题目数量
     */
    private Integer answeredQuestionCount;
    /**
     * 答对题数
     */
    private Integer correctAnswerCount;
    /**
     * 正确率
     */
    private BigDecimal correctPercentage;
    /**
     * 答题时长
     */
    private Integer answerTime;
    /**
     * 学生id
     */
    private Long studentId;
}
