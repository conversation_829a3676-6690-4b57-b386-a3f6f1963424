package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@TableName(value = "english_flow_deputy_question")
@Data
public class EnglishFowDeputyQuestion {

    @TableId(value = "id", type = IdType.INPUT)
    private UUID id;

    @TableField(value = "exam_id")
    private UUID examId;

    /**
     * 题型
     */
    @TableField(value = "type")
    private String type;
    /**
     * 大题编号
     */
    @TableField(value = "question_num")
    private Integer questionNum;

    /**
     * 小题编号
     */
    @TableField(value = "order_no")
    private Integer orderNo;

    /**
     * 分数
     */
    @TableField(value = "score")
    private BigDecimal score;

}
