package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "管理后台-阅读纠错分页查询参数")
public class ReadingPersonalCorrectionPageParam extends PageParam {

    @Schema(description = "姓名")
    private String studentName;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "年级（数字1-9，表示对应的年级）")
    private Integer grade;

    @Schema(description = "学期 1上学期  2下学期")
    private Integer semester;

    @Schema(description = "单元Id ")
    private UUID unitId;

    @Schema(description = "文体")
    private String genre;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "开始时间 格式：2025-04-01")
    private String timeStart;

    @Schema(description = "结束时间 格式：2025-04-10")
    private String timeEnd;
}
