package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import lombok.*;

import java.math.BigDecimal;

@TableName(value = "reading_personal_analysis_report", autoResultMap = true)
@Data
//@SuperBuilder(toBuilder = true)
@Builder
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalAnalysisReport extends BaseEntity {
    /**
     * 知识点总数
     */
    private Integer totalKnowledgePoints;

    /**
     * 弱点数量
     */
    private Integer weakPointCount;

    /**
     * 文体，最多50个字符
     */
    private String genre;

    /**
     * 答对题数
     */
    private Integer correctCount;

    /**
     * 答错题数
     */
    private Integer incorrectCount;

    /**
     * 做题时长（秒）
     */
    private Integer solveTime;

    /**
     * 正确率
     */
    private BigDecimal accuracyRate;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 用户阅读文章ID
     */
    private Long personalPassageId;

    /**
     * 状态 0分析中 1分析完成
     */
    private Integer status;

    /**
     * 查看状态 0未查看 1已查看
     */
    private Integer isView;

    /**
     * 分析结果
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String analysesResult;
}
