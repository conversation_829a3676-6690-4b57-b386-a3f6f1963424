package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.PostgresUUIDTypeHandler;
import lombok.*;

import java.util.UUID;

/**
 * 数学学校试卷关系表
 * @TableName math_school_exam
 */
@TableName(value ="math_school_exam")
@EqualsAndHashCode(callSuper=true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathSchoolExam extends BaseEntity {

    /**
     * 学校ID
     */
    @TableField("school_id")
    private Long schoolId;

    /**
     * 试卷ID
     */
    @TableField(value = "exam_id", typeHandler = PostgresUUIDTypeHandler.class)
    private UUID examId;



}