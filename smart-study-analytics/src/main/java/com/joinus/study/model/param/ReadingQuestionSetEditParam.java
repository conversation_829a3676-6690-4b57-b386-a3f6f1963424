package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "修改套题题目 请求参数")
public class ReadingQuestionSetEditParam {

    @Schema(description = "套题id", required = true, implementation = String.class, example = "")
    @NotNull(message = "套题id不能为空")
    private UUID questionSetId;
    @Schema(description = "审核状态（0-未审核 1-审核通过 2-审核不通过）", required = true, implementation = Integer.class, example = "1")
    @NotNull(message = "审核状态不能为空")
    @Range(min = 0, max = 1, message = "审核状态值不合法（0-未审核 1-审核通过 2-审核不通过）")
    private Integer isAudit; //审核状态（0-未审核 1-已审核）
    @Schema(description = "启用状态（0-禁用 1-启用）", required = true, implementation = Integer.class, example = "1")
    @NotNull(message = "启用状态不能为空")
    @Range(min = 0, max = 1, message = "启用状态值不合法（0-禁用 1-启用）")
    private Integer isEnabled;//启用状态（0-禁用 1-启用）
    @Schema(description = "题目列表", required = true, example = "")
    private List<ReadingQuestionSetItemEditParam> questionList;
}

