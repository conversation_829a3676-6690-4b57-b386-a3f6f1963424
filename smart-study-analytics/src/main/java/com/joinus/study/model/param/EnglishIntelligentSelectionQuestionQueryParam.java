package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "英语-智能选题查询参数")
public class EnglishIntelligentSelectionQuestionQueryParam implements Serializable {
    /**
     * 个人薄弱知识点id
     */
    private Long personalWeakKnowledgePointId;
    /**
     * 所属年级
     */
    @Schema(description = "所属年级 1、一年级 2、二年级 3、三年级 4、四年级 5、五年级 6、六年级 7、七年级 8、八年级 9、九年级 10、十年级 11、十一年级 12、十二年级")
    private Integer grade;
    /**
     * 所属学期
     */
    @Schema(description = "所属学期 1、上学期 2、下学期 (参数传中文内容)")
    private String semester;
    /**
     * 题目难度
     */
    @Schema(description = "题目难度 1、简单 2、中等 3、困难")
    private String difficulty;
}
