package com.joinus.study.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31 18:58
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReadingStudentPassagesQueryParam implements Serializable {
    private Long id;

    private Long studentId;

    private UUID knowledgePointId;

    private UUID unitId;

    private String genre;//文体

    private Long planId;

    private UUID passageId;

    private Integer printRange;//打印范围 0 全部 1 文章题目 2 答案 3 解析

    private List<UUID> weakKnowledgePointIds; // 薄弱知识点

    private List<String> genres;

    private Integer entryType;// 解释见ReadingEntryTypeEnum

    private Integer grade;

    private Integer semester;

    private String textbook;

    private List<UUID> doQuestionIds; // 已做题目ids

    private List<UUID> doSetsIds;

    private List<UUID> unitIds;

    private LocalDate localDate;

    private Integer dailyPassageNumber; // 每日活动目标数量;

    private Long personalPassageId;// 练习ID

    private Long parentId ;
}
