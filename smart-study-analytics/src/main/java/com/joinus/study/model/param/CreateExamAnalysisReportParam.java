package com.joinus.study.model.param;

import com.joinus.study.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 生成考情分析报告参数
 * @Author: anpy
 * @date: 2025/3/17 14:06
 */
@Data
@Builder
@ApiModel("生成考情分析报告参数")
@AllArgsConstructor
@NoArgsConstructor
public class CreateExamAnalysisReportParam {

    @NotNull(message = "试卷ID不能为空")
    @ApiModelProperty("试卷ID")
    private UUID examId;
    @ApiModelProperty("学生ID")
    private Long studentId;
    @ApiModelProperty("试卷数据列表")
    private List<QuestionData> examDataList;
    @ApiModelProperty("家长ID")
    private Long parentId;
    private Long personalExamId;
    @NotNull(message = "教材版本不能为空")
    private PublisherEnum publisher;
    @ApiModelProperty("年级")
    private Integer grade;
    private BookVolumeEnum bookVolume;

    private Long examAnalyzeResultId;

    private String parentName;
    private String telNumber;

    @Data
    @Builder
    @AllArgsConstructor
    public static class QuestionData {
        @ApiModelProperty("题目ID")
        @NotNull(message = "题目ID不能为空")
        private UUID questionId;
        @NotNull(message = "排序号不能为空")
        private Integer sortNo;
        @ApiModelProperty(value = "题目类型", notes = "com.joinus.study.model.enums.QuestionTypeEnum")
        @NotNull(message = "题目类型不能为空")
        private QuestionTypeEnum questionType;
        @ApiModelProperty(value = "结果", notes = "com.joinus.study.model.enums.PersonalExamQuestionResultEnum")
        private PersonalExamQuestionResultEnum result;
    }

}
