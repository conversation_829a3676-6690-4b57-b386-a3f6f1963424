package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import com.joinus.study.model.enums.MathActivityStudentFinishEnum;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import lombok.*;

import java.util.UUID;

@TableName("math_activity_week_unit_student")
@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathActivityWeekUnitStudent extends BaseEntity {
    private Long activityStudentId;
    private Long activityId;
    private Long weekId;
    private Long weekUnitId;
    private Long studentId;
    private MathActivityStudentFinishEnum finishResult; //  完成结果
    private UUID examId;
    private Long examAnalyzeResultId;
    @TableField(exist = false)
    public MathActivityWeekUnitTypeEnum type; // 类型 SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String examIdHistory;
    private Boolean unlocked; // 功能是否解锁
}
