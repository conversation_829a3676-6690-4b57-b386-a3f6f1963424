package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.BookVolumeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import lombok.*;

import java.util.UUID;

/**
 *
 * @TableName personal_exam
 */
@TableName(value ="personal_exam")
@Data
@Builder
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
public class PersonalExam extends BaseEntity {
    /**
     * 学生ID
     */
//    @TableField(value = "student_id",jdbcType = JdbcType.BIGINT,typeHandler = BigIntegerTypeHandler.class)
    private Long studentId;

    /**
     * 试卷ID
     */
    private UUID examId;

    /**
     * examSource
     */
    private String flag;

    private PublisherEnum publisher;

    /**
     * 试卷名称
     */
    private String examName;

    private Integer grade;

    private Integer semester;

    private BookVolumeEnum bookVolume;

}
