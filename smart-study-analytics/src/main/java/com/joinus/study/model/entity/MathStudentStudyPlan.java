package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.UUID;

import lombok.Data;

/**
 * 学生学习计划
 * @TableName math_student_study_plan
 */
@TableName(value ="math_student_study_plan")
@Data
public class MathStudentStudyPlan extends BaseEntity {

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 知识点ID
     */
    private UUID knowledgePointId;

    /**
     * 周数
     */
    private Integer weekNo;

    /**
     * 周内排序号
     */
    private Integer sortNo;

    @TableField(exist = false)
    private Boolean isCompleted;

}