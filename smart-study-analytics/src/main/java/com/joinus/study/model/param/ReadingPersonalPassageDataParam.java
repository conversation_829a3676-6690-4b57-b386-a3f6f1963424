package com.joinus.study.model.param;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 获取用户做题信息参数
 * @date: 2025/3/31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalPassageDataParam implements Serializable {

    private Long personalPassageId;

    private LocalDate startDate;

    private LocalDate endDate;

    private Long studentId;
}
