package com.joinus.study.model.param;

import com.joinus.study.model.enums.ExampleQuestionEnum;
import com.joinus.study.model.enums.OssEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@ApiModel(value = "单题解析参数")
@Data
public class SolveQuestionFromImgParam implements Serializable {
    @ApiModelProperty(value = "questionId",notes = "questionId")
    private UUID questionId;
    @ApiModelProperty(value = "学生id",notes = "学生id")
    private Long studentId;
    @ApiModelProperty(value = "图片key",notes = "图片key")
    private List<String> objectNames;
    @ApiModelProperty(value = "oss类型",notes = "oss类型")
    private OssEnum ossEnum;
    @ApiModelProperty(value = "排序号",notes = "排序号")
    private Integer sortNo;
    private Long studyRecordQuestionId;

    private String prompt;

    private String questionText;

    /**
     * 存储目标，指定将流式响应内容存储到哪个表
     * 可选值：ai_chat_record, user_log, custom_table 等
     */
    private String storageTarget;

    /**
     * 自定义参数，可用于传递额外信息
     */
    private String customParam;

    /**
     * 扩展参数，可以存储任意键值对
     */
    private Map<String, Object> extraParams;


}
