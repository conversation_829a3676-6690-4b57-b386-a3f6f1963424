package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.UUID;

import com.joinus.study.config.JsonbTypeHandler;
import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.OverallScoreEnum;
import lombok.*;
import org.apache.ibatis.type.EnumTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * 
 * @TableName exam_analyze_result
 */
@TableName(value ="exam_analyze_result")
@Data
@Builder
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ExamAnalyzeResult extends BaseEntity {
    /**
     * 试卷ID
     */
    private UUID examId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 处理结果 PENDING-未处理 IN_PROGRESS-处理中 FINISHED-完成 
     */
    @TableField(value = "result",jdbcType = JdbcType.VARCHAR,typeHandler = EnumTypeHandler.class)
    private ExamAnalyzeResultEnum result;

    /**
     * 考试成绩 EXCELLENT-优、GOOD-良、FAIR-差
     */
    private OverallScoreEnum overallScore;

    /**
     * 正确率
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String correctRate;

    /**
     * 百分位排名
     */
    private BigDecimal percentile;

    /**
     * 知识点总数
     */
    private Integer totalKnowledgePoints;

    /**
     * 已掌握知识点数
     */
    private Integer masteredKnowledgePoints;

    /**
     * 薄弱知识点数
     */
    private Integer weakKnowledgePoints;

    private Long personalExamId;
    private Long parentId;
    /**
     * 考点盲区考察范围
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String manualExamScope;

    @TableField(exist = false)
    private Long classId;
    @TableField(exist = false)
    private Long gradeId;
}