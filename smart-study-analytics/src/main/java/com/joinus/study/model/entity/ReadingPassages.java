package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31 18:05
 **/
@TableName(value = "reading_passages")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPassages implements Serializable {
    private UUID id;
    private Date updatedAt;
    private Date createdAt;
    private Date deletedAt;
    private UUID unitId;
    private String genre;
    private String title;
    private String content;
    private int difficulty;
    private String source;
    private UUID promptTemplateId;
    /**
     * 是否启用 1:启用 0:挂起
     */
    private Integer isEnabled;
    /**
     * 审核状态 0-未审核 1-审核通过 2-审核不通过
     */
    private Integer isAudit;
}
