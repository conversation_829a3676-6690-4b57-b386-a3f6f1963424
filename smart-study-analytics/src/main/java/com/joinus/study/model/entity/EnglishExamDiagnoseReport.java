package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/3 14:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("english_exam_diagnose_report")
public class EnglishExamDiagnoseReport {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "record_id")
    private Long recordId;
    @TableField(value = "exam_id")
    private UUID examId;
    @TableField(value = "created_at")
    private LocalDateTime createdAt;
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
    @TableField(value = "summary")
    private String summary;
    @TableField(value = "code")
    private String code;
    @TableField(value = "question_number")
    private Integer questionNumber;
    @TableField(value = "wrong_question_number")
    private Integer wrongQuestionNumber;
    @TableField(value = "right_question_number")
    private Integer rightQuestionNumber;
    @TableField(value = "accuracy_rate")
    private BigDecimal accuracyRate;
    @TableField(value = "knowledge_point_number")
    private Integer knowledgePointNumber;
    @TableField(value = "weak_knowledge_point_number")
    private Integer weakKnowledgePointNumber;
    @TableField(value = "master_knowledge_point_number")
    private Integer masterKnowledgePointNumber;
}
