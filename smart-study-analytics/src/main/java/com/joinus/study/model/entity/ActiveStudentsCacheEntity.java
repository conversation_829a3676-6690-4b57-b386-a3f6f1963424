package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.OffsetDateTime;

@TableName("active_students_cache")
@Data
public class ActiveStudentsCacheEntity implements Serializable {
    private Long schoolId;
    private String schoolName;
    private Long studentId;
    private String studentName;
    private Long classId;
    private String className;
    private Long gradeId;
    private String gradeName;
    private String learningPeriod;
    private Integer enrollmentYear;
    private String studentImg;
    private OffsetDateTime lastSyncedAt;
}
