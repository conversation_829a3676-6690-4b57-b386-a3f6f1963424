package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 语文阅读-兑换权益活动码表
 */
@TableName("reading_personal_equity_activity_code")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ReadingPersonalEquityActivityCode extends BaseEntity {

    @ApiModelProperty("活动ID")
    @TableField("activity_id")
    private Long activityId;

    @ApiModelProperty("兑换码（唯一）")
    private String code;

    @ApiModelProperty("使用人")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty("使用时间")
    @TableField("used_at")
    private Date usedAt;

    @ApiModelProperty("状态：1-未使用, 2-已使用, 3-已作废")
    private Integer status;

    @ApiModelProperty("使用者手机号")
    @TableField("user_phone")
    private String userPhone;
}
