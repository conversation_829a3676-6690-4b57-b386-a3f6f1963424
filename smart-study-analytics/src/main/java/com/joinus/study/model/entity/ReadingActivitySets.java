package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.UUID;

/**
 * @description: 活动套题关联表
 * @author: lifengxu
 * @date: 2025/6/20 15:36
 */
@TableName("reading_activity_sets")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReadingActivitySets extends BaseEntity{

    private Long activityId;

    private UUID setsId;

}
