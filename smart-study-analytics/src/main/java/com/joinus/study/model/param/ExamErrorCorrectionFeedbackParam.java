package com.joinus.study.model.param;

import com.joinus.study.model.entity.ExamErrorCorrectionFeedback;
import com.joinus.study.model.enums.BookVolumeEnum;
import lombok.*;


/**
 * 试卷纠错参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ExamErrorCorrectionFeedbackParam extends ExamErrorCorrectionFeedback {

    /**
     * 个人试卷表id
     */
    private Long personalExamId;

    private Long examAnalyzeResultId;

    private Integer grade;
    private BookVolumeEnum bookVolume;

}
