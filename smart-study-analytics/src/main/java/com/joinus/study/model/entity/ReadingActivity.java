package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

@TableName("reading_activity")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReadingActivity extends BaseEntity {

    private String name;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;

    private Integer dailyPassageNum;// 日推题数量

    private String createUserName;// 创建人
}
