package com.joinus.study.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("根据知识点和题型生成题目参数")
@Data
public class CreateQuestionByKnowledgeParam {
    @ApiModelProperty("题目数量")
    private int questionCount;
    @ApiModelProperty("知识点参数列表")
    private List<KnowledgePointParamsDTO> knowledgePointParams;

    @NoArgsConstructor
    @Data
    public static class KnowledgePointParamsDTO {
        @ApiModelProperty("知识点id")
        private UUID knowledgePointId;
        @ApiModelProperty("题型id列表")
        private List<UUID> questionTypeIds;
    }
}
