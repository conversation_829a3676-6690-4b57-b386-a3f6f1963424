package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "综合训练建议分析回调结果请求参数明细")
public class ReadingAISuggestionItemParam implements Serializable {

    @NotBlank(message = "建议标题不能为空")
    @Schema(description = "建议标题", required = true)
    private String suggestionTitle;

    @NotBlank(message = "内容不能为空")
    @Schema(description = "内容", required = true)
    private String content;
}
