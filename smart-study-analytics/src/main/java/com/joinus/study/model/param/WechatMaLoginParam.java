package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WechatMaLoginParam implements Serializable {

    @NotNull(message = "openid不能为空")
    @Schema(description = "openid", required = true)
    private String openid;

    @NotNull(message = "手机号码不能为空")
    @Schema(description = "手机号码", required = true)
    private String mobile;

}
