package com.joinus.study.model.param;

import com.joinus.study.model.enums.ExampleQuestionEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.PhotoQuestionTypeEnum;
import com.joinus.study.model.enums.QuestionTypeEnum;
import com.joinus.study.model.vo.CoordinatePoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "根据图片和坐标点切图参数")
public class CutImageFromPositionsParam implements Serializable {

    @ApiModelProperty(value = "oss枚举")
    private OssEnum ossEnum;
    @ApiModelProperty(value = "坐标点，一道题的坐标点。如果有图片，则是多个坐标点，如果没有图片，则是单个坐标点")
    private List<PositionsDTO> positions;
    private UUID questionId;
    private Long studyRecordQuestionId;
    private Long studyRecordId;
    private int sortNo = 0;

    @NoArgsConstructor
    @Data
    @ApiModel(description = "坐标点参数")
    public static class PositionsDTO {
        @ApiModelProperty(value = "图片地址")
        private String ossKey;
        @ApiModelProperty(value = "坐标点")
        private List<CoordinatePoint> position;

    }
}