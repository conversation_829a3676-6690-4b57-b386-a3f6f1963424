package com.joinus.study.model.param;

import com.joinus.study.model.enums.BookVolumeEnum;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.PublisherEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 专项训练试卷入库参数
 * @Author:  anpy
 * @date:  2025/5/8 09:30
 */
@Data
public class SpecializedTrainingUpdateExamParam {

    @ApiModelProperty("试卷id")
    private UUID examId;
    @ApiModelProperty("pdf文件key")
    private String pdfUrl;
    @NotNull(message = "学生id不能为空")
    @ApiModelProperty("学生id")
    private Long studentId;
    @ApiModelProperty("试卷类型")
    @NotBlank(message = "试卷类型不能为空")
    private ExamSourceType examSource;

    private Long weekUnitStudentId;

    private PublisherEnum publisher;

    private Integer grade;
    private Integer semester;
    private UUID catalogNodeId;//学练测目录节点ID
    private String testType;//学练测类型  SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST
    private BookVolumeEnum bookVolume;
}
