package com.joinus.study.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "英语-试卷诊断-提交生成报告")
public class EnglishExamAnalysisSubmitReportParam {

    @NotNull(message = "分析记录ID不能为空")
    @ApiModelProperty(value = "分析记录ID")
    private Long id;

    @NotNull(message = "学生ID不能为空")
    @ApiModelProperty(value = "学生ID", required = true)
    private Long studentId;

    @NotNull(message = "试卷ID不能为空")
    @ApiModelProperty(value = "试卷ID", required = true)
    private UUID examId;

    @NotNull(message = "所属年级不能为空")
    @ApiModelProperty(value = "所属年级", required = true)
    private Integer grade;

    @NotEmpty(message = "错题集不能为空")
    @ApiModelProperty(value = "错题集", required = true)
    private List<EnglishExamFalseQuestionNumberParam> questions;
}
