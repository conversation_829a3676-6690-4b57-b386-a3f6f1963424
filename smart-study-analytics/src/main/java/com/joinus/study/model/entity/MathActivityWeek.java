package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.MathActivityWeekTypeEnum;
import com.joinus.study.model.enums.PublisherEnum;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/13 18:52
 **/
@TableName("math_activity_week")
@Data
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathActivityWeek extends BaseEntity{

    private Long activityId;
    private String name;
    private Integer sortNo;
    private PublisherEnum publisher;
    private Integer grade;
    private MathActivityWeekTypeEnum type; // 步骤类型：REVIEW， PREVIEW
}
