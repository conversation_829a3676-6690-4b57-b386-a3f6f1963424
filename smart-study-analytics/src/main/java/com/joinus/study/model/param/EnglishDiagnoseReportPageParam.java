package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "英语-诊断报告分页参数")
public class EnglishDiagnoseReportPageParam extends PageParam {
    @ApiModelProperty(value = "学生id", required = true)
    private Long studentId;
}
