package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "英语-薄弱知识点新增参数")
public class EnglishPersonalWeakKnowledgePointCreateParam {

    @Schema(description = "学生id")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "知识点ID,来自AI库")
    @NotNull(message = "知识点ID不能为空")
    private UUID knowledgePointId;

    @Schema(description = "知识点名称,来自AI库")
    @NotEmpty(message = "知识点名称不能为空")
    private String knowledgePointName;

    @Schema(description = "试卷诊断题目数量(知识点出现次数)")
    @NotNull(message = "试卷诊断题目数量(知识点出现次数)不能为空")
    private Integer answeredQuestionCount;

    @Schema(description = "答错题目数量(知识点出错次数)")
    @NotNull(message = "答错题目数量(知识点出错次数)不能为空")
    private Integer wrongAnswerCount;

    @Schema(description = "试卷诊断记录id")
    @NotNull(message = "试卷诊断记录id不能为空")
    private Long diagnoseRecordId;
}
