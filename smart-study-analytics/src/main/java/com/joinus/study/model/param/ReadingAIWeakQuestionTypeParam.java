package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "题型分析回调结果请求参数")
public class ReadingAIWeakQuestionTypeParam implements Serializable {

    @NotBlank(message = "业务id不能为空")
    @Schema(description = "业务id，回调时用于确认哪次请求", example = "5a14bfdf-9a84-4579-8111-ef4a755d96f5", required = true)
    private String id;

    @NotNull(message = "分析类型不能为空")
    @Schema(description = "分析类型：1、单次测试，2、周报或月报", example = "1", required = true)
    private Integer type;

    @NotNull(message = "题型分析列表不能为空")
    @Schema(description = "题型分析列表", required = true)
    private List<ReadingAIWeakQuestionTypeItemParam> items;
}
