package com.joinus.study.model.param;

import com.joinus.study.model.enums.BookVolumeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("根据知识点和题型生成题目参数")
@Data
public class CreateQuestionByKnowledgeParamV2 {
    @ApiModelProperty("题目数量")
    private int questionCount;
    @ApiModelProperty("知识点参数列表")
    private List<UUID> knowledgePointIds;
    @ApiModelProperty("题型id列表")
    private List<UUID> questionTypeIds;
    private UUID examId;
    @ApiModelProperty("是否真题")
    private boolean enablePastExamPapers;

    private UUID chapterId;
    private UUID sectionId;
    @ApiModelProperty("教参版本")
    private String publisher;
    @ApiModelProperty("年级")
    private Integer grade;
    @ApiModelProperty("学期")
    private Integer semester;
    @ApiModelProperty("册别")
    private BookVolumeEnum bookVolume;

}
