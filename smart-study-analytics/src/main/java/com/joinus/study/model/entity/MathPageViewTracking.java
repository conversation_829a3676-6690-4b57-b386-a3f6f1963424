package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 页面访问记录表
 * @TableName math_page_view_tracking
 */
@TableName(value ="math_page_view_tracking")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathPageViewTracking implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联ID
     */
    private Long userId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 访问路径
     */
    private String fullPath;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 访问时间
     */
    private Date createdAt;

}