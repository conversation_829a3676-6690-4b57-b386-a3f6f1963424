package com.joinus.study.model.param;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 获取用户做题信息参数
 * @date: 2025/4/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class ReadingPersonalKnowledgePointDataParam implements Serializable {

    private Long personalPassageId;

    private LocalDate startDate;

    private LocalDate endDate;

    private Integer result;

    private Long studentId;

    private Long knowledgePointId;

    private String knowledgePointName;

}
