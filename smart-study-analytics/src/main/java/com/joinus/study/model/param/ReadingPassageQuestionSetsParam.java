package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "语文阅读-套题")
public class ReadingPassageQuestionSetsParam  extends PageParam {

    private UUID passageId;//文章id

    private UUID questionSetId;//套题id

    private Integer isAudit; //审核状态（0-未审核 1-已审核）

    private Integer isEnabled;//启用状态（0-禁用 1-启用）
}
