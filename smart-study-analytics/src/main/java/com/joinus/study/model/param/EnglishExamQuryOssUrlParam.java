package com.joinus.study.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "英语-试卷诊断-查询试卷图片参数")
public class EnglishExamQuryOssUrlParam {

    @NotEmpty(message = "图片不能为空")
    @ApiModelProperty(value = "图片OSS Key列表", required = true)
    private List<String> ossKeyList;
}
