package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@TableName(value = "english_personal_exercise")
@Data
@EqualsAndHashCode(callSuper = false)
public class EnglishPersonalExercise extends BaseEntity {
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 学生薄弱知识点id
     */
    private Long personalWeakKnowledgePointId;
    /**
     * 题目难度 1、简单 2、中等 3、困难
     */
    private Integer difficulty;
    /**
     * 所属年级 1、一年级 2、二年级 3、三年级
     */
    private Integer grade;
    /**
     * 所属学期 1、上学期 2、下学期
     */
    private String semester;
    /**
     * 暂停时间
     */
    private Date pauseAt;
    /**
     * 结束时间
     */
    private Date endAt;
    /**
     * 状态 0、初始 1、暂停 2、提交 3、批改完成
     */
    private Integer status;
    /**
     * 作答方式：1、在线作答
     */
    private Integer answerMethod;
    /**
     * 答题时长
     */
    private Integer answerTime;
}
