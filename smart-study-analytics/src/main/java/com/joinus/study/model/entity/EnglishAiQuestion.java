package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

@TableName(value = "english_ai_question")
@Data
@EqualsAndHashCode(callSuper = false)
public class EnglishAiQuestion {
    /**
     * 主键id,来自AI库
     */
    private UUID id;
    /**
     * 题目内容
     */
    private String content;
    /**
     * 题型
     */
    private String type;
    /**
     * 难度 1、一星 2、二星 3、三星
     */
    private Integer difficulty;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 是否上架
     */
    private Integer isEnabled;
    /**
     * 答案
     */
    private String answer;
    /**
     * 题目解析
     */
    private String analysis;
    /**
     * 年级 1、一年级 2、二年级 3、三年级
     */
    private Integer grade;
    /**
     * 学期
     */
    private String semester;
    /**
     * 标题
     */
    private String title;
    /**
     * 选项 [{"key": "A", "value": "when"}, {"key": "B", "value": "that"}, {"key": "C", "value": "if"}, {"key": "D", "value": "where"}]
     */
    private String options;

    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;

}
