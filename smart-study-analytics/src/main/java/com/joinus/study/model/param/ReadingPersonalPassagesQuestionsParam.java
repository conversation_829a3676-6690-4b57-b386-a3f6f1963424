package com.joinus.study.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description: 提交练习文章参数
 * @date: 2025/3/28 10:37
 */
@Data
public class ReadingPersonalPassagesQuestionsParam implements Serializable {

    @ApiModelProperty(value = "答题文章ID")
    private Long id;
    @ApiModelProperty(value = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @ApiModelProperty(value = "文章库内ID")
    @NotNull(message = "文章库内ID不能为空")
    private UUID passageId;
    @ApiModelProperty(value = "计划id")
    private Long planId;
    @ApiModelProperty(value = "练习问题答案 - 错题标注时 所有题目必须都传 非错题resultCode =1 错题 =0")
    @NotNull(message = "练习问题答案不能为空")
    List<QuestionsParam> questions;
    @ApiModelProperty(value = "耗时")
    @NotNull(message = "练习耗时不能为空")
    private Integer timeSpent;

    @ApiModelProperty(value = "入口类型：1阅读训练;2定向爆破;3练习计划")
    @NotNull(message = "入口类型不能为空")
    private Integer entryType;
    @ApiModelProperty(value = "套题id")
    private UUID setsId;
}
