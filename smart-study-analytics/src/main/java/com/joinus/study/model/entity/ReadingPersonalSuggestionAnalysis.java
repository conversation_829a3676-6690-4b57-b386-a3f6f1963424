package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@TableName("reading_personal_suggestion_analysis")
@Data
//@SuperBuilder(toBuilder = true)
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalSuggestionAnalysis extends BaseEntity {

    @ApiModelProperty(value = "报告id")
    private Long reportId;

    @ApiModelProperty(value = "建议标题")
    private String suggestionTitle;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "分析类型：1训练报告 2周期报告")
    private Integer analysisType;
}
