package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * @TableName reading_personal_print
 */
@TableName(value = "reading_personal_print")
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class ReadingPersonalPrint implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;
    private Date createdAt;
    private Date updatedAt;
    private UUID passageId; //文章ID
    private String questionIds; // 题目 id 逗号 间隔
    private String objectKey;// 打印存储的key
    private Integer entryType;
    private UUID setsId;
    private Long planId;
    private Long studentId;
}