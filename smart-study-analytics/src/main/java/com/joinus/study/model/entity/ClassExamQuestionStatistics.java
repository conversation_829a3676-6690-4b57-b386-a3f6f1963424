package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * @Description: 班级题目正确率统计表
 * @Author: anpy
 * @date: 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_exam_question_statistics")
public class ClassExamQuestionStatistics {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 试卷ID
     */
    private UUID examId;
    
    /**
     * 考试场次
     * 用于区分同一班级同一试卷的不同考试场次
     */
    @TableField(exist = false)
    private String examSession;
    
    /**
     * 题目ID
     */
    private UUID questionId;
    
    /**
     * 题目类型
     */
//    private QuestionTypeEnum questionType;
    
    /**
     * 班级题目正确率
     */
    private BigDecimal correctRate;
    
    /**
     * 正确人数
     */
    private Integer correctCount;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 删除时间
     */
    private Date deletedAt;
    
    /**
     * 班级考试ID
     */
    private Long classExamId;
    private Integer sortNo;
    private BigDecimal gradeCorrectRate;
}
