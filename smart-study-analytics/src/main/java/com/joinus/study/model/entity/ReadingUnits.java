package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * @Description reading_units
 * <AUTHOR>
 * @Date 2025/4/1 17:07
 **/
@TableName(value ="reading_units")
@Data
@EqualsAndHashCode(callSuper=false)
public class ReadingUnits  implements Serializable {
    @ApiModelProperty(value = "单元id")
    private UUID id;
    @ApiModelProperty(hidden = true)
    private Date createdAt;
    @ApiModelProperty(hidden = true)
    private Date updatedAt;
    @ApiModelProperty(hidden = true)
    private Date deletedAt;
    @ApiModelProperty(value = "学期")
    private Integer grade;
    @ApiModelProperty(hidden = true)
    private Integer semester;
    @ApiModelProperty(value = "单元名称")
    private String name;
    @ApiModelProperty(value = "单元排序")
    private Integer orderNo;
    @ApiModelProperty(hidden = true)
    private String outline;
    @ApiModelProperty(hidden = true)
    private String textbook;
    @ApiModelProperty(value = "是否免费")
    @TableField(exist = false)
    private Boolean isFree;

    @ApiModelProperty(value = "练习次数")
    @TableField(exist = false)
    private Integer practiceNum;
    @ApiModelProperty(value = "练习题数")
    @TableField(exist = false)
    private Integer questionNum;
    @ApiModelProperty(value = "正确题数")
    @TableField(exist = false)
    private Integer accuracyQuestionNum;
    @ApiModelProperty(value = "正确率")
    @TableField(exist = false)
    private Integer accuracyRate;
}
