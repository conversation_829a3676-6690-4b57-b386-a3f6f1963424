package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台-阅读题库修改参数")
public class ReadingPassagesUpdateParam implements Serializable {

    @Schema(description = "主键id", type = "array", example = "[\"550e8400-e29b-41d4-a716-************\"]")
    @NotNull(message = "id不能为空")
    private UUID id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;
}
