package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学生参与活动历史记录表
 * @TableName math_activity_week_unit_student_history
 */
@TableName(value ="math_activity_week_unit_student_history")
@Data
@EqualsAndHashCode(callSuper=true)
public class MathActivityWeekUnitStudentHistory extends BaseEntity {
    /**
     * 
     */
    private Long id;

    /**
     * math_activity_week_unit_student的id
     */
    private Long weekUnitStudentId;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 完成时间
     */
    private Date finishedAt;

    /**
     * 
     */
    private Date createdAt;

    /**
     * 
     */
    private Date updatedAt;

    /**
     * 
     */
    private Date deletedAt;

}