package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.PersonalExamQuestionResultEnum;
import com.joinus.study.model.enums.QuestionTypeEnum;
import lombok.*;

import java.util.UUID;

/**
 * 
 * @TableName personal_exam_question
 */
@TableName(value ="personal_exam_question")
@Data
@Builder
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
public class PersonalExamQuestion extends BaseEntity {
    /**
     * 个人试卷ID
     */
    private Long personalExamId;

    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 题目类型: MULTIPLE_CHOICE-选择题、FILL_BLANK-填空题、FREE_RESPONSE-解答题
     */
    private QuestionTypeEnum questionType;

    /**
     * 做题结果：correct-正确，mistake-错误
     */
    private PersonalExamQuestionResultEnum result;
    private Integer sortNo;
}