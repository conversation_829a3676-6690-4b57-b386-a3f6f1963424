package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import com.joinus.study.model.enums.SubjectEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.ibatis.type.EnumTypeHandler;
import org.apache.ibatis.type.JdbcType;

import javax.validation.constraints.NotNull;

/**
 * 
 * @TableName study_record
 */
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="study_record")
@Data
@EqualsAndHashCode(callSuper=false)
@Builder
public class StudyRecord extends BaseEntity {
    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 照片id
     */
    private String ossUrl;

    /**
     * 学科：math-数学，chinese-language-语文，english-英语
     */
    @TableField(typeHandler = EnumTypeHandler.class)
    private SubjectEnum subject;
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String positions;
    @ApiModelProperty(value = "题目数量")
    private Integer questionsCount;
}