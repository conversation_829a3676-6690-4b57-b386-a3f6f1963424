package com.joinus.study.model.param;

import com.joinus.study.model.enums.QuestionAnswerFeedbackTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/3/17 16:57
 */
@Data
public class QuestionAnswerFeedbackParam implements Serializable {
   @ApiModelProperty(value = "学生ID")
   @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @ApiModelProperty(value = "题目ID")
    @NotNull(message = "题目ID不能为空")
    private UUID questionId;

    @ApiModelProperty(value = "答案")
    private String questionAnswer;
    @ApiModelProperty(value = "答案的内容")
    private String questionAnswerContent;

    @ApiModelProperty(value = "反馈类型：upvote_count-点赞，problem_feedback-问题反馈，cancel_upvote_count-取消点赞")
    private QuestionAnswerFeedbackTypeEnum type;

    @ApiModelProperty(value = "反馈内容")
    private String content;

    @ApiModelProperty(value = "反馈建议")
    private String suggestion;
}
