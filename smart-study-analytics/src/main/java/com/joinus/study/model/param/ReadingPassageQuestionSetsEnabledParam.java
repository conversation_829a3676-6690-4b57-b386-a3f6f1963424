package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "语文阅读-套题状态")
public class ReadingPassageQuestionSetsEnabledParam {

    @Schema(description = "套题id", required = true, implementation = String.class, example = "550e8400-e29b-41d4-a716-************")
    private UUID questionSetId;//套题id

    @Schema(description = "启用状态（0-禁用 1-启用）", required = true, implementation = Integer.class, example = "1")
    private Integer isEnabled;//启用状态（0-禁用 1-启用）
}
