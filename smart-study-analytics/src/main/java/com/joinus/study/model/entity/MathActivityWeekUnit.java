package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/13 18:55
 **/
@TableName("math_activity_week_unit")
@Data
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathActivityWeekUnit extends BaseEntity {
    private Long weekId;
    private Integer sortNo;
    private UUID sectionId;
    private String sectionName;
    private UUID chapterId;
    private String chapterName;
    private UUID bookId;
    private MathActivityWeekUnitTypeEnum type; //类型  SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST
}
