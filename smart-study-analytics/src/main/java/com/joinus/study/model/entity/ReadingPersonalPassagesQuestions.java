package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 
 * @TableName reading_personal_passage_questions
 */
@TableName(value ="reading_personal_passage_questions")
@Data
@EqualsAndHashCode(callSuper=false)
public class ReadingPersonalPassagesQuestions extends BaseEntity {

    private Long personalPassageId;//reading_personal_passages 对应 ID

    private UUID questionId; // 题目ID

    private String questionType; // 题目类型

    private BigDecimal result; // 做题结果

    private String userAnswer;//用户答题内容

    private Integer isBlocked;// 对用户是否屏蔽 1 屏蔽 0 不屏蔽

    private Integer isErrorCorrection;
    private Integer isFeedback;

    /**
     * 答题题号 1、第一题 2、第二题 3、第三题
     */
    private Integer questionNo;

    private String answerAdvice;
    private String referenceAnswer;
}