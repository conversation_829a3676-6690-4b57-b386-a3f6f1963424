package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 语文阅读-学生权益表
 */
@TableName("reading_personal_equity_student")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReadingPersonalEquityStudent extends BaseEntity {

    @TableField("student_id")
    @ApiModelProperty("学生id")
    private Long studentId;

    @TableField("start_date")
    @ApiModelProperty("开始时间")
    private Date startDate;

    @TableField("end_date")
    @ApiModelProperty("结束时间")
    private Date endDate;

    @ApiModelProperty("状态：1正常、2已过期")
    private Integer status;
}
