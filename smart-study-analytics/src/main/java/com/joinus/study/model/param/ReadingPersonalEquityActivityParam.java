package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "兑换码兑换")
public class ReadingPersonalEquityActivityParam {

    @Schema(description = "学生id")
    @NotNull(message = "学生id不能为空")
    private Long studentId;

    @Schema(description = "学生姓名")
    @NotNull(message = "学生姓名不能为空")
    private String studentName;

    @Schema(description = "兑换码")
    @NotNull(message = "兑换码不能为空")
    private String code;

    @Schema(description = "手机号")
    @NotNull(message = "用户手机号不能为空")
    private String phone;

}
