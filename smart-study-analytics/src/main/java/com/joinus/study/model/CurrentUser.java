package com.joinus.study.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 当前用户信息类
 * 包含用户基本信息和权限信息
 */
@Data
public class CurrentUser implements Serializable {
    private static final long serialVersionUID = 1L;

    // 用户基本信息
    private Long userId;      // 用户ID
    private Long studentId;   // 学生ID
    private String username;  // 用户名
    private boolean admin;    // 是否是管理员
    private boolean isMathMember; // 是否是数学会员
    
    // 用户角色和权限信息
    private String role;                  // 0老师，1家长
    private boolean schoolManager;        // 是否是学校管理员
    private boolean gradeManager;         // 是否是年级管理员
    private boolean dormitoryManager;     // 是否是宿舍管理员
    private List<Long> schoolManagerClassIds;  // 学校管理员的所有管辖班级
    private List<Long> gradeManagerClassIds;   // 年级管理员的所有管辖班级
    private Boolean isSystemUser;         // 是否是体制内用户
    private Boolean isChinaMobile;        // 是否中国移动用户
}
