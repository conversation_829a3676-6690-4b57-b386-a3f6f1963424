package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import lombok.*;

import java.util.UUID;

/**
 * 
 * @TableName study_record_question
 */
@TableName(value ="study_record_question")
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StudyRecordQuestion extends BaseEntity {
    /**
     * 学习记录id
     */
    private Long studyRecordId;

    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 题目在本次学习记录的顺序，从1开始
     */
    private Integer sortNo;

    /**
     * 答案
     */
    private String questionAnswer;

    /**
     * 答案解析
     */
    private String questionAnswerContent;

    /**
     * 难度：星级
     */
    private Integer difficulty;

    /**
     * 举一反三题目ID
     */
    private UUID derivedQuestionId;

    @TableField(typeHandler = JsonbTypeHandler.class)
    private String knowledgePoint;

    private String ossKeys;
    /**
     * 当前学习记录最终结果：1：坐标、2：切题、3：解析、4：知识点
     */
    private String type;
}