package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31 18:08
 **/
@TableName(value = "reading_passage_questions")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPassageQuestions implements Serializable {
    private UUID id;
    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;
    private UUID passageId;
    private String questionType;
    private String content;
    private int difficulty;
    private int orderNo;
    private String source;
    private Integer isEnabled;
    /**
     * 审核状态 0-未审核 1-审核通过 2-审核不通过
     */
    private Integer isAudit;
    private String questionUiType;
}
