package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@TableName(value = "english_personal_exercise_question")
@Data
@EqualsAndHashCode(callSuper = false)
public class EnglishPersonalExerciseQuestion extends BaseEntity {
    /**
     * 练习做题表主键id
     */
    private Long personalExerciseId;
    /**
     * 题目ID,来自AI库
     */
    private UUID questionId;
    /**
     * 题型,来自AI库
     */
    private String questionType;
    /**
     * 参考答案,来自AI库
     */
    private String referenceAnswer;
    /**
     * 题目解析,来自AI库
     */
    private String questionAnalysis;
    /**
     * 知识点名称,来自AI库
     */
    private String knowledgePointName;
    /**
     * 做题结果：1-正确，0-错误
     */
    private Integer result;
    /**
     * 题目序号
     */
    private Integer questionNo;
    /**
     * 作答答案
     */
    private String userAnswer;
    /**
     * 是否纠错 1、是 0、否
     */
    private Integer isErrorCorrection;
}
