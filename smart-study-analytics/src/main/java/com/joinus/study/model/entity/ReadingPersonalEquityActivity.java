package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 语文阅读-兑换权益活动表
 */
@TableName(value = "reading_personal_equity_activity", autoResultMap = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ReadingPersonalEquityActivity extends BaseEntity {

    @ApiModelProperty("活动标题")
    private String activityTitle;

    @ApiModelProperty("批次名称")
    private String batchName;

    @ApiModelProperty("兑换码数量")
    private Integer codeCount;

    @TableField("start_date")
    @ApiModelProperty("活动开始日期")
    private Date startDate;

    @TableField("end_date")
    @ApiModelProperty("活动结束日期")
    private Date endDate;

    @TableField("equity_days")
    @ApiModelProperty("权益天数")
    private Integer equityDays;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("使用须知")
    private String activityNotice;

    @ApiModelProperty("活动状态 (1:正常,2:失效)")
    private Integer status;

    @ApiModelProperty("科目1:语文,2:数学,4:英语")
    private Integer subject;
}
