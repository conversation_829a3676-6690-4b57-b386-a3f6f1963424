package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

/**
 * @TableName reading_personal_passages
 */
@TableName(value = "reading_personal_passages")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPersonalPassages extends BaseEntity {

    private Long studentId;//学生ID

    private UUID passageId; //文章ID

    private Date pauseAt; // 暂停时间

    private Date endAt; // 答题结束时间

    private Integer status;//状态

    private Long planId;//计划ID

    private Integer answerMethod;//作答方式：1在线作答 2拍照解析

    private String knowledgePoints; // 知识点

    private String pdfUrl;

    /**
     * 是否弃用 1、是 0、否
     */
    private Integer isDiscard;

    /**
     * 入口类型：1阅读训练;2定向爆破;3练习计划
     */
    private Integer entryType;

    private UUID setsId;

    private Integer timeSpent;
}