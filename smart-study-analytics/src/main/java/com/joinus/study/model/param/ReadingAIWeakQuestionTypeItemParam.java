package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(description = "题型分析回调结果请求参数明细")
public class ReadingAIWeakQuestionTypeItemParam implements Serializable {
    @NotNull(message = "题型不能为空")
    @Schema(description = "题型", required = true)
    private String questionType;

    @NotNull(message = "建议不能为空")
    @Schema(description = "建议", required = true)
    private String suggestion;

    @NotNull(message = "干预方案不能为空")
    @Schema(description = "干预方案", required = true)
    private String interventionPlan;
}
