package com.joinus.study.model.param;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台-阅读题库题目修改参数")
public class ReadingPassageQuestionsUpdateParam {

    @Schema(description = "主键id", example = "[\"550e8400-e29b-41d4-a716-************\"]")
    @NotNull(message = "id不能为空")
    private UUID id;

  /*  @Schema(description = "题型", implementation = String.class, example = "判断题")
    private String questionType;*/

    @Schema(description = "题目内容")
    private String content;

    @ApiModelProperty(value = "选择题选项 注意需要提交所有选项 以key-value形式传参 ")
    private JSONArray options;

    @Schema(description = "答案")
    private String answer;

    @Schema(description = "答案解析")
    private String answerContent;

   /* @Schema(description = "题目知识点", implementation = String.class, example = "春眠不觉晓")
    private List<UUID> knowledgePointIds;*/
}
