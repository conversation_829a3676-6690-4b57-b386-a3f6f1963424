package com.joinus.study.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * @Description 打印相关生成返回html参数
 * <AUTHOR>
 * @Date 2025/4/10 16:58
 **/
@Data
public class ReadingPersonalPrintParam implements Serializable {
    @ApiModelProperty(value = "练习id 从列表打印必传")
    private Long id;
    @ApiModelProperty(value = "文章id")
    private UUID passageId;
    @ApiModelProperty(value = " 打印题目 列表")
    private List<UUID> questionIds; // 打印题目
    @ApiModelProperty(value = "打印预览html ")
    private String htmlContent;
    @ApiModelProperty(value = "打印范围 0 全部 1 文章题目 2 答案 3 解析 ")
    private Integer printRange;
    @ApiModelProperty(value = "入口类型：1阅读训练;2定向爆破;3练习计划;4暑期强化训练;5暑期巩固练习")
    private Integer entryType;
    @ApiModelProperty(value = "套题id")
    private UUID setsId;
    @ApiModelProperty(value = "套题id")
    private Long planId;
    @ApiModelProperty(value = "学生id")
    private Long studentId;
    @ApiModelProperty(value = "家长id")
    private Long parentId;
}
