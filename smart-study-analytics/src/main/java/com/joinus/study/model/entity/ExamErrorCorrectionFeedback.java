package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.ExamErrorCorrectionFeedbackEnum;
import com.joinus.study.model.enums.FeedbackTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.UUID;

/**
 * 试卷反馈实体类
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("exam_error_correction_feedback")
public class ExamErrorCorrectionFeedback extends BaseEntity{

    private Long studentId;
    private Long parentId;
    /**
     * 家长姓名
     */
    private String parentName;
    /**
     * 手机号
     */
    private String telephoneNumber;
    /**
     * 试卷名称
     */
    private String examName;
    /**
     * 试卷id
     */
    private UUID examId;

    /**
     * 状态 PENDING待处理 FINISHED已完成
     */
    private ExamErrorCorrectionFeedbackEnum result;

    private Long handlerId;
    /**
     * 处理人
     */
    private String handlerName;

    /**
     * 纠错完成时间
     */
    private Date finishedAt;

    private Long examAnalyzeResultId;

    /**
     * 反馈类型（USER_FEEDBACK-用户反馈,SYSTEM_RECOGNITION-系统识别）
     */
    private FeedbackTypeEnum feedbackType;
}
