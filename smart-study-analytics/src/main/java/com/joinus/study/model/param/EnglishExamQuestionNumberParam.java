package com.joinus.study.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "英语-试卷诊断-提交生成报告-小题题号 请求参数")
public class EnglishExamQuestionNumberParam {

    @NotNull(message = "小题题号不能为空")
    @ApiModelProperty(value = "小题题号", required = true)
    private Integer num;

    @NotEmpty(message = "是否错题不能为空")
    @ApiModelProperty(value = "是否错题：0、否，1、是", required = true)
    private Integer isWrong;
}
