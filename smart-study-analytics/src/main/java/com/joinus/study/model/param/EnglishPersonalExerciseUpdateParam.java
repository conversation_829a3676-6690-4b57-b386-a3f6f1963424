package com.joinus.study.model.param;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "英语-练习做题提交修改参数")
public class EnglishPersonalExerciseUpdateParam implements Serializable {

    @ApiModelProperty(value = "练习id")
    @NotNull(message = "练习id不能为空")
    private Long personalExerciseId;

    @ApiModelProperty(value = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @ApiModelProperty(value = "练习问题答案")
    @NotEmpty(message = "练习问题答案不能为空")
    private List<EnglishPersonalExerciseQuestionParam> questions;

    @ApiModelProperty(value = "答题时长")
    @NotNull(message = "答题时长不能为空")
    private Integer answerTime;
}
