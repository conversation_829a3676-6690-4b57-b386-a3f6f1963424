package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@TableName("reading_personal_weak_question_type_analysis")
@Data
//@SuperBuilder(toBuilder = true)
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalWeakQuestionTypeAnalysis extends BaseEntity {

    @ApiModelProperty(value = "报告ID")
    private Long reportId;

    @ApiModelProperty(value = "题型")
    private String questionType;

    @ApiModelProperty(value = "错误率")
    private BigDecimal errorRate;

    @ApiModelProperty(value = "建议")
    private String suggestion;

    @ApiModelProperty(value = "干预方案")
    private String interventionPlan;

    @ApiModelProperty(value = "分析类型：1训练报告 2周期报告")
    private Integer analysisType;
}

