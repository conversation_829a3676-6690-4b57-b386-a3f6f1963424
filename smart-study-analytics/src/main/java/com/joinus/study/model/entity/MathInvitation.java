package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数学邀请记录表
 * @TableName math_invitation
 */
@TableName(value ="math_invitation")
@Data
public class MathInvitation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请者学生id
     */
    private Long inviterStudentId;
    /**
     * 邀请者手机号码
     */
    private String inviterPhone;

    /**
     * 被邀请者学生id
     */
    private Long inviteeStudentId;
    /**
     * 被邀请者手机号码
     */
    private String inviteePhone;

    /**
     * 邀请时间
     */
    private Date invitedAt;

    /**
     * 邀请链路径，例如A_1333333333_B_1333333333_C_1333333333
     */
    private Object path;

    /**
     * 类型: HOLIDAY_TRAINING
     */
    private String type;

}
