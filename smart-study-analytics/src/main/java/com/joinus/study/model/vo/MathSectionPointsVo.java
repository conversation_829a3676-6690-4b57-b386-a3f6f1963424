package com.joinus.study.model.vo;

import com.joinus.study.model.dto.QuerySectionVideoDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * @Description 章节知识点
 * <AUTHOR>
 * @Date 2025/8/19 13:53
 **/
@Data
public class MathSectionPointsVo implements Serializable {
    @Schema(description = "节ID")
    private UUID sectionId;
    @Schema(description = "名称")
    private String sectionName;
    @Schema(description = "包含知识点")
    private List<MathKnowledgePointVO> points;
    @Schema(description = "章节视频")
    List<QuerySectionVideoDto> sectionVideos; //  =

}
