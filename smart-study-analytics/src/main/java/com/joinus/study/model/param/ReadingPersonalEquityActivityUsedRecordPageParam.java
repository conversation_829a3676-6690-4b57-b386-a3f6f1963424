package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/6/3 15:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "管理后台-活动使用分页查询参数")
public class ReadingPersonalEquityActivityUsedRecordPageParam extends PageParam {
    @NotNull(message = "活动id不能为空")
    @ApiModelProperty(value = "活动id", required = true)
    private Long id;
    @ApiModelProperty(value = "邀请码使用状态：1-未使用, 2-已使用, 3-已作废")
    private Integer status;
}
