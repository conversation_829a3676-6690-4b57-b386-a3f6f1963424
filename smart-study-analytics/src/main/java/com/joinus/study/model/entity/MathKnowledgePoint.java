package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 
 * @TableName math_knowledge_points
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_knowledge_points")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MathKnowledgePoint extends BaseEntity {
    /**
     * 
     */
    private String name;

    /**
     * 知识点描述
     */
    private String content;

    /**
     * 知识点排序
     */
    private Integer sortNo;

    private Boolean examPoint;

    private Boolean isBase;

    private String originalName;

}
