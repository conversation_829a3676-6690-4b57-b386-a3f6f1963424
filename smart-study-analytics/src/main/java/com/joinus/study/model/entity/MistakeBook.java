package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.MasteryDegreeEnum;
import com.joinus.study.model.enums.MistakeBookSourceEnum;
import com.joinus.study.model.enums.SubjectEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.UUID;


/**
 *
 * @TableName mistake_book
 */
@TableName(value ="mistake_book")
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class MistakeBook extends BaseEntity {

    @ApiModelProperty(value = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @ApiModelProperty(value = "题库中题目ID")
    @NotNull(message = "题库中题目ID不能为空")
    private UUID questionId;

    @ApiModelProperty(value = "来源：photo_question-拍题，exam_diagnosis-试卷诊断,wrong_topic-录错题", notes = "com.joinus.study.model.enums.MistakeBookSourceEnum")
    @NotNull(message = "来源不能为空")
    private MistakeBookSourceEnum source;


    @ApiModelProperty(value = "学科：math-数学，chinese-language-语文，english-英语",notes = "com.joinus.study.model.enums.SubjectEnum")
    @NotNull(message = "学科不能为空")
    private SubjectEnum subject;

    @ApiModelProperty(value = "答案")
    private String questionAnswer;

    @ApiModelProperty(value = "答案解析")
    private String questionAnswerContent;

    @ApiModelProperty(value = "难度：星级")
    private Integer difficulty;

    @ApiModelProperty(value = "举一反三问题ID")
    private UUID derivedQuestionId;

    @ApiModelProperty(value = "来源ID")
    @NotNull(message = "来源ID不能为空")
    private Long sourceId;

    @ApiModelProperty(value = "掌握程度")
    private MasteryDegreeEnum masteryDegree;

    @ApiModelProperty(value = "知识点id集合")
    private Object knowledgePointIds;
}
