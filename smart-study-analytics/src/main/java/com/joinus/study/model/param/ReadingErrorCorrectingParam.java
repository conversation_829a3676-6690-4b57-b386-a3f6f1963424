package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "阅读学情分析报告纠错参数")
public class ReadingErrorCorrectingParam {

    @Schema(description = "学生id")
    @NotNull(message = "学生id不能为空")
    private Long studentId;

    @Schema(description = "阅读记录id")
    @NotNull(message = "阅读记录id不能为空")
    private Long personalPassageId;

    @Schema(description = "阅读记录题目id")
    @NotNull(message = "阅读记录id不能为空")
    private Long personalPassageQuestionId;

    @Schema(description = "家长id")
    private Long parentId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "做题结果：1-正确，0-错误")
    private BigDecimal result;
}
