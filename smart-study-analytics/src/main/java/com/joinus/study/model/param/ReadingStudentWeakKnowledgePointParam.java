package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "AI定向爆破-薄弱知识点列表")
public class ReadingStudentWeakKnowledgePointParam extends PageParam {

    @Schema(description = "学生id")
    private Long studentId;
    @Schema(description = "年级")
    private Integer grade;
    @Schema(description = "学期")
    private Integer semester;
}
