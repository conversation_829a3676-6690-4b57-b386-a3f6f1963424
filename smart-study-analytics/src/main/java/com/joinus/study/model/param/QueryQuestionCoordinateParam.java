package com.joinus.study.model.param;

import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.PaperSubjectTypeEnum;
import com.joinus.study.model.enums.PhotoQuestionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Description: 查询坐标信息请求参数
 * @Author: anpy
 * @date: 2025/3/11 10:59
 */
@ApiModel(description = "查询坐标信息请求参数", value = "查询坐标信息请求参数")
@Data
public class QueryQuestionCoordinateParam {

    @ApiModelProperty(value = "学生id")
    private Long studentId;
    @ApiModelProperty(value = "题目图片")
    private String ossKey;
    @ApiModelProperty(value = "切题类型")
    private PaperSubjectTypeEnum paperSubjectType = PaperSubjectTypeEnum.PRIMARY_SCHOOL_MATH;
    @ApiModelProperty(value = "题目类型", notes = "com.joinus.study.model.enums.OssEnum")
    private OssEnum ossEnum = OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB;
    private PhotoQuestionTypeEnum photoQuestionTypeEnum = PhotoQuestionTypeEnum.SINGLE;
    @ApiModelProperty(value = "学习记录id")
    private Long studyRecordId;

}
