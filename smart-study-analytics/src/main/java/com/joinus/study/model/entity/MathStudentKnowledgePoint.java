package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.KnowledgePointPptHtmlsStateEnum;
import lombok.*;

import java.util.List;
import java.util.UUID;

/**
 * 
 * @TableName math_student_knowledge_point
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_student_knowledge_point")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MathStudentKnowledgePoint extends BaseEntity {

    private Long studentId;

    private UUID knowledgePointId;

    private KnowledgePointPptHtmlsStateEnum pptHtmlsCompleted;

    private Object trainingExamIds;

}
