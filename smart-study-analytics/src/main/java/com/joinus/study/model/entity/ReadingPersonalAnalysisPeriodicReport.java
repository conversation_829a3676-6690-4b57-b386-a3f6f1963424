package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@TableName(value = "reading_personal_analysis_periodic_report", autoResultMap = true)
@Data
//@SuperBuilder(toBuilder = true)
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalAnalysisPeriodicReport extends BaseEntity {
    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 知识点总数
     */
    private Integer totalKnowledgePoints;

    /**
     * 弱知识点数量
     */
    private Integer weakPointCount;

    /**
     * 答对数量
     */
    private Integer correctCount;

    /**
     * 答错数量
     */
    private Integer incorrectCount;

    /**
     * 题目总数
     */
    private Integer totalQuestions;

    /**
     * 正确率
     */
    private BigDecimal accuracyRate;

    /**
     * 做题文章数
     */
    private Integer passageCount;

    /**
     * 报告类型：1周报，2月报
     */
    private Integer reportType;

    /**
     * 状态 0分析中 1分析完成
     */
    private Integer status;

    /**
     * 分析结果
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String analysesResult;
}
