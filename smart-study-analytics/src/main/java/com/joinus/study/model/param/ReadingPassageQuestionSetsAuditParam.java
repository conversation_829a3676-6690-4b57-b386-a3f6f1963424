package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "语文阅读-套题审核状态")
public class ReadingPassageQuestionSetsAuditParam {

    @Schema(description = "套题id", required = true, implementation = String.class, example = "550e8400-e29b-41d4-a716-************")
    private UUID questionSetId;//套题id

    @Schema(description = "审核状态（0-未审核 1-审核通过 2-审核不通过）", required = true, implementation = Integer.class, example = "1")
    private Integer isAudit;//审核状态（0-未审核 1-审核通过 2-审核不通过）
}
