package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.model.enums.PublisherEnum;
import lombok.*;

import java.util.UUID;

/**
 * 
 * @TableName question_knowledge_point
 */
@TableName(value ="question_knowledge_point")
@Data
@EqualsAndHashCode(callSuper=false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionKnowledgePoint extends BaseEntity {
    /**
     * 试卷ID
     */
    private UUID examId;

    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 知识点ID
     */
    private UUID knowledgePointId;

    /**
     * 知识点名称
     */
    private String knowledgePointName;

    /**
     * 知识点对应的教材版本
     */
    private PublisherEnum publisher;
}