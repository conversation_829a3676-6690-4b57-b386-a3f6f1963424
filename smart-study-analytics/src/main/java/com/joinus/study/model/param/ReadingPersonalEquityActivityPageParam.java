package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "管理后台-邀请码分页查询参数")
public class ReadingPersonalEquityActivityPageParam extends PageParam {

    @ApiModelProperty(value = "邀请码标题")
    private String activityTitle;
    @ApiModelProperty(value = "邀请码使用状态：1:正常,2:失效")
    private Integer status;


}
