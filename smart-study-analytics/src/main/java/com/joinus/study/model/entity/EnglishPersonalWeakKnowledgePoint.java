package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.UUID;

@TableName(value = "english_personal_weak_knowledge_point")
@Data
@EqualsAndHashCode(callSuper = false)
public class EnglishPersonalWeakKnowledgePoint extends BaseEntity {
    /**
     * 知识点ID,来自AI库
     */
    private UUID knowledgePointId;
    /**
     * 知识点名称,来自AI库
     */
    private String knowledgePointName;
    /**
     * 知识点类型,多个逗号拼接
     */
    private String questionType;
    /**
     * 试卷诊断和练习题目数量(知识点出现次数)
     */
    private Integer answeredQuestionCount;
    /**
     * 答错题目数量(知识点出错次数)
     */
    private Integer wrongAnswerCount;
    /**
     * 错误率
     */
    private BigDecimal errorPercentage;
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 爆破状态(0-已爆破 1-未爆破)
     */
    private Integer status;
    /**
     * 试卷诊断记录id
     */
    private String diagnoseRecordId;
}
