package com.joinus.study.model.param;

import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.vo.CoordinatePoint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleCutImageFromPositionsParam {

    @NotNull(message = "坐标点不能为空")
    private List<CoordinatePoint> positions;

    @NotEmpty(message = "图片链接不能为空")
    private String ossKey;

    private OssEnum ossEnum;
}
