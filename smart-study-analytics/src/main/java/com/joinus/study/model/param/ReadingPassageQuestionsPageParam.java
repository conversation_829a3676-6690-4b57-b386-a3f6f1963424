package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "管理后台-阅读题库题目分页查询参数")
public class ReadingPassageQuestionsPageParam extends PageParam {

    @Schema(description = "文章id")
    private UUID passageId;
}
