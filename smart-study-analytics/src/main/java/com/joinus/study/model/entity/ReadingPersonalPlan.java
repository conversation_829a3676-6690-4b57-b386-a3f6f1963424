package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JSONTypeHandlerPg;
import com.joinus.study.model.dto.ReadingWeakKnowledgePointsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @TableName 阅读提分计划表
 */
@TableName(value ="reading_personal_plan",autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper=false)
public class ReadingPersonalPlan extends BaseEntity {
    /**
     * 名称，最多100个字符
     */
    private String planName;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 阅读篇数
     */
    private Integer readingCount;
    /**
     * 薄弱知识点ID集合
     */
    @TableField(typeHandler = JSONTypeHandlerPg.class)
    private ReadingWeakKnowledgePointsDto weakKnowledgePoint;
    /**
     * 学生ID
     */
    private Long studentId;

    @TableField(exist = false)
    private String weakKnowledgePointIds;
}
