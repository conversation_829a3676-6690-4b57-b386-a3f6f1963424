package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(description = "薄弱知识点分析回调结果请求参数明细")
public class ReadingAIWeakKnowledgePointsItemParam implements Serializable {

    @NotNull(message = "知识点不能为空")
    @Schema(description = "知识点", required = true)
    private String knowledgePoint;

    @NotNull(message = "题型不能为空")
    @Schema(description = "题型", required = true)
    private String questionType;

    @NotNull(message = "薄弱点分析不能为空")
    @Schema(description = "薄弱点分析", required = true)
    private String weaknessAnalysis;

    @NotNull(message = "优势不能为空")
    @Schema(description = "优势", required = true)
    private String strengths;

    @NotNull(message = "建议不能为空")
    @Schema(description = "建议", required = true)
    private String suggestions;

    @NotNull(message = "薄弱环节不能为空")
    @Schema(description = "薄弱环节", required = true)
    private String weakProfile;
}
