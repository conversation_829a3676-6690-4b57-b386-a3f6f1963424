package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/3 15:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("english_exam_report_knowledge_point")
public class EnglishExamReportKnowledgePoint {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //生成表字段
    @TableField(value = "record_id")
    private Long recordId;
    @TableField(value = "knowledge_points_id")
    private UUID knowledgePointsId;
    @TableField(value = "knowledge_points_name")
    private String knowledgePointsName;
    @TableField(value = "question_type")
    private String questionType;
    @TableField(value = "question_number")
    private String questionNumber;
    @TableField(value = "error_reason")
    private String errorReason;
    @TableField(value = "overcome_secret")
    private String overcomeSecret;
    @TableField(value = "report_id")
    private Long reportId;
}
