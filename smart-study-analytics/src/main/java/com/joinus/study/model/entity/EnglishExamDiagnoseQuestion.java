package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/1 13:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("english_exam_diagnose_question")
public class EnglishExamDiagnoseQuestion {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "record_id")
    private Long recordId;
    @TableField(value = "exam_id")
    private UUID examId;
    @TableField(value = "question_id")
    private UUID questionId;
    @TableField(value = "question_type")
    private String questionType;
    @TableField(value = "question_number")
    private Integer questionNumber;
    @TableField(value = "big_question_number")
    private Integer bigQuestionNumber;
    @TableField(value = "is_wrong")
    private Boolean isWrong;
    @TableField(value = "created_at")
    private LocalDateTime createdAt;
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
    @TableField(value = "score")
    private BigDecimal score;
    @TableField(value = "deleted_at")
    private LocalDateTime deletedAt;
}
