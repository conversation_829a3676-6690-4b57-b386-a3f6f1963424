package com.joinus.study.model.vo;

import com.joinus.study.model.enums.KnowledgePointPptHtmlsStateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Schema(description = "数学知识点")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MathKnowledgePointVO implements Serializable {

    @Schema(description = "知识点id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID id;

    @Schema(description = "知识点名称", implementation = String.class, example = "1")
    private String name;

    @Schema(description = "排序序号", implementation = Integer.class, example = "1")
    private Integer sortNo;

    @Schema(description = "掌握程度")
    private Integer masteryDegree;

    @Schema(description = "是否有精讲信息")
    private Boolean hasExplanation;

    @Schema(description = "讲义ppt视频完成状态")
    @Builder.Default
    private KnowledgePointPptHtmlsStateEnum pptHtmlsCompletedState = KnowledgePointPptHtmlsStateEnum.NOT_COMPLETED;

}
