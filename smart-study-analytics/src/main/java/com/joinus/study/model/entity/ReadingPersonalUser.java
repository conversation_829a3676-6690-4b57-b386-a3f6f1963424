package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @TableName reading_personal_user
 */
@TableName(value ="reading_personal_user")
@Data
@EqualsAndHashCode(callSuper=false)
public class ReadingPersonalUser {
    @TableId(value = "STUDENT_ID")
    private Long studentId;//学生ID
    private Integer grade;//年级（数字1-9，表示对应的年级）
    private Integer semester;//学期 1上学期  2下学期
    private String textbook;//教材
    private Integer isNew;//是否新手引导

    private Date createdAt;
    private Date updatedAt;
    private Date deletedAt;

    private String studentName;
    private String telNum;

    @TableField(exist = false)
    private String gradeName;
}
