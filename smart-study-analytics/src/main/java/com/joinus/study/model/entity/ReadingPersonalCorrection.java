package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@TableName(value = "reading_personal_correction")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPersonalCorrection extends BaseEntity{
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 阅读记录id
     */
    private Long personalPassageId;
    /**
     * 阅读记录题目id
     */
    private Long personalPassageQuestionId;
    /**
     * 家长id
     */
    private Long parentId;
    /**
     * 手机号
     */
    private String phone;
}
