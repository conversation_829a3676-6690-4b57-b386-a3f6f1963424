package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description: 获取用户周报月报详情参数
 * @date: 2025/4/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPeriodicReportDetailParam implements Serializable {

    @NotNull(message = "学生id不能为空")
    @Schema(description = "学生id", implementation = Long.class, example = "1", required = true)
    private Long studentId;

    @NotNull(message = "报告类型不能为空")
    @Schema(description = "报告类型 1周报 2月报", implementation = Integer.class, example = "1", required = true)
    private Integer reportType;

    @Schema(hidden = true)
    private LocalDate startDate;

    @Schema(hidden = true)
    private LocalDate endDate;

}
