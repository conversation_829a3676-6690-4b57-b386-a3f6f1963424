package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/7 17:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "添加个人反馈参数")
public class ReadingPersonalFeedbackParam implements Serializable {

    @Schema(description = "学生id")
    @NotNull(message = "学生id不能为空")
    private Long studentId;

    @Schema(description = "阅读记录id")
    @NotNull(message = "阅读记录id不能为空")
    private Long personalPassageId;

    @Schema(description = "阅读记录题目id")
    @NotNull(message = "阅读记录id不能为空")
    private Long personalPassageQuestionId;

    @Schema(description = "家长id")
    @NotNull(message = "家长id不能为空")
    private Long parentId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "反馈类行")
    @NotEmpty(message = "反馈类型不能为空")
    private String feedbackType;

    @Schema(description = "反馈建议")
    private String suggestion;
}
