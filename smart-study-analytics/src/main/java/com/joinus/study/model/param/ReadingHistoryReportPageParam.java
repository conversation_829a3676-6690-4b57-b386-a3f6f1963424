package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "语文阅读提分-历次学情分析报告列表")
public class ReadingHistoryReportPageParam extends PageParam {

    @NotNull(message = "学生id不能为空")
    @Schema(description = "学生id", required = true)
    private Long studentId;
}
