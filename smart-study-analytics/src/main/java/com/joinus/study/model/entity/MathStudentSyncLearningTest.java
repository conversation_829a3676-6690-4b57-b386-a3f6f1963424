package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 学生同步学练测记录表
 * @TableName math_student_sync_learning_test
 */
@TableName(value ="math_student_sync_learning_test")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathStudentSyncLearningTest extends BaseEntity {

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 教材版本 BEI_SHI_DA
     */
    private String publisher;

    /**
     * 年级 1-12
     */
    private Integer grade;

    /**
     * 学期 1、2
     */
    private Integer semester;

    /**
     * 学练测类型  SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST
     */
    private String testType;

    /**
     * 学练测目录节点ID
     */
    private Object catalogNodeId;

    /**
     * 学练测历史试卷ID
     */
    private Object examIdHistory;

}