package com.joinus.study.model.param;

import com.joinus.study.model.vo.PageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/31 18:58
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class ReadingPersonalPassagesQueryParam extends PageVo implements Serializable {
    @ApiModelProperty(value = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @ApiModelProperty(value = "做题文章ID")
    private Long id;

    @ApiModelProperty(value = "文章库内ID")
    private UUID passageId;

    @ApiModelProperty(value = "状态 1 未完成：暂停  2 已做完 ")
    private Integer status;

    @ApiModelProperty(value = "所属计划")
    private Long planId;

    @ApiModelProperty(value = "做题单元ID")
    private UUID unitId;

    @ApiModelProperty(value = "耗时")
    private Integer timeSpent;
}
