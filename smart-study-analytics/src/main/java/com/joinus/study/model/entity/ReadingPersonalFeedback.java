package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/6 15:06
 */
@TableName(value = "reading_personal_feedback")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPersonalFeedback extends BaseEntity{

    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 阅读记录id
     */
    private Long personalPassageId;
    /**
     * 阅读记录题目id
     */
    private Long personalPassageQuestionId;
    /**
     * 家长id
     */
    private Long parentId;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 反馈类型
     */
    private String feedbackType;
    /**
     * 反馈建议
     */
    private String suggestion;
    /**
     * 状态
     */
    private Integer status;
}
