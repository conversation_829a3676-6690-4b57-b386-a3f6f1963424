package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "英语-练习报告新增参数")
public class EnglishPersonalExerciseReportCreateParam implements Serializable {
    /**
     * 练习表主键
     */
    private Long personalExerciseId;
    /**
     * 练习题目数量
     */
    private Integer answeredQuestionCount;
    /**
     * 答对题数
     */
    private Integer correctAnswerCount;
    /**
     * 正确率
     */
    private BigDecimal correctPercentage;
    /**
     * 答题时长
     */
    private Integer answerTime;
    /**
     * 学生id
     */
    private Long studentId;
}
