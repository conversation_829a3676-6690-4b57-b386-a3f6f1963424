package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 学生学习周数记录
 * @TableName math_student_study_week_history
 */
@TableName(value ="math_student_study_week_history")
@Data
public class MathStudentStudyWeekHistory implements Serializable {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long studentId;

    /**
     * 学习周的第一天(周一)
     */
    private Date studyWeekFirstDay;

    /**
     * 
     */
    private Date createdAt;

}