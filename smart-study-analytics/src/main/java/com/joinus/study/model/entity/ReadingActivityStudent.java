package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


@TableName("reading_activity_student")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReadingActivityStudent extends BaseEntity {

    private Long activityId;

    private Long studentId;

    private Long schoolId;

    private Integer grade;

    private Integer semester;
}
