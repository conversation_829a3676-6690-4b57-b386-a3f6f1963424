package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("view_active_students")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ActiveStudentsEntity {

    private Long classId;
    private String className;
    private Long gradeId;
    private String gradeName;
    private Long studentId;
    private String studentName;
    private Long schoolId;
    private String schoolName;

}
