package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台-邀请码管理-创建/编辑邀请码参数")
public class ReadingPersonalEquityActivityOperateParam {

    @Schema(description = "主键id，编辑时不可为空")
    private Long id;

    @Schema(description = "活动标题")
    @NotNull(message = "活动标题不能为空")
    private String activityTitle;

    @Schema(description = "批次名称")
    @NotNull(message = "批次名称不能为空")
    private String batchName;

    @Schema(description = "兑换码数量")
    @NotNull(message = "兑换码数量不能为空")
    private Integer codeCount;

    @Schema(description = "活动开始日期")
    @NotNull(message = "活动开始日期不能为空")
    private String startDate;

    @Schema(description = "活动结束日期")
    @NotNull(message = "活动结束日期不能为空")
    private String endDate;

    @Schema(description = "权益天数")
    @NotNull(message = "权益天数不能为空")
    private Integer equityDays;

    @Schema(description = "创建人，新增时创建人不能为空")
    private String creator;

    @Schema(description = "使用须知")
    @NotNull(message = "使用须知不能为空")
    private String activityNotice;

    @Schema(description = "科目:1:语文,2:数学,4:英语")
    @NotNull(message = "科目不能为空")
    private Integer subject;
}
