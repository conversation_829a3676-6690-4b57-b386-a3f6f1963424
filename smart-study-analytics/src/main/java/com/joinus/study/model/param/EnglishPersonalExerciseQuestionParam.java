package com.joinus.study.model.param;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;

@Data
@Schema(description = "英语-练习题目提交参数")
public class EnglishPersonalExerciseQuestionParam implements Serializable {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "题目ID,来自AI库")
    @NotNull(message = "题目ID不能为空")
    private UUID questionId;

    @ApiModelProperty(value = "作答答案")
    @NotEmpty(message = "作答答案不能为空")
    private String userAnswer;
}
