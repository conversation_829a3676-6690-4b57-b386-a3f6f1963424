package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/1 13:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("english_exam_diagnose_record")
public class EnglishExamDiagnoseRecord {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "exam_id")
    private UUID examId;
    @TableField(value = "exam_name")
    private String examName;
    @TableField(value = "student_id")
    private Long studentId;
    @TableField(value = "grade")
    private Integer grade;
    @TableField(value = "total_score")
    private BigDecimal totalScore;
    @TableField(value = "score")
    private BigDecimal score;
    @TableField(value = "created_at")
    private LocalDateTime createdAt;
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
    @TableField(value = "deleted_at")
    private LocalDateTime deletedAt;
    @TableField(value = "status")
    private Integer status;
    @TableField(value = "student_name")
    private String studentName;
    @TableField(value = "school_id")
    private Long schoolId;
    @TableField(value = "school_name")
    private String schoolName;
    @TableField(value = "question_number")
    private Integer questionNumber;
    @TableField(value = "grade_id")
    private Long gradeId;
    @TableField(value = "grade_name")
    private String gradeName;
    @TableField(value = "class_id")
    private Long classId;
    @TableField(value = "class_name")
    private String className;
}
