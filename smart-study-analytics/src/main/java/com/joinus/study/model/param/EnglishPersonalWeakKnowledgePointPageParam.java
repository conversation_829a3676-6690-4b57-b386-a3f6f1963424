package com.joinus.study.model.param;

import com.joinus.common.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "英语-薄弱知识点分页查询参数")
public class EnglishPersonalWeakKnowledgePointPageParam extends PageParam {

    @Schema(description = "学生id")
    private Long studentId;
}
