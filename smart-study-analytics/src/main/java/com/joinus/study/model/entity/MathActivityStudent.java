package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import com.joinus.study.model.enums.MathActivityFinishEnum;
import com.joinus.study.model.enums.MathMemberLevelEnum;
import com.joinus.study.model.enums.PublisherEnum;
import lombok.*;

import java.util.Date;

@TableName("math_activity_student")
@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathActivityStudent extends BaseEntity{
    private Long activityId;
    private Integer grade;
    private PublisherEnum publisher;
    private Long studentId;
    private String orderNo;
    private String payMethod;
    private Date paidAt;
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String toastHistory;   //
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String refundHistory;  //
    private MathActivityFinishEnum finishResult;    // 0:未完成 1:预习完成 2:复习完成 3:完成
    private MathMemberLevelEnum membershipLevel; // 会员等级
    private Long parentId;

    private String telephoneNumber;

}
