package com.joinus.study.model.param;

import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "英语-练习做题新增参数")
public class EnglishPersonalExerciseCreateParam implements Serializable {
    /**
     * 学生ID
     */
    private Long studentId;
    /**
     * 学生薄弱知识点id
     */
    private Long personalWeakKnowledgePointId;
    /**
     * 题目难度 1、简单 2、中等 3、困难
     */
    private String difficulty;
    /**
     * 所属年级 1、一年级 2、二年级 3、三年级
     */
    private Integer grade;
    /**
     * 所属学期 1、上学期 2、下学期
     */
    private String semester;
    /**
     * 练习题列表
     */
    private List<EnglishPersonalExerciseQuestionDTO> questions;

}
