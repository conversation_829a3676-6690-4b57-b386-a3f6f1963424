package com.joinus.study.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "英语-题目查询参数")
public class EnglishAiQuestionQueryParam implements Serializable {
    /**
     * 所属年级
     */
    @Schema(description = "所属年级 1、一年级 2、二年级 3、三年级 4、四年级 5、五年级 6、六年级 7、七年级 8、八年级 9、九年级 10、十年级 11、十一年级 12、十二年级")
    private Integer grade;
    /**
     * 所属学期
     */
    @Schema(description = "所属学期 1、上学期 2、下学期 (参数传中文内容)")
    private String semester;
    /**
     * 知识点id
     */
    @Schema(description = "知识点id")
    private UUID knowledgePointId;
    /**
     * 题目数量
     */
    @Schema(description = "题目数量")
    private Integer questionCount;
    /**
     * 题目难度
     */
    @Schema(description = "题目难度")
    private List<Integer> questionDifficultyList;

}
