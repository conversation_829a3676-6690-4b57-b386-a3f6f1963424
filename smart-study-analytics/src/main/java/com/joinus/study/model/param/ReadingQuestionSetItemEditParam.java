package com.joinus.study.model.param;

import cn.hutool.json.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@Schema(description = "修改题目 请求参数")
public class ReadingQuestionSetItemEditParam {
    @Schema(description = "题id",  required = true, implementation = String.class, example = "123456")
    @NotNull(message = "题目id不能为空")
    private UUID id;

    @Schema(description = "题目内容",  required = true, implementation = String.class, example = "春眠不觉晓")
    @NotNull(message = "题目内容不能为空")
    private String content;

    @Schema(description = "选项",  required = false, implementation = JSONArray.class, example = "")
    private JSONArray options;

    @Schema(description = "答案解析",  required = true, implementation = String.class, example = "春眠不觉晓")
    @NotNull(message = "答案解析不能为空")
    private String answerContent;

    @Schema(description = "答案",  required = true, implementation = String.class, example = "春眠不觉晓")
    @NotNull(message = "答案")
    private String answer;

    @Schema(description = "prettyAnswer",  required = true, implementation = String.class, example = "春眠不觉晓")
    @NotNull(message = "prettyAnswer")
    private String prettyAnswer;
}

