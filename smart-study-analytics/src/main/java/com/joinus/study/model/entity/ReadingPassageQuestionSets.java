package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 阅读文章题目集合表
 */
@TableName(value = "reading_passage_question_sets")
@Data
@EqualsAndHashCode(callSuper = false)
public class ReadingPassageQuestionSets implements Serializable {
    private UUID id;
    private Date updatedAt;
    private Date createdAt;
    private Date deletedAt;
    /**
     * 关联文章ID（对应passage_id字段）
     */
    private UUID passageId;

    /**
     * 题目集名称（对应name字段）
     */
    private String name;

    /**
     * 审核状态（0-未审核 1-已审核）
     */
    private Integer isAudit;

    /**
     * 启用状态（0-禁用 1-启用）
     */
    private Integer isEnabled;
}
