package com.joinus.study.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;

@Data
public class StudyRecordQuestionSaveParam implements Serializable {

    @ApiModelProperty(value = "题目id")
    @NotNull(message = "题目信息不能为空")
    private UUID questionId;
    @ApiModelProperty(value = "题目顺序")
    @NotNull(message = "题目顺序信息不能为空")
    private Integer sortNo;
    @ApiModelProperty(value = "答案")
    @NotNull(message = "答案信息不能为空")
    private String questionAnswer;
    @ApiModelProperty(value = "答案解析")
    @NotNull(message = "答案解析信息不能为空")
    private String questionAnswerContent;
    @ApiModelProperty(value = "难度：星级")
    @NotNull(message = "难度信息不能为空")
    private Integer difficulty;
    @ApiModelProperty(value = "举一反三题目ID")
    private UUID derivedQuestionId;
}
