package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.study.config.JsonbTypeHandler;
import com.joinus.study.model.enums.ExamSourceType;
import lombok.*;

import java.util.UUID;

@TableName(value ="math_exams")
@Data
@Builder
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class MathExamsEntity{
    @TableId(type = IdType.AUTO)
    private UUID id;
    private String name;
    private Integer semester;
    private Integer grade;
    private String publisher;
    private Integer year;
    private String region;
    private ExamSourceType source;
    private String regionPath;
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String examSectionDescriptions;

}
