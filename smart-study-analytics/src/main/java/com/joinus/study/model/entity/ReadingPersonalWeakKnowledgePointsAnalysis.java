package com.joinus.study.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@TableName("reading_personal_weak_knowledge_points_analysis")
@Data
//@SuperBuilder(toBuilder = true)
@Builder
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ReadingPersonalWeakKnowledgePointsAnalysis extends BaseEntity {

    private UUID weakKnowledgePointId;

    @ApiModelProperty(value = "薄弱知识点名称")
    private String weakKnowledgePointName;

    @ApiModelProperty(value = "报告ID")
    private Long reportId;

    @ApiModelProperty(value = "题型")
    private String questionType;

    @ApiModelProperty(value = "薄弱点分析")
    private String weaknessAnalysis;

    @ApiModelProperty(value = "优势")
    private String strengths;

    @ApiModelProperty(value = "建议")
    private String suggestions;

    @ApiModelProperty(value = "总题数")
    private Integer totalQuestionCount;

    @ApiModelProperty(value = "答错数")
    private Integer wrongAnswerCount;

    @ApiModelProperty(value = "分析类型：1训练报告 2周期报告")
    private Integer analysisType;
}
