package com.joinus.study.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.regex.Pattern;

/**
 * 本地IP检查拦截器
 * 用于判断请求是否来自本地IP (localhost, 127.0.0.1, 192.168.x.x)
 */
@Component
public class LocalIpCheckInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(LocalIpCheckInterceptor.class);
    
    // 匹配192.168.x.x格式的IP地址
    private static final Pattern LOCAL_IP_PATTERN = Pattern.compile("^192\\.168\\.(\\d{1,3})\\.(\\d{1,3})$");

    /**
     * 判断请求是否来自本地IP
     * 
     * @param request 请求对象
     * @return 是否是本地IP
     */
    public static boolean isLocalIp(HttpServletRequest request) {
        String ipAddress = getIpAddress(request);
        // 检查是否是localhost或127.0.0.1
        if ("localhost".equalsIgnoreCase(ipAddress) || "127.0.0.1".equals(ipAddress) || "0:0:0:0:0:0:0:1".equals(ipAddress)) {
            return true;
        }

        // 检查是否是192.168.x.x格式
        return LOCAL_IP_PATTERN.matcher(ipAddress).matches();
    }

    /**
     * 获取请求的真实IP地址
     * 
     * @param request 请求对象
     * @return IP地址
     */
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1") || ip.equals("0:0:0:0:0:0:0:1")) {
                // 根据网卡取本机配置的IP
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (Exception e) {
                    logger.error("获取本地IP地址异常", e);
                }
            }
        }
        
        // 对于通过多个代理的情况，第一个IP为客户端真实IP，多个IP按照','
        if (ip != null && ip.length() > 15) { 
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return ip;
    }
}
