package com.joinus.study.interceptor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.joinus.common.model.response.ApiResult;
import com.joinus.study.constant.Constant;
import com.joinus.study.interceptor.util.WebUtil;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.utils.JWTUtil;
import com.joinus.study.utils.RedisUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Component
@Slf4j
public class MathActivityH5Interceptor extends BaseInterceptor {

    @Value("#{'${math-activity-h5.white-urls:/api/smart-study/math/activity/h5/sms,/api/smart-study/math/activity/h5/login,/api/smart-study/math/activity/h5/logout}'.split(',')}")
    private List<String> whiteUrls;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if ("OPTIONS".equals(request.getMethod())) {
            return super.preHandle(request, response, handler);
        }

        // 检查请求头中是否包含request-origion: Knife4j，如果是则跳过token验证
        String requestOrigin = request.getHeader("request-origion");
        if ("Knife4j".equals(requestOrigin)) {
            log.info("Knife4j请求，跳过token验证：{}", request.getRequestURI());
            return super.preHandle(request, response, handler);
        }

        // 检查是否是本地IP请求，如果是则跳过token验证
        if (LocalIpCheckInterceptor.isLocalIp(request)) {
            log.info("本地IP请求，跳过token验证：{}，IP: {}", request.getRequestURI(),
                    LocalIpCheckInterceptor.getIpAddress(request));
            return super.preHandle(request, response, handler);
        }

        String url = request.getRequestURI();
        if (WebUtil.isWhiteRequest(url, whiteUrls.size(), whiteUrls) || url.contains("doc.html")) {
            return super.preHandle(request, response, handler);
        }

        String token = JWTUtil.getToken(request);
        if (StrUtil.isEmpty(token)) {
            return sendErrorResponse(response, JSON.toJSONString(ApiResult.failed("token不能为空！")));
        }
        try {
            Claims claims = JWTUtil.parseJWT(token);
            String userId = Convert.toStr(claims.get("userId"));
            String redisToken = Convert.toStr(redisUtil.get(Constant.QYL_LOGIN_REDIS_CACHE_NAME + userId));
            if (StrUtil.isEmpty(redisToken)) {
                return sendErrorResponse(response, JSON.toJSONString(ApiResult.failed("token已过期！")));
            }
            Claims redisClaims = JWTUtil.parseJWT(redisToken);
            String redisUserId = Convert.toStr(redisClaims.get("userId"));
            if (StrUtil.isBlank(userId) || StrUtil.isBlank(redisUserId)) {
                log.warn("空手机号: 请求userId={}, Redis userId={}", userId, redisUserId);
                return sendErrorResponse(response, JSON.toJSONString(ApiResult.failed("登录信息异常！")));
            }
            if (!StrUtil.equals(userId, redisUserId)) {
                return sendErrorResponse(response, JSON.toJSONString(ApiResult.failed("登录手机号不一致,请重新登录！")));
            }
            String studentIdStr = request.getHeader("student-id");
            CurrentUser currentUser = new CurrentUser();
            currentUser.setUserId(Convert.toLong(redisUserId));
            currentUser.setUsername(Convert.toStr(claims.get("phone")));
            if (StrUtil.isNotBlank(studentIdStr)) {
                currentUser.setStudentId(Convert.toLong(studentIdStr));
            }
            request.setAttribute("current_user_id", redisUserId);
            request.setAttribute("current_user", currentUser);
        } catch (Exception e) {
            log.warn("Token解析异常: {}", token, e);
            return sendErrorResponse(response, JSON.toJSONString(ApiResult.failed("无效的token！")));
        }
        return true;
    }

    boolean sendErrorResponse(HttpServletResponse response, String message) {
        try {
            response.setContentType("application/json;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(JSON.toJSONString(ApiResult.failed(message)));
            response.getWriter().flush();
            return false;
        } catch (IOException e) {
            log.warn("响应输出异常", e);
            return false;
        }
    }


}
