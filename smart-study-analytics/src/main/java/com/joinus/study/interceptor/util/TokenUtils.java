package com.joinus.study.interceptor.util;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

public class TokenUtils {

    private static final String QYL_TOKEN_KEY = "joinusqyl";
    /**
     *
     * @description token加密
     * @return String 加密后的token
     */
    public static String encryptToken(String content){
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(QYL_TOKEN_KEY.getBytes("UTF-8"));
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec secKey = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            byte[] byteContent = content.getBytes("UTF-8");
            cipher.init(Cipher.ENCRYPT_MODE, secKey);// 初始化
            byte[] result = cipher.doFinal(byteContent);
            String rsString = StringUtils.upperCase(Hex.encodeHexString(result));
            return rsString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @description token解密
     * @return String 解密后的内容
     */
    public static String decryptToken(String token){
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(QYL_TOKEN_KEY.getBytes("UTF-8"));
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec secKey = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, secKey);// 初始化
            byte[] hexByte = Hex.decodeHex(token);
            byte[] result = cipher.doFinal(hexByte);
            return new String(result, "UTF-8"); // 加密
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
