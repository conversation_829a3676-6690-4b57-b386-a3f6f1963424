package com.joinus.study.interceptor.util;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.NamedThreadLocal;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;


public final class WebUtil {
    private static final Logger logger = LoggerFactory.getLogger(WebUtil.class);

    private WebUtil() {
    }

    public static ThreadLocal<HttpServletRequest> REQUEST = new NamedThreadLocal<HttpServletRequest>(
            "ThreadLocalRequest");


    /** 判断是否是白名单 */
    public static boolean isWhiteRequest(String url, int size, List<String> whiteUrls) {
        if (url == null || "".equals(url) || size == 0) {
            return true;
        } else {
            PathMatcher matcher = new AntPathMatcher();
            for (String urlTemp : whiteUrls) {
                if (matcher.match(urlTemp.toLowerCase(), url.toLowerCase())) {
                    return true;
                }
            }
        }
        return false;
    }
}
