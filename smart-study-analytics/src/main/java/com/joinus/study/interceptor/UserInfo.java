package com.joinus.study.interceptor;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class UserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long userId; // 用户ID

    private String role; // 0老师，1家长

    private boolean schoolManager; // 是否是学校管理员

    private boolean gradeManager; // 是否是年级管理员

    private boolean dormitoryManager; // 是否是宿舍管理员

    private List<Long> schoolManagerClassIds; // 学校管理员的所有管辖班级

    private List<Long> gradeManagerClassIds; // 年级管理员的所有管辖班级

    private Boolean isSystemUser; // 是否是体制内用户

    private Boolean isChinaMobile; // 是否中国移动用户
}
