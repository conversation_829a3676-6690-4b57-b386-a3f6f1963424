package com.joinus.study.interceptor.util;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;

public final class DataUtils {
    private DataUtils() {
    }

    @SuppressWarnings("rawtypes")
    public static final boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof CharSequence) {
            return isBlank((CharSequence) obj);
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        if (obj instanceof Collection) {
            return ((Collection) obj).isEmpty();
        }
        if (obj instanceof Map) {
            return ((Map) obj).isEmpty();
        }
        return false;
    }
    public static final boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }

    private static boolean isBlank(final CharSequence cs) {
        int strLen;
        if ((strLen = cs.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

}
