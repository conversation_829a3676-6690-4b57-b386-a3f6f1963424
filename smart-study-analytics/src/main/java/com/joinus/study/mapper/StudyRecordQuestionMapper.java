package com.joinus.study.mapper;

import com.joinus.study.model.entity.StudyRecordQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【study_record_question】的数据库操作Mapper
 * @createDate 2025-03-11 09:48:14
 * @Entity com.joinus.study.model.entity.StudyRecordQuestion
 */
public interface StudyRecordQuestionMapper extends BaseMapper<StudyRecordQuestion> {

    List<Long> selectRecordQuestion(@Param("studentId") Long studentId, @Param("questionId") UUID questionId);

    void updateKnowledgePoint(@Param("id") Long id, @Param("difficultyInt") int difficultyInt, @Param("knowledgePoint") String knowledgePoint);
}




