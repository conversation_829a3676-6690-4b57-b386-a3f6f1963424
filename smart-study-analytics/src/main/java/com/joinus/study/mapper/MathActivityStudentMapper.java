package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.MathActivityStudent;
import com.joinus.study.model.vo.MathActivityStudentVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/13 16:46
 **/
@Mapper
public interface MathActivityStudentMapper extends BaseMapper<MathActivityStudent> {

    List<MathActivityStudentVo> selectMathActivityStudentVo(MathActivityStudent mathActivityStudent);
}
