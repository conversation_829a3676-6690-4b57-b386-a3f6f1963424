package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.GradeInfo;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import org.apache.ibatis.annotations.Param;

public interface GradeLeaveMapper extends BaseMapper<ExamAnalyzeResult> {

    /**
     * 根据学生id获取学段
     */
    GradeInfo getGradeByStudentId(@Param("studentId") Long studentId);
}




