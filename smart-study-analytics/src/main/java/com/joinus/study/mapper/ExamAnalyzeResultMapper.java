package com.joinus.study.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.vo.ExamAnalysisReportVo;
import com.joinus.study.model.vo.KnowledgePointStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;
import com.joinus.study.model.vo.ExamAnalyzeResultVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【exam_analyze_result】的数据库操作Mapper
* @createDate 2025-03-11 09:41:56
* @Entity com.joinus.study.model.entity.ExamAnalyzeResult
*/
public interface ExamAnalyzeResultMapper extends BaseMapper<ExamAnalyzeResult> {

    /**
     * 计算正确率的百分位数
     * @param examId
     * @param percentile
     * @return
     */
    BigDecimal calculatePercentile(@Param("examId") UUID examId, @Param("percentile") String percentile,@Param("studentId") Long studentId);

    /**
     * 插入考试分析结果并返回生成的ID
     * @param entity 考试分析结果实体
     * @return 生成的记录ID
     */
    Long insertExamAnalyzeResult(@Param("entity") ExamAnalyzeResult entity);

    /**
     * 根据考试id查询考试分析结果
     * @param personalExamId
     * @return
     */
    ExamAnalysisReportVo selectExamAnalyzeResultByExamId( @Param("personalExamId") Long personalExamId);

    List<ExamAnalyzeResultVo> selectExamAnalyzeResultList(@Param("page") Page<ExamAnalyzeResultVo> page,
                                                          @Param("studentId") Long studentId);

    List<ExamAnalyzeResultVo> selectSpecializedExamAnalyzeResultListV2(@Param("page") Page<ExamAnalyzeResultVo> page, @Param("studentId") Long studentId);

    Integer selectTotalStudentCountByExamId(@Param("examId") UUID examId, @Param("studentId") Long studentId);

    KnowledgePointStatisticsVo getMasteredCount(@Param("studentId") Long studentId, @Param("examId") UUID examId, @Param("knowledgePointId") UUID knowledgePointId);

    List<String> getOssUrls(@Param("id") Long id);

    String getOssUrlString(@Param("examId") UUID examId);

    Integer selectTestPapersCount(@Param("examId") UUID examId);

    /*
     * 方法描述 : 根据学生id查询学生信息
     */
    Map<String,Object> getStudentInfo(@Param("studentId") Long studentId);

    /**
     * 获取考察范围的小节
     * @param analyzeReportId 考情报告id
     * @return 小节id
     */
    List<UUID> selectManualExamScopeSection(Long analyzeReportId);

    List<UUID> selectKnowledgePointByAnalyzeId(Long analyzeReportId);

    List<ExamAnalyzeResultVo> pageAllRecords(Page<ExamAnalyzeResultVo> page, Long studentId);
}
