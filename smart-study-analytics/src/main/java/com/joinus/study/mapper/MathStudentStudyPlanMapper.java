package com.joinus.study.mapper;

import com.joinus.study.model.entity.MathStudentStudyPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_student_study_plan(学生学习计划)】的数据库操作Mapper
* @createDate 2025-09-12 16:19:30
* @Entity com.joinus.study.model.entity.MathStudentStudyPlan
*/
public interface MathStudentStudyPlanMapper extends BaseMapper<MathStudentStudyPlan> {

    List<MathStudentStudyPlan> selectStudentPlanList(Long studentId);
}




