package com.joinus.study.mapper;

import com.joinus.study.model.entity.QuestionAnswerFeedback;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【question_answer_feedback】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.QuestionAnswerFeedback
*/
public interface QuestionAnswerFeedbackMapper extends BaseMapper<QuestionAnswerFeedback> {

    void save(@Param("param") QuestionAnswerFeedback answerFeedback);
}




