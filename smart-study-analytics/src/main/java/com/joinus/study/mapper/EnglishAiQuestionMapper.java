package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.EnglishAiQuestion;
import com.joinus.study.model.param.EnglishAiQuestionQueryParam;
import com.joinus.study.model.vo.EnglishAiQuestionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EnglishAiQuestionMapper extends BaseMapper<EnglishAiQuestion> {
    List<EnglishAiQuestionVO> list(@Param("queryParam") EnglishAiQuestionQueryParam queryParam);
}
