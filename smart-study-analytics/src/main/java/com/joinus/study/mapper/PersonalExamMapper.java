package com.joinus.study.mapper;

import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.PersonalExam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.param.SpecialTrainingQuestionTypesParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【personal_exam】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.PersonalExam
*/
public interface PersonalExamMapper extends BaseMapper<PersonalExam> {

    /**
     * 获取考试题目信息
     * @param examId 试卷id
     * @return
     */
    List<ExamQuestionInfoDto> getExamQuestionInfo(@Param("examId") UUID examId);

    List<Long> selectByExamIdAndStudentId(@Param("studentId")Long studentId,@Param("examId") UUID id);

    /**
     * 根据ID查询PersonalExam
     * @param id 主键ID
     * @return PersonalExam实体
     */
    PersonalExam selectPersonalExamById(@Param("id") Long id);

    List<QuestionKnowledgePoint> listWeakKnowledgePoints(@Param("personalExamId") Long personalExamId);

    List<String> selectExistExamPhoto(@Param("examId") UUID examId);

    List<String> selectExistExamPhotoByOriginalPaper(@Param("examId") UUID examId);

    /**
     * 根据问题id查询问题信息
     * @param questionId 问题id
     */
    QuestionInfoByIdDto getQuestionInfoById(@Param("questionId") UUID questionId);

    /**
     * 根据问题id查询问题信息
     * @param questionIds 问题id
     */
    List<QuestionInfoByIdDto> getQuestionInfoByIds(@Param("questionIds") List<UUID> questionIds);

    /**
     * 查看题目解析结果
     * @param questionId 问题id publisherName：不带版字
     */
    QuestionAnalyzeResultDto questionAnalyzeResult(@Param("questionId") UUID questionId);

    List<QuestionAnalyzeResultDto> questionAnalyzeResults(@Param("questionIds") List<UUID> questionIds);

    List<QuestionInfoByIdDto> getQuestionInfoListByExamId(@Param("examId") UUID examId);

    PersonalExam selectLatestNoAnalyzedPersonalExam(@Param("examId") UUID examId, @Param("studentId") Long studentId);

    List<ExamOnlineQuestionDetailDto> selectOnlineSpecializedTrainingExamDetail(@Param("personalExamId") Long personalExamId);
}
