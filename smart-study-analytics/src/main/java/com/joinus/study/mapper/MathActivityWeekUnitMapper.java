package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.enums.PublisherEnum;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_activity_week_unit(数学活动周期单元表)】的数据库操作Mapper
* @createDate 2025-06-16 09:51:24
* @Entity generator.domain.MathActivityWeekUnit
*/
public interface MathActivityWeekUnitMapper extends BaseMapper<MathActivityWeekUnit> {
    //获取预习第一周第一章第一节预览
    List<Long> selectGiftWeekUnitIds(@Param("activityId") Long activityId, @Param("publisher") PublisherEnum publisher,@Param("grade") Integer  grade,@Param("isPreview") boolean isPreview );
}




