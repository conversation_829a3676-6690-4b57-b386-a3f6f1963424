package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.ReadingPersonalUserPageParam;
import com.joinus.study.model.vo.ReadingPersonalUserVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadingPersonalUserMapper extends BaseMapper<ReadingPersonalUser> {

    List<ReadingPersonalUserVO> pages(@Param("page") Page<ReadingPersonalUserVO> page, @Param("pageParam") ReadingPersonalUserPageParam pageParam);

    Integer getContinuousContactCountByDays(@Param("days") int i);
}




