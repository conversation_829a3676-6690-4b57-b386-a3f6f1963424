package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.ReadingActivityWithStudentDTO;
import com.joinus.study.model.entity.ReadingActivity;
import org.apache.ibatis.annotations.Param;
import com.joinus.study.model.vo.ReadingActivityVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadingActivityMapper extends BaseMapper<ReadingActivity> {

    // 根据计划ID和学生ID获取活动信息和学生信息
    ReadingActivityWithStudentDTO getActivityWithStudent(@Param("activityId") Long activityId,@Param("studentId") Long studentId);

    List<ReadingActivityVO> queryReadingActivityList();

    Integer getStudentCompleteNum(@Param("studentId") Long studentId, @Param("activityId") Long activityId);
}
