package com.joinus.study.mapper;

import com.joinus.study.model.entity.MathStudentSyncLearningTest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_student_sync_learning_test(学生同步学练测记录表)】的数据库操作Mapper
* @createDate 2025-08-13 14:29:22
* @Entity com.joinus.study.model.entity.MathStudentSyncLearningTest
*/
public interface MathStudentSyncLearningTestMapper extends BaseMapper<MathStudentSyncLearningTest> {

    /**
     * 查询节点的掌握度
     * @param catalogNodeId
     * @param studentId
     * @param examAnalyzeResultId
     * @return
     */
    Double selectNodesMasteryDegree(@Param("catalogNodeId") UUID catalogNodeId, @Param("studentId") Long studentId, @Param("examAnalyzeResultId") Long examAnalyzeResultId);

    /**
     * 查询最后一次考试分析结果id
     * @param nodeId
     * @param studentId
     * @return
     */
    Long getLastExamAnalyzeResultId(@Param("nodeId") UUID nodeId, @Param("studentId") Long studentId);

    MathStudentSyncLearningTest selectByExamHistoryId(@Param("examId") UUID examId);
}




