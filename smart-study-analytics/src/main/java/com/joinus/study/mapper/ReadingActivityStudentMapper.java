package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ReadingActivityStudent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadingActivityStudentMapper extends BaseMapper<ReadingActivityStudent> {

    List<ReadingActivityStudent> getStudentJoinActivityInfo(@Param("studentId") Long studentId);

    Integer getJoinTrainingCampCount();
}
