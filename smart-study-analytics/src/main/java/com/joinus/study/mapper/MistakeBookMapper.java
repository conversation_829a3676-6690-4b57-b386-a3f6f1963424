package com.joinus.study.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.MistakeBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.param.QueryMistakeBookParam;
import com.joinus.study.model.vo.ExamMistakeInfoVo;
import com.joinus.study.model.vo.FilesVo;
import com.joinus.study.model.vo.MistakeBookDetailsVo;
import com.joinus.study.model.vo.MistakeBookVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【mistake_book】的数据库操作Mapper
* @createDate 2025-03-11 09:47:33
* @Entity com.joinus.study.model.entity.MistakeBook
*/
public interface MistakeBookMapper extends BaseMapper<MistakeBook> {

    /**
     * 分页查询错题
     * @param page
     * @param param
     * @return
     */
    List<MistakeBookVo> pages(@Param("page") Page page, @Param("param") QueryMistakeBookParam param);

    /**
     * 根据问题id查询文件
     * @param questionId
     * @return
     */
    List<FilesVo> getFilesByQuestionId(@Param("questionId") UUID questionId);

    List<String> getCompleteQuestionsDetail(@Param("questionId") UUID questionId);

    void batchInsert(@Param("mistakeBookList") List<MistakeBook> mistakeBookList);

    MistakeBookDetailsVo getAdjacentData(@Param("id") Long id, @Param("studentId") Long studentId);

    void editById(@Param("param") MistakeBook mistakeBook);

    List<ExamMistakeInfoVo> getExamMistake(@Param("studentId") Long studentId, @Param("examId") UUID examId);

    void updateByMBId(@Param("mistakeBook") MistakeBook mistakeBook);
}




