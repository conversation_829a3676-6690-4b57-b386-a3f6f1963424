package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.joinus.study.model.param.ReadingPlanPageParam;
import com.joinus.study.model.vo.ReadingPersonalPlanVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ReadingPersonalPlanMapper extends BaseMapper<ReadingPersonalPlan> {

    /**
     * 阅读计划分页列表
     * @param page
     * @param pageParam
     * @return
     */
    List<ReadingPersonalPlanVo> planPages(Page<ReadingPersonalPlanVo> page, ReadingPlanPageParam pageParam);

    List<Map<String, Object>> getKnowledgePointsNames(List<String> knowledgePointIds);

    List<Map<String, Object>> getKnowledgePointsInfos();

    /**
     * 判断 一个周期只能有一个计划
     * @param studentId
     * @param startDate
     * @param endDate
     * @return
     */
    boolean checkPlanByStudentIdAndDate(@Param("studentId") Long studentId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    List<ReadingPersonalPlan> queryPlanStartRemindersList();

    /**
     * 查询首页阅读计划三条
     * @param studentId
     * @return
     */
    List<ReadingPersonalPlan> planUnderWayList(@Param("studentId") Long studentId);

    /**
     * 查询planId关联的阅读文章数量
     * @param planId
     * @return
     */
    int selectPassagesByPlanId(@Param("planId") Long planId);

    /**
     * 查询学生当前时间的阅读计划
     * @param studentId
     * @return
     */
    ReadingPersonalPlan getPlanInfoByStudentId(@Param("studentId") Long studentId);

    List<Map<String, Object>> getKnowledgePointsInfoGroupByType(@Param("grade") Integer grade, @Param("semester") Integer semester);

    /**
     * @description: 获取学生某月阅读计划日历
     * @author: lifengxu
     * @date: 2025/8/22 9:21
     */
    List<ReadingPersonalPlanVo> getPlanCalendar(@Param("studentId") Long studentId, @Param("start") LocalDate start, @Param("end") LocalDate end);
}




