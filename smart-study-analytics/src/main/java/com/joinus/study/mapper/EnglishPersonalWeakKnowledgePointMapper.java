package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.EnglishPersonalWeakKnowledgePoint;
import com.joinus.study.model.param.EnglishPersonalWeakKnowledgePointPageParam;
import com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EnglishPersonalWeakKnowledgePointMapper extends BaseMapper<EnglishPersonalWeakKnowledgePoint> {
    /**
     * 分页
     *
     * @param page
     * @param pageParam
     * @return
     */
    List<EnglishPersonalWeakKnowledgePointVO> pages(Page<EnglishPersonalWeakKnowledgePointVO> page, @Param("pageParam") EnglishPersonalWeakKnowledgePointPageParam pageParam);

    /**
     * 试卷诊断报告,薄弱知识点列表
     *
     * @param studentId
     * @param diagnoseRecordId
     * @return
     */
    List<EnglishPersonalWeakKnowledgePointVO> list(@Param("studentId") Long studentId, @Param("diagnoseRecordId") Long diagnoseRecordId);

}
