package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.PersonalExamDto;
import com.joinus.study.model.entity.MathExamsEntity;
import com.joinus.study.model.enums.ExamFileType;
import com.joinus.study.model.param.StudentAndTimePageParam;
import com.joinus.study.model.po.MathExamQuestinCountPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;


public interface MathExamsMapper extends BaseMapper<MathExamsEntity> {
    /**
     * 查询区域试卷列表
     * @return
     */
    List<PersonalExamDto> selectExamListByRegion(@Param("param") StudentAndTimePageParam param, long offset, long pageSize);

    Long selectExamListByRegion_count(@Param("param") StudentAndTimePageParam param);

    List<String> selectExamPhotos(@Param("examId") UUID examId,@Param("examFileType") ExamFileType examFileType);

    MathExamsEntity selectExamById(@Param("examId") UUID examId);

    String getExamName(@Param("examId") UUID examId);

    List<MathExamQuestinCountPo> listQuestionCountByIds(@Param("examIds") List<UUID> examIds);

}
