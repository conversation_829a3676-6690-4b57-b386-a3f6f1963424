package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ActiveStudentsEntity;
import com.joinus.study.model.vo.ActiveStudentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * @Description: u6d3bu8dc3u5b66u751fu89c6u56feMapper
 * @Author: anpy
 * @date: 2025/4/9
 */
@Mapper
public interface ActiveStudentsMapper extends BaseMapper<ActiveStudentsEntity> {
    
    /**
     * u6839u636eu5b66u751fIDu67e5u8be2u73edu7ea7ID
     *
     * @param studentId u5b66u751fID
     * @return u73edu7ea7ID
     */
    ActiveStudentVo getClassIdByStudentId(@Param("studentId") Long studentId);
    
    /**
     * 根据班级ID获取所有学生ID
     *
     * @param classId 班级ID
     * @return 学生ID列表
     */
    List<Long> getStudentIdsByClassId(@Param("classId") Long classId);

    /**
     * 根据年级ID获取所有学生ID
     *
     * @param gradeId 年级ID
     * @return 学生ID列表
     */
    List<Long> getStudentIdsByGradeId(@Param("gradeId") Long gradeId);

    List<ActiveStudentsEntity> getClassIdByGradeId(Long gradeId);
    
    /**
     * 根据年级ID和试卷ID获取所有参加考试的班级ID
     *
     * @param gradeId 年级ID
     * @param examId 试卷ID
     * @return 班级ID列表
     */
    List<Long> getClassIdsByGradeIdAndExamId(@Param("gradeId") Long gradeId, @Param("examId") UUID examId);

    List<Long> getExamClassIds(UUID examId, Long gradeId);

    ActiveStudentsEntity getByStudentId(@Param("studentId") Long studentId);
}
