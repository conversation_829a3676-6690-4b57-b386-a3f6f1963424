package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalAnalysisReport;
import com.joinus.study.model.param.ReadingPersonalKnowledgePointDataParam;
import com.joinus.study.model.param.ReadingPersonalPassageDataParam;
import com.joinus.study.model.param.ReadingPersonalQuestionTypeDataParam;
import com.joinus.study.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【reading_personal_analysis_report】的数据库操作Mapper
 * @createDate 2025-03-28 09:47:33
 * @Entity com.joinus.study.model.entity.ReadingPersonalAnalysisReportEntity
 */
public interface ReadingPersonalAnalysisReportMapper extends BaseMapper<ReadingPersonalAnalysisReport> {
    List<ReadingHistoryReportVo> queryReadingHistoryReportList(@Param("page") Page<ReadingHistoryReportVo> page, @Param("studentId") Long studentId);

    Map<String, Object> queryPersonalPassageData(ReadingPersonalPassageDataParam param);

    List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointList(ReadingPersonalKnowledgePointDataParam param);

    List<ReadingKnowledgePointVo> queryPersonalPassageKnowledgePointData(ReadingPersonalKnowledgePointDataParam param);

    List<ReadingQuestionTypeVo> queryPersonalPassageQuestionTypeData(ReadingPersonalQuestionTypeDataParam param);

    List<ReadingPeriodicReportDetailLineChartVo> queryPersonalPassageDataForLineChart(ReadingPersonalPassageDataParam param);

    List<ReadingHighFreqPerformanceVo> queryPersonalPassageHighFreqPerformanceData(ReadingPersonalPassageDataParam param);

    String getKnowledgePointByQuestionId(UUID questionId);

    List<Map<String, Object>> queryPassageInfo(@Param("reportId") Long reportId);
}
