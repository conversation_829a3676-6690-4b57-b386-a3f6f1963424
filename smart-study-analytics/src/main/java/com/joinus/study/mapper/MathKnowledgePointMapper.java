package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.bo.MathCatalogNodeStatisticsDto;
import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.entity.MathKnowledgePoint;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathKnowledgePoints
*/
public interface MathKnowledgePointMapper extends BaseMapper<MathKnowledgePoint> {

    KnowledgePointDto getKnowledgePointFromViewById(@Param("id") UUID id);
    List<KnowledgePointDto> getKnowledgePointFromViewByIds(@Param("ids") List<UUID> ids);
}




