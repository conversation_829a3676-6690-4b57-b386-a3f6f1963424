package com.joinus.study.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.PersonalExamQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.vo.ExamQuestionFileVo;
import com.joinus.study.model.vo.FlexiblyGeneratedVo;
import com.joinus.study.model.vo.MathQueryPageVo;
import com.joinus.study.model.vo.PersonalExamQuestionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【personal_exam_question】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.PersonalExamQuestion
*/
public interface PersonalExamQuestionMapper extends BaseMapper<PersonalExamQuestion> {

    void insertPersonalExamQuestion(@Param("entity") PersonalExamQuestion entity);

    /**
     * 方法描述 查询试卷上对应知识点的题目信息
     */
    List<ExamQuestionFileVo> selectQuestionInfoByknowledgePointId(@Param("personalExamId") Long personalExamId,@Param("knowledgePointId") UUID knowledgePointId);
    ExamQuestionFileVo selectQuestionFileInfoById(@Param("questionId") UUID questionId);


    String selectErrorQuestionNosByPersonalExamId(@Param("personalExamId")Long personalExamId,@Param("questionType") String questionType);

    List<PersonalExamQuestionVo> listByVo(@Param("page") Page page, @Param("vo")MathQueryPageVo vo);
}




