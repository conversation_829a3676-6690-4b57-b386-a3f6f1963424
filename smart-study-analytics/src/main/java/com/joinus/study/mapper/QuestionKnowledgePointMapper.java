package com.joinus.study.mapper;

import com.joinus.study.model.dto.KnowledgePointDto;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.KnowledgePointStatisticsVo;
import com.joinus.study.model.vo.MathKnowledgePointVO;
import com.joinus.study.model.vo.StudentInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_knowledge_point】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.QuestionKnowledgePoint
*/
public interface QuestionKnowledgePointMapper extends BaseMapper<QuestionKnowledgePoint> {

    List<KnowledgePointStatisticsVo> selectKnowledgePointStatisticsByExamId(@Param("id") Long id);

    List<StudentInfoVo> getStudentIds(@Param("studentId") Long studentId);

    List<KnowledgePointStatisticsVo> getClassKnowledgePointStatics(@Param("studentIds") List<Long> studentIds,
                                                                   @Param("classIds") List<Long> classIds,
                                                                   @Param("examId") UUID examId);

    List<MathKnowledgePointVO> selectKnowledgePointBySectionId(@Param("sectionId") UUID sectionId);

    List<MathKnowledgePointVO> selectKnowledgePointByKnowledgeIds(@Param("knowledgePointIds") List<String> knowledgePointIds);

    // 查询学生知识点掌握度
    Integer selectPointMasteryDegree(@Param("studentId") Long studentId,@Param("knowledgePointId") UUID knowledgePointId);

    List<KnowledgePointDto> getKnowledgePointFromView(@Param("sectionId") UUID sectionId, @Param("publisherName") String publisherName, @Param("knowledgePointId") UUID knowledgePointId, @Param("knowledgePointIds") List<UUID> knowledgePointIds);
}




