package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.dto.ReadingActivityLastPracticeDto;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description 对应表reading_personal_passages mapper
 * <AUTHOR>
 * @Date 2025/3/28 16:01
 **/
public interface ReadingPersonalPassagesMapper extends BaseMapper<ReadingPersonalPassages> {

    /**
     * 管理后台-分页
     *
     * @param page
     * @param pageParam
     * @return
     */
    List<ReadingPersonalPassagesBackendVO> pages(Page<ReadingPersonalPassagesBackendVO> page, @Param("pageParam") ReadingPersonalPassagesPageParam pageParam);

    /**
     * 管理后台-详情
     *
     * @param id
     * @return
     */
    ReadingPersonalPassagesBackendVO query(@Param("id") Long id);

    /**
     * 学生训练列表-分页
     *
     * @param page
     * @param param
     * @return
     */
    List<StudentReadingPersonalPassagesVo> studentPersonalList(@Param("page") Page page, @Param("param") ReadingPersonalPassagesQueryParam param);

    List<StudentReadingPersonalPassagesVo> studentPersonalList(@Param("param") ReadingPersonalPassagesQueryParam param);


    /**
     * 错题本-分页
     *
     * @param page
     * @param pageParam
     * @return
     */
    List<ReadingErrorBookVO> pagesOfErrorBook(Page<ReadingErrorBookVO> page, @Param("pageParam") ReadingErrorBookPageParam pageParam);

    List<ReadingPassageQuestionsAnswersVO> queryPersonalPassagesQuestionList(@Param("id") Long id);

    List<Long> listStudentsWithTrainingRecords(@Param("date") LocalDate date);

    /**
     * 方法描述 获取指定状态的题目
     *
     * @param: [param]
     * @return: java.util.List<com.joinus.study.model.vo.ReadingPersonalPassagesVo>
     * @author: DELL
     * @date: 2025/4/11 17:48
     */
    List<ReadingPersonalPassagesVo> selectReadingPersonalPassagesVoList(@Param("param") ReadingPersonalPassagesQueryParam param);

    ReadingPassages getPassageInfoByPersonalPassagesId(@Param("id") Long id);

    /**
     * 获取薄弱知识点列表
     *
     * @param studentId 学生id
     * @return
     * @author: zhaojianming
     * @date: 2025-05-06
     */
    List<ReadingWeekKnowledgePointVo> queryStudentWeekKnowledgePointList(@Param("studentId") Long studentId,
                                                                         @Param("param") ReadingStudentWeakKnowledgePointParam param,
                                                                         @Param("excludePersonalPassageId") Long excludePersonalPassageId);

    List<ReadingWeekKnowledgePointVo> queryStudentWeekKnowledgePointList(@Param("page") Page page,
                                                                         @Param("studentId") Long studentId,
                                                                         @Param("param") ReadingStudentWeakKnowledgePointParam param,
                                                                         @Param("excludePersonalPassageId") Long excludePersonalPassageId);

    //获取学生作答文章信息
    ReadingPersonalPassagesVo getPersonalPassagesById(@Param("id") Long id);

    //获取学生作答题目及答案信息
    List<ReadingPersonalPassageQuestionVo> getPersonalPassageQuestionList(@Param("id") Long id);

    BigDecimal getAccuracyRateByParam(ReadingPersonalPassageDataParam param);

    // [假期训练]获取学生假期训练上次（不包含当天）练习类型
    List<ReadingActivityLastPracticeDto> getActivityLastPractice(@Param("planId") Long planId, @Param("studentId") Long studentId);

    // [假期训练]获取当日未完成练习记录
    Long getPendingPersonalPassageIds(@Param("param") ReadingStudentPassagesQueryParam param, @Param("localDate") LocalDate localDate);

    // [假期训练]获取学生假期训练上次训练错误知识点
    List<ReadingWeekKnowledgePointVo> getActivityStudentWeakKnowledgePoint(@Param("personalPassageIds") List<Long> personalPassageIds);

    Integer getPersistDays(@Param("studentId") Long studentId);

    Integer getAllTimeSpent(@Param("studentId") Long studentId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    // 修改训练记录计划id
    Integer updatePlanIdIsNullById(@Param("id") Long id);

    Integer getPrintTimes(@Param("studentId") Long studentId);

    List<ReadingWeekTimeStatisticsVO> getWeekReadingTimeSpent(@Param("studentId") Long studentId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    List<ReadingGrowthCycleVO> getWeekReadingGrowthCycle(@Param("studentId") Long studentId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    ReadingPersonalUserVO getCountByStudentId(@Param("studentId") Long studentId);

    Integer getSummerTrainingCampDays(@Param("studentId") Long studentId);
}
