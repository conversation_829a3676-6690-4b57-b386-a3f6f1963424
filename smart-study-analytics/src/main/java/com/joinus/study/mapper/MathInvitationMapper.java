package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.MathInvitation;

/**
* <AUTHOR>
* @description 针对表【math_invitation(数学邀请记录表)】的数据库操作Mapper
* @createDate 2025-06-25 17:29:13
* @Entity com.joinus.study.model.entity.MathInvitation
*/
public interface MathInvitationMapper extends BaseMapper<MathInvitation> {

    void mathInvitationAdd(MathInvitation param);
}




