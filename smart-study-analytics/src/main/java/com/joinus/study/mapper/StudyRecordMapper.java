package com.joinus.study.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.StudyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.param.MistakeBookAddParam;
import com.joinus.study.model.param.QueryStudyRecordParam;
import com.joinus.study.model.vo.MistakeBookDetailsVo;
import com.joinus.study.model.vo.QuestionVo;
import com.joinus.study.model.vo.StudyRecordQuestionDetailsVo;
import com.joinus.study.model.vo.StudyRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【study_record】的数据库操作Mapper
* @createDate 2025-03-11 09:48:14
* @Entity com.joinus.study.model.entity.StudyRecord
*/
public interface StudyRecordMapper extends BaseMapper<StudyRecord> {

    /**
     * 查询学习记录分页列表
     * @param param
     * @param page
     * @return
     */
    List<StudyRecordVo> pages(@Param("param") QueryStudyRecordParam param, @Param("page") Page page);

    /**
     * 查询学习记录详情
     * @param id
     * @return
     */
    List<StudyRecordQuestionDetailsVo> detail(@Param("id") Long id);

    /**
     * 根据问题id查询问题
     * @param questionId
     * @return
     */
    QuestionVo getQuestionById(@Param("questionId") UUID questionId);

    List<Map<String, Object>> questionDetail(@Param("studyId") Long studyId, @Param("questionId") String questionId, @Param("studentId") Long studentId);

    List<String> questionsDetail(@Param("studyId") Long studyId, @Param("questionIds") List<String> questionIds, @Param("studentId") Long studentId);

    StudyRecordQuestionDetailsVo getQuestionAnswer(@Param("questionId") UUID questionId);

    List<StudyRecordQuestionDetailsVo> getStudyQuestionAnswer(@Param("bookAddParam") MistakeBookAddParam bookAddParam);

    String getQuestionKnowledgesBystudyId(@Param("sourceId") UUID sourceId, @Param("questionId") UUID questionId, @Param("studentId") Long studentId);
}




