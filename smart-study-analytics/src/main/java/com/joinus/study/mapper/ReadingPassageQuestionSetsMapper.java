package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.dto.ReadingSetsQuestionDto;
import com.joinus.study.model.entity.ReadingPassageQuestionSets;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPassageQuestionSetsVo;
import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
import com.joinus.study.model.vo.ReadingQuestionSetsItemViewVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface ReadingPassageQuestionSetsMapper extends BaseMapper<ReadingPassageQuestionSets> {

    List<ReadingPassageQuestionSetsVo> queryQuestionSetsList(@Param("page") Page page, @Param("passageId") UUID passageId);

    List<ReadingQuestionSetsItemViewVo> getQuestionSetsItemInfo(@Param("questionSetId") UUID questionSetId);

    List<Map<String, Object>> getKnowledgePoints(@Param("questionId") UUID questionId);

    /**
     * @Description [阅读训练]获取套题id
     * <AUTHOR>
     * @date 2025/5/12
     */
    List<UUID> getReadingTrainingSetsIds(@Param("param") ReadingStudentPassagesQueryParam param);

    /**
     * @Description 根据套题id获取文章信息
     * <AUTHOR>
     * @date 2025/5/12
     */
    ReadingPersonalPassagesVo getPassagesBySetsId(@Param("setsId") UUID setsId);

    /**
     * @Description 根据套题id获取套题题目
     * <AUTHOR>
     * @date 2025/5/12
     */
    List<ReadingPersonalPassageQuestionVo> getQuestionsByParam(@Param("setsId") UUID setsId, @Param("questionIds") List<UUID> questionIds);


    /**
     * @Description [练习计划]获取套题ID、题目ID
     * <AUTHOR>
     * @date 2025/5/12
     */
    List<ReadingSetsQuestionDto> getExercisePlanSetsIds(@Param("param") ReadingStudentPassagesQueryParam param);

    /**
     * @description: [假期强化练习] 获取套题ID
     * @author: lifengxu
     * @date: 2025/5/29
     */
    List<UUID> getHolidayIntensiveTrainingSetsIds(@Param("param") ReadingStudentPassagesQueryParam param);
}
