package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.excel.ReadingPersonalEquityActivityCodeExcel;
import com.joinus.study.model.entity.ReadingPersonalEquityActivity;
import com.joinus.study.model.param.ReadingPersonalEquityActivityPageParam;
import com.joinus.study.model.param.ReadingPersonalEquityActivityUsedRecordPageParam;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityPageItemVO;
import com.joinus.study.model.vo.ReadingPersonalEquityActivityUsedRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadingPersonalEquityActivityMapper extends BaseMapper<ReadingPersonalEquityActivity> {

    List<ReadingPersonalEquityActivityUsedRecordVo> selectUsedRecordPage(@Param("page") Page<ReadingPersonalEquityActivityUsedRecordVo> page, @Param("pageParam") ReadingPersonalEquityActivityUsedRecordPageParam pageParam);

    /**
     * 邀请码下载列表
     *
     * @param id
     * @return
     */
    List<ReadingPersonalEquityActivityCodeExcel> downloadListInvitationCode(@Param("id") Long id);

    List<ReadingPersonalEquityActivityPageItemVO> pages(@Param("page") Page<ReadingPersonalEquityActivityPageItemVO> page, @Param("pageParam") ReadingPersonalEquityActivityPageParam pageParam);
}




