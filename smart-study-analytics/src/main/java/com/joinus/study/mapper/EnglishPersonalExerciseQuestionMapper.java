package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO;
import com.joinus.study.model.entity.EnglishPersonalExerciseQuestion;
import com.joinus.study.model.vo.EnglishPersonalExerciseQuestionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EnglishPersonalExerciseQuestionMapper extends BaseMapper<EnglishPersonalExerciseQuestion> {
    List<EnglishPersonalExerciseQuestionDTO> listByPersonalExerciseId(@Param("personalExerciseId") Long personalExerciseId);
}
