package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ClassExamStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.UUID;

/**
 * @Description: 班级考试统计Mapper
 * @Author: anpy
 * @date: 2025/4/9
 */
@Mapper
public interface ClassExamStatisticsMapper extends BaseMapper<ClassExamStatistics> {
    
    /**
     * 根据班级ID、试卷ID和考试场次查询班级考试统计信息
     *
     * @param classId 班级ID
     * @param examId  试卷ID
     * @return 班级考试统计信息
     */
    ClassExamStatistics selectByClassIdAndExamId(
            @Param("classId") Long classId, 
            @Param("examId") UUID examId);
}
