package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO;
import com.joinus.study.model.entity.EnglishExamDiagnoseQuestion;
import com.joinus.study.model.vo.EnglishExamReportKnowledgePointVO;
import com.joinus.study.model.vo.EnglishExamReportQuestionTypeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/9/1 18:22
 */
@Mapper
public interface EnglishExamDiagnoseQuestionMapper extends BaseMapper<EnglishExamDiagnoseQuestion> {

    Integer queryKnowledgePointNumber(@Param("recordId") Long recordId);

    Integer queryWeakKnowledgeNumber(@Param("recordId") Long recordId);

    List<EnglishExamReportQuestionTypeVO> listQuestionType(@Param("recordId") Long recordId);

    List<EnglishExamReportKnowledgePointVO> listKnowledgePoints(@Param("recordId") Long recordId);

    List<EnglishExamDiagnoseQuestion> listMasterQuestions(@Param("examId") UUID examId);

    List<String> listQuestionTypeByPointId(@Param("recordId") Long recordId, @Param("pointId") UUID pointId);

    Integer queryCountByPointId(@Param("recordId") Long recordId, @Param("pointId") UUID pointId);

    Integer queryWrongCountByPointId(@Param("recordId") Long recordId, @Param("pointId") UUID pointId);

    List<EnglishExamDiagnoseQuestionDTO> listByRecordIdsAndKnowledgePointId(@Param("diagnoseRecordIds") List<Long> diagnoseRecordIds, @Param("knowledgePointId") UUID knowledgePointId);

    List<EnglishExamDiagnoseQuestionDTO> listByRecordIdAndKnowledgePointId(@Param("diagnoseRecordId") Long diagnoseRecordId,@Param("knowledgePointId")  UUID knowledgePointId);

}
