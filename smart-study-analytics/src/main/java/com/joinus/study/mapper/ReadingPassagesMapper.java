package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPassages;
import com.joinus.study.model.entity.ReadingUnits;
import com.joinus.study.model.param.ReadingPassageQuestionsPageParam;
import com.joinus.study.model.param.ReadingPassagesPageParam;
import com.joinus.study.model.vo.ReadingGradeVO;
import com.joinus.study.model.vo.ReadingPassageQuestionsVO;
import com.joinus.study.model.vo.ReadingPassagesBackendVO;
import com.joinus.study.model.vo.ReadingQuestionSetsViewVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

public interface ReadingPassagesMapper extends BaseMapper<ReadingPassages> {
    /**
     * 分页
     *
     * @param page
     * @param pageParam
     * @return
     */

    List<ReadingPassagesBackendVO> pages(Page<ReadingPassagesBackendVO> page, @Param("pageParam") ReadingPassagesPageParam pageParam);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ReadingPassagesBackendVO query(@Param("id") UUID id);

    /**
     * 年级-学期-单元列表
     *
     * @return
     */
    List<ReadingGradeVO> listGradeSemesterUnit();

    /**
     * 文章的题目分页
     *
     * @param page
     * @param pageParam
     * @return
     */
    List<ReadingPassageQuestionsVO> pagesOfQuestions(Page<ReadingPassageQuestionsVO> page, @Param("pageParam") ReadingPassageQuestionsPageParam pageParam);

    /**
     * 查询问题详情
     *
     * @param questionId
     * @return
     */
    ReadingPassageQuestionsVO queryQuestion(@Param("questionId") UUID questionId);

    /**
     * 文章详情
     *
     * @param passageId
     * @return
     */
    ReadingQuestionSetsViewVo getPassageInfo(@Param("passageId") UUID passageId);

    /**
     * @description: 根据个人训练记录ID查询单元信息
     * @author: lifengxu
     * @date: 2025/8/29 13:38
     */
    ReadingUnits getUnitInfoByPersonalId(@Param("personalId") Long personalId);
}
