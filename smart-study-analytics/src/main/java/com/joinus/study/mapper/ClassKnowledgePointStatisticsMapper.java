package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ClassKnowledgePointStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 班级知识点正确率统计Mapper
 * @Author: anpy
 * @date: 2025/4/9
 */
@Mapper
public interface ClassKnowledgePointStatisticsMapper extends BaseMapper<ClassKnowledgePointStatistics> {
    
    /**
     * 根据班级ID、试卷ID和考试场次查询班级知识点正确率统计信息
     *
     * @param classId 班级ID
     * @param examId  试卷ID
     * @return 班级知识点正确率统计信息列表
     */
    List<ClassKnowledgePointStatistics> selectByClassIdAndExamId(
            @Param("classId") Long classId, 
            @Param("examId") UUID examId);
    
    /**
     * 根据班级ID、试卷ID、考试场次和知识点ID查询班级知识点正确率统计信息
     *
     * @param classId         班级ID
     * @param examId          试卷ID
     * @param knowledgePointId 知识点ID
     * @return 班级知识点正确率统计信息
     */
    ClassKnowledgePointStatistics selectByClassIdExamIdAndKnowledgePointId(
            @Param("classId") Long classId, 
            @Param("examId") UUID examId,
            @Param("knowledgePointId") UUID knowledgePointId);
}
