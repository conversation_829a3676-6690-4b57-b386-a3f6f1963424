package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.MathActivityStudentRecord;
import com.joinus.study.model.dto.ExamSummerPlanListDto;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.MathActivityWeekUnitStudent;
import com.joinus.study.model.po.MathActivityWeekUnitPo;
import com.joinus.study.model.vo.MathActivityStudentStudyRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/6/13 16:45
 **/
@Mapper
public interface MathActivityWeekUnitStudentMapper  extends BaseMapper<MathActivityWeekUnitStudent> {
    //批量插入
    void insertBatch(List<MathActivityWeekUnitStudent> activityWeekUnitStudents);
    //批量删除
    void deleteByActivityIdStudentId(@Param("activityId") Long activityId, @Param("studentId") Long studentId);

    List<MathActivityStudentRecord> selectMathActivityStudentStudyRecord(@Param("activityId") Long activityId, @Param("studentId") Long studentId);

    List<Date> selectFinishedAtByStudentId(@Param("studentId")Long studentId);
    /**
     * 查询学生暑期全部学习计划总览
     * @param studentId
     * @return
     */
    List<Map<String, Object>> selectSummerPlansStatistics(@Param("studentId") Long studentId,@Param("activityId") Long activityId);

    /**
     * 根据学生id和第几周来查询学生暑期全部学习计划
     * @param studentId
     * @param weekSort
     * @return
     */
    List<ExamSummerPlanListDto> selectStudentSummerPlans(@Param("studentId") Long studentId, @Param("weekSort") Integer weekSort,
            @Param("activityId") Long activityId);

    /**
     * 查询学生单元掌握度
     * @param sectionId
     * @param studentId
     * @return
     */
    Double selectSectionMasteryDegree(@Param("sectionId") String sectionId, @Param("studentId") Long studentId, @Param("examAnalyzeResultId") Long examAnalyzeResultId);

    /**
     * 更新ExamIdHistory
     * @param weekUnitStudentId
     * @param examIdHistory
     */
    void updateExamIdHistoryById(@Param("id") Long weekUnitStudentId,@Param("examIdHistory") String examIdHistory);

    Long selectIdByExamIdHistory(@Param("examId") String examId,@Param("studentId") Long studentId);

    /**
     * 查询试卷的类型，区分是不是综合测评的卷子
     * @param studentId
     * @param examId
     * @return
     */
    MathActivityWeekUnit selectExamTypeByStudentAndExamId(@Param("studentId") Long studentId, @Param("examId") String examId);

    /**
     * 查询综合测评状态是否可以测试
     * @param weekType
     * @param studentId
     */
    List<Map<String, Object>> selectComprehensiveTestStatus(@Param("weekType") String weekType,@Param("studentId") Long studentId);

    /**
     * 查询学生当前活动信息
     * @param activityId
     * @param studentId
     * @return
     */
    Map<String, Object> getStudentCurrentWeekInfo(Long activityId, Long studentId);

    MathActivityWeekUnit getMathActivityWeekUnitByTrainingExamId(UUID examId);

    List<MathActivityWeekUnitPo> selectExamTypeByExamIds(@Param("examIds") List<UUID> examIds);
}
