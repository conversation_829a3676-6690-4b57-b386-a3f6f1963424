package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.SchoolConfig;

/**
* <AUTHOR>
* @description 针对表【school_config(学科教育学校配置表)】的数据库操作Mapper
* @createDate 2025-06-25 17:28:59
* @Entity com.joinus.study.model.entity.SchoolConfig
*/
public interface SchoolConfigMapper extends BaseMapper<SchoolConfig> {

    void schoolConfigAdd(SchoolConfig schoolConfig);

    void updateSchoolConfigById(SchoolConfig schoolConfig);
}




