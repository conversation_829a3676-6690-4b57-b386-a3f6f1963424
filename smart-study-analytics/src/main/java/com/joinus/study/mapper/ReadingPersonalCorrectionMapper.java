package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalCorrection;
import com.joinus.study.model.param.ReadingPersonalCorrectionPageParam;
import com.joinus.study.model.vo.ReadingPersonalCorrectionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReadingPersonalCorrectionMapper extends BaseMapper<ReadingPersonalCorrection> {
    /**
     * 分页-管理后台
     *
     * @param page
     * @param pageParam
     * @return
     */
    List<ReadingPersonalCorrectionVO> pages(Page<ReadingPersonalCorrectionVO> page, @Param("pageParam") ReadingPersonalCorrectionPageParam pageParam);
}
