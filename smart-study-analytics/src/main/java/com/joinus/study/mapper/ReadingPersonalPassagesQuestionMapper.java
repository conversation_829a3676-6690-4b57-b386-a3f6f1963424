package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ReadingPersonalPassagesQuestions;
import com.joinus.study.model.entity.ReadingUnits;
import com.joinus.study.model.param.ReadingQuestionknowledgePintVo;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * @Description 对应表reading_personal_passage_questions mapper
 * <AUTHOR>
 * @Date 2025/3/28 16:01
 **/
public interface ReadingPersonalPassagesQuestionMapper extends BaseMapper<ReadingPersonalPassagesQuestions> {

    /**
     * 根据文章id查询文章题目 及 答案
     *
     * @param passageId 文章id
     * @return
     */
    List<ReadingPersonalPassageQuestionVo> selectPersonalPassageQuestionVoList(@Param("passageId") UUID passageId, @Param("questionIds") List<UUID> questionIds);

    List<ReadingPersonalPassageQuestionVo> getPersonalPassageQuestionVoList(@Param("passageId") UUID passageId, @Param("questionIds") List<UUID> questionIds);

    List<ReadingPersonalPassageQuestionVo> selectPersonalPassageQuestionVoByPassageId(Long personalPassageId);

    /**
     * 方法描述 获取题目对应知识点
     *
     * @param: [questionIds]
     * @return: java.util.List<com.joinus.study.model.param.ReadingQuestionknowledgePintVo>
     * @author: DELL
     * @date: 2025/4/1 14:34
     */
    List<ReadingQuestionknowledgePintVo> getPointIdsByQuestionIds(List<UUID> questionIds);

    List<ReadingPersonalPassagesQuestions> getPassagesQuestionsListByStudentId(@Param("studentId") Long studentId,
                                                                               @Param("startDate") LocalDate startDate,
                                                                               @Param("endDate") LocalDate endDate);

    /**
     * 方法描述 获取学生已做题ids
     *
     * @param: [studentId]
     * @return: java.util.List<java.util.UUID>
     * @author: DELL
     * @date: 2025/4/1 14:34
     */
    List<UUID> getStudentDoQuestionIds(@Param("studentId") Long studentId, @Param("entryType") Integer entryType);

    /**
     * @description: 获取学生已做题套题ids
     * @author: lifengxu
     * @date: 2025/7/11 13:48
     */
    List<UUID> getStudentDoSetsIds(@Param("studentId") Long studentId, @Param("entryType") Integer entryType);

    /**
     * 方法描述 更新Ai批改结果
     *
     * @param: [questions]
     * @return: void
     * @author: DELL
     * @date: 2025/4/1 14:34
     */
    void updateResultCode(ReadingPersonalPassagesQuestions questions);

    /**
     * 方法描述 根据id查询知识点
     *
     * @param: [id]
     * @return: java.lang.String
     * @author: lianli
     * @date: 2025/4/1 14:34
     */
    String selectKnowledgePointsByid(Long id);

    /**
     * @Description 获取学生当前知识点错题
     * <AUTHOR>
     * @date 2025/5/7
     */
    List<ReadingPersonalPassageQuestionVo> getCurrentKnowledgeErrorQuestions(@Param("param") ReadingStudentPassagesQueryParam param);

    /**
     * @Description 根据知识点获取文章关联题目
     * <AUTHOR>
     * @date 2025/5/8
     */
    List<ReadingPersonalPassageQuestionVo> getQuestionsByKnowledgePoint(@Param("param") ReadingStudentPassagesQueryParam param,
                                                                        @Param("existQuestionIds") Set<UUID> existQuestionIds,
                                                                        @Param("existQuestionTypes") Set<String> existQuestionTypes,
                                                                        @Param("count") int count);

    /**
     * @description: [强化训练]获取学生假期训练已练习套题ID
     * @author: lifengxu
     * @date: 2025/5/29
     */
    List<UUID> getActivityStudentDoSetsIds(@Param("param") ReadingStudentPassagesQueryParam param,
                                           @Param("personalPassageId") Long personalPassageId,
                                           @Param("localDate") LocalDate localDate,@Param("status") Integer status);

    /**
     * @description: [强化训练]获取学生已经完成单元ID
     * @author: lifengxu
     * @date: 2025/5/29
     */
    List<UUID> getCompletedUnitIds(@Param("param") ReadingStudentPassagesQueryParam param,@Param("completedNumber") Integer completedNumber);

    /**
     * @description: [强化训练]获取待出题单元ID
     * @author: lifengxu
     * @date: 2025/5/29
     */
    UUID getActivityPracticePendingUnitIds(@Param("param") ReadingStudentPassagesQueryParam param);

    /**
     * @description: [巩固复习]获取学生假期训练练习记录IDs
     * @author: lifengxu
     * @date: 2025/5/29
     */
    List<Long> getActivityStudentDoPersonalPassageIds(@Param("param") ReadingStudentPassagesQueryParam param,
                                                      @Param("localDate") LocalDate localDate,@Param("status") Integer status);

    /**
     * @description: [巩固复习]获取学生假期训练练习题目IDs
     * @author: lifengxu
     * @date: 2025/6/5
     */
    List<UUID> getActivityStudentDoQuestionIds(@Param("param") ReadingStudentPassagesQueryParam param);


    /**
     * @Description [定向爆破]获取文章ID、题目ID
     * <AUTHOR>
     * @date 2025/5/12
     */
    List<ReadingPersonalPassageQuestionVo> getDirectionalBlastingQuestionIds(@Param("param") ReadingStudentPassagesQueryParam param,
                                                                             @Param("count") Integer count);

    /**
     * @description: [单元列表]统计学生各个单元的练习次数、练习题数、正确率
     * @author: lifengxu
     * @date: 2025/8/21 16:19
     */
    List<ReadingUnits> getUnitPracticeInfo(@Param("studentId") Long studentId, @Param("unitIds") List<UUID> unitIds);
}
