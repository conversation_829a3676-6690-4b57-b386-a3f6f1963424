package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.EnglishExam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/9/1 13:55
 */
@Mapper
public interface EnglishExamMapper extends BaseMapper<EnglishExam> {

    List<EnglishExam> listByName(@Param("name") String name);

}
