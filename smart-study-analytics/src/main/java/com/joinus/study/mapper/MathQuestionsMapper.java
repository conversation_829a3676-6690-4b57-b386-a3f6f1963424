package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.dto.AnswerIdForFeedback;
import com.joinus.study.model.dto.PastExamQuestionDto;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.entity.MathQuestionsEntity;
import com.joinus.study.model.vo.FileVO;
import com.joinus.study.model.vo.FlexiblyGeneratedVo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;

public interface MathQuestionsMapper extends BaseMapper<MathQuestionsEntity> {
    QuestionAnswerDto selectAnswersById(@Param("questionId") UUID questionId);
    QuestionAnswerDto selectAnswersByQuestionId(@Param("questionId") UUID questionId);

    AnswerIdForFeedback selectAnswerIdByRequestIdForFeedback(@Param("questionId") UUID questionId);

    List<FlexiblyGeneratedVo> selectQuestionInfo(@Param("questionId") UUID questionId);

    PastExamQuestionDto selectPastExamQuestionDto(@Param("examId")UUID examId,@Param("questionId") UUID questionId);

    List<FileVO> listFilesById(@Param("questionId") UUID questionId);
}
