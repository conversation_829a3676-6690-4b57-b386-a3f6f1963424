package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingKnowledgePoints;
import com.joinus.study.model.entity.ReadingPersonalFeedback;
import com.joinus.study.model.param.ReadingPersonalFeedbackPageParam;
import com.joinus.study.model.vo.ReadingPersonalFeedbackDetailsVo;
import com.joinus.study.model.vo.ReadingPersonalFeedbackVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description:
 * @date: 2025/5/6 15:05
 */
public interface ReadingPersonalFeedbackMapper extends BaseMapper<ReadingPersonalFeedback> {

    /**
     * 后台反馈分页
     * @param pageParam
     * @param page
     * @return
     */
    List<ReadingPersonalFeedbackVO> pages(Page<ReadingPersonalFeedbackVO> page, @Param("pageParam") ReadingPersonalFeedbackPageParam pageParam);

    /**
     * 反馈详情
     * @param id
     * @return
     */
    ReadingPersonalFeedbackDetailsVo getDetails(@Param("id") Long id);
   /**
     * 获取题目知识点
     * @param questionId
     * @return
     */
    List<ReadingKnowledgePoints> getKnowledgePoints(@Param("questionId") UUID questionId);
}
