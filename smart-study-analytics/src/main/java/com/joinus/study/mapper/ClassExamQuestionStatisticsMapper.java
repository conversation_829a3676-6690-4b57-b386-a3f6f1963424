package com.joinus.study.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.study.model.entity.ClassExamQuestionStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 班级题目正确率统计Mapper
 * @Author: anpy
 * @date: 2025/4/9
 */
@Mapper
public interface ClassExamQuestionStatisticsMapper extends BaseMapper<ClassExamQuestionStatistics> {
    
    /**
     * 根据班级ID、试卷ID和考试场次查询班级题目正确率统计信息
     *
     * @param classId 班级ID
     * @param examId  试卷ID
     * @return 班级题目正确率统计信息列表
     */
    List<ClassExamQuestionStatistics> selectByClassIdAndExamId(
            @Param("classId") Long classId, 
            @Param("examId") UUID examId);
    
    /**
     * 根据班级ID、试卷ID、考试场次和题目ID查询班级题目正确率统计信息
     *
     * @param classId    班级ID
     * @param examId     试卷ID
     * @param questionId 题目ID
     * @return 班级题目正确率统计信息
     */
    ClassExamQuestionStatistics selectByClassIdExamIdAndQuestionId(
            @Param("classId") Long classId, 
            @Param("examId") UUID examId,
            @Param("questionId") UUID questionId);
}
