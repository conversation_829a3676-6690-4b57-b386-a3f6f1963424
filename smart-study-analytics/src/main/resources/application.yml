server:
  port: 9102
  servlet:
    context-path: /api/smart-study
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: smart-study-analytics
  datasource:
    dynamic:
      primary: postgresql
      strict: false
      datasource:
        postgresql:
          driver-class-name: org.postgresql.Driver
          url: ***********************************************************
          username: smart_study_analytics
          password: qF9hI4bN3y
  kafka:
    producer:
      retries: 5
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        group-id: smart-study-analytics
    bootstrap-servers: 192.168.0.106:9093,192.168.0.125:9093,192.168.0.179:9093
apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true
mybatis-plus:
  global-config:
    db-config:
      insert-strategy:
        not_null
  configuration:
    jdbc-type-for-null: 'null'
management:
  endpoints:
    web:
      exposure:
        # 暴露prometheus、健康检查、信息和指标端点
        include: health
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
token:
  white:
    pad: /a,/b
logging:
  filter:
    enabled: true  # 是否启用日志记录，默认为 true
    # 排除的URL模式，逗号分隔，支持Ant风格
    exclude-url-patterns: /api/public/**,/health,/metrics,/actuator/health,/edu-knowledge-hub/text/stream/mock,**/export,**/download,**/upload,**/file/**,/math/anon/logging/exams/*/questions/*
    # 流式接口URL模式，逗号分隔，只记录请求不记录响应
    streaming-url-patterns: /edu-knowledge-hub/question-single-analysis-results,/edu-knowledge-hub/ai-question-single-analysis-results,/edu-knowledge-hub/question-multi-analysis-results,/edu-knowledge-hub/flexibly-generating,/edu-knowledge-hub/text/stream/**