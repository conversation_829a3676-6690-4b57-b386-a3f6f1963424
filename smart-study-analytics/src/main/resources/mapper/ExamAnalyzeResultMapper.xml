<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ExamAnalyzeResultMapper">

    <select id="insertExamAnalyzeResult" parameterType="com.joinus.study.model.entity.ExamAnalyzeResult" resultType="long">
        INSERT INTO exam_analyze_result
        (exam_id,
         student_id,
         overall_score,
         correct_rate,
         percentile,
         total_knowledge_points,
         mastered_knowledge_points,
         weak_knowledge_points,
         result,
         personal_exam_id,
         parent_id)
        VALUES (#{entity.examId}::uuid,
                #{entity.studentId},
                #{entity.overallScore}::overall_score_enum,
                #{entity.correctRate}::jsonb,
                #{entity.percentile},
                #{entity.totalKnowledgePoints},
                #{entity.masteredKnowledgePoints},
                #{entity.weakKnowledgePoints},
                #{entity.result}::exam_analyze_result_result_enum,
                #{entity.personalExamId},
                #{entity.parentId})
        RETURNING id
    </select>


    <select id="calculatePercentile" resultType="java.math.BigDecimal">
        WITH student_class AS (
        -- 获取指定学生的班级ID
        SELECT class_id
        FROM view_active_students
        WHERE student_id = #{studentId}
        ),
        latest_results AS (
        SELECT DISTINCT ON (ear.student_id)
        ear.student_id,
        ear.correct_rate,
        vas.class_id,
        ear.created_at
        FROM exam_analyze_result ear
        JOIN view_active_students vas ON ear.student_id = vas.student_id
        WHERE ear.exam_id = #{examId}
        AND ear.deleted_at IS NULL
        AND ear.student_id != #{studentId}
        AND vas.class_id = (SELECT class_id FROM student_class)
        ORDER BY ear.student_id, ear.created_at DESC
        )
        SELECT
        COALESCE(
        ROUND(
        COUNT(CASE
        WHEN CAST(REGEXP_REPLACE(lr.correct_rate ->> 'percentile', '[^0-9.]', '', 'g') AS DECIMAL(10, 2)) &lt;= CAST(#{percentile} AS DECIMAL(10, 2))
        THEN 1
        END) * 100.0 /
        NULLIF(COUNT(*), 0),
        100
        ),
        100
        ) AS percentile
        FROM latest_results lr
    </select>

    <select id="selectExamAnalyzeResultList" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        SELECT pe.id as personalExamId,
               pe.exam_id AS examId,
               pe.exam_name AS examName,
               ear.created_at AS createdAt,
               COUNT(DISTINCT peq.question_id) AS questionCount,
               me.source,
               ear.result AS result,
               ear.id AS examAnalyzeResultId,
               pe.grade           as grade,
               pe.publisher       as publisher,
               pe.book_volume     as bookVolume
        FROM public.exam_analyze_result ear
                 INNER JOIN public.math_exams me ON me.id = ear.exam_id and me.source in ('用户上传','常规考试卷')
                 left JOIN public.personal_exam pe ON pe.id = ear.personal_exam_id
                 left JOIN public.personal_exam_question peq ON peq.personal_exam_id = pe.id
        WHERE ear.student_id = #{studentId} and ear.deleted_at is null
        GROUP BY ear.id, pe.exam_name, ear.exam_id, ear.created_at, ear.result, pe.id, me.source, pe.grade, pe.publisher, pe.book_volume
        order by ear.created_at desc
    </select>

    <select id="selectSpecializedExamAnalyzeResultListV2" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        with math_exam_question_count as
                (select
                     pe.id,
                     me.source
                from personal_exam pe
                       inner join math_exams me on pe.exam_id = me.id
                where pe.student_id =  #{studentId}
                and me.source in ('专项训练', '考点盲区训练', '暑期训练','同步学练测','知识点专项训练','错题集专项训练')
                and pe.deleted_at is null
                and me.deleted_at is null)
        select pe.id              as personalExamId,
               pe.exam_id         as examId,
               pe.exam_name       as examName,
               pe.created_at      as createdAt,
               meqc.source        as source,
               ear.result         as result,
               ear.id             as examAnalyzeResultId,
               pe.grade           as grade,
               pe.publisher       as publisher,
               pe.book_volume     as bookVolume
        from personal_exam pe
                 inner join math_exam_question_count meqc on pe.id = meqc.id
                 left join exam_analyze_result ear on ear.personal_exam_id = pe.id and ear.deleted_at is null
        where pe.student_id = #{studentId}
          and pe.deleted_at is null
        order by pe.created_at desc
    </select>


    <select id="pageAllRecords" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        with math_exam_question_count as
                 (select pe.id,
                         me.source
                  from personal_exam pe
                           inner join math_exams me on pe.exam_id = me.id
                  where pe.student_id = #{studentId}
                    and me.source in ('专项训练', '考点盲区训练', '暑期训练','同步学练测','知识点专项训练','错题集专项训练')
                    and pe.deleted_at is null
                    and me.deleted_at is null)
        select * from (
                  SELECT pe.id as personalExamId,
                         pe.exam_id AS examId,
                         pe.exam_name AS examName,
                         ear.created_at AS createdAt,
                         me.source,
                         ear.result AS result,
                         ear.id AS examAnalyzeResultId,
                         pe.grade           as grade,
                         pe.publisher       as publisher,
                         pe.book_volume     as bookVolume
                  FROM public.exam_analyze_result ear
                           INNER JOIN public.math_exams me ON me.id = ear.exam_id and me.source in ('用户上传','常规考试卷')
                           left JOIN public.personal_exam pe ON pe.id = ear.personal_exam_id
                  WHERE ear.student_id = #{studentId} and ear.deleted_at is null
                  GROUP BY ear.id, pe.exam_name, ear.exam_id, ear.created_at, ear.result, pe.id, me.source, pe.grade, pe.publisher, pe.book_volume
                  union
                  select pe.id              as personalExamId,
                         pe.exam_id         as examId,
                         pe.exam_name       as examName,
                         pe.created_at      as createdAt,
                         meqc.source        as source,
                         ear.result         as result,
                         ear.id             as examAnalyzeResultId,
                         pe.grade           as grade,
                         pe.publisher       as publisher,
                         pe.book_volume     as bookVolume
                  from personal_exam pe
                           inner join math_exam_question_count meqc on pe.id = meqc.id
                           left join exam_analyze_result ear on ear.personal_exam_id = pe.id and ear.deleted_at is null
                  where pe.student_id = #{studentId}
                    and pe.deleted_at is null
        ) a order by a.createdAt desc

    </select>

    <select id="selectExamAnalyzeResultByExamId" resultType="com.joinus.study.model.vo.ExamAnalysisReportVo">
        SELECT
            me.ID AS exam_id,
            pe.id AS personal_exam_id,
            pe.exam_name AS name,
            me.source,
            pe.publisher,
            aer.student_id,
            COUNT ( peq.question_id ) AS total_questions,
            aer.overall_score,
            aer.correct_rate AS correct_rate,
            aer.percentile,
            aer.total_knowledge_points,
            aer.mastered_knowledge_points,
            aer.weak_knowledge_points,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'MULTIPLE_CHOICE' THEN peq.question_id END ) AS multiple_choice_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'FILL_IN_THE_BLANK' THEN peq.question_id END ) AS fill_blank_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'PROBLEM_SOLVING' THEN peq.question_id END ) AS free_response_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'TRUE_FALSE' THEN peq.question_id END ) AS true_false_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'CALCULATION' THEN peq.question_id END ) AS calculation_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'APPLICATION' THEN peq.question_id END ) AS application_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'PROOF' THEN peq.question_id END ) AS proof_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'OTHER' THEN peq.question_id END ) AS other_total
        FROM
            exam_analyze_result aer
            LEFT JOIN math_exams me ON aer.exam_id = me.id
            LEFT JOIN personal_exam pe ON aer.personal_exam_id = pe.id
            LEFT JOIN personal_exam_question peq ON pe.id = peq.personal_exam_id
        WHERE
            aer.personal_exam_id = #{personalExamId}
        GROUP BY
            me.ID,
            pe.id,
            pe.exam_name,
            me.source,
            me.publisher,
            aer.student_id,
            aer.overall_score,
            aer.correct_rate,
            aer.percentile,
            aer.total_knowledge_points,
            aer.mastered_knowledge_points,
            aer.weak_knowledge_points
    </select>
    <select id="selectTotalStudentCountByExamId" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT pe.student_id)
        FROM
            view_active_students vas
                JOIN
            personal_exam pe ON vas.student_id = pe.student_id
        WHERE
                vas.class_id = (SELECT class_id FROM view_active_students WHERE student_id = #{studentId})
          AND pe.exam_id = #{examId}
        GROUP BY
            vas.class_id, vas.class_name;
    </select>
    <select id="getMasteredCount" resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT
        COUNT(vas.student_id) totalQuestionCount,
        SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            view_active_students vas
                JOIN
            personal_exam pe ON vas.student_id = pe.student_id
                JOIN
            personal_exam_question peq ON pe.id = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id
        WHERE
            qkp.knowledge_point_id = #{knowledgePointId}
          AND qkp.exam_id = #{examId}
          AND vas.class_id = (SELECT class_id FROM view_active_students WHERE student_id = #{studentId})


    </select>
    <select id="getOssUrls" resultType="java.lang.String">
        SELECT
            f.oss_url AS "ossUrl",
            mef.sort_no
        FROM public.exam_analyze_result ear
                 INNER JOIN public.math_exams me on me.id = ear.exam_id
                 INNER JOIN public.math_exam_files mef on mef.exam_id = me.id
                 INNER JOIN public.files f on f.id = mef.file_id
        WHERE ear.id =#{id}
            and mef.type = 'ORIGINAL_PAPER'
        GROUP BY  mef.sort_no,  f.oss_url
        ORDER BY mef.sort_no ASC
    </select>
    <select id="selectTestPapersCount" resultType="java.lang.Integer">
        SELECT COUNT
                   ( DISTINCT student_id)
        FROM
            personal_exam
        WHERE
            exam_id =#{examId}
            and deleted_at is null
    </select>
    <select id="getStudentInfo" resultType="java.util.Map">
        SELECT
            school_name as "schoolName",
            student_id as "studentId",
            student_name as "studentName",
            class_id as "classId",
            class_name as "className",
            grade_name as "gradeName"
        from view_active_students
        WHERE
        student_id =#{studentId}
    </select>

    <select id="selectManualExamScopeSection" resultType="java.util.UUID">
        SELECT
            s ->> 'sectionId' AS section_id
        FROM
            exam_analyze_result e,
            LATERAL jsonb_array_elements(
                    (SELECT jsonb_agg(elements)
                     FROM (
                              SELECT jsonb_array_elements(chapters -> 'sections')
                              FROM jsonb_array_elements(e.manual_exam_scope #> '{publishers}') p(pub),
                                   LATERAL jsonb_array_elements(pub #> '{chapters}') chapters
                          ) t(elements))
                    ) s
        WHERE e.id = #{analyzeReportId}
          AND (s ->> 'select') IN ('true')
    </select>

    <select id="selectKnowledgePointByAnalyzeId" resultType="java.util.UUID">
        select distinct qkp.knowledge_point_id
        from personal_exam pe,
             exam_analyze_result ear,
             question_knowledge_point qkp
        where ear.id = #{analyzeReportId}
          and qkp.exam_id = pe.exam_id
          and pe.id = ear.personal_exam_id
    </select>

    <select id="getOssUrlString" resultType="java.lang.String">
        SELECT STRING_AGG(f.oss_url, ',' ORDER BY mef.sort_no ASC) AS oss_url
        FROM (SELECT exam_id, file_id, sort_no, type
              FROM public.math_exam_files
             ) mef
                 INNER JOIN public.files f ON f.id = mef.file_id
        where mef.type = 'ORIGINAL_PAPER'
          and mef.exam_id = #{examId}
        group by mef.exam_id
    </select>



</mapper>
