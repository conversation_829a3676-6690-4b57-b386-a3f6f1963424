<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.GradeLeaveMapper">

    <select id="getGradeByStudentId" resultType="com.joinus.study.model.dto.GradeInfo">
        WITH t_education_duration AS (
            VALUES
                ('高中', 3, 10),
                ('初中3年制', 3, 7),
                ('初中4年制', 4, 6),
                ('小学6年制', 6, 1),
                ('小学5年制', 5, 1),
                ('幼儿园', 3, -2)
        ),
             current_date_info AS (
                 SELECT
                     EXTRACT(YEAR FROM CURRENT_DATE) as current_year,
                     CASE
                         WHEN EXTRACT(MONTH FROM CURRENT_DATE) BETWEEN 2 AND 7 THEN -1
                         ELSE 0
                         END as semester_adjustment
             )
        SELECT
            CASE
                WHEN cdi.semester_adjustment = -1 THEN 2
                ELSE 1
                END as current_semester,
            CASE
                WHEN vas.enrollment_year IS NOT NULL THEN
                    ed.start_grade + (cdi.current_year - vas.enrollment_year) + cdi.semester_adjustment
                WHEN vas.the_number IS NOT NULL THEN
                    ed.start_grade + (cdi.current_year - vas.the_number + ed.learning_period_count) + cdi.semester_adjustment
                ELSE NULL
                END as current_grade_level,
            st.math "math"
        FROM
            view_active_students vas
                JOIN t_education_duration ed(learning_period, learning_period_count, start_grade)
                     ON vas.learning_period = ed.learning_period
                CROSS JOIN current_date_info cdi
                left join school_textbook st on st.school_id = vas.school_id
        WHERE
            vas.student_id = #{studentId}
    </select>
</mapper>
