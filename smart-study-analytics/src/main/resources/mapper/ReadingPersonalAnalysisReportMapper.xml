<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalAnalysisReportMapper">
    
    <select id="queryReadingHistoryReportList" resultType="com.joinus.study.model.vo.ReadingHistoryReportVo">
        WITH LocalPassages AS (
            SELECT
                p.id as personal_passage_id,
                p.status,
                p.answer_method,
                p.end_at,
                p.created_at,
                p.entry_type,
                p.plan_id,
                p.passage_id,
                p.updated_at,
                r.id as report_id,
                r.accuracy_rate,
                COALESCE(r.status, 0) as report_status,
                r.is_view,
                pl.plan_name,
                ra.name as activity_name
            FROM
                reading_personal_passages p
                    LEFT JOIN reading_personal_analysis_report r ON r.personal_passage_id = p.id
                    LEFT JOIN reading_personal_plan pl ON p.plan_id = pl.id AND p.entry_type IN (1, 3)
                    LEFT JOIN reading_activity ra ON ra.id = p.plan_id AND p.entry_type IN (4, 5)
            WHERE
                p.student_id = #{studentId}
              AND p.entry_type != 2
            AND p.deleted_at IS NULL
            AND p.status IN (2, 3)
            ),
            AggregatedKnowledgePoints AS (
                SELECT
                    pp.id as personal_passage_id,
                    STRING_AGG(kp.name, ',') AS knowledge_points
                FROM
                    reading_personal_passages pp
                    INNER JOIN
                    reading_personal_passage_questions ppq ON pp.id = ppq.personal_passage_id
                    AND ppq.result = 0
                    AND ppq.deleted_at IS NULL
                    INNER JOIN
                    reading_question_knowledge_points qkp ON qkp.question_id = ppq.question_id
                    INNER JOIN
                    reading_knowledge_points kp ON kp.id = qkp.knowledge_point_id
                WHERE
                    pp.id IN (SELECT personal_passage_id FROM LocalPassages)
                GROUP BY
                    pp.id
            )
        SELECT
            lp.report_id as reportId,
            lp.personal_passage_id as personalPassageId,
            pa.title as title,
            lp.accuracy_rate as accuracyRate,
            CASE WHEN lp.entry_type IN (1, 3) THEN lp.plan_name ELSE lp.activity_name END as planName,
            pa.content as content,
            CASE WHEN lp.answer_method = 1 THEN to_char(lp.end_at, 'YYYY/MM/DD HH24:MI:SS')
                 ELSE to_char(lp.created_at, 'YYYY/MM/DD HH24:MI:SS') END as startTime,
            to_char(lp.end_at, 'YYYY/MM/DD HH24:MI:SS') as endTime,
            lp.report_status as reportStatus,
            lp.status,
            lp.answer_method as answerMethod,
            lp.is_view as isView,
            COALESCE(akp.knowledge_points, '') as weakPoints
        FROM
            LocalPassages lp
                LEFT JOIN
                reading_passages pa ON pa.id = lp.passage_id
                LEFT JOIN
                AggregatedKnowledgePoints akp ON akp.personal_passage_id = lp.personal_passage_id
        ORDER BY
            lp.updated_at DESC
    </select>
    <select id="queryPersonalPassageData" resultType="java.util.Map">
        select
            pp.student_id as "studentId",
            max(p.genre) as "genre",
            max(pp.time_spent) as "timeSpent",
            count(distinct qkp.knowledge_point_id) as "totalKnowledgePoints",
            count(distinct CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN qkp.knowledge_point_id END) AS "weakPointCount",
            COUNT(DISTINCT CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
            COUNT(DISTINCT CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
            COUNT(DISTINCT pp.id) AS "passageCount",
            COUNT(DISTINCT ppq.id) AS "totalQuestions"
        from
            reading_personal_passages pp
                inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
                inner join reading_passages p on p.id = pp.passage_id
                left join reading_question_knowledge_points qkp on qkp.question_id = ppq.question_id
        where
            pp.deleted_at is null
            and pp.status = 3
            <if test="startDate != null">
                and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="studentId != null">
                and pp.student_id = #{studentId}
            </if>
            <if test="personalPassageId != null">
                and pp.id = #{personalPassageId}
            </if>
        group by
        pp.student_id
    </select>
    <select id="queryPersonalPassageKnowledgePointList"
            resultType="com.joinus.study.model.vo.ReadingKnowledgePointVo">
        select
        DISTINCT kp.id,
        kp.name
        from
        reading_personal_passages pp
        inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
        inner join reading_passages p on p.id = pp.passage_id
        inner join reading_question_knowledge_points qkp on qkp.question_id = ppq.question_id
        inner join reading_knowledge_points kp on kp.id = qkp.knowledge_point_id
        where
        pp.deleted_at is null
        and pp.status = 3
        and pp.student_id = #{studentId}
        <if test="startDate != null">
            and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="personalPassageId != null">
            and pp.id = #{personalPassageId}
        </if>
        <if test="result != null">
            and ppq.result = #{result}
        </if>
    </select>

    <select id="queryPersonalPassageKnowledgePointData"
            resultType="com.joinus.study.model.vo.ReadingKnowledgePointVo">
        select
        kp.id,
        kp.name,
        count(distinct ppq.id) as "totalQuestions",
        count(distinct CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
        count(distinct CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
        ROUND(sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) as "accuracyRate"
        from
        reading_personal_passages pp
        inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
        inner join reading_passages p on p.id = pp.passage_id
        inner join reading_question_knowledge_points qkp on qkp.question_id = ppq.question_id
        inner join reading_knowledge_points kp on kp.id = qkp.knowledge_point_id
        where
        pp.deleted_at is null
        and pp.status = 3
        and pp.student_id = #{studentId}
        <if test="startDate != null">
            and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="personalPassageId != null">
            and pp.id = #{personalPassageId}
        </if>
        <if test="knowledgePointId != null">
            and qkp.knowledge_point_id = #{knowledgePointId}
        </if>
        <if test="knowledgePointName != null">
            and kp.name = #{knowledgePointName}
        </if>
        group by kp.id,kp.name
    </select>

    <select id="queryPersonalPassageDataForLineChart" resultType="com.joinus.study.model.vo.ReadingPeriodicReportDetailLineChartVo">
        select
        pp.student_id as "studentId",
        DATE(pp.created_at) AS "viewDate",
        max(p.genre) as genre,
        count(distinct qkp.knowledge_point_id) as "totalKnowledgePoints",
        count(distinct CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN qkp.knowledge_point_id END) AS "weakPointCount",
        COUNT(DISTINCT CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
        COUNT(DISTINCT CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
        COUNT(DISTINCT pp.id) AS "passageCount",
        COUNT(DISTINCT ppq.id) AS "totalQuestions"
        from
        reading_personal_passages pp
        inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
        inner join reading_passages p on p.id = pp.passage_id
        inner join reading_question_knowledge_points qkp on qkp.question_id = ppq.question_id
        where
        pp.deleted_at is null
        and pp.student_id = #{studentId}
        and pp.status = 3
        <if test="startDate != null">
            and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="personalPassageId != null">
            and pp.id = #{personalPassageId}
        </if>
        group by pp.student_id, DATE(pp.created_at)
    </select>

    <select id="getKnowledgePointByQuestionId" resultType="java.lang.String">
        SELECT
            STRING_AGG(rk.name, ',')
        FROM
            reading_question_knowledge_points rqk
            INNER JOIN reading_knowledge_points rk ON rqk.knowledge_point_id = rk.id
        WHERE
            rk.deleted_at IS NULL
          AND rqk.question_id = #{questionId}
    </select>

    <select id="queryPassageInfo" resultType="java.util.Map">
        select ar.id as "reportId",
               ar.student_id as "studentId",
               rp.title as "passageTitle"
        from public.reading_personal_analysis_report ar
                 inner join public.reading_personal_passages pp on pp.id = ar.personal_passage_id
                 inner join public.reading_passages rp on rp.id = pp.passage_id
        where ar.id = #{reportId} and pp.is_discard = 0 and ar.status = 1
    </select>

    <select id="queryPersonalPassageQuestionTypeData"
            resultType="com.joinus.study.model.vo.ReadingQuestionTypeVo">
        select
        ppq.question_type,
        count(distinct ppq.id) as "totalQuestions",
        count(distinct CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
        count(distinct CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
        ROUND(1 - sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) as errorRate
        from
        reading_personal_passages pp
        inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
        inner join reading_passages p on p.id = pp.passage_id
        where
        pp.deleted_at is null
        and pp.status = 3
        and pp.student_id = #{studentId}
        <if test="startDate != null">
            and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
        </if>
        <if test="personalPassageId != null">
            and pp.id = #{personalPassageId}
        </if>
        <if test="questionType != null">
            and ppq.question_type = #{questionType}
        </if>
        group by ppq.question_type
    </select>
    <select id="queryPersonalPassageHighFreqPerformanceData"
            resultType="com.joinus.study.model.vo.ReadingHighFreqPerformanceVo">
        select
        kp.id,
        kp.name,
        ppq.question_type,
        count(distinct ppq.id) as view_count,
        ROUND(sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) as accuracy_rate
        from
        reading_personal_passages pp
        inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id and ppq.deleted_at is null
        inner join reading_passages p on p.id = pp.passage_id
        inner join reading_question_knowledge_points qkp on qkp.question_id = ppq.question_id
        inner join reading_knowledge_points kp on kp.id = qkp.knowledge_point_id
        where
        pp.deleted_at is null
        and pp.status = 3
        and pp.student_id = #{studentId}
        <if test="startDate != null">
            and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
        </if>
        group by kp.id,kp.name,ppq.question_type
        order by view_count desc, accuracy_rate desc
    </select>
</mapper>
