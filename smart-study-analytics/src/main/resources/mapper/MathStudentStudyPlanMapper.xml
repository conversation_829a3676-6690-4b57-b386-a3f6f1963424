<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathStudentStudyPlanMapper">

    <select id="selectStudentPlanList" resultType="com.joinus.study.model.entity.MathStudentStudyPlan">
        select sp.*, (skp.training_exam_ids ='COMPLETED' AND
                      skp.ppt_htmls_completed IS NOT NULL) as is_completed
        from math_student_study_plan sp
        left join math_student_knowledge_point skp on sp.knowledge_point_id = skp.knowledge_point_id
        where sp.student_id = #{studentId} and sp.deleted_at is null
        order by sp.week_no asc, sp.sort_no asc
    </select>
</mapper>
