<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathStudentInfoMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.MathStudentInfo">
            <result property="studentId" column="student_id" />
            <result property="grade" column="grade" />
            <result property="publisher" column="publisher" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        student_id,grade,publisher,created_at,updated_at,deleted_at
    </sql>
</mapper>
