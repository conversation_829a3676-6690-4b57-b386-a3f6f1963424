<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ActiveStudentsMapper">

    <!-- 根据学生ID查询班级ID -->
    <select id="getClassIdByStudentId" resultType="com.joinus.study.model.vo.ActiveStudentVo">
        SELECT *
        FROM view_active_students
        WHERE student_id = #{studentId}
    </select>

    <!-- 根据班级ID获取所有学生ID -->
    <select id="getStudentIdsByClassId" resultType="java.lang.Long">
        SELECT student_id
        FROM view_active_students
        WHERE class_id = #{classId}
    </select>

    <!-- 根据年级ID获取所有学生ID -->
    <select id="getStudentIdsByGradeId" resultType="java.lang.Long">
        SELECT student_id
        FROM view_active_students
        WHERE grade_id = #{gradeId}
    </select>
    <select id="getClassIdByGradeId" resultType="com.joinus.study.model.entity.ActiveStudentsEntity">
        SELECT DISTINCT class_id  FROM view_active_students WHERE grade_id = #{gradeId}
    </select>
    
    <!-- 根据年级ID和试卷ID获取所有参加考试的班级ID -->
    <select id="getClassIdsByGradeIdAndExamId" resultType="java.lang.Long">
        SELECT DISTINCT vas.class_id
        FROM view_active_students vas
        INNER JOIN exam_analyze_result ear ON vas.student_id = ear.student_id
        WHERE vas.grade_id = #{gradeId}
        AND ear.exam_id = #{examId}
        AND ear.deleted_at IS NULL
    </select>

    <select id="getExamClassIds" resultType="java.lang.Long">
        SELECT DISTINCT vas.class_id
        FROM view_active_students vas
                 INNER JOIN exam_analyze_result ear ON vas.student_id = ear.student_id
        WHERE vas.grade_id = #{gradeId}
          AND ear.exam_id = #{examId}
          AND ear.deleted_at IS NULL
    </select>

    <select id="getByStudentId" resultType="com.joinus.study.model.entity.ActiveStudentsEntity">
        select * from view_active_students where student_id = #{studentId}
    </select>

</mapper>
