<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingActivityMapper">

    <!--[暑期训练]根据计划ID和学生ID获取活动信息和学生信息-->
    <select id="getActivityWithStudent" resultType="com.joinus.study.model.dto.ReadingActivityWithStudentDTO">
        SELECT ra.id                as activityId,
               ra.begin_time        as activityBeginTime,
               ra.end_time          as activityEndTime,
               ra.daily_passage_num as dailyPassageNum,
               ras.grade            as grade,
               ras.semester         as semester
        FROM reading_activity ra
                 INNER JOIN reading_activity_student ras ON ras.activity_id = ra."id"
            AND ras.deleted_at IS NULL AND ras.student_id = #{studentId}
        WHERE ra.deleted_at IS NULL
          AND ra."id" = #{activityId}
    </select>
    <select id="queryReadingActivityList" resultType="com.joinus.study.model.vo.ReadingActivityVO">
        select id,
               name,
               daily_passage_num,
               TO_CHAR(begin_time, 'YYYY-MM-DD') AS begin_time,
               TO_CHAR(end_time, 'YYYY-MM-DD') AS end_time
        from reading_activity
        where deleted_at is null
          and NOW() <![CDATA[ >= ]]> begin_time
          and NOW() <![CDATA[ <= ]]> end_time;
    </select>
    <select id="getStudentCompleteNum" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            READING_PERSONAL_PASSAGES
        WHERE
            STUDENT_ID = #{studentId}
          AND plan_id = #{activityId}
          AND end_at::DATE = CURRENT_DATE
          AND deleted_at is null
          AND status = 3
          AND (entry_type = 4 or entry_type = 5)
    </select>
</mapper>
