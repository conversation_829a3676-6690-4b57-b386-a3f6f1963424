<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ClassExamQuestionStatisticsMapper">

    <!-- u6839u636eu73edu7ea7IDu548cu8bd5u5377IDu67e5u8be2u73edu7ea7u9898u76eeu6b63u786eu7387u7edfu8ba1u4fe1u606f -->
    <select id="selectByClassIdAndExamId" resultType="com.joinus.study.model.entity.ClassExamQuestionStatistics">
        SELECT *
        FROM class_exam_question_statistics
        WHERE class_id = #{classId}
          AND exam_id = #{examId}
          AND deleted_at IS NULL
    </select>

    <!-- u6839u636eu73edu7ea7IDu3001u8bd5u5377IDu548cu9898u76eeIDu67e5u8be2u73edu7ea7u9898u76eeu6b63u786eu7387u7edfu8ba1u4fe1u606f -->
    <select id="selectByClassIdExamIdAndQuestionId" resultType="com.joinus.study.model.entity.ClassExamQuestionStatistics">
        SELECT *
        FROM class_exam_question_statistics
        WHERE class_id = #{classId}
          AND exam_id = #{examId}
          AND question_id = #{questionId}
          AND deleted_at IS NULL
    </select>

</mapper>
