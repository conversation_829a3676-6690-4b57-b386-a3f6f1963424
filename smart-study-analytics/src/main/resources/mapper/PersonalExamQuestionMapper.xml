<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.PersonalExamQuestionMapper">

    <insert id="insertPersonalExamQuestion" parameterType="com.joinus.study.model.entity.PersonalExamQuestion">
        INSERT INTO personal_exam_question
        (
            personal_exam_id,
            question_id,
            question_type,
            result,
            sort_no
        )
        VALUES
            (
                #{entity.personalExamId},
                #{entity.questionId}::uuid,
                #{entity.questionType}::question_type_enum,
                #{entity.result}::result_enum,
                #{entity.sortNo}
            )
    </insert>
    <select id="selectQuestionInfoByknowledgePointId"
            resultType="com.joinus.study.model.vo.ExamQuestionFileVo">
        select
            distinct mq.id as "id", mq.sort_no,
            mq.personal_exam_id as personalExamId,
            mq.question_type as "questionType",
            mq.question_id as "questionId",
            mq.result as "result"
        from personal_exam_question mq
        INNER join question_knowledge_point qkp on mq.question_id = qkp.question_id and qkp.deleted_at is null
        where qkp.knowledge_point_id = #{knowledgePointId}::uuid
          and mq.personal_exam_id = #{personalExamId}
          and qkp.exam_id is not null
          and mq.deleted_at is null
    </select>

    <select id="selectQuestionFileInfoById" resultType="com.joinus.study.model.vo.ExamQuestionFileVo">
        select
            mqs.id as "questionId",
            mqs.content as "questionContent",
            STRING_AGG(f.oss_url,',' ORDER BY qf.sort_no ASC ) as "questionOssUrl"
        from math_questions mqs
        left JOIN math_question_files qf on qf.question_id = mqs.id and qf.type = 1
        left join files f on f.id = qf.file_id and f.deleted_at is null
        where mqs.id = #{questionId}::uuid and mqs.deleted_at is null
        GROUP BY
            mqs.id,
            mqs.content
    </select>
    <select id="selectErrorQuestionNosByPersonalExamId" resultType="java.lang.String">
        select
                STRING_AGG(peq.sort_no::TEXT, ',')
        FROM personal_exam_question peq
        WHERE peq.result != 'correct' and peq.personal_exam_id =#{personalExamId}
        <if test="questionType != null">
            and peq.question_type = #{questionType}::question_type_enum
        </if>
    </select>
    <select id="listByVo" resultType="com.joinus.study.model.vo.PersonalExamQuestionVo">
        select
            distinct mq.id as personalExamId,
            mq.*,
            ea.id as "reportId",
            me.student_id as studentId,
            me.exam_id as examId,
            me.publisher as publisher,
            me.exam_name as examName
        from personal_exam_question mq
        inner join personal_exam me on me.id = mq.personal_exam_id
        left join question_knowledge_point qkp on qkp.question_id = mq.question_id and qkp.deleted_at is null
        left join exam_analyze_result ea on ea.exam_id = me.exam_id and ea.deleted_at is null and ea.student_id = me.student_id and ea.personal_exam_id=me.id
        where mq.deleted_at is null and me.deleted_at is null and ea.id is not null
        <if test="vo.questionType != null">
            and mq.question_type = #{vo.qquestionType}
        </if>
        <if test="vo.result != null">
            and mq.result = #{vo.result}::result_enum
        </if>
        <if test="vo.studentId != null">
            and me.student_id = #{vo.studentId}
        </if>
        <if test="vo.knowledgePointId != null">
            and qkp.knowledge_point_id = #{vo.knowledgePointId}::uuid
        </if>
        order by mq.id desc
    </select>
</mapper>
