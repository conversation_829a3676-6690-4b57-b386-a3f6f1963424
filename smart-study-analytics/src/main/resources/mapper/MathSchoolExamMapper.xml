<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathSchoolExamMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.MathSchoolExam">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="schoolId" column="school_id" jdbcType="BIGINT"/>
        <result property="examId" column="exam_id" jdbcType="OTHER" typeHandler="com.joinus.study.config.PostgresUUIDTypeHandler"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,school_id,exam_id,created_at,updated_at,deleted_at
    </sql>


</mapper>
