<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.EnglishExamDiagnoseQuestionMapper">

    <select id="queryKnowledgePointNumber" resultType="int">
        SELECT COUNT(DISTINCT t3.knowledge_point_id) AS knowledgePointNumber
        FROM english_exam_diagnose_question t1
                 LEFT JOIN english_flow_deputy_question t2 ON t1.question_id = t2.id
                 LEFT JOIN english_question_knowledge_points t3 ON t3.question_id = t2.id
        WHERE t1.record_id = #{recordId}
    </select>

    <select id="queryWeakKnowledgeNumber" resultType="int">
        SELECT COUNT(DISTINCT t3.knowledge_point_id) AS knowledgePointNumber
        FROM english_exam_diagnose_question t1
                 LEFT JOIN english_flow_deputy_question t2 ON t1.question_id = t2.id
                 LEFT JOIN english_question_knowledge_points t3 ON t3.question_id = t2.id
        WHERE t1.record_id = #{recordId}
          AND t1.is_wrong = true
    </select>

    <select id="listQuestionType" resultType="com.joinus.study.model.vo.EnglishExamReportQuestionTypeVO">
        select question_type,
               count(question_id)         as questionNumber,
               sum(case when is_wrong = true then 1 else 0 end) as wrongQuestionNumber
        from english_exam_diagnose_question
        where record_id = #{recordId}
          AND question_type IS NOT NULL
          AND question_id IS NOT NULL
        group by question_type
    </select>

    <select id="listKnowledgePoints" resultType="com.joinus.study.model.vo.EnglishExamReportKnowledgePointVO">
        select t3.knowledge_point_id                     as knowledge_point_id,
               t4.name                                   as knowledge_point_name,
               string_agg(DISTINCT t1.question_type, ',')         as questionType
        from english_exam_diagnose_question t1
                 left join english_flow_deputy_question t2 on t1.question_id = t2.id
                 left join english_question_knowledge_points t3 on t3.question_id = t2.id
                 left join english_knowledge_points t4 on t4.id = t3.knowledge_point_id
        where t1.record_id = #{recordId}
          and t1.is_wrong = true
        group by t3.knowledge_point_id, t4.name
    </select>

    <select id="listMasterQuestions" resultType="com.joinus.study.model.entity.EnglishExamDiagnoseQuestion">
        select t1.id           as question_id,
               t1.question_num as bigQuestionNumber,
               t1.order_no     as questionNumber,
               t2.type         as question_type,
               t1.score
        from english_flow_deputy_question t1
                 left join english_flow_master_question t2 on t1.master_id = t2.id
        where t1.exam_id = #{examId}
    </select>

    <select id="listQuestionTypeByPointId" resultType="java.lang.String">
        select distinct t1.question_type
        from english_exam_diagnose_question t1
                 left join english_flow_deputy_question t2 on t1.question_id = t2.id
                 left join english_question_knowledge_points t3 on t3.question_id = t2.id
        where t1.record_id = #{recordId}
          and t3.knowledge_point_id = #{pointId}
    </select>

    <select id="queryCountByPointId" resultType="int">
        select count(t1.id)
        from english_exam_diagnose_question t1
                 left join english_flow_deputy_question t2 on t1.question_id = t2.id
                 left join english_question_knowledge_points t3 on t3.question_id = t2.id
        where t1.record_id = #{recordId}
          and t3.knowledge_point_id = #{pointId}
    </select>

    <select id="queryWrongCountByPointId" resultType="int">
        select count(t1.id)
        from english_exam_diagnose_question t1
                 left join english_flow_deputy_question t2 on t1.question_id = t2.id
                 left join english_question_knowledge_points t3 on t3.question_id = t2.id
        where t1.record_id = #{recordId}
          and t3.knowledge_point_id = #{pointId}
          and t1.is_wrong = true
    </select>

    <select id="listByRecordIdsAndKnowledgePointId" resultType="com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO">
        SELECT
            t1.question_id,
            max(t4.content) AS question_content
        FROM english_exam_diagnose_question t1
                 INNER JOIN english_question_knowledge_points t2 ON t2.question_id = t1.question_id
                 INNER JOIN english_knowledge_points t3 ON t2.knowledge_point_id = t3.id
                 INNER JOIN english_flow_deputy_question t4 ON t1.question_id = t4.id
        WHERE t2.knowledge_point_id = #{knowledgePointId}
        <if test='diagnoseRecordIds != null and diagnoseRecordIds.size() > 0'>
            AND t1.record_id IN
            <foreach collection="diagnoseRecordIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.question_id
    </select>

    <select id="listByRecordIdAndKnowledgePointId" resultType="com.joinus.study.model.dto.EnglishExamDiagnoseQuestionDTO">
        select
            t1.question_id,
            max(t1.question_number) AS question_number,
            max(t1.question_type) AS question_type,
            max(t2.content) AS question_content
        from english_exam_diagnose_question t1
                 inner join english_flow_deputy_question t2 on t1.question_id = t2.id
                 inner join english_question_knowledge_points t3 on t3.question_id = t2.id
        where t1.record_id = #{diagnoseRecordId}
          and t3.knowledge_point_id = #{knowledgePointId}
        GROUP BY t1.question_id
    </select>
</mapper>
