<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathPageViewTrackingMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.MathPageViewTracking">
            <result property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="studentId" column="student_id" />
            <result property="fullPath" column="full_path" />
            <result property="businessType" column="business_type" />
            <result property="createdAt" column="created_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,student_id,full_path,business_type,created_at
    </sql>
</mapper>
