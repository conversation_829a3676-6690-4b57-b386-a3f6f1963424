<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.EnglishExamMapper">

    <select id="listByName" resultType="com.joinus.study.model.entity.EnglishExam">
        select * from english_flow_exam where is_en = true and status = 2 and name = #{name} order by created_at desc;
    </select>
</mapper>
