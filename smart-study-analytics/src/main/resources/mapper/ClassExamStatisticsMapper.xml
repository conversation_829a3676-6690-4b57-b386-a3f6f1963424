<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ClassExamStatisticsMapper">

    <!-- 根据班级ID和试卷ID查询班级考试统计信息 -->
    <select id="selectByClassIdAndExamId" resultType="com.joinus.study.model.entity.ClassExamStatistics">
        SELECT *
        FROM class_exam_statistics
        WHERE class_id = #{classId}
          AND exam_id = #{examId}
          AND deleted_at IS NULL
        order by created_at
        LIMIT 1
    </select>

</mapper>
