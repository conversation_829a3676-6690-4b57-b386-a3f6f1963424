<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.QuestionKnowledgePointMapper">

    <select id="selectKnowledgePointStatisticsByExamId"
            resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT qkp.exam_id,
               qkp.knowledge_point_name name,
               qkp.knowledge_point_id,
               COUNT(DISTINCT peq.question_id) totalQuestionCount,
               STRING_AGG(peq.question_type||' ' || peq.sort_no::TEXT, ',') AS questionNos,
               SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            personal_exam pe
                JOIN
            personal_exam_question peq ON pe.ID = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id AND pe.exam_id=qkp.exam_id and pe.publisher=qkp.publisher
        WHERE
            pe.id =#{id}
        GROUP BY
            qkp.knowledge_point_id ,qkp.exam_id,
            qkp.knowledge_point_name


    </select>
    <select id="getStudentIds" resultType="com.joinus.study.model.vo.StudentInfoVo">

        SELECT
            vas.student_id,vas.class_id
        FROM
            view_active_students vas
        WHERE
                class_id = ( SELECT class_id FROM view_active_students WHERE student_id =#{studentId} )
    </select>
    <select id="getClassKnowledgePointStatics"
            resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT
            qkp.knowledge_point_name name,
            qkp.knowledge_point_id,
            COUNT(peq.question_id) totalQuestionCount,
            SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            personal_exam pe
                JOIN
            personal_exam_question peq ON pe.id = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id
        WHERE
            qkp.exam_id = #{examId}
          <if test="studentIds != null and studentIds.size() > 0">
          AND pe.student_id IN
          <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
            #{studentId}
          </foreach>
          </if>
        GROUP BY
            qkp.knowledge_point_id,
            qkp.exam_id,
            qkp.knowledge_point_name
    </select>
    <select id="selectKnowledgePointBySectionId" resultType="com.joinus.study.model.vo.MathKnowledgePointVO">
     SELECT p.id,
            p.name,
            p.sort_no
     FROM
         math_knowledge_points p
     INNER JOIN
         math_section_knowledge_points s ON p.id = s.knowledge_point_id
     WHERE p.deleted_at IS NULL
       and p.exam_point = false
       AND s.section_id = #{sectionId}
    </select>
    <select id="selectPointMasteryDegree" resultType="java.lang.Integer">
        SELECT correct_count*100/total_count AS accuracy_rate
        FROM (
                 SELECT
                     SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS correct_count,
                     COUNT(peq.id) AS total_count
                 FROM exam_analyze_result ear
                          LEFT JOIN personal_exam_question peq ON ear.personal_exam_id = peq.personal_exam_id
                          LEFT JOIN question_knowledge_point qkp ON peq.question_id = qkp.question_id AND qkp.exam_id = ear.exam_id
                 WHERE ear.student_id = #{studentId}
                   AND qkp.knowledge_point_id = #{knowledgePointId}
             ) AS subquery
    </select>
    <select id="getKnowledgePointFromView" resultType="com.joinus.study.model.dto.KnowledgePointDto">
        SELECT
            knowledge_point_id as  id,
            knowledge_point_name as name,
            textbook_id,
            textbook_name,
            grade,
            semester,
            publisher,
            chapter_id,
            chapter_name,
            chapter_sort_no,
            section_id,
            section_name,
            section_sort_no,
            knowledge_point_original_name,
            exam_point
        FROM
            view_math_knowledge_points
        WHERE publisher = #{publisherName}
        <if test="knowledgePointId != null">
            AND knowledge_point_id = #{knowledgePointId}
        </if>
        <if test="sectionId != null">
            AND section_id = #{sectionId}
        </if>
        <if test="knowledgePointIds != null and knowledgePointIds.size() > 0">
            AND knowledge_point_id IN
            <foreach collection="knowledgePointIds" item="pointId" separator="," open="(" close=")">
                #{pointId}
            </foreach>
        </if>

    </select>
    <select id="selectKnowledgePointByKnowledgeIds"
            resultType="com.joinus.study.model.vo.MathKnowledgePointVO">
        SELECT p.id,
        p.name,
        p.sort_no
        FROM
        math_knowledge_points p
        WHERE p.deleted_at IS NULL
        <if test="knowledgePointIds != null and knowledgePointIds.size() > 0">
            AND p.id IN
            <foreach collection="knowledgePointIds" item="pointId" separator="," open="(" close=")">
                #{pointId} :: uuid
            </foreach>
        </if>
    </select>

</mapper>
