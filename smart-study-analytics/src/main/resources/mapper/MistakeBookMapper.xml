<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MistakeBookMapper">
    <update id="editById">


        UPDATE mistake_book
        SET
            student_id = #{param.studentId},
            question_id = #{param.questionId},
            source = #{param.source}::source_enum,
            subject = #{param.subject}::subject_enum,
            question_answer = #{param.questionAnswer},
            question_answer_content = #{param.questionAnswerContent},
            difficulty = #{param.difficulty},
            created_at = #{param.createdAt},
            updated_at = #{param.updatedAt},
            deleted_at = #{param.deletedAt},
            derived_question_id = #{param.derivedQuestionId},
            source_id = #{param.sourceId},
            knowledge_point_ids = #{param.knowledgePointIds}
        WHERE id = #{param.id}
    </update>

    <select id="pages" resultType="com.joinus.study.model.vo.MistakeBookVo">
        SELECT
            mb.ID,
            mb.student_id,
            mb.question_id,
            mb.SOURCE,
            mb.subject,
            mb.question_answer,
            mb.question_answer_content,
            mb.difficulty,
            mb.created_at,
            mb.source_id,
            mb.knowledge_point_ids
        FROM
            mistake_book AS mb
        WHERE
        mb.deleted_at IS NULL AND
        mb.student_id = #{ param.studentId}
        <if test="param.source != null">
            AND mb.source::text = #{param.source}
        </if>
        <if test="param.subject != null">
            AND mb.subject::text = #{param.subject}
        </if>
        <if test="param.difficulty != null">
            AND mb.difficulty = #{param.difficulty}
        </if>
        <if test="param.startTime != null">
            AND mb.created_at  <![CDATA[ >= ]]> #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND mb.created_at <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.masteryDegree != null">
            AND mb.mastery_degree = #{param.masteryDegree}
        </if>
         order by mb.created_at desc
    </select>
    <select id="getFilesByQuestionId" resultType="com.joinus.study.model.vo.FilesVo">
        SELECT
            f.*
        FROM
            math_question_files qf
                INNER JOIN files f ON f.ID = qf.file_id
                where  qf.type=1
                AND qf.question_id = #{questionId}
    </select>
    <select id="getCompleteQuestionsDetail" resultType="java.lang.String">

        SELECT
            FORMAT(' ID: %s, question_type: %s, question_content: %s, Difficulty: %s, Answer: %s, Answer_Content: %s, Knowledge Points: %s',
                   mq.id,
                   mq.question_type,
                   mq.content,
                   mq.difficulty,
                   mqa.answer,
                   mqa.content,
                   STRING_AGG(qkp.knowledge_point_name, ', ')
                ) AS formatted_result
        FROM
            public.math_questions AS mq
                LEFT JOIN
            public.math_answers AS qar ON mq.id = qar.question_id
                LEFT JOIN
            public.math_answers AS mqa ON qar.answer_id = mqa.id
                LEFT JOIN
            public.question_knowledge_point AS qkp ON mq.id = qkp.question_id
        WHERE mq.id=#{questionId}
        GROUP BY
            mq.id, mq.question_type, mq.content, mq.difficulty, mqa.answer, mqa.content
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
                    INSERT INTO mistake_book
                    ("student_id", "question_id", "source", "subject", "question_answer",
                    "question_answer_content", "difficulty", "created_at", "updated_at", "deleted_at", "derived_question_id","source_id")
                    VALUES
                    <foreach collection="mistakeBookList" item="item" separator=",">
                        (#{item.studentId},
                        #{item.questionId},
                        #{item.source}::source_enum,
                        #{item.subject}::subject_enum,
                        #{item.questionAnswer},
                        #{item.questionAnswerContent},
                        #{item.difficulty},
                        #{item.createdAt},
                        #{item.updatedAt},
                        #{item.deletedAt},
                        #{item.derivedQuestionId},
                        #{item.sourceId}
                        )
                    </foreach>
    </insert>
    <select id="getAdjacentData" resultType="com.joinus.study.model.vo.MistakeBookDetailsVo">
        WITH RankedData AS (
            SELECT
                mb.ID,
                mb.student_id,
                mb.question_id,
                mb.SOURCE,
                mb.subject,
                mb.question_answer,
                mb.question_answer_content,
                mb.difficulty,
                mb.created_at,

                -- 窗口函数计算记录总数
                COUNT(*) OVER() AS TotalCount,

        -- 窗口函数计算当前记录的行号
                    ROW_NUMBER() OVER(ORDER BY  mb.ID DESC) AS RowNum,

        -- 获取前一条记录的 ID
                    LAG(mb.ID) OVER(ORDER BY  mb.ID DESC) AS PrevID,

        -- 获取后一条记录的 ID
                    LEAD(mb.ID) OVER(ORDER BY  mb.ID DESC) AS NextID

            FROM
                mistake_book AS mb
            WHERE
                mb.deleted_at IS NULL
              AND  mb.student_id = #{studentId}
        )
        SELECT
            *,
            TotalCount questionTotal,
            RowNum currentIndex,
            PrevID BeforeId,
            NextID nextId
        FROM RankedData
        WHERE ID =#{id}
        ORDER BY  ID DESC
    </select>

    <select id="getExamMistake" resultType="com.joinus.study.model.vo.ExamMistakeInfoVo">
       SELECT
            peq.personal_exam_id AS "perExamId",
            peq.question_id AS "questionId",
            mq.difficulty AS "difficulty"
        FROM  public.personal_exam_question peq
                  LEFT JOIN  public.math_questions mq ON mq.id = peq.question_id
        WHERE
           peq.personal_exam_id = #{personalExamId}
          AND peq.result = 'mistake'
    </select>
    <select id="getAnswerByQuestionId" resultType="com.joinus.study.model.vo.ExamMistakeInfoVo">
        SELECT
            qar.question_id AS "questionId",
            mqa.answer AS "questionAnswer",
            mqa.content AS "questionContent"
        FROM  public.math_question_answers qar
                  LEFT JOIN  public.math_answers mqa ON mqa.id = qar.answer_id
        where qar.question_id in
        <if test="questionIdList != null and questionIdList.size() > 0">
            <foreach collection="questionIdList" item="questionId" separator="," open="(" close=")">
                #{questionId}
            </foreach>
        </if>
        order by mqa.created_at DESC limit 1
    </select>

    <update id="updateByMBId">
        update mistake_book set question_answer = #{mistakeBook.questionAnswer},
                                question_answer_content = #{mistakeBook.questionAnswerContent},deleted_at = null,
        created_at = CURRENT_TIMESTAMP,updated_at = CURRENT_TIMESTAMP,
        knowledge_point_ids = #{mistakeBook.knowledgePointIds}
        <if test="#{mistakeBook.questionAnswerContent} != null and #{mistakeBook.questionAnswerContent} != ''">
            ,difficulty = #{mistakeBook.difficulty}
        </if>
        where id = #{mistakeBook.id}
    </update>
</mapper>
