<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ClassKnowledgePointStatisticsMapper">

    <!-- 根据班级ID和试卷ID查询班级知识点正确率统计信息 -->
    <select id="selectByClassIdAndExamId" resultType="com.joinus.study.model.entity.ClassKnowledgePointStatistics">
        SELECT *
        FROM class_knowledge_point_statistics
        WHERE class_id = #{classId}
          AND exam_id = #{examId}
          AND deleted_at IS NULL
    </select>

    <!-- 根据班级ID、试卷ID和知识点ID查询班级知识点正确率统计信息 -->
    <select id="selectByClassIdExamIdAndKnowledgePointId" resultType="com.joinus.study.model.entity.ClassKnowledgePointStatistics">
        SELECT *
        FROM class_knowledge_point_statistics
        WHERE class_id = #{classId}
          AND exam_id = #{examId}
          AND knowledge_point_id = #{knowledgePointId}
          AND deleted_at IS NULL
    </select>

</mapper>
