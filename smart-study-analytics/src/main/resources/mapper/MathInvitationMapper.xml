<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathInvitationMapper">
    <insert id="mathInvitationAdd">
        INSERT INTO math_invitation
            (inviter_student_id,inviter_phone,invitee_student_id,invitee_phone,path,type)
        VALUES
            ( #{inviterStudentId},
             #{inviterPhone},
             #{inviteeStudentId},
             #{inviteePhone},
             #{path} ::ltree,
             #{type})
    </insert>
</mapper>
