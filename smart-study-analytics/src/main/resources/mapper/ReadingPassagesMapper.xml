<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPassagesMapper">

    <!--   管理后台-分页 -->
    <select id="pages" resultType="com.joinus.study.model.vo.ReadingPassagesBackendVO">
        WITH
        question_counts AS (
        SELECT passage_id, count(*) AS question_number
        FROM reading_passage_questions
        GROUP BY passage_id
        ),
        knowledge_aggregation AS (
        SELECT
        tpq.passage_id,
        string_agg(DISTINCT tkp.name, ',') AS knowledge_points
        FROM reading_passage_questions tpq
        LEFT JOIN reading_question_knowledge_points tqk ON tqk.question_id = tpq.id
        LEFT JOIN reading_knowledge_points tkp ON tqk.knowledge_point_id = tkp.id
        WHERE 1=1
        <if test="null != pageParam.knowledgePoints and '' != pageParam.knowledgePoints">
            AND tkp.name ILIKE concat('%', #{pageParam.knowledgePoints}, '%')
        </if>
        GROUP BY tpq.passage_id
        )
        SELECT
            tp.id,
            CASE WHEN tu.grade = 1 THEN '一年级'
            WHEN tu.grade = 2 THEN '二年级'
            WHEN tu.grade = 3 THEN '三年级'
            WHEN tu.grade = 4 THEN '四年级'
            WHEN tu.grade = 5 THEN '五年级'
            WHEN tu.grade = 6 THEN '六年级'
            WHEN tu.grade = 7 THEN '七年级'
            WHEN tu.grade = 8 THEN '八年级'
            WHEN tu.grade = 9 THEN '九年级'
            ELSE '未知年级' END AS grade_name,

            CASE WHEN tu.semester = 1 THEN '上学期'
            ELSE '下学期' END AS semester,
            tu.name AS unit_name,
            tp.genre,
            tp.title,
            tp.content,
            coalesce(tqc.question_number, 0) AS question_number,
            tka.knowledge_points,
            tp.created_at,
            tp.is_enabled,
            tp.source,
            tp.is_audit
        FROM reading_passages tp
            LEFT JOIN reading_units tu ON tp.unit_id = tu.id
            LEFT JOIN question_counts tqc ON tqc.passage_id = tp.id
            LEFT JOIN knowledge_aggregation tka ON tka.passage_id = tp.id
        WHERE tp.deleted_at IS NULL
        <if test="null != pageParam.knowledgePoints and '' != pageParam.knowledgePoints">
            AND tka.knowledge_points IS NOT NULL
        </if>
        <if test="null != pageParam.grade">
            AND tu.grade = #{pageParam.grade}
        </if>
        <if test="null != pageParam.semester">
            AND tu.semester = #{pageParam.semester}
        </if>
        <if test="null != pageParam.unitId">
            AND tu.id = #{pageParam.unitId}
        </if>
        <if test="null != pageParam.genre and '' != pageParam.genre">
            AND tp.genre ILIKE concat('%',#{pageParam.genre},'%')
        </if>
        <if test="null != pageParam.title and '' != pageParam.title">
            AND tp.title ILIKE concat('%',#{pageParam.title},'%')
        </if>
        <if test="null != pageParam.timeStart and '' != pageParam.timeStart">
            AND tp.created_at >= (#{pageParam.timeStart})::timestamp
        </if>
        <if test="null != pageParam.timeEnd and '' != pageParam.timeEnd">
            AND tp.created_at &lt;= (#{pageParam.timeEnd} ||' 23:59:59.999')::timestamp
        </if>
        <if test="null != pageParam.content and '' != pageParam.content">
            AND tp.content ILIKE concat('%',#{pageParam.content},'%')
        </if>
        <if test="null != pageParam.isEnabled">
            AND  tp.is_enabled = #{pageParam.isEnabled}
        </if>
        <if test="null != pageParam.isAudit">
            AND  tp.is_audit = #{pageParam.isAudit}
        </if>
        ORDER BY tp.is_enabled DESC, tp.created_at DESC
    </select>

    <!--   管理后台-详情 -->
    <resultMap id="resultMap" type="com.joinus.study.model.vo.ReadingPassagesBackendVO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="genre" property="genre"/>
        <result column="content" property="content"/>
        <result column="knowledge_points" property="knowledgePoints"/>
        <result column="unit_id" property="unitId"/>
    </resultMap>
    <select id="query" resultMap="resultMap">
        SELECT
            tp.id,
            tp.genre,
            tp.title,
            tp.content,
            tpqd.knowledge_points,
            tp.unit_id,
            tp.is_enabled,
            tp.is_audit,
            tp.deleted_at
        FROM reading_passages tp
                 LEFT JOIN(SELECT
                               tpq.passage_id,
                               string_agg(DISTINCT tkp.name::text, ',') AS knowledge_points
                           FROM reading_passage_questions tpq
                                    LEFT JOIN reading_question_knowledge_points tqk ON tqk.question_id = tpq.id
                                    LEFT JOIN reading_knowledge_points tkp ON tqk.knowledge_point_id = tkp.id
                           WHERE tpq.passage_id = #{id}
                           GROUP BY tpq.passage_id) tpqd ON tpqd.passage_id = tp.id
        WHERE 1 = 1
          AND tp.id = #{id}
    </select>



    <!--    年级-学期-单元列表-->
    <resultMap id="gradeSemesterUnitMap" type="com.joinus.study.model.vo.ReadingGradeVO">
        <result column="grade" property="grade"/>
        <result column="grade_name" property="gradeName"/>
        <collection property="semesterList" ofType="com.joinus.study.model.vo.ReadingSemesterVO">
            <result column="semester" property="semester"/>
            <result column="semester_name" property="semesterName"/>
            <collection property="unitList" ofType="com.joinus.study.model.vo.ReadingUnitsVO">
                <result column="unit_id" property="id"/>
                <result column="unit_name" property="name"/>
            </collection>
        </collection>
    </resultMap>
    <select id="listGradeSemesterUnit" resultMap="gradeSemesterUnitMap">
        SELECT
            tu.grade,
            CASE WHEN tu.grade = 1 THEN '一年级'
                 WHEN tu.grade = 2 THEN '二年级'
                 WHEN tu.grade = 3 THEN '三年级'
                 WHEN tu.grade = 4 THEN '四年级'
                 WHEN tu.grade = 5 THEN '五年级'
                 WHEN tu.grade = 6 THEN '六年级'
                 WHEN tu.grade = 7 THEN '七年级'
                 WHEN tu.grade = 8 THEN '八年级'
                 WHEN tu.grade = 9 THEN '九年级'
                 ELSE '未知年级' END AS grade_name,
            tu.semester,
            CASE WHEN tu.semester = 1 THEN '上学期'
                 ELSE '下学期' END AS semester_name,
            tu.id AS unit_id,
            tu.name AS unit_name
        FROM reading_units tu
        ORDER BY tu.grade, tu.semester, tu.order_no
    </select>


    <!--    文章题目分页-->
    <select id="pagesOfQuestions" resultType="com.joinus.study.model.vo.ReadingPassageQuestionsVO">
        WITH
        questions_knowledge AS (
        SELECT
        tq.id,
        string_agg(tkp.name::text, ',') AS knowledge_points
        FROM reading_passage_questions tq
        LEFT JOIN reading_question_knowledge_points tqk ON tqk.question_id = tq.id
        LEFT JOIN reading_knowledge_points tkp ON tqk.knowledge_point_id = tkp.id
        WHERE 1 = 1
        <if test="null != pageParam.passageId">
        AND tq.passage_id = #{pageParam.passageId}
        </if>
        GROUP BY tq.id
        )
        SELECT
            tpq.order_no,
            tpq.id,
            tpq.content,
            tpq.question_type,
            tqk.knowledge_points,
            tpq.is_enabled,
            tpq.created_at,
            tpq.source,
            tpq.is_audit
        FROM reading_passages tp
            LEFT JOIN reading_passage_questions tpq ON tpq.passage_id = tp.id
            LEFT JOIN questions_knowledge tqk ON tqk.id = tpq.id
        WHERE tpq.deleted_at IS NULL
        <if test="null != pageParam.passageId">
            AND tp.id = #{pageParam.passageId}
        </if>
        ORDER BY tpq.is_enabled DESC, tpq.order_no ASC NULLS LAST, tpq.created_at DESC
    </select>

    <!--    题目详情-->
    <resultMap id="questionsMap" type="com.joinus.study.model.vo.ReadingPassageQuestionsVO">
        <result column="id" property="id"/>
        <result column="content" property="content"/>
        <result column="question_type" property="questionType"/>
        <result column="order_no" property="orderNo"/>
        <result column="answer" property="answer"/>
        <result column="answer_content" property="answerContent"/>
        <result column="knowledge_points" property="knowledgePoints"/>
        <result column="formula_answer" property="formulaAnswer"/>
        <result column="formula_template" property="formulaTemplate"/>
    </resultMap>
    <select id="queryQuestion" resultMap="questionsMap">
        SELECT tpq.order_no,
               tpq.id,
               tpq.content,
               tpq.question_type,
               tqa.answer,
               tqa.content answer_content,
               tqkp.knowledge_points,
               rqa.answer formula_answer,
               raf.formula_template formula_template
        FROM reading_passage_questions tpq
                 LEFT JOIN reading_question_answers tqa ON tqa.question_id = tpq.id
            AND tqa.deleted_at IS NULL
            AND tqa.answering_formula_id IS NULL
                 LEFT JOIN reading_question_answers rqa ON rqa.question_id = tpq.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 LEFT JOIN reading_answering_formulas raf ON raf.id = rqa.answering_formula_id
                 LEFT JOIN(SELECT tq.id,
                                  string_agg(tkp.name::text, ',') AS knowledge_points
                           FROM reading_passage_questions tq
                                    LEFT JOIN reading_question_knowledge_points tqk ON tqk.question_id = tq.id
                                    LEFT JOIN reading_knowledge_points tkp ON tqk.knowledge_point_id = tkp.id
                           WHERE tq.id = #{questionId}
                           GROUP BY tq.id) tqkp ON tqkp.id = tpq.id
        WHERE tpq.id = #{questionId}
    </select>
    <select id="getPassageInfo" resultType="com.joinus.study.model.vo.ReadingQuestionSetsViewVo">


        SELECT
            tp.id passageId,
            tu.grade,
            CASE WHEN tu.grade = 1 THEN '一年级'
                 WHEN tu.grade = 2 THEN '二年级'
                 WHEN tu.grade = 3 THEN '三年级'
                 WHEN tu.grade = 4 THEN '四年级'
                 WHEN tu.grade = 5 THEN '五年级'
                 WHEN tu.grade = 6 THEN '六年级'
                 WHEN tu.grade = 7 THEN '七年级'
                 WHEN tu.grade = 8 THEN '八年级'
                 WHEN tu.grade = 9 THEN '九年级'
                 ELSE '未知年级' END AS gradeName,

            CASE WHEN tu.semester = 1 THEN '上学期'
                 ELSE '下学期' END AS semester,
            tu.name AS unitName,
            tu.textbook,
            tp.genre,
            tp.title,
            tp.content,
            tp.created_at,
            tp.source,
            tp.is_audit passageIsAudit
        FROM reading_passages tp
                 LEFT JOIN reading_units tu ON tp.unit_id = tu.id
               where tp.id = #{passageId}
    </select>

    <select id="getUnitInfoByPersonalId" resultType="com.joinus.study.model.entity.ReadingUnits">
        select tu.grade,
               tu.semester
        from reading_personal_passages rpp
                 inner join reading_passages rp on rp.Id = rpp.passage_id
                 inner join reading_units tu on tu.id = rp.unit_id
        where rpp.id = #{personalId}
    </select>

</mapper>
