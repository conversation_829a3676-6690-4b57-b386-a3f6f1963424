<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathActivityWeekUnitMapper">

    <select id="selectGiftWeekUnitIds" resultType="java.lang.Long">
        SELECT mawu.id
        FROM math_activity_week_unit mawu
        INNER JOIN math_activity_week maw ON mawu.week_id = maw.id
                                AND maw.deleted_at IS NULL
                                AND maw.activity_id = #{activityId}
                                AND maw.type = 'REVIEW'
                                AND maw.grade = #{grade}
                                AND maw.publisher = #{publisher.value}
        WHERE mawu.deleted_at IS NULL
        <if test="isPreview">
            UNION
            select id from math_activity_week_unit where chapter_id in(
            SELECT chapter_id
            FROM (
            SELECT
            mawu.chapter_id,
            mawu.chapter_name,
            MIN(maw.sort_no) AS min_sort_no,
            MIN(mawu.section_name) AS min_section_name
            FROM math_activity_week_unit mawu
            INNER JOIN math_activity_week maw ON mawu.week_id = maw.id
            WHERE
            mawu.type = 'SECTION_TEST'
            AND mawu.deleted_at IS NULL
            AND maw.deleted_at IS NULL
            AND maw.activity_id = #{activityId}
            AND maw.type = 'PREVIEW'
            AND maw.grade = #{grade}
            AND maw.publisher = #{publisher.value}
            GROUP BY mawu.chapter_id, mawu.chapter_name
            ORDER BY min_sort_no, min_section_name
            ) AS chapters
            LIMIT 1
            )
        </if>

    </select>
</mapper>
