<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalUserMapper">

    <select id="pages" resultType="com.joinus.study.model.vo.ReadingPersonalUserVO">
        SELECT
            rpu.student_id,
            rpu.student_name,
        CASE WHEN rpu.grade = 1 THEN '一年级'
        WHEN rpu.grade = 2 THEN '二年级'
        WHEN rpu.grade = 3 THEN '三年级'
        WHEN rpu.grade= 4 THEN '四年级'
        WHEN rpu.grade= 5 THEN '五年级'
        WHEN rpu.grade= 6 THEN '六年级'
        WHEN rpu.grade = 7 THEN '七年级'
        WHEN rpu.grade = 8 THEN '八年级'
        WHEN rpu.grade = 9 THEN '九年级'
        ELSE '高中' END AS grade_name,
            rpu.tel_num,
            rpu.created_at register_time,
            vs.school_name,
            vs.class_name,
            ras. created_at    joinSummerTrainingTime,
            CASE
                WHEN  vs.school_name = 'Ax学校' THEN 2
                ELSE 1
                END as source,
           0        readingTrainingCampCount,
           0        summerTrainingCampCount,
           0        directionalBlastCount,
           0        isOpen
        FROM
            reading_personal_user rpu
                LEFT JOIN active_students_cache vs ON rpu.student_id = vs.student_id
                LEFT JOIN  reading_activity_student ras  ON ras.student_id=rpu.student_id
                LEFT JOIN  reading_activity ra ON ras.activity_id= ra.id AND ra.id=1
        WHERE
            rpu.deleted_at IS NULL
        <if test="null != pageParam.grade">
            AND  rpu.grade = #{pageParam.grade}
        </if>
        <if test="null != pageParam.studentName and '' != pageParam.studentName">
            AND rpu.student_name ILIKE concat('%',#{pageParam.studentName},'%')
        </if>
        <if test="null != pageParam.telNum and '' != pageParam.telNum">
            AND rpu.tel_num ILIKE concat('%',#{pageParam.telNum},'%')
        </if>
        <if test="null != pageParam.source and pageParam.source==1">
            AND  vs.school_name != '青于蓝学科训练营'
        </if>
        <if test="null != pageParam.source and pageParam.source==2">
            AND  vs.school_name = '青于蓝学科训练营'
        </if>
        <if test="null != pageParam.schoolName and '' != pageParam.schoolName">
            AND  vs.school_name ILIKE concat('%',#{pageParam.schoolName},'%')
        </if>
        <if test="pageParam.joinSummerTrainingTimeStart != null">
            and ras.created_at <![CDATA[ >= ]]> #{pageParam.joinSummerTrainingTimeStart}
        </if>
        <if test="pageParam.joinSummerTrainingTimeEnd != null">
            and ras.created_at <![CDATA[ <= ]]> #{pageParam.joinSummerTrainingTimeEnd}
        </if>
          order by rpu.created_at desc
    </select>
    <select id="getContinuousContactCountByDays" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM (
                 SELECT student_id
                 FROM (
                          SELECT student_id, DATE(created_at) as unique_day
                     FROM reading_personal_passages
                 WHERE entry_type IN (1, 2, 4,5) -- 包含1阅读训练, 2定向爆破, 45暑期阅读训练营
                 GROUP BY student_id, DATE(created_at)
             ) AS unique_practice_days
        GROUP BY student_id
        <if test="null != days and days==3">
            HAVING COUNT(unique_day) <![CDATA[ >= ]]> 3
        </if>
        <if test="null != days and days==7">
            HAVING COUNT(unique_day) <![CDATA[ >= ]]> 7
        </if>
        <if test="null != days and days==8">
            HAVING COUNT(unique_day) <![CDATA[ > ]]> 7
        </if>

            ) AS students_with_more_than_count_days;
    </select>
</mapper>