<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.EnglishAiQuestionMapper">

    <select id="list" resultType="com.joinus.study.model.vo.EnglishAiQuestionVO">
        SELECT
            ROW_NUMBER() OVER (ORDER BY RANDOM()) AS question_no,
            t1.id,
            t1.content,
            t1.type,
            t1.difficulty,
            t1.answer,
            t1.analysis,
            t1.options,
            t3.name AS knowledge_point_name
        FROM english_ai_question t1
                 INNER JOIN english_ai_question_knowledge_points t2 ON t2.question_id = t1.id
                 INNER JOIN english_knowledge_points t3 ON t2.knowledge_point_id = t3.id
        WHERE t1.deleted_at IS NULL AND t1.enabled = 1 AND t1.status = 't'
          AND t3.deleted_at IS NULL
        <if test="queryParam != null and queryParam.grade != null">
            AND t1.grade = #{queryParam.grade}
        </if>
        <if test="queryParam != null and queryParam.semester != null and queryParam.semester != ''">
            AND t1.semester = #{queryParam.semester}
        </if>
        <if test='queryParam != null and queryParam.questionDifficultyList != null and queryParam.questionDifficultyList.size() > 0'>
            AND t1.difficulty IN
            <foreach collection="queryParam.questionDifficultyList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam != null and queryParam.knowledgePointId != null">
            AND t2.knowledge_point_id = #{queryParam.knowledgePointId}
        </if>
        <if test="queryParam != null and queryParam.questionCount != null">
            ORDER BY RANDOM()
            LIMIT #{queryParam.questionCount};
        </if>
    </select>


</mapper>
