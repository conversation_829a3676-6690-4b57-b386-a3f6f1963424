<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathKnowledgePointMapper">
    <select id="getKnowledgePointFromViewById" resultType="com.joinus.study.model.dto.KnowledgePointDto">
        SELECT
        knowledge_point_id as  id,
        knowledge_point_name as name,
        textbook_id,
        textbook_name,
        grade,
        semester,
        publisher,
        chapter_id,
        chapter_name,
        chapter_sort_no,
        section_id,
        section_name,
        section_sort_no,
        knowledge_point_original_name,
        exam_point
        FROM
        view_math_knowledge_points
        WHERE knowledge_point_id = #{id}
    </select>
    <select id="getKnowledgePointFromViewByIds" resultType="com.joinus.study.model.dto.KnowledgePointDto">
        SELECT
        knowledge_point_id as  id,
        knowledge_point_name as name,
        textbook_id,
        textbook_name,
        grade,
        semester,
        publisher,
        chapter_id,
        chapter_name,
        chapter_sort_no,
        section_id,
        section_name,
        section_sort_no,
        knowledge_point_original_name,
        exam_point
        FROM
        view_math_knowledge_points
        WHERE
             knowledge_point_id IN
            <foreach collection="ids" item="pointId" separator="," open="(" close=")">
                #{pointId}
            </foreach>
    </select>

</mapper>
