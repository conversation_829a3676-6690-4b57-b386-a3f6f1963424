<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.StudyRecordMapper">

    <select id="pages" resultType="com.joinus.study.model.vo.StudyRecordVo">
        SELECT
            sr.oss_url AS ossUrl,
            sr.created_at ,
           sr.questions_count AS questionCount,
            sr.id ,
            sr.student_id
        FROM
           study_record AS sr
              LEFT JOIN
            study_record_question AS srq ON sr.id = srq.study_record_id
        WHERE
            sr.deleted_at is null
            AND
            student_id = #{param.studentId}
        GROUP BY
            sr.id, sr.oss_url, sr.created_at , sr.student_id
        ORDER BY
            sr.created_at DESC
    </select>
    <select id="detail" resultType="com.joinus.study.model.vo.StudyRecordQuestionDetailsVo">
        SELECT
            sr.id ,
            sr.student_id,
            srq.*,
            CASE srq.difficulty
                WHEN 1 THEN '简单'
                WHEN 2 THEN '简单'
                WHEN 3 THEN '中等'
                WHEN 4 THEN '中等'
                WHEN 5 THEN '困难'
                ELSE ''
                END AS difficultyStr,
            srq.id "studyRecordQuestionId"
        FROM
            study_record AS sr
              left JOIN study_record_question AS srq ON sr.id = srq.study_record_id
        WHERE
            sr.id =#{id}
         and sr.deleted_at is null
    </select>


        <resultMap id="QuestionMap" type="com.joinus.study.model.vo.QuestionVo">
            <result column="id" property="id" typeHandler="com.joinus.study.config.PostgresUUIDTypeHandler"/>
            <result column="content" property="content" />
            <result column="question_Type" property="questionType" />
            <collection property="filesVos" ofType="com.joinus.study.model.vo.FilesVo">
                <result column="id" property="fileId" typeHandler="com.joinus.study.config.PostgresUUIDTypeHandler"/>
                <result column="name" property="name" />
                <result column="type" property="type" />
                <result column="mime_type" property="mimeType" />
                <result column="oss_url" property="ossUrl" />
                <result column="ocr_html" property="ocrHtml" />
            </collection>
        </resultMap>
    <select id="getQuestionById" resultMap="QuestionMap">
        SELECT
            mq.id,
            mq.content,
            mq.question_type,
            f.id as fileId,
            f.name,
            f.type,
            f.mime_type,
            f.oss_url,
            f.ocr_html
        FROM
            math_questions mq
                left join math_question_files qf on qf.question_id = mq.id and qf.type=1
                LEFT JOIN files f ON qf.file_id = f.ID
        WHERE mq.id= #{questionId}::uuid
    </select>

    <select id="questionDetail" resultType="java.util.Map">
        select srq.question_answer as "questionAnswer",
               srq.question_answer_content as "questionContent"
            from study_record sr
            inner join study_record_question srq on srq.study_record_id = sr.id
        where sr.student_id = #{studentId}
          and srq.question_id = #{questionId}::uuid
          and sr.id =#{studyId} and srq.question_answer is not null
          and srq.question_answer_content is not null
    </select>

    <select id="questionsDetail" resultType="java.lang.String">
        select srq.question_answer || '  ' || srq.question_answer_content
        from study_record sr
                 inner join study_record_question srq on srq.study_record_id = sr.id
        where sr.student_id = #{studentId}
          and sr.id =#{studyId}
        and srq.question_id in
        <foreach collection="questionIds" open="(" close=")" separator="," item="questionId">
            #{questionId}::uuid
        </foreach>
    </select>
    <select id="getQuestionAnswer" resultType="com.joinus.study.model.vo.StudyRecordQuestionDetailsVo">
        SELECT
            mq.id as question_id,
            mq.difficulty,
            mqa.answer as questionAnswer,
            mqa.content as questionAnswerContent
        FROM
            math_questions AS mq
                JOIN
            math_question_answers AS qar ON mq.id = qar.question_id
                JOIN
            math_answers AS mqa ON qar.answer_id = mqa.id
        WHERE
            mq.id = #{questionId}

    </select>
    <select id="getStudyQuestionAnswer" resultType="com.joinus.study.model.vo.StudyRecordQuestionDetailsVo">
        SELECT
            rq.id as studyRecordQuestionId,
            r.id ,
            rq.question_id as question_id,
            rq.difficulty,
            rq.question_answer as questionAnswer,
            rq.question_answer_content as questionAnswerContent,
            rq.knowledge_point,
            rq.oss_keys
        FROM
            study_record r
                LEFT JOIN study_record_question rq ON r.ID = rq.study_record_id
        WHERE
            r.ID =#{bookAddParam.studyId}
          AND rq.question_id = #{bookAddParam.questionId}
          AND r.student_id =#{bookAddParam.studentId}
    </select>
    <select id="getQuestionKnowledgesBystudyId" resultType="java.lang.String">


    </select>
</mapper>
