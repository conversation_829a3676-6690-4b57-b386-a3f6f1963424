<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalEquityActivityMapper">

    <select id="selectUsedRecordPage"
            resultType="com.joinus.study.model.vo.ReadingPersonalEquityActivityUsedRecordVo">

                  select
                      t.id,
                      t.code inviteCode,
                      t.user_name userName,
                      t.status,
                      COALESCE(TO_CHAR(t.used_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS usedTime
                  from
                      reading_personal_equity_activity_code t
                        where  t.activity_id=#{pageParam.id}
                          <if test="pageParam.status != null ">
                              and  t.status=#{pageParam.status}
                          </if>
    </select>

    <!--    邀请码下载列表-->
    <select id="downloadListInvitationCode" resultType="com.joinus.study.excel.ReadingPersonalEquityActivityCodeExcel">
        SELECT
            t2.activity_title,
            t2.batch_name,
            t1.code,
            CASE WHEN t1.status = 1 THEN '未使用'
                 WHEN t1.status = 2 THEN '已使用'
                 WHEN t1.status = 3 THEN '已作废'
                 ELSE '未知状态' END AS status,
            t1.user_name,
            COALESCE(TO_CHAR(t1.used_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS usedAt
        FROM reading_personal_equity_activity_code t1
                 INNER JOIN reading_personal_equity_activity t2 ON t1.activity_id = t2.id
        WHERE t1.deleted_at IS NULL
          AND t2.deleted_at IS NULL
          AND t1.activity_id = #{id}
    </select>

    <select id="pages"
            resultType="com.joinus.study.model.vo.ReadingPersonalEquityActivityPageItemVO">
        with activity_code_used as (select activity_id, count(*) used_count
                                    from reading_personal_equity_activity_code
                                    where status = 2
                                    group by activity_id)
        select t1.id,
               t1.activity_title          activityTitle,
               t1.batch_name              batchName,
               t1.code_count              codeCount,
               COALESCE(t2.used_count, 0) codeUsedCount,
               t1.equity_days             equityDays,
               t1.start_date              startDate,
               t1.end_date                endDate,
               t1.creator,
                CASE
                WHEN NOW() > t1.end_date THEN 2
                ELSE t1.status
                END AS status,
               t1.created_at              createdAt
        from reading_personal_equity_activity t1
                 left join activity_code_used t2 on t1.id = t2.activity_id
        WHERE t1.deleted_at IS NULL
        <if test="pageParam.status == 1">
            AND t1.status = 1 and NOW() <![CDATA[ < ]]> t1.end_date
        </if>
        <if test="pageParam.status == 2">
            AND (t1.status = 2 or NOW() > t1.end_date)
        </if>
        <if test="pageParam.activityTitle != null and pageParam.activityTitle != ''">
            and  t1.activity_title like '%' || #{pageParam.activityTitle} || '%'
        </if>
        order by t1.id desc
    </select>
</mapper>