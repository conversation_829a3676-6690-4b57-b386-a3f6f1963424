<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalCorrectionMapper">

    <!--   管理后台-分页 -->
    <select id="pages" resultType="com.joinus.study.model.vo.ReadingPersonalCorrectionVO">
        SELECT
            tmp.id,
            tmp.phone,
            tmp.student_name,
            tmp.content,
            tmp.genre,
            tmp.title,
            tmp.unit_name,
            tmp.unit_id,
            tmp.created_at,
            tmp.question_id,
            tmp.question_no,
            CASE WHEN tmp.grade = 1 THEN '一年级'
            WHEN tmp.grade = 2 THEN '二年级'
            WHEN tmp.grade = 3 THEN '三年级'
            WHEN tmp.grade = 4 THEN '四年级'
            WHEN tmp.grade = 5 THEN '五年级'
            WHEN tmp.grade = 6 THEN '六年级'
            WHEN tmp.grade = 7 THEN '七年级'
            WHEN tmp.grade = 8 THEN '八年级'
            WHEN tmp.grade = 9 THEN '九年级'
            ELSE '未知年级' END AS grade_name,

            CASE WHEN tmp.semester = 1 THEN '上学期'
            ELSE '下学期' END AS semester
        FROM(
                             SELECT
                                 tpc.personal_passage_id AS id,
                                 min(coalesce(tpc.phone, tpu.tel_num)) AS phone,
                                 min(tpu.student_name) AS student_name,
                                 min(tp.content) AS content,
                                 min(tp.genre) AS genre,
                                 min(tp.title) AS title,
                                 min(tu.grade) AS grade,
                                 min(tu.semester) AS semester,
                                 min(tu.name) AS unit_name,
                                 min(tu.id::text) AS unit_id,
                                 min(tpc.created_at) AS created_at,
                                 string_agg(tppq.question_id::text, ',') AS question_id,
                                 string_agg('第'||coalesce(tppq.question_no::text, ' ')||'题', ',') AS question_no
                             FROM reading_personal_correction tpc
                                      LEFT JOIN reading_personal_user tpu ON tpc.student_id = tpu.student_id
                                      LEFT JOIN reading_personal_passages tpp ON tpc.personal_passage_id = tpp.id
                                      LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
                                      LEFT JOIN reading_units tu ON tp.unit_id = tu.id
                                      LEFT JOIN reading_personal_passage_questions tppq ON tpc.personal_passage_question_id = tppq.id
                                      LEFT JOIN reading_passage_questions tpq ON tppq.question_id = tpq.id
                             GROUP BY tpc.personal_passage_id) tmp
        WHERE 1 = 1
        <if test="null != pageParam.studentName and '' != pageParam.studentName">
            AND tmp.student_name ILIKE concat('%',#{pageParam.studentName},'%')
        </if>
        <if test="null != pageParam.phone and '' != pageParam.phone">
            AND tmp.phone ILIKE concat('%',#{pageParam.phone},'%')
        </if>
        <if test="null != pageParam.grade">
            AND tmp.grade = #{pageParam.grade}
        </if>
        <if test="null != pageParam.semester">
            AND tmp.semester = #{pageParam.semester}
        </if>
        <if test="null != pageParam.unitId">
            AND tmp.unit_id = (#{pageParam.unitId})::TEXT
        </if>
        <if test="null != pageParam.genre and '' != pageParam.genre">
            AND tmp.genre ILIKE concat('%',#{pageParam.genre},'%')
        </if>
        <if test="null != pageParam.title and '' != pageParam.title">
            AND tmp.title ILIKE concat('%',#{pageParam.title},'%')
        </if>
        <if test="null != pageParam.timeStart and '' != pageParam.timeStart">
            AND tmp.created_at >= (#{pageParam.timeStart})::timestamp
        </if>
        <if test="null != pageParam.timeEnd and '' != pageParam.timeEnd">
            AND tmp.created_at &lt;= (#{pageParam.timeEnd} ||' 23:59:59.999')::timestamp
        </if>
        ORDER BY tmp.id DESC
    </select>

</mapper>
