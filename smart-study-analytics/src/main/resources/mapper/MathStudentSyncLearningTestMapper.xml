<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathStudentSyncLearningTestMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.MathStudentSyncLearningTest">
            <id property="id" column="id" />
            <result property="studentId" column="student_id" />
            <result property="publisher" column="publisher" />
            <result property="grade" column="grade" />
            <result property="semester" column="semester" />
            <result property="testType" column="test_type" />
            <result property="catalogNodeId" column="catalog_node_id" />
            <result property="examIdHistory" column="exam_id_history" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,student_id,publisher,grade,semester,test_type,
        catalog_node_id,exam_id_history,created_at,updated_at,deleted_at
    </sql>
    <select id="selectNodesMasteryDegree" resultType="java.lang.Double">
        SELECT
            FLOOR(AVG(correct_rate)*100)/100 AS average_correct_rate
        FROM (
            SELECT
            correct_count::FLOAT / NULLIF(total_count, 0) AS correct_rate
            FROM (
                SELECT
                    qkp.knowledge_point_id,
                    SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS correct_count,
                    COUNT(qkp.id) AS total_count
                FROM exam_analyze_result ear
                LEFT JOIN personal_exam_question peq ON ear.personal_exam_id = peq.personal_exam_id
                LEFT JOIN question_knowledge_point qkp ON peq.question_id = qkp.question_id AND qkp.exam_id = ear.exam_id
                WHERE ear.student_id = #{studentId}
                    <if test="examAnalyzeResultId != null">
                        and ear.id = #{examAnalyzeResultId}
                    </if>
                    <if test="catalogNodeId != null">
                        AND qkp.knowledge_point_id IN (
                        SELECT mskp.knowledge_point_id
                        FROM math_section_knowledge_points mskp
                        WHERE mskp.section_id = #{catalogNodeId}
                        )
                    </if>
                    GROUP BY qkp.knowledge_point_id
                ) AS subquery
        ) AS rates;
    </select>
    <select id="getLastExamAnalyzeResultId" resultType="java.lang.Long">
        select ear.id
        from exam_analyze_result ear
        where ear.deleted_at is null
          and ear.result = 'FINISHED'
          and ear.student_id = #{studentId}
          and ear.exam_id in (
            select UNNEST(mt.exam_id_history)
            from math_student_sync_learning_test mt
            where mt.catalog_node_id = #{nodeId}
              and mt.student_id = #{studentId}
              and mt.deleted_at is null
        ) order by ear.created_at desc limit 1
    </select>
    <select id="selectByExamHistoryId" resultType="com.joinus.study.model.entity.MathStudentSyncLearningTest">
        select * from math_student_sync_learning_test
                 where #{examId} = any (exam_id_history)
    </select>
</mapper>
