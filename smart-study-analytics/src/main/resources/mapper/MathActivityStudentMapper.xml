<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathActivityStudentMapper">

    <select id="selectMathActivityStudentVo" resultType="com.joinus.study.model.vo.MathActivityStudentVo">
        select
            mas.id as activityStudentId,
            mas.activity_id,
            mas.student_id,
            mas.parent_id,
            mas.telephone_number,
            mas.grade,
            mas.publisher,
            mas.finish_result,
            mas.created_at,
            ma.start_time,
            ma.end_time
        from math_activity_student mas
            inner join math_activity ma on mas.activity_id = ma.id
        where mas.deleted_at is null and ma.deleted_at is null
          <if test="id != null">
            and mas.id = #{id}
          </if>
          <if test="activityId != null and studentId != null">
            and mas.activity_id = #{activityId}
          </if>
        <if test="studentId != null">
            and mas.student_id = #{studentId}
        </if>
    </select>
</mapper>
