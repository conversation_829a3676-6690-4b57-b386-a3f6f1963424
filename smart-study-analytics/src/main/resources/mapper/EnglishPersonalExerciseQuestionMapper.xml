<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.EnglishPersonalExerciseQuestionMapper">

    <select id="listByPersonalExerciseId" resultType="com.joinus.study.model.dto.EnglishPersonalExerciseQuestionDTO">
        SELECT t1.id,
               t1.question_id,
               t1.question_no,
               t1.question_type,
               t1.reference_answer,
               t1.question_analysis,
               t1.knowledge_point_name,
               t2.content AS question_content,
               t2.options,
               t1.user_answer,
               t1.result
        FROM english_personal_exercise_question t1
                 INNER JOIN english_ai_question t2 ON t1.question_id = t2.id
        WHERE t1.personal_exercise_id = #{personalExerciseId}
        ORDER BY t1.question_no
    </select>

</mapper>
