<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathActivityWeekUnitStudentHistoryMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.MathActivityWeekUnitStudentHistory">
            <result property="id" column="id" />
            <result property="weekUnitStudentId" column="week_unit_student_id" />
            <result property="studentId" column="student_id" />
            <result property="finishedAt" column="finished_at" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,week_unit_student_id,student_id,finished_at,created_at,updated_at,
        deleted_at
    </sql>
</mapper>
