<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.SchoolConfigMapper">

    <resultMap id="BaseResultMap" type="com.joinus.study.model.entity.SchoolConfig">
            <id property="id" column="id" />
            <result property="schoolId" column="school_id" />
            <result property="mathHolidayH5Images" column="math_holiday_h5_images" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,school_id,math_holiday_h5_images,created_at,updated_at,deleted_at
    </sql>
    <insert id="schoolConfigAdd">
        INSERT INTO school_config
            (school_id, math_holiday_h5_images)
        VALUES
            ( #{schoolId},
             #{mathHolidayH5Images, jdbcType=OTHER}::jsonb)
    </insert>
    <update id="updateSchoolConfigById">
        UPDATE school_config
        SET math_holiday_h5_images = #{mathHolidayH5Images, jdbcType=OTHER}::jsonb,
            updated_at = now()
        WHERE
            id = #{id}
    </update>
</mapper>
