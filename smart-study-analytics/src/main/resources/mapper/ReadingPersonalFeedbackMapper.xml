<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper
                PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
                "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalFeedbackMapper">

<!--   管理后台-分页 -->
<select id="pages" resultType="com.joinus.study.model.vo.ReadingPersonalFeedbackVO">
            SELECT
            tpc.id AS id,
            tpc.feedback_type AS feedback_type,
            tpc.suggestion AS suggestion,
            tpc.status AS status,
            coalesce(tpc.phone, tpu.tel_num) AS phone,
            tpu.student_name AS student_name,
            tp.content AS content,
            tp.genre AS genre,
            tp.title AS title,
            tu.grade AS grade,
            CASE WHEN tu.grade = 1 THEN '一年级'
            WHEN tu.grade = 2 THEN '二年级'
            WHEN tu.grade = 3 THEN '三年级'
            WHEN tu.grade = 4 THEN '四年级'
            WHEN tu.grade = 5 THEN '五年级'
            WHEN tu.grade = 6 THEN '六年级'
            WHEN tu.grade = 7 THEN '七年级'
            WHEN tu.grade = 8 THEN '八年级'
            WHEN tu.grade = 9 THEN '九年级'
            ELSE '未知年级' END AS grade_name,
            CASE WHEN tu.semester = 1 THEN '上学期'
            ELSE '下学期' END AS semester,
            tu.name AS unit_name,
            tu.id::text AS unit_id,
            tpc.created_at AS created_at,
            tppq.question_id::text AS question_id,
            '第'||coalesce(tppq.question_no::text, ' ')||'题' AS question_no
            FROM reading_personal_feedback tpc
            LEFT JOIN reading_personal_user tpu ON tpc.student_id = tpu.student_id
            LEFT JOIN reading_personal_passages tpp ON tpc.personal_passage_id = tpp.id
            LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
            LEFT JOIN reading_units tu ON tp.unit_id = tu.id
            LEFT JOIN reading_personal_passage_questions tppq ON tpc.personal_passage_question_id = tppq.id
            LEFT JOIN reading_passage_questions tpq ON tppq.question_id = tpq.id
            WHERE 1 = 1
            <if test="null != pageParam.studentName and '' != pageParam.studentName">
                AND tpu.student_name ILIKE concat('%',#{pageParam.studentName},'%')
            </if>
            <if test="null != pageParam.phone and '' != pageParam.phone">
                AND (tpu.tel_num ILIKE concat('%',#{pageParam.phone},'%') or tpc.phone ILIKE concat('%',#{pageParam.phone},'%'))
            </if>
            <if test="null != pageParam.grade">
                AND  tu.grade = #{pageParam.grade}
            </if>
            <if test="null != pageParam.semester">
                AND tu.semester = #{pageParam.semester}
            </if>
            <if test="null != pageParam.unitId">
                AND tu.id = #{pageParam.unitId}
            </if>
            <if test="null != pageParam.genre and '' != pageParam.genre">
                AND tp.genre ILIKE concat('%',#{pageParam.genre},'%')
            </if>
            <if test="null != pageParam.title and '' != pageParam.title">
                AND tp.title ILIKE concat('%',#{pageParam.title},'%')
            </if>
            <if test="null != pageParam.timeStart and '' != pageParam.timeStart">
                AND tpc.created_at >= (#{pageParam.timeStart})::timestamp
            </if>
            <if test="null != pageParam.timeEnd and '' != pageParam.timeEnd">
                AND tpc.created_at &lt;= (#{pageParam.timeEnd} ||' 23:59:59.999')::timestamp
            </if>
            <if test="null != pageParam.status">
                AND tpc.status =#{pageParam.status}
            </if>
            ORDER BY tpc.id DESC
</select>
    <select id="getDetails" resultType="com.joinus.study.model.vo.ReadingPersonalFeedbackDetailsVo">
        SELECT
            tpc.id AS id,
            tp.id AS passageId,
            tp.title AS title,
            tp.content AS content,
            tpq.id AS question_id,
            tpq.content questionContent,
            tppq.question_type,
            tpc.feedback_type,
            tpc.suggestion AS suggestion,
            tpc.status AS status,
            tpc.created_at AS created_at,
            tqa.id answerId,
            tqa.answer,
            tqa.content answerContent,
            rqa.answer as formulaAnswer,
            raf.formula_template as formulaTemplate
        FROM reading_personal_feedback tpc
                 LEFT JOIN reading_personal_passages tpp ON tpc.personal_passage_id = tpp.id
                 LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
                 LEFT JOIN reading_personal_passage_questions tppq ON tpc.personal_passage_question_id = tppq.id
                 LEFT JOIN reading_passage_questions tpq ON tppq.question_id = tpq.id
                 LEFT JOIN reading_question_answers tqa  ON tqa.question_id=tpq.id
            AND tqa.deleted_at IS NULL
            AND tqa.answering_formula_id IS NULL
                 LEFT JOIN reading_question_answers rqa on rqa.question_id = tpq.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 LEFT JOIN reading_answering_formulas raf on raf.id = rqa.answering_formula_id
        WHERE 1 = 1
          AND tpc.id=#{id}
        ORDER BY tpc.id DESC

    </select>
    <select id="getKnowledgePoints" resultType="com.joinus.study.model.entity.ReadingKnowledgePoints">

        SELECT
            tkp.id,
            tkp.name
        FROM
            reading_question_knowledge_points tqkp
                INNER JOIN  reading_knowledge_points tkp ON tqkp.knowledge_point_id = tkp.ID
                AND  tqkp.question_id=#{questionId}
    </select>

</mapper>
