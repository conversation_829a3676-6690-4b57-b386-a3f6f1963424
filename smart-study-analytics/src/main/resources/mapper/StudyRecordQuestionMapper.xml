<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.StudyRecordQuestionMapper">

    <update id="updateKnowledgePoint">
        update study_record_question
        set knowledge_point = #{knowledgePoint}::jsonb,
            difficulty   = #{difficultyInt},
            type = 'KNOWLEDGE'
        where id = #{id}
    </update>

    <select id="selectRecordQuestion" resultType="long">
        select srq.id from study_record sr ,study_record_question srq
        where sr.student_id = #{studentId}
          and sr.id = srq.study_record_id
          and srq.question_id = #{questionId}
    </select>
</mapper>
