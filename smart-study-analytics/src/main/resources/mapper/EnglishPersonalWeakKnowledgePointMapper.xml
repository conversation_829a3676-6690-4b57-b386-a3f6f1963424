<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.EnglishPersonalWeakKnowledgePointMapper">

    <select id="pages" resultType="com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO">
        SELECT
        t1.id,
        t1.knowledge_point_id,
        t1.knowledge_point_name,
        t1.question_type,
        ROUND(t1.error_percentage * 100) || '%' AS error_percentage_str
        FROM english_personal_weak_knowledge_point t1
        WHERE t1.deleted_at IS NULL
        AND t1.status != 0 AND t1.error_percentage > 0.20
        <if test="pageParam != null and pageParam.studentId != null">
            AND t1.student_id = #{pageParam.studentId}
        </if>
        ORDER BY t1.error_percentage DESC, t1.id DESC
    </select>

    <select id="list" resultType="com.joinus.study.model.vo.EnglishPersonalWeakKnowledgePointVO">
        SELECT
            t1.id,
            t1.knowledge_point_id,
            t1.knowledge_point_name
        FROM english_personal_weak_knowledge_point t1
        WHERE t1.deleted_at IS NULL
          AND t1.status != 0 AND t1.error_percentage > 0.20
        <if test="studentId != null">
            AND t1.student_id = #{studentId}
        </if>
        <if test="diagnoseRecordId != null">
            AND (
            t1.diagnose_record_id = #{diagnoseRecordId}::text
            OR
            #{diagnoseRecordId}::text = ANY(STRING_TO_ARRAY(t1.diagnose_record_id, ','))
            )
        </if>
        ORDER BY t1.error_percentage DESC, t1.id DESC
    </select>

</mapper>
