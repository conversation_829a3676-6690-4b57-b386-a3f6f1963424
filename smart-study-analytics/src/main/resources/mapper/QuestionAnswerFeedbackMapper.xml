<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.QuestionAnswerFeedbackMapper">

    <insert id="save">
        INSERT INTO question_answer_feedback
        (student_id,
         question_id,
         question_answer_id,
         type,
         content,
         suggestion,
         created_at,
         updated_at,
         deleted_at
         )
        VALUES (#{param.studentId},
                #{param.questionId}::uuid,
                #{param.questionAnswerId}::uuid,
                #{param.type}::type_enum,
                #{param.content},
                #{param.suggestion},
                #{param.createdAt},
                #{param.updatedAt},
                #{param.deletedAt}
        )

    </insert>
</mapper>
