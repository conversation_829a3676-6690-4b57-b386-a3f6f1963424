<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <title>初中语文阅读理解练习题</title>
    <style>
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1em;
            border-bottom: 1px solid #ccc;
        }

        .logo {
            width: 2em;
            height: 2em;
            margin-right: 1em;
        }

        .logo-img-wrap {
            display: flex;
            align-items: center;
        }

        .slogan {
            font-family: <PERSON>m<PERSON><PERSON>, SimHei, STFangsong !important;
            font-size: 1.3em;
        }

        .header {
            text-align: center;
            margin: 3em 0;
            font-family: <PERSON><PERSON><PERSON><PERSON>, <PERSON>m<PERSON><PERSON>, STFangsong !important;
        }

        .question-answer {
            height: 2em;
            border: 1px dashed #ccc;
        }

        .cut-bottom {
            font-family: <PERSON><PERSON><PERSON><PERSON>, <PERSON>m<PERSON><PERSON>, STFangsong !important;
        }

        .title,
        .author {
            text-align: center;
            font-size: 1.5em;
        }

        .author {
            margin-top: 1em;
        }

        .article {
            /* 首行缩进2 */
            text-indent: 2em;
            font-size: 1em;
            margin-top: 2em;
            white-space: pre-line;
        }

        .question-wrap {
            margin-top: 2em;
        }

        .question {
            font-size: 1em;
            margin: 1em 0;
        }

        .answer {
            font-size: 0.8em;
            margin: 1em 0;
        }

        .qrcode-img {
            width: 6em;
            height: 6em;
        }

        h2 {
            display: block;
            font-size: 1.5em;
            margin-block-start: 0.83em;
            margin-block-end: 0.83em;
            margin-inline-start: 0px;
            margin-inline-end: 0px;
            font-weight: bold;
            unicode-bidi: isolate;
            font-family: SimSun, SimHei, STFangsong !important;
        }

        p {
            display: block;
            margin-block-start: 1em;
            margin-block-end: 1em;
            margin-inline-start: 0px;
            margin-inline-end: 0px;
            unicode-bidi: isolate;
        }
    </style>
</head>

<body>
<div class="page-header">
    <div class="logo-wrap">
        <!-- 青于蓝的logo -->
        <div class="logo-img-wrap">
            <img src="https://cdn-ali-static.ijx.ink/assets/images/qingyulan_180.png" alt="" class="logo" />
            <h2>青于蓝</h2>
        </div>
        <span class="slogan">青，取之于蓝，而青于蓝</span>
    </div>
    <!--二维码-->
    <span></span>
</div>
<div class="header">
    <!--<h2>阅读理解练习题</h2> <p>姓名：__________ 得分：__________</p>-->
</div>
<!--二维码-->
<div class="cut-bottom title"> <span>PASSAGES_TITLE</span></div>
<div class="cut-bottom article">PASSAGES_CONTENT</div>
<div class="cut-bottom question-wrap">QUESTION_CONTENT</div>
<!--答案及解析-->
<div class="cut-bottom answer">ANSWER_CONTENT</div>
</body>

</html>