plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}
group 'com.joinus.mqtt'
version 'basic-mqtt-1.0.4'

repositories {
    mavenCentral()
}

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}

dependencies {

    implementation project(':common')

//    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    //由于springcloud2020弃用了Ribbon，因此Alibaba在2021版本nacos中删除了Ribbon的jar包，因此无法通过lb路由到指定微服务，出现了503情况。
    //所以只需要引入springcloud loadbalancer包即可
//    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
    implementation 'com.alibaba.mqtt:server-sdk'
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}
configurations.all {
    exclude group: 'com.baomidou', module: 'mybatis-plus-boot-starter'
    exclude group: 'com.baomidou', module: 'dynamic-datasource-spring-boot-starter'
    exclude group: 'mysql', module: 'mysql-connector-java'
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-redis'
}

bootJar {
    launchScript()
    archiveName "snake-basic-mqtt.jar"
}
docker {
    name "${dockerRepo}/${dockerPrefix}/snake-basic-mqtt"
    tag 'taskLatest', "${dockerRepo}/${dockerPrefix}/snake-basic-mqtt:latest"
    tag 'taskVersion', "${dockerRepo}/${dockerPrefix}/snake-basic-mqtt:${version}"
    dockerfile file('Dockerfile')
    copySpec.from("build/libs").into("./")
    buildArgs([BUILD_VERSION: 'version'])
}
dockerPrepare.dependsOn(bootJar)