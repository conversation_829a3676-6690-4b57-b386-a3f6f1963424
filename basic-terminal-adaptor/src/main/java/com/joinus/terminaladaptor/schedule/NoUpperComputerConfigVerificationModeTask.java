package com.joinus.terminaladaptor.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.joinus.dao.TerminalEntity;
import com.joinus.terminaladaptor.service.ITerminalService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.joinus.terminaladaptor.service.HikVisionService;

import java.util.List;


@Slf4j
@Component("noUpperComputerConfigVerificationModeTask")
@JobHandler(value = "noUpperComputerConfigVerificationModeTaskHandler")
public class NoUpperComputerConfigVerificationModeTask extends IJobHandler {
    @Autowired
    private ITerminalService terminalService;
    @Autowired
    private HikVisionService hikVisionService;

    @Override
    public ReturnT<String> execute(String schoolId) {
        log.info("noUpperComputerConfigVerificationModeTask start, 传入参数:{}", schoolId);
        List<TerminalEntity> hikNoUpperComputerTerminalList = terminalService.getHikNoUpperComputerTerminal(Convert.toLong(schoolId));
        log.info("noUpperComputerConfigVerificationModeTask 需要修改配置的设备数量:{}", hikNoUpperComputerTerminalList.size());
        if(CollUtil.isEmpty(hikNoUpperComputerTerminalList)){
            return ReturnT.SUCCESS;
        }
        for (TerminalEntity terminalEntity : hikNoUpperComputerTerminalList) {
            try {
                hikVisionService.configVerificationMode(terminalEntity.getTerminalNum(), terminalEntity.getSchoolId());
                log.info("noUpperComputerConfigVerificationModeTask 学校:{}, 设备:{} 修改配置成功", terminalEntity.getSchoolId(), terminalEntity.getTerminalNum());
            } catch (Exception e) {
                log.warn("noUpperComputerConfigVerificationModeTask 学校:{}, 设备:{} 修改配置失败,错误原因:{}", terminalEntity.getSchoolId(), terminalEntity.getTerminalNum(), e.getMessage(), e);
            }
        }
        log.info("noUpperComputerConfigVerificationModeTask end");
        return ReturnT.SUCCESS;
    }



}
