package com.joinus.terminaladaptor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.enmus.TerminalTypeEnum;
import com.joinus.common.model.enums.ActiveEnum;
import com.joinus.dao.*;
import com.joinus.dao.mapper.StudentMapper;
import com.joinus.terminaladaptor.bean.DeployStudentInfo;
import com.joinus.terminaladaptor.bean.IncrementStudentInfo;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.enums.DirectTerminalDeployInfoStatusEnum;
import com.joinus.terminaladaptor.enums.ManufacturerEnum;
import com.joinus.terminaladaptor.service.*;
import com.joinus.terminaladaptor.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29 09:27:49
 */
@Slf4j
@Service
public class StudentServiceImpl extends ServiceImpl<StudentMapper, StudentEntity> implements IStudentService {

    @Autowired
    private ITerminalService terminalService;
    @Autowired
    private IDirectTerminalDeployInfoService directTerminalDeployInfoService;
    @Autowired
    private IClassService classService;
    @Autowired
    private IGradeService gradeService;
    @Autowired
    private RedisUtil redisUtil;
    
    @Override
    public void pushStudentIncrement(IncrementStudentInfo incrementStudentInfo) {
        StudentEntity student = this.getById(incrementStudentInfo.getStudentId());
        if (ObjectUtil.isEmpty(student)) {
            log.info("未查询到学生信息:{}", incrementStudentInfo);
            return;
        }
        if (incrementStudentInfo.getOperType() == null) {
            log.info("未查询到学生操作类型:{}", incrementStudentInfo);
            return;
        }

        //获取该学生应该下发的设备编号
        List<TerminalEntity> resultTermianlNumList = this.getTerminalListByStudent(student, incrementStudentInfo.getOperType());

        if (CollUtil.isEmpty(resultTermianlNumList)) {
            log.warn("该学生:{},没有需要下发的设备", student.getId() + "|" + student.getStudentName());
            return;
        }

        //是否包含上位机  如果包含上位机 下发学生,增加该学生年级班级信息
        ClassEntity classEntity = classService.getById(student.getClassId());
        GradeEntity gradeEntity = gradeService.getById(classEntity.getGradeId());
        student.setClassName(classEntity.getClassName());
        student.setGradeId(gradeEntity.getId());
        student.setGradeName(gradeEntity.getGradeName());
        //  查询每个设备下发信息
        List<DirectTerminalDeployInfoEntity>  existDeployStudentInfoList = directTerminalDeployInfoService.getPushDirectTerminalDeployInfoBySchoolIdAndStudentId(student.getSchoolId(), student.getId());
        Map<String, DirectTerminalDeployInfoEntity> existTerminalNumDeployMap = existDeployStudentInfoList.stream()
                .collect(Collectors.toMap(DirectTerminalDeployInfoEntity::getTerminalNum, Function.identity(), (existing, replacement) -> replacement));
        List<DirectTerminalDeployInfoEntity> directTerminalDeployInfoList = new ArrayList<>();
        DirectTerminalDeployInfoStatusEnum newStatusEnum = incrementStudentInfo.getOperType() != null && incrementStudentInfo.getOperType().equals(2) ? DirectTerminalDeployInfoStatusEnum.AWAIT_DELETE : DirectTerminalDeployInfoStatusEnum.NOT_PUSH;
        resultTermianlNumList.forEach(terminal -> {
            try {
                DirectTerminalDeployInfoEntity directTerminalDeployInfo = this.buildPushStudentDetail(student, terminal, newStatusEnum, existTerminalNumDeployMap);
                if (ObjectUtil.isNotEmpty(directTerminalDeployInfo)) {
                    directTerminalDeployInfoList.add(directTerminalDeployInfo);
                }
                //维护 增量学生信息 id
                if (ObjectUtil.isNotEmpty(incrementStudentInfo.getStudentIncrementId())) {
                    TerminalEntity updateTerminal = new TerminalEntity();
                    updateTerminal.setId(terminal.getId());
                    updateTerminal.setWhiteLastId(incrementStudentInfo.getStudentIncrementId());
                    com.alibaba.fastjson2.JSONObject json = updateTerminal.toTerminalStudentIncrementJson();
                    redisUtil.setList(GlobalConstants.REDIS_KEY_INCREMENT_STUDENT_TERMINALS, Collections.singletonList(json.toJSONString()), 0);
                }
                //维护增量 学生照片id
                if (ObjectUtil.isNotEmpty(incrementStudentInfo.getStudentPhotoIncrementId())) {
                    String redisKey = "TERMINAL_PHOTO_LAST_ID_" + (terminal.getManufacturer() != null && terminal.getManufacturer() == ManufacturerEnum.UNIVIEW.getCode() ? terminal.getImei() : terminal.getTerminalNum());
                    if (redisUtil.hasKey(redisKey)) {
                        Long id = Convert.toLong(redisUtil.get(redisKey));
                        if (incrementStudentInfo.getStudentPhotoIncrementId() > id) {
                            redisUtil.set(redisKey, Convert.toStr(incrementStudentInfo.getStudentPhotoIncrementId()));
                        }
                    } else {
                        redisUtil.set(redisKey, Convert.toStr(incrementStudentInfo.getStudentPhotoIncrementId()));
                    }
                }
            } catch (Exception e) {
                log.info("该学生:{},增量下发的设备:{},失败,原因:{}", student.getId() + "|" + student.getStudentName(), terminal.getTerminalNum(), e.getMessage(), e);
            }
        });
        log.info("该学生:{},下发的设备入库条数:{}", student.getId() + "|" + student.getStudentName(), directTerminalDeployInfoList.size());
        if (CollUtil.isNotEmpty(directTerminalDeployInfoList)) {
            directTerminalDeployInfoService.saveOrUpdateBatch(directTerminalDeployInfoList);
        }

    }

    private  DirectTerminalDeployInfoEntity buildPushStudentDetail(StudentEntity student, TerminalEntity terminal, DirectTerminalDeployInfoStatusEnum newStatus, Map<String, DirectTerminalDeployInfoEntity> existTerminalNumDeployMap) {
        DirectTerminalDeployInfoEntity dbDeployInfo = existTerminalNumDeployMap.get(terminal.getTerminalNum());
        DirectTerminalDeployInfoEntity directTerminalDeployInfo  =new DirectTerminalDeployInfoEntity();
        if (ObjectUtil.isNotEmpty(dbDeployInfo)) {
            directTerminalDeployInfo.setId(dbDeployInfo.getId());
            directTerminalDeployInfo.setUpdatedAt(LocalDateTime.now());

            DeployStudentInfo dbStudent = JSONUtil.toBean(dbDeployInfo.getDeployInfo(), DeployStudentInfo.class);
            String[] fieldNames = {"id", "studentImg", "studentName", "cardCode"};
            if (terminal.getTerminalType().equals(TerminalTypeEnum.UPPER_COMPUTER_TYPE_ID.getTerminalType())) {
                fieldNames = new String[]{"id", "studentImg", "studentName", "cardCode", "isTime1", "isTime2", "isTime3", "classId", "className", "gradeId", "gradeName", "studentCode", "isDorm", "isGuaShi", "sex"};
            } else {
                if (terminal.getManufacturer() != null && terminal.getManufacturer() == ManufacturerEnum.UNIVIEW.getCode()) {
                    fieldNames = new String[]{"id", "studentImg", "studentName", "cardCode", "classId", "className", "gradeId", "gradeName"};
                }
            }
            DeployStudentInfo pushStudent = BeanUtil.copyProperties(student, DeployStudentInfo.class);
            if (contentEquals(dbStudent, pushStudent, fieldNames)) {
                log.warn("要下发数据:{},和数据库中已下发数据:{},一致不进行下发", pushStudent, dbStudent);
                return null;
            }

        } else {
            directTerminalDeployInfo.setStudentId(student.getId());
            directTerminalDeployInfo.setTerminalNum(terminal.getTerminalNum());
            directTerminalDeployInfo.setCreatedAt(LocalDateTime.now());
        }
        //如果是下发
        if (DirectTerminalDeployInfoStatusEnum.NOT_PUSH.equals(newStatus)) {
            DeployStudentInfo deployStudentInfo = new DeployStudentInfo();
            deployStudentInfo.setIsTime1(student.getIsTime1());
            deployStudentInfo.setIsTime2(student.getIsTime2());
            deployStudentInfo.setIsTime3(student.getIsTime3());
            deployStudentInfo.setClassId(student.getClassId());
            deployStudentInfo.setClassName(student.getClassName());
            deployStudentInfo.setGradeId(student.getGradeId());
            deployStudentInfo.setGradeName(student.getGradeName());
            deployStudentInfo.setStudentCode(student.getStudentCode());
            deployStudentInfo.setIsDorm(student.getIsDorm().getStatus());
            deployStudentInfo.setIsGuaShi(student.getIsGuaShi().getStatus());
            deployStudentInfo.setSex(student.getSex().getSex());
            deployStudentInfo.setId(student.getId());
            deployStudentInfo.setStudentImg(student.getStudentImg());
            deployStudentInfo.setStudentName(student.getStudentName());
            deployStudentInfo.setCardCode(student.getCardCode());
            directTerminalDeployInfo.setDeployInfo(JSONUtil.toJsonStr(deployStudentInfo));
        }
        directTerminalDeployInfo.setRemarks("0");
        directTerminalDeployInfo.setIsactive(1);
        directTerminalDeployInfo.setStatus(newStatus.getCode());
        directTerminalDeployInfo.setSchoolId(student.getSchoolId());
        directTerminalDeployInfo.setManufacturerId(terminal.getManufacturer());

        return  directTerminalDeployInfo;
    }

    private  boolean contentEquals(DeployStudentInfo dbStudent, DeployStudentInfo pushStudent, String[] fieldNames) {
        for (String fieldName : fieldNames) {
            Object valueDb = ReflectUtil.getFieldValue(dbStudent, fieldName);
            Object valuePush = ReflectUtil.getFieldValue(pushStudent, fieldName);
            if (valueDb == null || valuePush == null) {
                if (valueDb != valuePush) {
                    return false;
                }
            } else if (!valueDb.equals(valuePush)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<TerminalEntity> getTerminalListByStudent(StudentEntity student, Integer operType) {
        List<TerminalEntity> resultTermianlNumList = new ArrayList<>();
         //获取学校下所有的 无上位一体机和 上位机
        List<TerminalEntity> schoolTerminalList= terminalService.listTerminalsBySchoolId(student.getSchoolId());
        Map<Long, List<TerminalEntity>> terminalMap = schoolTerminalList.stream()
                .collect(Collectors.groupingBy(TerminalEntity::getTerminalType));
        //获取上位机
        List<TerminalEntity> schoolUpperComputerList = terminalMap.get(TerminalTypeEnum.UPPER_COMPUTER_TYPE_ID.getTerminalType());
        List<TerminalEntity> schoolNoUpperComputerList = terminalMap.get(TerminalTypeEnum.NO_UPPER_COMPUTER_MACHINE.getTerminalType());
        //如果是删除
        if (operType != null && operType.equals(2)) {
            if (CollUtil.isNotEmpty(schoolUpperComputerList)) {
                List<Long> schoolUpperComputerTerminalIdList = schoolUpperComputerList.stream().map(TerminalEntity::getId).collect(Collectors.toList());
                resultTermianlNumList.addAll(terminalService.getFaceTerminalNumByIds(schoolUpperComputerTerminalIdList));
            }
            if (CollUtil.isNotEmpty(schoolNoUpperComputerList)) {
                resultTermianlNumList.addAll(schoolNoUpperComputerList);
            }
            return resultTermianlNumList;
        }

        //获取 该学生绑定分组设备Id
        List<Long> studentBindGroupTerminalIdList = this.baseMapper.getBindGroupTerminalIdByStudentId(student.getId());
        //获取该学生绑定 floorIds.
        List<Long> studentBindFloorIdList = this.baseMapper.getBindFloorIdsByStudentId(student.getId());

        if (CollUtil.isNotEmpty(schoolUpperComputerList)) {
            List<TerminalEntity> studentUpperTerminalList = new ArrayList<>();
            Map<Boolean, List<TerminalEntity>> schoolUpperNotBindedPresenceMap = schoolUpperComputerList.stream()
                    .collect(Collectors.partitioningBy(entity -> StrUtil.isBlank(entity.getFloorIds()) && (entity.getExistGroup() == null || entity.getExistGroup() == 0)));
            // 获取没有绑定 分组和宿舍终端的上位机
            List<TerminalEntity> schoolNotBindedUpperTerminalList = schoolUpperNotBindedPresenceMap.get(true);
            if (CollUtil.isNotEmpty(schoolNotBindedUpperTerminalList)) {
                studentUpperTerminalList.addAll(schoolNotBindedUpperTerminalList);
            }

            List<TerminalEntity> schoolBindedUpperTerminalList = schoolUpperNotBindedPresenceMap.get(false);
            if (CollUtil.isNotEmpty(schoolBindedUpperTerminalList)) {
                // 按照绑定宿舍和绑定分组进行 拆分
                Map<Boolean, List<TerminalEntity>> schoolBindedGroupUpperTerminalMap = schoolBindedUpperTerminalList.stream()
                        .collect(Collectors.partitioningBy(entity -> StrUtil.isBlank(entity.getFloorIds())));
                List<TerminalEntity> schoolGroupUpperTerminalList = schoolBindedGroupUpperTerminalMap.get(true);
                //该学生绑定分组的设备需要进行下发
                if (CollUtil.isNotEmpty(schoolGroupUpperTerminalList)) {
                    studentUpperTerminalList.addAll(schoolGroupUpperTerminalList.stream().filter(terminalEntity -> studentBindGroupTerminalIdList.contains(terminalEntity.getId())).collect(Collectors.toList()));
                }

                List<TerminalEntity> dormTerminalList = schoolBindedGroupUpperTerminalMap.get(false);
                if (CollUtil.isNotEmpty(dormTerminalList)) {
                    studentUpperTerminalList.addAll(dormTerminalList.stream()
                            .filter(terminal -> containsAnyFloor(terminal.getFloorIds(), studentBindFloorIdList))
                            .collect(Collectors.toList()));
                }
            }

            //如果上位机 不为空 ,则获取 上位机下面的 人脸识别设备 和 自己
            if (CollUtil.isNotEmpty(studentUpperTerminalList)) {
                List<Long> studentUpperTerminalIdList = studentUpperTerminalList.stream().map(TerminalEntity::getId).collect(Collectors.toList());
                resultTermianlNumList.addAll(terminalService.getFaceTerminalNumByIds(studentUpperTerminalIdList));
            }
        }
        log.warn("该学生:{},待下发的上位机设备号:{}", student.getId() + "|" + student.getStudentName(),  resultTermianlNumList.stream().map(TerminalEntity::getTerminalNum).collect(Collectors.toList()));


        //无上位一体机
        if(CollUtil.isNotEmpty(schoolNoUpperComputerList)){
            //获取该学生绑定巡更设备
            List<Long> studentBindPatrolsTerminalIdList =  this.baseMapper.getBindPatrolsTerminalIdByStudentId(student.getId());

            //获取该学生学校绑定巡更设备
            List<Long> schoolBindPatrolsTerminalIdList =  this.baseMapper.getBindPatrolsTerminalIdBySchoolId(student.getSchoolId());
            //是否是巡更设备
            List<TerminalEntity> studentNoUpperTermialList = new ArrayList<>();
            //学生绑定的巡更设备全部下发
            studentNoUpperTermialList.addAll(schoolNoUpperComputerList.stream().filter(terminal -> studentBindPatrolsTerminalIdList.contains(terminal.getId())).collect(Collectors.toList()));
            List<TerminalEntity> schoolNotUpperNotBindedPresenceList = schoolNoUpperComputerList.stream().filter(terminal -> !schoolBindPatrolsTerminalIdList.contains(terminal.getId())).collect(Collectors.toList());
            Map<Boolean, List<TerminalEntity>> schoolUpperNotBindedPresenceMap =schoolNotUpperNotBindedPresenceList.stream()
                    .collect(Collectors.partitioningBy(entity -> StrUtil.isBlank(entity.getFloorIds()) && (entity.getExistGroup() == null || entity.getExistGroup() == 0)));
            // 获取没有绑定 分组和宿舍终端的上位机
            List<TerminalEntity> schoolNotBindedNotUpperTerminalList = schoolUpperNotBindedPresenceMap.get(true);
            if (CollUtil.isNotEmpty(schoolNotBindedNotUpperTerminalList)) {
                studentNoUpperTermialList.addAll(schoolNotBindedNotUpperTerminalList);
            }

            List<TerminalEntity> schoolBindedNotUpperTerminalList = schoolUpperNotBindedPresenceMap.get(false);
            if (CollUtil.isNotEmpty(schoolBindedNotUpperTerminalList)) {
                // 按照绑定宿舍和绑定分组进行 拆分
                Map<Boolean, List<TerminalEntity>> schoolBindedGroupNotUpperTerminalMap = schoolBindedNotUpperTerminalList.stream()
                        .collect(Collectors.partitioningBy(entity -> StrUtil.isBlank(entity.getFloorIds())));
                List<TerminalEntity> schoolGroupNotUpperTerminalList = schoolBindedGroupNotUpperTerminalMap.get(true);
                //该学生绑定分组的设备需要进行下发
                if (CollUtil.isNotEmpty(schoolGroupNotUpperTerminalList)) {
                    studentNoUpperTermialList.addAll(schoolGroupNotUpperTerminalList.stream().filter(terminalEntity -> studentBindGroupTerminalIdList.contains(terminalEntity.getId())).collect(Collectors.toList()));
                }

                List<TerminalEntity> schoolDormNotUpperTerminalList = schoolBindedGroupNotUpperTerminalMap.get(false);
                if (CollUtil.isNotEmpty(schoolDormNotUpperTerminalList)) {
                    studentNoUpperTermialList.addAll(schoolDormNotUpperTerminalList.stream()
                            .filter(terminal -> containsAnyFloor(terminal.getFloorIds(), studentBindFloorIdList))
                            .collect(Collectors.toList()));
                }
            }

            List<String> noUpperComputerTerminalNumList = studentNoUpperTermialList.stream().map(TerminalEntity::getTerminalNum).collect(Collectors.toList());
            log.warn("该学生:{},待下发的无上位机设备号:{}", student.getId() + "|" + student.getStudentName(), noUpperComputerTerminalNumList);
            resultTermianlNumList.addAll(studentNoUpperTermialList);
        }
        return resultTermianlNumList;
    }

    @Override
    public StudentEntity getStudentInfoByCardCodeAndSchoolId(String cardCode, Long schoolId) {
        QueryWrapper<StudentEntity> queryStudentWrapper = new QueryWrapper<>();
        queryStudentWrapper.eq("cardcode",cardCode);
        queryStudentWrapper.eq("isactive", ActiveEnum.VALID);
        queryStudentWrapper.eq("SCHOOL_ID",schoolId);
        return getBaseMapper().selectOne(queryStudentWrapper);
    }

    private boolean containsAnyFloor(String floors, List<Long> bindFloorIds) {
        List<String> floorList = Arrays.asList(floors.split(","));
        // 将字符串楼层数组转换成长整数数组
        List<Long> floorIds = floorList.stream().map(Long::parseLong).collect(Collectors.toList());
        // 检查是否有任何一个匹配的楼层ID
        return bindFloorIds.stream().anyMatch(floorIds::contains);
    }

}
