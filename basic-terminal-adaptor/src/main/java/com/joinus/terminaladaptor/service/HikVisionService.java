package com.joinus.terminaladaptor.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.joinus.dao.DictionaryEntity;
import com.joinus.dao.TerminalEntity;
import com.joinus.dao.TerminalPersonEntity;
import com.joinus.terminaladaptor.bean.hik.DeviceInfoDTO;
import com.joinus.terminaladaptor.bean.hik.NetworkInfoDTO;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.constant.HikVisionUriConstant;
import com.joinus.terminaladaptor.enums.TerminalOperatorTypeEnum;
import com.joinus.terminaladaptor.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 海康一体机相关方法
 * @date 2024-11-28 17:25:18
 */
@Slf4j
@Service
public class HikVisionService {

    @Autowired
    private ITerminalService terminalService;

    @Autowired
    private IDictionaryService dictionaryService;

    @Autowired
    private ITerminalPersonService terminalPersonServicel;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${server.domain.hik.vision.adaptor:https://basic-hik-vision.uat.fangxiao.top/api/basic-hik}")
    private String hikVisionAdaptorDomain;

    private static JSONConfig jsonConfig = JSONUtil.parseObj("{\"ignoreNullValue\":true,\"dateFormat\":null," +
            "\"ignoreError\":false,\"ignoreCase\":false,\"order\":false}").toBean(JSONConfig.class);

    @Value("${loggable.terminal-num:}")
    private String loggableTerminalNum;

    @Value("#{'${hik-not-play-voice.schoolIds:29660,190}'.split(',')}")
    private List<Long> hikNotPlayVoiceSchoolIdList;

    @Value("#{'${hik-not-play-voice.terminalNums:}'.split(',')}")
    private List<String> hikNotPlayVoiceTerminalNumList;

    @Value("${basic-terminal-adaptor.domain.name:https://basic-terminal-adaptor.ijiaxiao.net}")
    private String basicTerminalAdaptorDomain;

    @Value("#{'${hik.rules.voice.schoolId.list:190}'.split(',')}")
    private List<Long> hikRulesVoiceSchoolIdList;

    @Value("#{'${hik.enable.offline.open-door.schoolId.list:190}'.split(',')}")
    private List<Long> enableOfflineOpenDoorSchoolIdList;


    /**
     * [deviceId]
     * @return void
     * @description 核对设备SN
     * <AUTHOR>
     * @date 2024/7/12 15:44
     */
    public void checkSNforHik(String deviceId) {
        DeviceInfoDTO deviceInfoMap = queryDeviceInfo(deviceId);
        String serialNumber = deviceInfoMap.getSerialNumber();
        log.info("serialNumber:{}", serialNumber);
        if (StrUtil.isNotBlank(serialNumber) && serialNumber.length() > 9) {
            String sn = serialNumber.substring(serialNumber.length() - 9);
            List<TerminalEntity> terminalList = terminalService.getTerminalBySN(sn);

            List<TerminalEntity> eqTerminalList = terminalList.stream().filter(terminal -> deviceId.equals(terminal.getTerminalNum())).collect(Collectors.toList());
            List<TerminalEntity> notEqTerminalList = terminalList.stream().filter(terminal -> !deviceId.equals(terminal.getTerminalNum())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(notEqTerminalList)) {
                notEqTerminalList.forEach(terminal -> {
                    UpdateWrapper<TerminalEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.set("SN", "")
                            .set("UPDATE_TIME", new Date())
                            .eq("TERMINAL_NUM", terminal.getTerminalNum());
                    terminalService.update(updateWrapper);
                    String content = String.format("系统[basic-terminal-adaptor]删除,getTerminalNum {%s} 原有SN:{%s}", terminal.getTerminalNum(), terminal.getSn());
                    addTerminalPerson(terminal, content);
                });
            }
            if (CollUtil.isEmpty(eqTerminalList)) {
                TerminalEntity terminalChange = terminalService.getTerminalByTerminalNum(deviceId);
                TerminalEntity changeTerminal = new TerminalEntity();
                changeTerminal.setSn(sn);
                changeTerminal.setUpdateTime(LocalDateTime.now());
                changeTerminal.setId(terminalChange.getId());
                terminalService.updateById(changeTerminal);
                String content = String.format("系统[basic-terminal-adaptor]编辑SN:{%s} 原有SN:{%s}", sn, terminalChange.getSn());
                addTerminalPerson(terminalChange, content);
            }

        }
    }

    private  void addTerminalPerson(TerminalEntity terminal,String content){
        DictionaryEntity dictionary = dictionaryService.getById(terminal.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_EDIT.getCode());
        terminalPersonEntity.setUserNames("admin");
        terminalPersonEntity.setTerminalId(terminal.getId());
        terminalPersonEntity.setTerminalName(terminal.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminal.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminal.getSimNum());
        terminalPersonEntity.setOperateContent(content);
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
    }

    private DeviceInfoDTO queryDeviceInfo(String serialNo) {
        try {
            Map<String, Object> deviceInfoMap = hikVisionXmlRequestGet(hikVisionAdaptorDomain + HikVisionUriConstant.BASIC_INFO_URI, serialNo);
            return DeviceInfoDTO.fromMap(deviceInfoMap);
        } catch (Exception e) {
            log.warn("获取设备基本信息异常", e);
        }
        return null;
    }

    public void updateIpAddressAndSoftVersionForHik(String deviceId) {
        try {
            NetworkInfoDTO networkInfoMap = queryNetworkInfo(deviceId);
            DeviceInfoDTO deviceInfoMap = queryDeviceInfo(deviceId);
            String firmwareVersion = deviceInfoMap.getFirmwareVersion();
            String firmwareReleasedDate = deviceInfoMap.getFirmwareReleasedDate();
            String deviceVersion = String.format("%s %s", firmwareVersion, firmwareReleasedDate);
            String ipAddress = networkInfoMap.getNetworkInterface().getIpAddress().getIPAddress();
            log.info("ipAddress:{}", ipAddress);
            TerminalEntity terminal = terminalService.getTerminalByTerminalNum(deviceId);
            if(terminal == null){
                return;
            }
            if (!ipAddress.equals(terminal.getLastIp()) || !deviceVersion.equals(terminal.getSoftVersion())) {
                TerminalEntity updateTerminal = new TerminalEntity();
                updateTerminal.setIsLogin(GlobalConstants.TERMINAL_IS_LOGIN_ONLINE);
                updateTerminal.setLastIp(ipAddress);
                updateTerminal.setSoftVersion(deviceVersion);
                updateTerminal.setUpdateTime(LocalDateTime.now());
                updateTerminal.setLastTime(LocalDateTime.now());
                updateTerminal.setId(terminal.getId());
                com.alibaba.fastjson2.JSONObject json = updateTerminal.toJSON();
                redisUtil.setList(GlobalConstants.REDIS_KEY_ACTIVE_TERMINALS, Collections.singletonList(json.toJSONString()), 0);
                redisUtil.putHash(GlobalConstants.REDIS_KEY_ONLINE_TERMINALS, terminal.getId(), Timestamp.valueOf(updateTerminal.getLastTime()));
                if (!ipAddress.equals(terminal.getLastIp())) {
                    String content = String.format("系统[basic-terminal-adaptor]编辑LAST_IP:{%s} 原有LAST_IP:{%s}", ipAddress, terminal.getLastIp());
                    addTerminalPerson(terminal, content);
                }
                if (!deviceVersion.equals(terminal.getSoftVersion())) {
                    String content = String.format("系统[basic-terminal-adaptor]编辑SOFT_VERSION:{%s} 原有SOFT_VERSION:{%s}", deviceVersion, terminal.getSoftVersion());
                    addTerminalPerson(terminal, content);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("updateIpAddressAndSoftVersionForHik 异常 terminalNum {} ", deviceId);
        }

    }

    private NetworkInfoDTO queryNetworkInfo(String serialNo){
        try {
            Map<String, Object> networkMap = hikVisionXmlRequestGet(hikVisionAdaptorDomain + HikVisionUriConstant.NETWORK_INFO, serialNo);
            if (loggableTerminalNum.contains(serialNo)) {
                log.info("queryNetworkInfo 设备编号:{},networkMap, {}", serialNo, networkMap);
            }
            return new NetworkInfoDTO(networkMap);
        } catch (Exception e) {
            log.warn("获取设备网络基本信息异常", e);
        }
        return null;
    }

    public void configVerificationMode(String serialNo, Long schoolId) {
        TerminalEntity terminal = terminalService.getTerminalByTerminalNum(serialNo);
        if(terminal == null){
            return;
        }
        JSONObject param = hikNotPlayVoiceSchoolIdList.contains(schoolId) || hikNotPlayVoiceTerminalNumList.contains(serialNo) ? buildConfigLocationVerificationModeParam() : buildConfigVerificationModeParam( terminal,schoolId);
        try {
            hikVisionRequest(hikVisionAdaptorDomain + HikVisionUriConstant.CONFIG_VERIFICATION_MODE_URI,
                    Method.PUT, param.toString(), serialNo);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("hik-vision adaptor 请求异常 terminalNum {} request {}", serialNo, param);
        }

    }

    private JSONObject buildConfigVerificationModeParam(TerminalEntity terminal , Long schoolId) {
        JSONObject param = new JSONObject();

        JSONObject remoteAuthOption = new JSONObject();
        remoteAuthOption.set("remoteAuthAddress", basicTerminalAdaptorDomain+"/api/terminal-adaptor/terminal/hik/tts/device/remote-check");
        remoteAuthOption.set("enableLocal", 0);
        remoteAuthOption.set("enableRemote", 7);
        remoteAuthOption.set("enableQRSuccRemote", 0);
        remoteAuthOption.set("devDirection", 1);
        remoteAuthOption.set("connectTimeOut", 5);
        remoteAuthOption.set("uploadFaceType", 0);
        remoteAuthOption.set("enableUploadFace", 1);
        remoteAuthOption.set("enableCJmode", 0);

        JSONObject heartBeatOption = new JSONObject();
        heartBeatOption.set("enableHeartBeat", 1);
        heartBeatOption.set("heartBeatAddress", basicTerminalAdaptorDomain+"/api/terminal-adaptor/terminal/hik/tts/device/heartbeat");
        heartBeatOption.set("heartBeatInterval", 120);

        JSONObject doorControlOption = new JSONObject();
        List<Long> rulesTypeIdList = Arrays.asList(89L,110L);
        if (rulesTypeIdList.contains(terminal.getTypeId())) {
            doorControlOption.set("enableOfflineOpenDoor", hikRulesVoiceSchoolIdList.contains(schoolId) && !enableOfflineOpenDoorSchoolIdList.contains(schoolId) ? 0 : 1);
        } else {
            doorControlOption.set("enableOfflineOpenDoor", 1);
        }
        doorControlOption.set("enableOfflineDoorNormallyOpen", 0);

        JSONObject qrCodeOption = new JSONObject();
        qrCodeOption.set("enable485QRCode", 0);

        JSONObject localAuthPrompts = new JSONObject();
        localAuthPrompts.set("idAuthSucc", "");
        localAuthPrompts.set("idAuthFail", "");
        localAuthPrompts.set("faceAuthSucc", "");
        localAuthPrompts.set("faceAuthFail", "");
        localAuthPrompts.set("cardAuthSucc", "");
        localAuthPrompts.set("cardAuthFail", "");

        param.set("remoteAuthOption", remoteAuthOption);
        param.set("heartBeatOption", heartBeatOption);
        param.set("doorControlOption", doorControlOption);
        param.set("qrCodeOption", qrCodeOption);
        param.set("localAuthPrompts", localAuthPrompts);
        return param;
    }

    private JSONObject buildConfigLocationVerificationModeParam() {
        JSONObject param = new JSONObject();

        JSONObject remoteAuthOption = new JSONObject();
        remoteAuthOption.set("remoteAuthAddress", "");
        remoteAuthOption.set("enableLocal", 1);
        remoteAuthOption.set("enableRemote", 0);
        remoteAuthOption.set("enableQRSuccRemote", 0);
        remoteAuthOption.set("devDirection", 1);
        remoteAuthOption.set("connectTimeOut", 5);
        remoteAuthOption.set("uploadFaceType", 0);
        remoteAuthOption.set("enableUploadFace", 1);
        remoteAuthOption.set("enableCJmode", 0);

        JSONObject heartBeatOption = new JSONObject();
        heartBeatOption.set("enableHeartBeat", 0);
        heartBeatOption.set("heartBeatAddress", "");
        heartBeatOption.set("heartBeatInterval", 0);

        JSONObject doorControlOption = new JSONObject();
        doorControlOption.set("enableOfflineOpenDoor", 1);
        doorControlOption.set("enableOfflineDoorNormallyOpen", 0);

        JSONObject qrCodeOption = new JSONObject();
        qrCodeOption.set("enable485QRCode", 0);

        JSONObject localAuthPrompts = new JSONObject();
        localAuthPrompts.set("idAuthSucc", "");
        localAuthPrompts.set("idAuthFail", "");
        localAuthPrompts.set("faceAuthSucc", "");
        localAuthPrompts.set("faceAuthFail", "");
        localAuthPrompts.set("cardAuthSucc", "");
        localAuthPrompts.set("cardAuthFail", "");

        param.set("remoteAuthOption", remoteAuthOption);
        param.set("heartBeatOption", heartBeatOption);
        param.set("doorControlOption", doorControlOption);
        param.set("qrCodeOption", qrCodeOption);
        param.set("localAuthPrompts", localAuthPrompts);
        return param;
    }


    /**
     * [requestUrl, method, paramsJson, serialNo]
     * @return cn.hutool.json.JSONObject
     * @description 调用设备接口（get） xml
     * <AUTHOR>
     * @date 2024/5/14 15:42
     */
    private Map<String, Object> hikVisionXmlRequestGet(String requestUrl, String serialNo) {
        try {
            Map<String, String> headerMap = MapUtil.builder(new HashMap<String, String>())
                    .put("Deviceid", serialNo)
                    .put("Connection", "close")
                    .build();
            if (loggableTerminalNum.contains(serialNo)) {
                log.info("response hik-vision adaptor 请求接口:{},headerMap, {}", requestUrl, headerMap);
            }
            HttpRequest request = HttpUtil.createGet(requestUrl)
                    .headerMap(headerMap, true)
                    .contentType(ContentType.XML.toString())
                    .setConnectionTimeout(3 * 1000)
                    .setReadTimeout(10 * 1000);
            HttpResponse response = request.execute();
            if (response.isOk()) {
                if (loggableTerminalNum.contains(serialNo)) {
                    log.info("response hik-vision adaptor 请求接口:{},返回结果:{},设备号:{}", requestUrl, StrUtil.isNotBlank(response.body()) ? StrUtil.cleanBlank(response.body()) : null, serialNo);
                }
                return XmlUtil.xmlToMap(response.body());
            } else {
                log.warn("response hik-vision adaptor 异常 请求接口:{},返回结果:{},设备号:{}", requestUrl, StrUtil.isNotBlank(response.body()) ? StrUtil.cleanBlank(response.body()) : null, serialNo);
            }
        } catch (Exception e) {
            log.error("请求接口:{},异常:{}", requestUrl, e.getMessage(), e);
        }
        return null;
    }

    /**
     * [requestUrl, method, paramsJson, serialNo]
     * @return cn.hutool.json.JSONObject
     * @description 调用设备接口（post,put,delete） json
     * <AUTHOR>
     * @date 2024/5/14 15:42
     */
    private JSONObject hikVisionRequest(String requestUrl, Method method, String paramsJson,
                                        String serialNo) {
        try {
            Map<String, String> headerMap = MapUtil.builder(new HashMap<String, String>())
                    .put("Deviceid", serialNo)
                    .put("Connection", "close")
                    .build();
            if (loggableTerminalNum.contains(serialNo)) {
                log.info("response hik-vision adaptor 请求接口:{},请求参数:{},headerMap, {}", requestUrl, paramsJson, headerMap);
            }
            HttpRequest request = HttpUtil.createPost(requestUrl)
                    .headerMap(headerMap, true)
                    .method(method)
                    .body(paramsJson)
                    .contentType("application/json")
                    .setConnectionTimeout(3 * 1000)
                    .setReadTimeout(10 * 1000);
            HttpResponse response = request.execute();
            if (response.isOk()) {
                if (loggableTerminalNum.contains(serialNo)) {
                    log.info("response hik-vision adaptor 请求接口:{},请求参数:{},返回结果:{},设备号:{}", requestUrl, paramsJson, StrUtil.isNotBlank(response.body()) ? StrUtil.cleanBlank(response.body()) : null, serialNo);
                }
                boolean isValidJson = JSONUtil.isJson(response.body());
                if (isValidJson) {
                    return JSONUtil.parseObj(response.body(), jsonConfig);
                }
            } else {
                log.warn("response hik-vision adaptor 异常 请求接口:{},请求参数:{},返回结果:{},设备号:{}", requestUrl, paramsJson,
                        StrUtil.isNotBlank(response.body()) ? StrUtil.cleanBlank(response.body()) : null, serialNo);
            }
        } catch (Exception e) {
            log.error("请求接口:{},请求参数:{},异常:{}", requestUrl, paramsJson, e.getMessage(), e);
        }
        return null;
    }
}
