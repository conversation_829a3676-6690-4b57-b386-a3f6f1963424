package com.joinus.terminaladaptor.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.DirectTerminalDeployInfoEntity;
import com.joinus.dao.mapper.DirectTerminalDeployInfoMapper;
import com.joinus.terminaladaptor.service.IDirectTerminalDeployInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;


@Slf4j
@Service
public class DirectTerminalDeployInfoServiceImpl extends ServiceImpl<DirectTerminalDeployInfoMapper, DirectTerminalDeployInfoEntity> implements IDirectTerminalDeployInfoService {


    @Override
    public List<DirectTerminalDeployInfoEntity> getPushDirectTerminalDeployInfoBySchoolIdAndStudentId(Long schoolId, Long studentId) {
        Assert.isTrue(ObjectUtil.isNotEmpty(schoolId), "schoolId不能为空");
        Assert.isTrue(ObjectUtil.isNotEmpty(studentId), "studentId不能为空");
        LambdaQueryWrapper<DirectTerminalDeployInfoEntity> queryWrapper = Wrappers.lambdaQuery(DirectTerminalDeployInfoEntity.class);
        queryWrapper.eq(DirectTerminalDeployInfoEntity::getSchoolId, schoolId);
        queryWrapper.eq(DirectTerminalDeployInfoEntity::getStudentId, studentId);
        return this.baseMapper.selectList(queryWrapper);
    }
}
