package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.ClassEntity;
import com.joinus.dao.mapper.ClassMapper;
import com.joinus.terminaladaptor.service.IClassService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-18
 */
@Slf4j
@Service
public class ClassServiceImpl extends ServiceImpl<ClassMapper, ClassEntity> implements IClassService {
    @Override
    public ClassEntity getClassInfo(Long id, Long schoolId) {
        QueryWrapper<ClassEntity> queryClassWrapper = new QueryWrapper<>();
        queryClassWrapper.eq("id",id);
        queryClassWrapper.eq("isActive",1);
        queryClassWrapper.eq("SCHOOL_ID",schoolId);
        return getBaseMapper().selectOne(queryClassWrapper);
    }

}
