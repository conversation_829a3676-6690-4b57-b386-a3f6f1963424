package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.CameraCaptureRecordEntity;
import com.joinus.dao.TerminalEntity;
import com.joinus.dao.mapper.CameraCaptureRecordMapper;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.service.ICameraCaptureRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CameraCaptureRecordServiceImpl extends ServiceImpl<CameraCaptureRecordMapper, CameraCaptureRecordEntity>
        implements ICameraCaptureRecordService {


    @Override
    public List<CameraCaptureRecordEntity> getCameraCaptureRecordEntityListByTerminalNumAndSchoolIdCaptureBatchs(String terminalNum, Long schoolId , List<String> captureBatchList) {

        QueryWrapper<CameraCaptureRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("CAPTURE_BATCH", captureBatchList);
        queryWrapper.eq("TERMINAL_NUM", terminalNum);
        queryWrapper.eq("SCHOOL_ID", schoolId);
        return this.getBaseMapper().selectList(queryWrapper);
    }
}
