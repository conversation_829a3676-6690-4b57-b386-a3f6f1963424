package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.GradeEntity;
import com.joinus.dao.mapper.GradeMapper;
import com.joinus.terminaladaptor.service.IGradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29 15:44:42
 */
@Slf4j
@Service
public class GradeServiceImpl extends ServiceImpl<GradeMapper, GradeEntity> implements IGradeService {
    @Override
    public GradeEntity getGradeInfo(Long id, Long schoolId) {
        QueryWrapper<GradeEntity> queryGradeWrapper = new QueryWrapper<>();
        queryGradeWrapper.eq("id",id);
        queryGradeWrapper.eq("isActive",1);
        queryGradeWrapper.eq("SCHOOL_ID",schoolId);
        return getBaseMapper().selectOne(queryGradeWrapper);
    }
}
