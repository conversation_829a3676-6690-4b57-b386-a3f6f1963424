package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.CameraEntity;
import com.joinus.dao.mapper.CameraMapper;
import com.joinus.terminaladaptor.service.ICameraService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CameraServiceImpl extends BaseServiceImpl<CameraMapper, CameraEntity> implements ICameraService {

    @Override
    public List<CameraEntity> getCameraEntityNvrId(Long nvrId) {
        QueryWrapper<CameraEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("NVR_ID", nvrId);
        return this.getBaseMapper().selectList(queryWrapper);
    }
}
