package com.joinus.terminaladaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.model.enmus.TerminalTypeEnum;
import com.joinus.common.model.enums.ActiveEnum;
import com.joinus.dao.*;
import com.joinus.dao.mapper.TerminalMapper;
import com.joinus.terminaladaptor.bean.nvr.CaptureInfo;
import com.joinus.terminaladaptor.bean.nvr.HeartbeatInfo;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.enums.TerminalOperatorTypeEnum;
import com.joinus.terminaladaptor.kafka.KafkaProducer;
import com.joinus.terminaladaptor.service.*;
import com.joinus.terminaladaptor.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 终端信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28 17:17:50
 */
@Service
@Slf4j
public class TerminalServiceImpl extends ServiceImpl<TerminalMapper, TerminalEntity> implements ITerminalService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IDictionaryService dictionaryService;

    @Autowired
    private ITerminalPersonService terminalPersonServicel;

    @Autowired
    private ICameraCaptureRecordService cameraCaptureRecordService;

    @Autowired
    private ICameraService cameraService;

    @Autowired
    private ICameraNvrService cameraNvrService;

    @Value("${capture.max-time.difference:30}")
    private Integer captureMaxTimeDifference ;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Override
    public TerminalEntity getTerminalByTerminalNum(String terminalNum) {
        QueryWrapper<TerminalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("isactive", GlobalConstants.COMMON_ISACTIVE);
        queryWrapper.eq("terminal_num", terminalNum);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public TerminalEntity getTerminalByImei(String imei) {
        QueryWrapper<TerminalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("isactive", GlobalConstants.COMMON_ISACTIVE);
        queryWrapper.eq("imei", imei);
        return this.getBaseMapper().selectOne(queryWrapper);
    }


    @Override
    public TerminalEntity getTerminalByTerminalNumAndSchoolId(String terminalNum,Long schoolId) {
        QueryWrapper<TerminalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("isactive", GlobalConstants.COMMON_ISACTIVE);
        queryWrapper.eq("terminal_num", terminalNum);
        queryWrapper.eq("school_id", schoolId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public List<TerminalEntity> getTerminalBySN(String sn) {
        QueryWrapper<TerminalEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("SN", sn);
        queryWrapper.eq("ISACTIVE", GlobalConstants.COMMON_ISACTIVE);
        queryWrapper.eq("TERMINAL_TYPE",GlobalConstants.ALL_IN_ONE_TERMINAL_TYPE_ID);
        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public void nvrKeepAlive(Long schoolId, String terminalNum, List<HeartbeatInfo> heartbeatInfoList) {
        List<HeartbeatInfo> loginTerminalList = heartbeatInfoList.stream().filter(heartbeatInfo -> heartbeatInfo.getIsLogin().equals(GlobalConstants.TERMINAL_IS_LOGIN_ONLINE)).collect(Collectors.toList());
        log.info("上位机terminalNum:{}, 上送录像机个数:{},在线录像机个数:{}", terminalNum, heartbeatInfoList.size(), loginTerminalList.size());
        for (HeartbeatInfo heartbeatInfo : loginTerminalList) {
            try {
                TerminalEntity terminal = this.getTerminalByTerminalNumAndSchoolId(heartbeatInfo.getTerminalNum(), schoolId);
                if (null == terminal) {
                    log.warn("上位机terminalNum:{},通过录像机terminalNum:{}和学校ID:{},查询录像机信息不能为空!", terminalNum, heartbeatInfo.getTerminalNum(), schoolId);
                    continue;
                }
                TerminalEntity updateTerminal = new TerminalEntity();
                if (StrUtil.isNotBlank(terminal.getLastIp()) && !terminal.getLastIp().equals(heartbeatInfo.getIpAddress())) {
                    updateTerminal.setLastIp(heartbeatInfo.getIpAddress());
                    String content = String.format("系统[basic-terminal-adaptor]编辑LAST_IP:{%s} 原有LAST_IP:{%s}", heartbeatInfo.getIpAddress(), terminal.getLastIp());
                    addTerminalPerson(terminal, content);
                }
                if (StrUtil.isNotBlank(terminal.getSoftVersion()) && !terminal.getSoftVersion().equals(heartbeatInfo.getSoftVersion())) {
                    updateTerminal.setLastIp(heartbeatInfo.getIpAddress());
                    String content = String.format("系统[basic-terminal-adaptor]编辑SOFT_VERSION:{%s} 原有SOFT_VERSION:{%s}", heartbeatInfo.getSoftVersion(), terminal.getSoftVersion());
                    addTerminalPerson(terminal, content);
                }
                updateTerminal.setIsLogin(GlobalConstants.TERMINAL_IS_LOGIN_ONLINE);
                updateTerminal.setUpdateTime(LocalDateTime.now());
                updateTerminal.setLastTime(LocalDateTime.now());
                updateTerminal.setId(terminal.getId());
                com.alibaba.fastjson2.JSONObject json = updateTerminal.toJSON();
                redisUtil.setList(GlobalConstants.REDIS_KEY_ACTIVE_TERMINALS, Collections.singletonList(json.toJSONString()), 0);
                redisUtil.putHash(GlobalConstants.REDIS_KEY_ONLINE_TERMINALS, terminal.getId(), Timestamp.valueOf(updateTerminal.getLastTime()));
            } catch (Exception e) {
                log.warn("上位机terminalNum:{},录像机terminalNum:{},学校ID:{},处理心跳信息报错", terminalNum, heartbeatInfo.getTerminalNum(), schoolId, e);
            }

        }

    }

    @Override
    public void saveCapturedImg(Long schoolId, String terminalNum, List<CaptureInfo> captureInfoList) {
        List<CameraCaptureRecordEntity> cameraCaptureRecordList = new ArrayList<>();
        int captureInfoCount = captureInfoList.size();
        captureInfoList = captureInfoList.stream().filter(captureInfo -> StrUtil.isNotBlank(captureInfo.getCaptureBatch())).collect(Collectors.toList());
        log.info("录像机terminalNum:{},抓拍信息批次不为空的条数:{},总条数:{}", terminalNum, captureInfoList.size(), captureInfoCount);
        List<String> captureBatchList = captureInfoList.stream().map(CaptureInfo::getCaptureBatch).distinct().collect(Collectors.toList());
        log.info("录像机terminalNum:{},上传批次:{}", terminalNum, captureBatchList);
        try {

            CameraNvrEntity cameraNvr = cameraNvrService.getCameraNvrEntityByTerminalNumAndSchoolId(terminalNum, schoolId);
            Map<Integer, CameraEntity>  resutlMap= null;
            if (null == cameraNvr) {
                log.warn("录像机terminalNum:{},学校ID:{},未获取到录像机", terminalNum, schoolId);
            } else {
                List<CameraEntity> cameraList =  cameraService.getCameraEntityNvrId(cameraNvr.getId());
                resutlMap = cameraList.stream()
                        .collect(Collectors.toMap(
                                CameraEntity::getChannel,
                                record -> record,
                                (existingValue, newValue) -> existingValue
                        ));
            }

            List<CameraCaptureRecordEntity> dbCameraCaptureRecordList = cameraCaptureRecordService.getCameraCaptureRecordEntityListByTerminalNumAndSchoolIdCaptureBatchs(terminalNum, schoolId , captureBatchList);
            Map<String, String> resultMap = dbCameraCaptureRecordList.stream()
                    .collect(Collectors.toMap(
                            record -> record.getCaptureBatch() + ":" + record.getChannelNumber(),
                            CameraCaptureRecordEntity::getCaptureImgUrl,
                            (existingValue, newValue) -> existingValue
                    ));
            for (CaptureInfo captureInfo : captureInfoList) {
                String captureUrl = resultMap.get(captureInfo.getCaptureBatch() + ":" + captureInfo.getChannelNumber());
                if (StrUtil.isNotBlank(captureUrl)) {
                    log.warn("录像机terminalNum:{},captureInfo:{},该条抓拍记录已经上送,不在进行入库操作!", terminalNum, captureInfo);
                    continue;
                }
                CameraCaptureRecordEntity cameraCaptureRecordEntity = new CameraCaptureRecordEntity();
                cameraCaptureRecordEntity.setCaptureImgUrl(captureInfo.getCaptureImgUrl());
                cameraCaptureRecordEntity.setCaptureBatch(captureInfo.getCaptureBatch());
                cameraCaptureRecordEntity.setCapturedAt(captureInfo.getCapturedAt());
                cameraCaptureRecordEntity.setChannelNumber(captureInfo.getChannelNumber());
                cameraCaptureRecordEntity.setCreatedAt(new Date());
                cameraCaptureRecordEntity.setTerminalNum(terminalNum);
                cameraCaptureRecordEntity.setSchoolId(schoolId);
                if (null != resutlMap) {
                    CameraEntity camera = resutlMap.get(captureInfo.getChannelNumber());
                    if (ObjectUtil.isNotEmpty(camera)) {
                        cameraCaptureRecordEntity.setClassId(camera.getClassId());
                        cameraCaptureRecordEntity.setGradeId(camera.getGradId());
                    }
                }
                cameraCaptureRecordList.add(cameraCaptureRecordEntity);
            }
            log.info("录像机terminalNum:{},需要入库的抓拍记录条数:{}", terminalNum, cameraCaptureRecordList.size());
            cameraCaptureRecordService.saveBatch(cameraCaptureRecordList);

            List<CameraCaptureRecordEntity> sendKafkaList = cameraCaptureRecordList.stream()
                    .filter(cameraCaptureRecordEntity -> {
                        if (cameraCaptureRecordEntity.getClassId() == null) {
                            return false;
                        }
                        DateTime dateTime = DateUtil.parse(cameraCaptureRecordEntity.getCaptureBatch(), "yyyyMMddHHmm");
                        long between = DateUtil.between(dateTime, DateUtil.date(), DateUnit.MINUTE);
                        return Math.abs(between) <= captureMaxTimeDifference;
                    })
                    .collect(Collectors.toList());
            log.info("录像机terminalNum:{},需要发送kafka抓拍记录条数:{}", terminalNum, sendKafkaList.size());
            if (CollUtil.isNotEmpty(sendKafkaList)) {
                kafkaProducer.sendMessage(JSONUtil.toJsonStr(sendKafkaList));
            }
        } catch (Exception e) {
            log.warn("录像机terminalNum:{},学校ID:{},处理抓拍信息报错", terminalNum, schoolId, e);
        }

    }


    @Override
    public List<TerminalEntity> listTerminalsBySchoolId(Long schoolId) {
        LambdaQueryWrapper<TerminalEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TerminalEntity::getIsActive, ActiveEnum.VALID);
        wrapper.eq(TerminalEntity::getSchoolId, schoolId);
        wrapper.in(TerminalEntity::getTerminalType, Arrays.asList(TerminalTypeEnum.UPPER_COMPUTER_TYPE_ID.getTerminalType(), TerminalTypeEnum.NO_UPPER_COMPUTER_MACHINE.getTerminalType()));
        return this.list(wrapper);
    }

    @Override
    public List<TerminalEntity> getFaceTerminalNumByIds(List<Long> terminalIdList) {
        return this.baseMapper.getFaceTerminalNumByIds(terminalIdList);
    }

    @Override
    public List<TerminalEntity> getHikNoUpperComputerTerminal(Long schoolId) {
        LambdaQueryWrapper<TerminalEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TerminalEntity::getIsActive, ActiveEnum.VALID);
        wrapper.eq(TerminalEntity::getIsLogin, ActiveEnum.VALID);
        if (schoolId != null) {
            wrapper.eq(TerminalEntity::getSchoolId, schoolId);
        }
        wrapper.eq(TerminalEntity::getTerminalType, TerminalTypeEnum.NO_UPPER_COMPUTER_MACHINE.getTerminalType());
        wrapper.isNotNull(TerminalEntity::getSn);
        wrapper.orderByAsc(TerminalEntity::getSchoolId);
        return this.list(wrapper);
    }


    private  void addTerminalPerson(TerminalEntity terminal,String content){
        DictionaryEntity dictionary = dictionaryService.getById(terminal.getTypeId());
        TerminalPersonEntity terminalPersonEntity = new TerminalPersonEntity();
        terminalPersonEntity.setOperateType(TerminalOperatorTypeEnum.TYPE_EDIT.getCode());
        terminalPersonEntity.setUserNames("admin");
        terminalPersonEntity.setTerminalId(terminal.getId());
        terminalPersonEntity.setTerminalName(terminal.getTerminalName());
        terminalPersonEntity.setTerminalTypeName(dictionary != null ? (dictionary.getDictName() + "[" + dictionary.getDictDesc() + "]") : "");
        terminalPersonEntity.setTerminalNum(terminal.getTerminalNum());
        terminalPersonEntity.setTerminalSimNum(terminal.getSimNum());
        terminalPersonEntity.setOperateContent(content);
        terminalPersonEntity.setOperateTime(new Date());
        terminalPersonServicel.save(terminalPersonEntity);
    }
}
