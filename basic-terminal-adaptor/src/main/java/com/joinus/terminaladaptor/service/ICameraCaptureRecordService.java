package com.joinus.terminaladaptor.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.CameraCaptureRecordEntity;
import com.joinus.dao.DictionaryEntity;

import java.util.List;

public interface ICameraCaptureRecordService extends IService<CameraCaptureRecordEntity> {

    List<CameraCaptureRecordEntity> getCameraCaptureRecordEntityListByTerminalNumAndSchoolIdCaptureBatchs(String terminalNum, Long schoolId, List<String> captureBatchList);
}
