package com.joinus.terminaladaptor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.TerminalEntity;
import com.joinus.terminaladaptor.bean.nvr.CaptureInfo;
import com.joinus.terminaladaptor.bean.nvr.HeartbeatInfo;

import java.util.List;


/**
 * <p>
 * 终端信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28 17:17:53
 */
public interface ITerminalService extends IService<TerminalEntity> {

    TerminalEntity getTerminalByTerminalNum(String terminalNum);

    TerminalEntity getTerminalByImei(String imei);

    List<TerminalEntity> getTerminalBySN(String sn);


    TerminalEntity getTerminalByTerminalNumAndSchoolId(String terminalNum,Long schoolId);


    void nvrKeepAlive(Long schoolId, String terminalNum, List<HeartbeatInfo> heartbeatInfoList);


    void saveCapturedImg(Long schoolId, String terminalNum, List<CaptureInfo> captureInfoList);


    List<TerminalEntity> listTerminalsBySchoolId(Long schoolId);



    List<TerminalEntity>   getFaceTerminalNumByIds(List<Long> terminalIdList);

    List<TerminalEntity> getHikNoUpperComputerTerminal(Long schoolId);

}
