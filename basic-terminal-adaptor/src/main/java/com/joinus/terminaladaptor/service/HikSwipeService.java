package com.joinus.terminaladaptor.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.dao.*;
import com.joinus.dao.enums.StudentDormEnum;
import com.joinus.dao.leave.LeaveEntity;
import com.joinus.terminaladaptor.bean.CardSignInRecord;
import com.joinus.terminaladaptor.bean.hik.HikTtsRemoteCheck;
import com.joinus.terminaladaptor.bean.hik.PlayVoiceMsg;
import com.joinus.terminaladaptor.bean.hik.SignInMsg;
import com.joinus.terminaladaptor.bean.hik.StudentInOutTimeRuleDataVo;
import com.joinus.terminaladaptor.constant.GlobalConstants;
import com.joinus.terminaladaptor.util.LocalDateTimeUtils;
import com.joinus.terminaladaptor.util.QiniuUtil;
import com.joinus.terminaladaptor.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @description 刷卡信息服务类
 * @date 2024-11-29 09:09:25
 */
@Slf4j
@Service
public class HikSwipeService {

    @Value("#{'${voice.notifications.teacher.grade-class.name.list:教师,教职工}'.split(',')}")
    private List<String> teacherOperationsList;

    @Value("${server.domain.hik.vision.adaptor.img:http://***********:6011}")
    private String hikImgAdaptorDomain;

    @Value("${qyl.server.domain.name:https://qyl.uat.fangxiao.top}")
    private String qylDomainName;

    @Value("${loggable.sn:FK3877952,FK3877945}")
    private String loggableSn;

    @Value("${qiniu.oss.domain.url:https://capture.fangxiao.top/}")
     private String imgDomainName;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IStudentService studentService;

    @Autowired
    private ITerminalService terminalService;

    @Autowired
    private IClassService classService;

    @Autowired
    private IGradeService gradeService;

    @Autowired
    private ISchoolService schoolService;

    @Autowired
    private IVsLeaveService vsLeaveService;

    @Value("#{'${hik.rules.voice.schoolId.list:190}'.split(',')}")
    private List<Long> hikRulesVoiceSchoolIdList;

    @Value("${fangxiao.server.domain.name:https://visiting-schools.uat.ijiaxiao.net}")
    private String fangxiaoServerDomainName;

    @Value("#{'${voice.notifications.student.schoolId.list:190}'.split(',')}")
    private List<Long> studentOperationsList;

    public CardSignInRecord buildCardSignInRecordFromHik(SignInMsg signInMsg, TerminalEntity terminal, Integer signType){
        CardSignInRecord cardSignInRecord = CardSignInRecord.builder().build();
        String cardCode;
        String studentId;
        Long schoolId;
        int direction = GlobalConstants.TERMINAL_DIRECTION.UNKOWN;
        if (terminal.getDirection() != null) {
            direction = terminal.getDirection();
        }
        if (StrUtil.isNotBlank(signInMsg.getEmployeeNoString())) {
            StudentEntity student = studentService.getById(signInMsg.getEmployeeNoString());
            if (null == student) {
                log.info("未找到学生信息：{}", signInMsg.getEmployeeNoString());
                return null;
            }
            HikTtsRemoteCheck remoteCheck =new HikTtsRemoteCheck();
            remoteCheck.setCardTime(DateUtil.toLocalDateTime(signInMsg.getTime()).toLocalTime());
            remoteCheck.setSn(terminal.getSn());
            if(hikRulesVoiceSchoolIdList.contains(student.getSchoolId())){
                Map<String, String> resultVoiceMap = this.buildVoiceResponse(student, remoteCheck, terminal);
               if (GlobalConstants.FAILED.equals(resultVoiceMap.get("checkResult"))) {
                   direction = terminal.getDirection() == GlobalConstants.TERMINAL_DIRECTION.OUT ? GlobalConstants.TERMINAL_DIRECTION.NO_OUT :
                           terminal.getDirection() == GlobalConstants.TERMINAL_DIRECTION.IN ? GlobalConstants.TERMINAL_DIRECTION.NO_IN :
                                   terminal.getDirection() == GlobalConstants.TERMINAL_DIRECTION.UNKOWN ? GlobalConstants.TERMINAL_DIRECTION.NO_SIGN_IN : direction;
                }
            }
            studentId = student.getId().toString();
            schoolId = student.getSchoolId();
            cardCode = student.getCardCode();
        } else {
            studentId = UUID.randomUUID().toString();
            cardCode = signInMsg.getCardNo();
            schoolId = terminal.getSchoolId();
        }


        cardSignInRecord.setDirection(direction);
        cardSignInRecord.setCardNum(StrUtil.isNotBlank(cardCode) ? cardCode : GlobalConstants.STRANGER_CARD_NUM);
        cardSignInRecord.setCardTime(signInMsg.getTime());
        cardSignInRecord.setSignType(signType);
        cardSignInRecord.setSchoolId(schoolId);

        if (GlobalConstants.SIGN_TYPE.FACE == signType) {
            if (StrUtil.isNotBlank(signInMsg.getPicDataUrl())) {
                String imgUrl = hikImgAdaptorDomain + signInMsg.getPicDataUrl();
                InputStream inputStream = null;
                try {
                    URL url = new URL(imgUrl);
                    inputStream = url.openStream();
                    // key规则 /学校id/日期（yyyyMMdd）/timestamp(13位)_学生id.jpg
                    String key =
                            terminal.getSchoolId() + "/" + DateUtil.format(new Date(), "yyyyMMdd") + "/" + System.currentTimeMillis() + "_" + studentId + ".jpg";
                    String token = QiniuUtil.getQiniuToken();
                    if (StrUtil.isBlank(token)) {
                        log.warn("获取七牛云token 失败:{},img:{}", signInMsg.getEmployeeNoString(), imgUrl);
                    }
                    String imageUrl = QiniuUtil.uploadFile(new ByteArrayInputStream(IoUtil.readBytes(inputStream)), token, key);
                    QiniuUtil.deleteAfterDays(key, GlobalConstants.QINIU_FILE_EXPIRE_DAYS);
                    cardSignInRecord.setImageUrl(imgDomainName+imageUrl);
                } catch (Exception e) {
                    log.error("获取海康刷脸图片异常：{}", imgUrl, e);
                } finally {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            log.error("关闭输入流时发生异常", e);
                        }
                    }
                }
            }
        } else if (GlobalConstants.SIGN_TYPE.CARD == signType) {
            return cardSignInRecord;
        } else {
            log.error("无法判断刷卡类型：{}", signInMsg);
            return null;
        }
        return cardSignInRecord;
    }



    public void saveAccessRecord(CardSignInRecord cardSignInRecord, TerminalEntity terminal) {
        String uuid = UUID.randomUUID().toString();
        String checkKey = String.format("Record_%s_%s_%d", terminal.getTerminalNum(), cardSignInRecord.getCardNum(), cardSignInRecord.getCardTime().getTime());
        if (redisUtil.hasKey(checkKey)){
            log.warn(uuid + "重复上传{},{}", terminal.getImei(), cardSignInRecord.toString());
            redisUtil.set(checkKey, "1", 7200);
            return;
        }
        redisUtil.set(checkKey, "1", 7200);
        List<String> records = new ArrayList<>();
        cn.hutool.json.JSONObject json = new cn.hutool.json.JSONObject();
        json.set("@class", "com.joinus.edc.entity.SignIn");
        json.set("id", cardSignInRecord.getId());
        json.set("terminalNum", terminal.getTerminalNum());
        //这里的typeId对应t_terminal表的type_id,而非termianl_type
        json.set("typeId", terminal.getTypeId());
        json.set("schoolId", terminal.getSchoolId());
        json.set("terminalType", terminal.getTypeName());
        json.set("cardNum", cardSignInRecord.getCardNum());
        json.set("direction", cardSignInRecord.getDirection());
        json.set("thermometer", cardSignInRecord.getThermometer());
        json.set("imageUrl", cardSignInRecord.getImageUrl());
        json.set("latitude", cardSignInRecord.getLatitude());
        json.set("longitude", cardSignInRecord.getLongitude());
        json.set("address", cardSignInRecord.getAddress());
        json.set("signType", cardSignInRecord.getSignType());
        cn.hutool.json.JSONArray array = new cn.hutool.json.JSONArray();
        array.add("java.util.Date");
        array.add(cardSignInRecord.getCardTime().getTime());
        json.set("cardTime", array);
        records.add(json.toString());
        log.info(uuid + "考勤信息:"+records.toString());
        long size = redisUtil.setList("CARD_SIGNIN_RECORD", records, 0);
    }

    public JSONObject remoteCheckCardSignInRecordFromHik(HikTtsRemoteCheck remoteCheck) {
        JSONObject resultJson = new JSONObject();
        JSONObject remoteCheckJson = new JSONObject();
        JSONObject info = new JSONObject();
        PlayVoiceMsg playVoiceMsg= new PlayVoiceMsg();
        try {
            List<TerminalEntity> terminalList = terminalService.getTerminalBySN(remoteCheck.getSn());
            if (CollUtil.isNotEmpty(terminalList)) {
                TerminalEntity terminal = terminalList.get(0);
                Map<String, Object> resultMap = buildRemoteCheckCardSignInRecordFromHik(remoteCheck, terminal);
                if (loggableSn.contains(remoteCheck.getSn())) {
                    log.info("组装刷卡记录参数结束,SN:{}", remoteCheck.getSn());
                }
                if (null != resultMap) {
                    String errMsg = (String) resultMap.get("errMsg");
                    if (StrUtil.isNotBlank(errMsg)) {
                        playVoiceMsg.setPrompts(errMsg);
                    } else {
                        CardSignInRecord cardSignInRecord = (CardSignInRecord) resultMap.get("cardSignInRecord");
                        if (null != cardSignInRecord) {
                            //插入刷卡记录
                            //   saveAccessRecord(cardSignInRecord, terminal);
                            if (GlobalConstants.STRANGER_CARD_NUM.equals(cardSignInRecord.getCardNum())) {
                                playVoiceMsg.setPrompts("人脸识别失败");
                            } else {
                                playVoiceMsg.setPrompts("识别成功");
                                StudentEntity student = (StudentEntity) resultMap.get("student");
                                if (null != student) {
                                    SchoolEntity school=schoolService.getById(terminal.getSchoolId());
                                    //配餐学校
                                    if(Convert.toStr(school.getSchoolPay()).contains("2") && terminal.getTypeId()==GlobalConstants.CANTTEN_AREA_TERMINAL_TYPE_ID ){
                                        //获取配餐语音播报
                                        PlayVoiceMsg groupMealPlayVoiceMsg = getGroupMealPlayVoice(student.getId());
                                        playVoiceMsg.setPrompts(groupMealPlayVoiceMsg.getPrompts());
                                        playVoiceMsg.setCheckResult(groupMealPlayVoiceMsg.getCheckResult());
                                    }else {
                                        Map<String, String>  resultVoiceMap = this.buildVoiceResponse(student, remoteCheck,terminal);
                                        playVoiceMsg.setPrompts(resultVoiceMap.get("msg"));
                                        playVoiceMsg.setCheckResult(resultVoiceMap.get("checkResult"));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("hik tts语音提示出错:" + remoteCheck.toString() + ",错误信息:" + e);
        } finally {
            info.set("prompts", playVoiceMsg.getPrompts());
            info.set("encodeType", "");
            remoteCheckJson.set("serialNo", remoteCheck.getSerialNo());
            remoteCheckJson.set("info", info);
            remoteCheckJson.set("checkResult", playVoiceMsg.getCheckResult());
            resultJson.set("RemoteCheck", remoteCheckJson);
        }

        return resultJson;
    }


    private Map<String, String> buildVoiceResponse(StudentEntity student, HikTtsRemoteCheck remoteCheck, TerminalEntity terminal) {
        Map<String, String> resultMap = new HashMap<>();
        String playStudentLeaveVoice = checkStudentLeave(student, remoteCheck);
        if (StrUtil.isNotBlank(playStudentLeaveVoice)) {
            log.info("播放学生请假信息 id:{},SN:{}", student.getId(), remoteCheck.getSn());
            resultMap.put("msg", playStudentLeaveVoice);
            resultMap.put("checkResult", GlobalConstants.SUCCESS);
            return resultMap;
        }
        boolean isInRulesVoiceSchool = hikRulesVoiceSchoolIdList.contains(student.getSchoolId());
        List<Long> rulesTypeIdList = Arrays.asList(89L,110L);
        if (isInRulesVoiceSchool && rulesTypeIdList.contains(terminal.getTypeId())) {
            // 获取学生是否包含允许时段
            boolean isStudentAccordRule = checkStudentAccordRule(student, remoteCheck, terminal);
            if (!isStudentAccordRule) {
                resultMap.put("msg", "未在允许时段");
                resultMap.put("checkResult", GlobalConstants.FAILED);
                return resultMap;
            }
        }
        String playVoice = receivePlayVoice(student, terminal);
        resultMap.put("msg", playVoice);
        resultMap.put("checkResult", GlobalConstants.SUCCESS);
        return resultMap;
    }

    public String  checkStudentLeave(StudentEntity student, HikTtsRemoteCheck remoteCheck) {
        //如果学生请假发送请假播放
        if (StrUtil.isNotBlank(remoteCheck.getSn()) && loggableSn.contains(remoteCheck.getSn())) {
            log.info("开始查询学生请假信息 id:{},SN:{}", student.getId(), remoteCheck.getSn());
        }
        List<LeaveEntity> vsLeaveList = vsLeaveService.studentOnLeaveBystudentId(student.getId());
        if (ObjectUtil.isNotEmpty(vsLeaveList)) {
            log.info("学生请假信息:{},SN:{}", vsLeaveList, remoteCheck.getSn());
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime leaveStartTime;
            LocalDateTime leaveEndTime;
            for (LeaveEntity vsLeave : vsLeaveList) {
                // 是否循环请假：0、否，1、是
                if (0 == vsLeave.getIfIsCycle()) {
                    //请假时长的处理
                    String stateDate = DateUtil.format(vsLeave.getStartDate(), LocalDateTimeUtils.YYYY_MM_DD_EN);
                    String endDate = DateUtil.format(vsLeave.getEndDate(), LocalDateTimeUtils.YYYY_MM_DD_EN);
                    leaveStartTime = LocalDateTimeUtils.getInstance(stateDate + " " + vsLeave.getStartTime() + ":00");
                    leaveEndTime = LocalDateTimeUtils.getInstance(endDate + " " + vsLeave.getEndTime() + ":00");
                } else {
                    String currentDate = LocalDateTimeUtils.format(now, LocalDateTimeUtils.YYYY_MM_DD_EN);
                    leaveStartTime = LocalDateTimeUtils.getInstance(currentDate + " " + vsLeave.getStartTime() + ":00");
                    leaveEndTime = LocalDateTimeUtils.getInstance(currentDate + " " + vsLeave.getEndTime() + ":00");
                }
                if (LocalDateTimeUtils.isBetween(now, leaveStartTime, leaveEndTime)) {
                    return "学生已请假";
                }
            }
        }
        if (StrUtil.isNotBlank(remoteCheck.getSn()) && loggableSn.contains(remoteCheck.getSn())) {
            log.info("学生请假信息判断已结束 id:{},SN:{}", student.getId(), remoteCheck.getSn());
        }
        return  null;
    }

    private boolean checkStudentAccordRule(StudentEntity student, HikTtsRemoteCheck remoteCheck,  TerminalEntity terminal) {
        try {
            HttpResponse httpResponse = HttpUtil.createGet(fangxiaoServerDomainName + "/api/anon/student/in-out-time/rule/" + student.getSchoolId() + " ?terminalNum=" + terminal.getTerminalNum())
                    .timeout(5000)
                    .execute();

            if (null != httpResponse && httpResponse.isOk()) {
                Map<String, Object> map = (Map) JSONUtil.parseObj(httpResponse.body()).get("data");
                if (loggableSn.contains(remoteCheck.getSn())) {
                    log.info("开始查询设备请假时段信息:{} terminalNum:{}, 学生id:{},SN:{}", map, terminal.getTerminalNum(), student.getId(), remoteCheck.getSn());
                }
                List<StudentInOutTimeRuleDataVo> rules;
                if (student.getIsDorm() == StudentDormEnum.DORM) {
                    JSONArray zsRulesJsonArray= (JSONArray) map.get("zsRules");
                    rules = JSONUtil.toList(
                            JSONUtil.parseArray(zsRulesJsonArray),
                            StudentInOutTimeRuleDataVo.class
                    );
                }else {
                    JSONArray zdRulesJsonArray= (JSONArray) map.get("zdRules");
                    rules = JSONUtil.toList(
                            JSONUtil.parseArray(zdRulesJsonArray),
                            StudentInOutTimeRuleDataVo.class
                    );
                }
                List<StudentInOutTimeRuleDataVo> studentRuleList = rules.stream().filter(studentInOutTimeRuleDataVo -> student.getClassId().equals(studentInOutTimeRuleDataVo.getClassId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(studentRuleList)) {
                    return false;
                }
                // 获取该学生进出校规则  timeScope 00:00-01:00,01:01-02:00    inOutType  0,1   0 进 1 出 2 可进可出
                StudentInOutTimeRuleDataVo studentInOutTimeRuleDataVo = studentRuleList.get(0);
                String inOutType = studentInOutTimeRuleDataVo.getInOutType();
                String[] inOutTypeArr = inOutType.split(",");
                String  timeScope = studentInOutTimeRuleDataVo.getTimeScope();
                String[] timeScopeArr = timeScope.split(";");
                List<String> timeScopeList =  new ArrayList<>();
                // 0 出  1 进   5 无方向
                Integer terminalDirection = terminal.getDirection();
                IntStream.range(0, Math.min(inOutTypeArr.length, timeScopeArr.length))
                        .filter(i -> {
                            int type = Convert.toInt(inOutTypeArr[i]);
                            return (terminalDirection == 1 && (type == 0 || type == 2)) ||
                                    (terminalDirection == 0 && (type == 1 || type == 2)) ||
                                    (terminalDirection == 5);
                        })
                        .forEach(i -> timeScopeList.add(timeScopeArr[i]));
                if (loggableSn.contains(remoteCheck.getSn())) {
                    log.info("开始查询设备请假时段匹配后进出方向信息:{} terminalNum:{}, 学生id:{},SN:{}", timeScopeList, terminal.getTerminalNum(), student.getId(), remoteCheck.getSn());
                }
                if (CollUtil.isEmpty(timeScopeList)) {
                    return false;
                }
                // 验证进出校时间
                if (ObjectUtil.isEmpty(remoteCheck.getCardTime())) {
                    remoteCheck.setCardTime(LocalTime.now());
                }
                if (isInTimeScope(timeScopeList, remoteCheck.getCardTime())) {
                    return true;
                }
            }
        }catch (Exception e) {
            log.warn("调用访校获取规则出错,studentId:{},错误原因:{}", student.getId(), e.getMessage(), e);
        }
        return false;
    }

    private boolean isInTimeScope(List<String> timeScopeList, LocalTime cardTime) {
        for (String timeScope : timeScopeList) {
            String[] times = timeScope.split("-");
            if (times.length != 2) {
                continue;
            }
            LocalTime start = LocalTime.parse(times[0].trim().replace('：', ':'));
            LocalTime end = LocalTime.parse(times[1].trim().replace('：', ':'));
            // 新增验证：开始时间必须小于结束时间
            if (start.isAfter(end)) {
                log.warn("非法时间段配置（跨天时段）：{}，当前系统不支持跨天配置", timeScope);
                continue;
            }
            if (!cardTime.isBefore(start) && !cardTime.isAfter(end)) {
                return true;
            }
        }
        return false;
    }

    public Map<String,Object> buildRemoteCheckCardSignInRecordFromHik(HikTtsRemoteCheck remoteCheck, TerminalEntity terminal) {
        Map<String, Object> resultMap = new HashMap<>();
        CardSignInRecord cardSignInRecord = CardSignInRecord.builder().build();
        int direction = GlobalConstants.TERMINAL_DIRECTION.UNKOWN;
        if (terminal.getDirection() != null) {
            direction = terminal.getDirection();
        }
        StudentEntity student = null;
        if (GlobalConstants.VERIFY_TYPE.CARD.equals(remoteCheck.getVerifyType())) {
            cardSignInRecord.setSignType(GlobalConstants.SIGN_TYPE.CARD);
            if (StrUtil.isBlank(remoteCheck.getCardNo())) {
                log.warn("刷卡认证学生卡号不能为空：{}", remoteCheck);
                return null;
            }
            if(StrUtil.isBlank(remoteCheck.getUserNo())){
                resultMap.put("errMsg","无权限");
                return resultMap;
            }

            student = studentService.getById(remoteCheck.getUserNo());
            if (null == student) {
                log.warn("未找到学生信息：{}", remoteCheck);
                resultMap.put("errMsg", "学校不存在该卡号人员");
                return resultMap;
            }
            cardSignInRecord.setSchoolId(student.getSchoolId());
            cardSignInRecord.setCardNum(student.getCardCode());
        } else if (GlobalConstants.VERIFY_TYPE.FACE.equals(remoteCheck.getVerifyType())) {
            String cardCode;
            Long schoolId;
            cardSignInRecord.setSignType(GlobalConstants.SIGN_TYPE.FACE);
            if (StrUtil.isBlank(remoteCheck.getUserNo())) {
                cardCode = GlobalConstants.STRANGER_CARD_NUM;
                schoolId = terminal.getSchoolId();
            } else {
                student = studentService.getById(remoteCheck.getUserNo());
                if (null == student) {
                    cardCode = GlobalConstants.STRANGER_CARD_NUM;
                    schoolId = terminal.getSchoolId();
                } else {
                    schoolId = student.getSchoolId();
                    cardCode = student.getCardCode();
                }
            }
            cardSignInRecord.setSchoolId(schoolId);
            cardSignInRecord.setCardNum(cardCode);
            if (StrUtil.isBlank(remoteCheck.getPicture())) {
                log.warn("刷脸认证照片不能为空：{}", remoteCheck);
                return null;
            }

        } else {
            log.warn("暂时不支持的认证方式：{}", remoteCheck);
            return null;
        }
        cardSignInRecord.setDirection(direction);
        cardSignInRecord.setCardTime(new Date());
        resultMap.put("cardSignInRecord", cardSignInRecord);
        resultMap.put("student", student);
        return resultMap;
    }

    private PlayVoiceMsg getGroupMealPlayVoice(Long studentId) {
        PlayVoiceMsg groupMealPlayVoiceMsg=new PlayVoiceMsg();
        groupMealPlayVoiceMsg.setPrompts("网络异常");
        try {
            log.info("调用青于蓝获取配餐语音播报接口,studentId:{}", studentId);
            HttpResponse httpResponse = cn.hutool.http.HttpUtil.createGet(qylDomainName + "/cater/student/checkMealState?studentId=" + studentId)
                    .timeout(5000)
                    .execute();
            if (null != httpResponse && httpResponse.isOk()) {
                Map map = (Map) JSONUtil.parseObj(httpResponse.body()).get("data");
                groupMealPlayVoiceMsg.setCheckResult(Convert.toStr(map.get("mealStatus")));
                groupMealPlayVoiceMsg.setPrompts(Convert.toStr(map.get("mealTips")));
            }
        } catch (Exception e) {
            log.warn("调用青于蓝出错,studentId:{}", studentId, e);
        }
        log.info("调用青于蓝获取配餐语音播报接口返回,studentId:{},groupMealPlayVoiceMsg:{}", studentId, groupMealPlayVoiceMsg);
        return groupMealPlayVoiceMsg;
    }

    public String receivePlayVoice(StudentEntity student,  TerminalEntity terminal) {
        ClassEntity classInfo = classService.getClassInfo(student.getClassId(), student.getSchoolId());
        GradeEntity gradeInfo = gradeService.getGradeInfo(classInfo.getGradeId(), student.getSchoolId());
        if (studentOperationsList.contains(student.getSchoolId())) {
            log.info("洛阳理工附属中学语音播报,StudentId:" + student.getId());
            String identity = gradeInfo.isTeacher(teacherOperationsList) ?
                    (classInfo.isTeacher(teacherOperationsList) ? "老师" : "工作人员") : "同学";
            String directionMsg;
            switch (terminal.getDirection()) {
                case 1:
                    directionMsg = "同学".equals(identity) ? "好，您已平安返校" : "好";
                    break;
                case 0:
                    directionMsg = "同学".equals(identity) ? "好，您已平安离校" : "辛苦了";
                    break;
                default:
                    directionMsg = "好";
            }
            return identity + directionMsg;
        } else {
            if (classInfo.isTeacher(teacherOperationsList) || gradeInfo.isTeacher(teacherOperationsList)) {
                log.info("教师 教职工语音播报,StudentId:" + student.getId());
                return "老师好";
            } else {
                log.info("走读生 住宿生语音播报,StudentId:" + student.getId());
                //播放走读生还是住宿生
                if (student.getIsDorm().equals(StudentDormEnum.DORM)) {
                    return "住宿生";
                } else {
                    return "走读生";
                }
            }
        }
    }
}
