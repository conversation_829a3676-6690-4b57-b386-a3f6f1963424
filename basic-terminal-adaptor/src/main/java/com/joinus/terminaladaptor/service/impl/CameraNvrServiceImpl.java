
package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.CameraNvrEntity;
import com.joinus.dao.mapper.CameraNvrMapper;
import com.joinus.terminaladaptor.service.ICameraNvrService;
import org.springframework.stereotype.Service;

@Service
public class CameraNvrServiceImpl extends BaseServiceImpl<CameraNvrMapper, CameraNvrEntity> implements ICameraNvrService {

    @Override
    public CameraNvrEntity getCameraNvrEntityByTerminalNumAndSchoolId(String terminalNum, Long schoolId) {
        QueryWrapper<CameraNvrEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("SCHOOL_ID", schoolId);
        queryWrapper.eq("TERMINAL_NUM", terminalNum);
        return this.getBaseMapper().selectOne(queryWrapper);
    }
}
