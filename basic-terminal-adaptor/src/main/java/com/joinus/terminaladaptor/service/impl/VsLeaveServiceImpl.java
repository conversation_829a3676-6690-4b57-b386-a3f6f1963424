package com.joinus.terminaladaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.dao.leave.LeaveEntity;
import com.joinus.dao.mapper.VsLeaveMapper;
import com.joinus.terminaladaptor.service.IVsLeaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 请假 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29 15:52:24
 */
@Slf4j
@Service
public class VsLeaveServiceImpl extends ServiceImpl<VsLeaveMapper, LeaveEntity> implements IVsLeaveService {

    @Override
    public List<LeaveEntity> studentOnLeaveBystudentId(Long studentId) {
        QueryWrapper<LeaveEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("STATUS", 1);
        queryWrapper.eq("LEAVEER_ID", studentId);
        return this.getBaseMapper().selectList(queryWrapper);
    }
}
