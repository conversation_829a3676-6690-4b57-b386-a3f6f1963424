package com.joinus.terminaladaptor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.dao.StudentEntity;
import com.joinus.dao.TerminalEntity;
import com.joinus.terminaladaptor.bean.IncrementStudentInfo;

import java.util.List;

/**
 * <p>
 * 学生表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29 09:26:55
 */
public interface IStudentService extends IService<StudentEntity> {


   void  pushStudentIncrement ( IncrementStudentInfo incrementStudentInfo);


   List<TerminalEntity> getTerminalListByStudent(StudentEntity student, Integer operType);

   StudentEntity getStudentInfoByCardCodeAndSchoolId(String cardCode, Long schoolId);

}
