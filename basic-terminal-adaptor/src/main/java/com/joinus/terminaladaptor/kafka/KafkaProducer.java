package com.joinus.terminaladaptor.kafka;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class KafkaProducer {

    private final KafkaTemplate<Object, Object> template;


    @Value("${kafka.topic.absence.capture-identify:public.absence.class.attendance.capture.identify}")
    public String topic;

    public KafkaProducer(KafkaTemplate<Object, Object> template) {
        this.template = template;
    }


    public void sendMessage( String message) {
        try {
            template.send(topic, message).get(5, TimeUnit.SECONDS);
            log.info("AbsenceCaptureIdentifyProducer成功发送了一条JMS消息:{}", message);
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("AbsenceCaptureIdentifyProducer发送失败,消息内容:{}", message);
        }
    }


}
