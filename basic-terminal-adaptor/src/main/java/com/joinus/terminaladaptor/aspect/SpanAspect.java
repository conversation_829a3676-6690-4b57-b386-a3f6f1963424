package com.joinus.terminaladaptor.aspect;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class SpanAspect {
    Tracer tracer = GlobalOpenTelemetry.getTracer("basic-terminal-adaptor");

    @Pointcut("@within(org.springframework.stereotype.Service)")
    public void servicePointCut() {
    }

    @Around("servicePointCut()")
    public Object withSpan(ProceedingJoinPoint joinPoint) throws Throwable {
        Span span = tracer.spanBuilder(joinPoint.getSignature().getName()).setParent(Context.current()).startSpan();
        span.setAttribute("class", joinPoint.getTarget().getClass().getName());
        span.setAttribute("method", joinPoint.getSignature().getName());
        /*span.setAttribute("args", Arrays.toString(joinPoint.getArgs()));*/
        try (Scope scope = Context.current().with(span).makeCurrent()) {
            Object result = joinPoint.proceed();
            /*if (ObjectUtil.isNotNull(result)) {
                span.setAttribute("result", result.toString());
            }*/
            return result;
        } catch (Throwable t) {
            span.setStatus(StatusCode.ERROR, t.getMessage());
            log.error("span error", t);
            throw t;
        } finally {
            span.end();
        }
    }
}