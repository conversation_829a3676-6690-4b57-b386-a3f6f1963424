package com.joinus.terminaladaptor.bean;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020/9/16
 */
@Data
@Builder
public class CardSignInRecord {
    private long id;
    private long schoolId;
    private String terminalType;
    private String terminalNum;
    private String cardNum;
    private Date cardTime;
    private float thermometer;
    private int direction;
    private Date saveTime;
    private String imageUrl;
    private String latitude;
    private String longitude;
    private String address;
    private int signType;
    private String subTerminalNum;
}
