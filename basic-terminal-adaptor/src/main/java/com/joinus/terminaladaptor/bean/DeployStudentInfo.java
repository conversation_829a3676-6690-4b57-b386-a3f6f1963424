package com.joinus.terminaladaptor.bean;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.joinus.dao.enums.*;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class DeployStudentInfo {

    private Long id;
    private String cardCode;/* 卡号 */
    private Integer isDorm;/* 是否住宿生，0 无效 1有效 2待定*/
    private Integer isGuaShi;/* 是否挂失 1 是 0 否 */
    private Long schoolId;/* 学校id */
    private Integer sex;/* 性别 */
    private String studentCode;/* 学生代码（学生编号） */
    private String studentImg;/* 学生肖像 */
    private String studentName;/* 学生姓名 */
    private Long classId;/* 班级id */
    private String className;/* 班级名称 */
    private Long gradeId;/* 年级id */
    private String gradeName;/* 年级名称 */
    private Integer isTime1;/* 时段1标志，0 无效 1有效 */
    private Integer isTime2;/* 时段2标志，0 无效 1有效 */
    private Integer isTime3;/* 时段3标志，0 无效 1有效 */



}
