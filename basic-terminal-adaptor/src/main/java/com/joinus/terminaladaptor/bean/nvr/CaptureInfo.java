package com.joinus.terminaladaptor.bean.nvr;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 抓拍信息
 * @date 2024-12-5 16:39:45
 */

@Data
public class CaptureInfo {
    private String captureImgUrl;
    private Integer channelNumber;
    private String captureBatch;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date capturedAt;


}
