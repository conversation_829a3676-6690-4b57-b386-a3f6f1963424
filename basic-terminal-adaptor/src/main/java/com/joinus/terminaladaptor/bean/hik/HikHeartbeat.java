package com.joinus.terminaladaptor.bean.hik;

import lombok.Data;

@Data
public class HikHeartbeat {

    //设备序列号
    private String sn;

    //设备名称
    private String devName;

    //设备 IP 地址
    private String ip;

    //设备 MAC 地址
    private String mac;

    //设备当前时间戳
    private String timestamp;


    @Override
    public String toString() {
        return "HikHeartbeat{" +
                "sn='" + sn + '\'' +
                ", devName='" + devName + '\'' +
                ", ip='" + ip + '\'' +
                ", mac='" + mac + '\'' +
                ", timestamp='" + timestamp + '\'' +
                '}';
    }
}
