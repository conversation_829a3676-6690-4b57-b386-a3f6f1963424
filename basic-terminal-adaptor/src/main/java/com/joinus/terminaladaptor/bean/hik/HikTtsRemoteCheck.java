package com.joinus.terminaladaptor.bean.hik;

import lombok.Data;

import java.time.LocalTime;

@Data
public class HikTtsRemoteCheck {

    //设备序列号
    private String sn;

    //设备名称
    private String devName;

    //设备 IP 地址
    private String ip;

    //设备 MAC 地址
    private String mac;

    //设备序列号
    private Integer serialNo;

    //人员编号，verifyType=2时，必填，且人员编号放在该字段上传
    private String userNo;

    //姓名
    private String name;

    //卡号 or 身份证号码。verifyType=1、3 时，必填，且卡号/身份证号放在该字段上传
    private String cardNo;

    //认证方式：1-刷卡 ，2-刷 脸，3-刷身份证，4-刷二 维码
    private Integer verifyType;

    //设备名称 进出方向：1-进 2-出
    private Integer direction;

    //抓拍照片，base64 方式字符串
    private String picture;

    //二维码字符串。verifyType=4 时，必填
    private String qrCode;


    //证件有效期  刷身份证必填 且二维码字符串放在该字段上传。
    private String periodValid;

    //住址  刷身份证必填
    private String address;

    //身份证照片，base64 编码 刷身份证必填
    private String IDPic;

    //测温型号必填，比如 36.4
    private String temperature;

    //测温型号必填体温状态，0-正常，1-高温异常，2-低温异常
    private Integer temperatureAbnormal;

    private LocalTime cardTime;

    @Override
    public String toString() {
        return "RemoteCheck{" +
                "sn='" + sn + '\'' +
                ", devName='" + devName + '\'' +
                ", ip='" + ip + '\'' +
                ", mac='" + mac + '\'' +
                ", serialNo=" + serialNo +
                ", userNo='" + userNo + '\'' +
                ", name='" + name + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", verifyType='" + verifyType + '\'' +
                ", direction=" + direction +
                ", qrCode='" + qrCode + '\'' +
                ", periodValid='" + periodValid + '\'' +
                ", address='" + address + '\'' +
                ", IDPic='" + IDPic + '\'' +
                ", temperature='" + temperature + '\'' +
                ", temperatureAbnormal=" + temperatureAbnormal +
                '}';
    }
}
