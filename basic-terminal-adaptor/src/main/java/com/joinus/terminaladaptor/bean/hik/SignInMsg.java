package com.joinus.terminaladaptor.bean.hik;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Data
@Slf4j
public class SignInMsg {

    private String currentEvent;

    private String frontSerialNo;

    @Alias("DeviceID")
    private String deviceID;

    @Alias("MajorType")
    private String majorType;

    @Alias("PurePwdVerifyEnable")
    private String purePwdVerifyEnable;

    @Alias("Time")
    private String time;

    private String serialNo;

    @Alias("DoorNo")
    private String doorNo;

    @Alias("MinorType")
    private String minorType;

    private String attendanceStatus;

    private String mask;

    @Alias("CardType")
    private String cardType;

    @Alias("CardNo")
    private String cardNo;


    private String employeeNoString;

    @Alias("PicDataUrl")
    private String picDataUrl;

    public Integer getMajorType() {
        return Integer.parseInt(majorType.substring(2), 16);
    }

    public Integer getMinorType() {
        return Integer.parseInt(minorType.substring(2), 16);
    }

    public Date getTime() {
        try {
          return DateUtil.parseDateTime(time);
        } catch (Exception e) {
            log.error("海康时间格式错误", e);
            return null;
        }
    }

}
