package com.joinus.terminaladaptor.bean.hik;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NetworkInfoDTO {
    private NetworkInterface NetworkInterface;

    public NetworkInfoDTO(Map<String, Object> map) {
        Object mapList = map.get("NetworkInterface");
        if (mapList instanceof Map) {
            this.NetworkInterface = new NetworkInterface((Map<String, Object>) map.get("NetworkInterface"));
        } else  if (mapList instanceof List) {
            // 处理 list 不是 Map 的情况
            List<NetworkInterface> listData = (List<NetworkInterface>) mapList;
            for (Object item : listData) {
                this.NetworkInterface = new NetworkInterface((Map<String, Object>) item);
                break;
            }
        }
    }

    public class NetworkInterface {
        private IPAddress IPAddress;

        public IPAddress getIpAddress() {
            return IPAddress;
        }

        public NetworkInterface(Map<String, Object> map) {
            this.IPAddress = new IPAddress((Map<String, Object>) map.get("IPAddress"));
        }
    }

    public class IPAddress {
        private String ipAddress;

        public String getIPAddress() {
            return ipAddress;
        }


        public void setIPAddress(Map<String, Object> data) {
            // 假设map中包含一个key为"ip"的条目，它的值是IP地址字符串
            this.ipAddress = (String) data.get("ipAddress");
        }

        public IPAddress(Map<String, Object> map) {
            this.ipAddress = (String) map.get("ipAddress");
        }
    }

    public NetworkInterface getNetworkInterface() {
        return NetworkInterface;
    }

    public void setNetworkInterface(NetworkInterface NetworkInterface) {
        this.NetworkInterface = NetworkInterface;
    }
}
