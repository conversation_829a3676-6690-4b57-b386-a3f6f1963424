package com.joinus.terminaladaptor.bean.hik;

import cn.hutool.core.map.MapUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceInfoDTO implements Serializable {


    private String serialNumber;

    private String firmwareReleasedDate;

    private String firmwareVersion;


    //</editor-fold>
    public static DeviceInfoDTO fromMap(Map<String, Object> map) {
        DeviceInfoDTO deviceInfo = new DeviceInfoDTO();
        if (map.containsKey("serialNumber")) {
            deviceInfo.setSerialNumber(MapUtil.getStr(map, "serialNumber"));
        }
        if (map.containsKey("firmwareVersion")) {
            deviceInfo.setFirmwareVersion(MapUtil.getStr(map, "firmwareVersion"));
        }
        if (map.containsKey("firmwareReleasedDate")) {
            deviceInfo.setFirmwareReleasedDate(MapUtil.getStr(map, "firmwareReleasedDate"));
        }
        return deviceInfo;
    }
}
