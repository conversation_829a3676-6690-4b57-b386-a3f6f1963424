package com.joinus.terminaladaptor;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@SpringBootApplication
@EnableWebMvc
@MapperScan(basePackages = {"com.joinus.common.mapper","com.joinus.dao.mapper"})
@ComponentScan(basePackages = {"com.joinus.common","com.joinus.terminaladaptor"})
public class TerminalAdaptorApplication {
    public static void main(String[] args) {
        SpringApplication.run(TerminalAdaptorApplication.class);
    }
}