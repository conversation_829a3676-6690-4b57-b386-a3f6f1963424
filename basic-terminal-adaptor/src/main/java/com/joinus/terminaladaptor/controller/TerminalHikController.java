package com.joinus.terminaladaptor.controller;


import cn.hutool.json.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.joinus.common.common.BaseController;
import com.joinus.terminaladaptor.bean.hik.HikHeartbeat;
import com.joinus.terminaladaptor.bean.hik.HikTtsHeartBeatResult;
import com.joinus.terminaladaptor.bean.hik.HikTtsRemoteCheck;
import com.joinus.terminaladaptor.service.HikSwipeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@EnableApolloConfig
@RestController
@RequestMapping("/terminal/hik")
@Slf4j
public class TerminalHikController extends BaseController {


    @Autowired
    private HikSwipeService swipeService;

    @Value("${loggable.sn:FK3877952,FK3877945}")
    private String loggableSn;
    /**
     * 返回设备心跳  返回结果: -1（失败）， 0（成功）
     * @return
     */
    @ResponseBody
    @GetMapping(value = "/tts/device/heartbeat")
    public Object getDeviceHeartBeat(HikHeartbeat hikHeartbeat) {
        if (loggableSn.contains(hikHeartbeat.getSn())) {
            log.info("HikHeartbeat:{}", hikHeartbeat);
        }
        return HikTtsHeartBeatResult.builder().Result(0).Msg("success").build();
    }


    /**
     * 远程校验
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/tts/device/remote-check")
    public Object remoteCheck(@RequestBody HikTtsRemoteCheck remoteCheck) {
        log.info("remoteCheck:{}", remoteCheck.toString());
        long startTime = System.currentTimeMillis();
        JSONObject jsonObject = swipeService.remoteCheckCardSignInRecordFromHik(remoteCheck);
        long endTime = System.currentTimeMillis();
        if (loggableSn.contains(remoteCheck.getSn())) {
            long duration = endTime - startTime;
            log.info("remoteCheck,方法运行时长:{}毫秒,", duration);
        }
        return jsonObject;
    }

}
