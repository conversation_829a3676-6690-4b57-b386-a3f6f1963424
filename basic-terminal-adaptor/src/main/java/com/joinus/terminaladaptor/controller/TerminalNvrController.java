package com.joinus.terminaladaptor.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.joinus.common.common.BaseController;
import com.joinus.common.model.response.ApiResult;
import com.joinus.dao.TerminalEntity;
import com.joinus.terminaladaptor.bean.nvr.CaptureInfo;
import com.joinus.terminaladaptor.bean.nvr.HeartbeatInfo;
import com.joinus.terminaladaptor.bean.nvr.TerminalBaseInfo;
import com.joinus.terminaladaptor.service.ITerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@EnableApolloConfig
@RestController
@RequestMapping("/terminal/nvr")
@Slf4j
public class TerminalNvrController extends BaseController {

    @Autowired
    private ITerminalService iTerminalService;
    
    
    /**
     * 返回设备心跳  返回结果: -1（失败）， 0（成功）
     * @return
     */
    @PostMapping (value = "/device/heartbeat")
    public ApiResult<String> getDeviceHeartBeat(@RequestHeader("schoolId") Long schoolId, @RequestHeader("terminalNum") String terminalNum,  @RequestBody List<HeartbeatInfo> heartbeatInfoList) {
        log.info("设备上送心跳信息 schoolId:{},terminalNum:{},heartbeatMessage:{}", schoolId, terminalNum, heartbeatInfoList);
        if (null == schoolId) {
            return ApiResult.failed("学校ID不能为空!");
        }

        if (StrUtil.isBlank(terminalNum)) {
            return ApiResult.failed("设备编号不能为空!");
        }

        if ( CollUtil.isEmpty(heartbeatInfoList)) {
            return ApiResult.failed("录像机心跳不能为空!");
        }

        TerminalEntity terminal = iTerminalService.getTerminalByTerminalNumAndSchoolId(terminalNum, schoolId);
        if (null == terminal) {
            log.warn("通过terminalNum:{}和,schoolId:{},查询设备信息不能为空", terminalNum, schoolId);
            return ApiResult.failed("通过设备编号查询设备信息不能为空!");
        }

        iTerminalService.nvrKeepAlive(schoolId, terminalNum, heartbeatInfoList);
        return ApiResult.success("ok");
    }

    /**
     *
     * @return
     */
    @GetMapping (value = "/device/base-info")
    public ApiResult<TerminalBaseInfo> getBaseInfo(@RequestHeader("terminalNum") String terminalNum) {
        log.info("获取设备基础信息 terminalNum:{}", terminalNum);
        if (StrUtil.isBlank(terminalNum)) {
            return ApiResult.failed("设备编号不能为空!");
        }

        TerminalEntity terminal = iTerminalService.getTerminalByTerminalNum(terminalNum);
        if (null == terminal) {
            log.warn("通过terminalNum:{},查询设备信息不能为空", terminalNum);
            return ApiResult.failed("通过设备编号查询设备信息不能为空!");
        }
        TerminalBaseInfo terminalBaseInfo = new TerminalBaseInfo();
        terminalBaseInfo.setSchoolId(terminal.getSchoolId());
        return ApiResult.success(terminalBaseInfo);
    }

    /**
     * @return
     */
    @PutMapping (value = "/save-captured-img")
    public ApiResult<String> saveCapturedImg(@RequestHeader("schoolId") Long schoolId, @RequestHeader("terminalNum") String terminalNum,  @RequestBody List<CaptureInfo> captureInfoList) {
        log.info("设备上送抓拍信息schoolId:{},terminalNum:{},抓拍信息总条数:{},抓拍信息:{}", schoolId, terminalNum, captureInfoList.size(), JSONUtil.toJsonStr(captureInfoList));
        if (null == schoolId) {
            return ApiResult.failed("学校ID不能为空!");
        }

        if (StrUtil.isBlank(terminalNum)) {
            return ApiResult.failed("录像机编号不能为空!");
        }

        if (CollUtil.isEmpty(captureInfoList)) {
            return ApiResult.failed("设备上送抓拍信息不能为空!");
        }

        iTerminalService.saveCapturedImg(schoolId, terminalNum, captureInfoList);

        return ApiResult.success("ok");
    }


}
