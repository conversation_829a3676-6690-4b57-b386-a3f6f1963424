package com.joinus.terminaladaptor.constant;

/**
 * <AUTHOR>
 * @description 常量类
 * @date 2024-11-28 17:21:52
 */
public class GlobalConstants {

    /**
     * 有效
     **/
    public static final int COMMON_ISACTIVE = 1;

    /**
     * 一体机类型在sys_dic中的id
     */
    public static final int ALL_IN_ONE_TERMINAL_TYPE_ID = 268;

    /**
     * 食堂区域功能分类
     **/
    public static final int CANTTEN_AREA_TERMINAL_TYPE_ID = 92;


    /**
     *
     **/
    public static final Integer QINIU_FILE_EXPIRE_DAYS = 30 ;


    /**
     * 在线
     **/
    public static final Integer TERMINAL_IS_LOGIN_ONLINE = 1;


    /**
     * 活动设备set的key
     **/
    public static final String REDIS_KEY_ACTIVE_TERMINALS = "ACTIVE_TERMINALS";


    public static final String REDIS_KEY_INCREMENT_STUDENT_TERMINALS = "aio-gateway:increment_student_terminals";


    /**
     * 增量处理学生ID
     **/
    public static final String REDIS_KEY_BUSINESS_PHONES="basic-terminal-adaptor:INCREMENT_STUDENT_ID_%s";

    // kafka重复消费时间间隔(秒)
    public static final Integer REDIS_INTERVAL_SECONDS = 30 ;

    /**
     * 在线设备hash集合
     **/
    public static final String REDIS_KEY_ONLINE_TERMINALS = "ONLINE_TERMINALS";

    /**
     * 陌生人卡号
     */
    public static final String STRANGER_CARD_NUM = "9999999999";

    /**
     * 签到类型
     */
    public interface SIGN_TYPE {
        int CARD = 0;
        int FACE = 1;
    }

    /**
     * 进出方向
     */
    public interface TERMINAL_DIRECTION {
        int OUT = 0;
        int IN = 1;
        int UNKOWN = 5;
        int NO_OUT = 6;
        int NO_IN = 7;

        //禁止签到
        int NO_SIGN_IN = 8;
    }

    public interface VERIFY_TYPE {
        Integer CARD = 1;
        Integer FACE = 2;
    }

    public interface UNV_VOICE_TYPE{
        int DORM_STUDENT = 6;
        int TEACHER = 11;
        int HOME_STUDENT = 33;
        int LEAVE_STUDENT=13;
    }

    public static final String SUCCESS="success";

    public static final String FAILED="failed";

}
