/**
 * Copyright (C),  2011-2020, ZheJiang Uniview Technologies Co., Ltd. All rights reserved.
 * <http://www.uniview.com/>
 * <p>
 * FileName : LapiUriConstat
 * Author   : z06274
 * Date     : 2020/7/16 20:23
 * DESCRIPTION: 与设备请求交互接口URI地址
 * <p>
 * History:
 * DATE        NAME        DESC
 */
package com.joinus.terminaladaptor.constant;

/**
 * <AUTHOR>
 * @date 2024-11-28 17:36:09
 * @description 与设备请求交互接口URI地址
 */
public interface HikVisionUriConstant {

    /**
     * 心跳保活接口URI
     */
    String HEART_URI = "/LAPI/V1.0/PACS/Controller/HeartReportInfo";

    /**
     * 出入记录接收URI
     */
    String ACCESS_RECEIVE_URI = "/LAPI/V1.0/System/Event/Notification/PersonVerification";

    /**
     * 出入记录结果返回URI
     */
    String ACCESS_RESPONSE_URI = "/LAPI/V1.0/PACS/Controller/Event/Notifications";

    /**
     * 人员库信息查询URI
     */
    String PEOPLE_BASIC_INFO_URI = "/LAPI/V1.0/PeopleLibraries/BasicInfo";

    /**
     * 人员信息下发
     */
    String PEOPLE_ADD_URI = "/ISAPI/AccessControl/UserInfo/SetUp?format=json";

    /**
     * 人员信息删除
     */
    String PEOPLE_DEL_URI = "/ISAPI/AccessControl/UserInfoDetail/Delete?format=json";

    /**
     * 人员照片添加
     */
    String PEOPLE_IMG_ADD_URI = "/ISAPI/Intelligent/FDLib/FDSetUp?format=json";

    /**
     * 人员卡号添加
     */
    String PEOPLE_CARD_ADD_URI = "/ISAPI/AccessControl/CardInfo/SetUp?format=json";

    /**
     * 人员信息查询
     */
    String PEOPLE_QUERY_URI = "/ISAPI/AccessControl/UserInfo/Search?format=json";

    /**
     * 人员卡号查询
     */
    String PEOPLE_CARD_QUERY_URI = "/ISAPI/AccessControl/CardInfo/Search?format=json";

    /**
     * 人员数量查询
     */
    String PEOPLE_COUNT_QUERY_URI = "/ISAPI/AccessControl/UserInfo/Count?format=json";


    /**
     * 配置设备周计划（用于刷卡时段下发）
     **/
    String CONFIG_SWIPE_TIME = "/ISAPI/AccessControl/UserRightWeekPlanCfg/{}?format=json";

    /**
     * 配置NTP服务器，用于对时
     **/
    String CONFIG_NTP = "/ISAPI/System/time/ntpServers";

    /**
     * 获取tcpIp参数
     **/
    String NETWORK_INFO = "/ISAPI/System/Network/interfaces";

    /**
     * 配置对时方式
     **/
    String CONFIG_TIME_KEEP = "/ISAPI/System/time";

    /**
     * 设备基本信息
     */
    String BASIC_INFO_URI = "/ISAPI/System/deviceInfo";


    /**
     * 设置http远程核验配置参数
     */
    String CONFIG_VERIFICATION_MODE_URI = "/ISAPI/AccessControl/httpRemoteAuthCfg?format=json";

    /**
     * 配置设备信息
     **/
    String CONFIG_URI = "/LAPI/V1.0/Channels/0/System/DataServers";

    /**
     * 播放语音
     **/
    String VOICE_URI = "/LAPI/V1.0/PACS/Controller/AJXVoiceInfo";

    /**
     * 文字提醒
     **/
    String TEXT_URI = "/LAPI/V1.0/PACS/Controller/GUIShowInfo";

    /**
     * 测温口罩配置
     **/
    String RULE_URL = "/LAPI/V1.0/PACS/Controller/AttributeVerification/Rule";

    /**
     * 人脸检测参数信息
     */
    String FACE_DETECTION_URL = "/LAPI/V1.0/Smart/FaceDetection/Rule";

    /**
     * 查询人员ID接口
     */
    String PEOPLE_ID_URL= "/LAPI/V1.0/PeopleLibraries/%s/UpdateTime";

    /**
     * 设备重启
     */
    String REBOOT_URI = "/LAPI/V1.0/System/Reboot";

    /**
     * 查询人脸库设置
     */
    String PEOPLE_LIBRARY_URL= "/LAPI/V1.0/PeopleLibraries/%s/PermissionInfo";

    /**
     * 查看LAPI长连接配置
     */
    String LAPI_LONG_CONNECTION_SETTINGS_URL= "/LAPI/V1.0/PACS/Controller/LongConnectionInfo";
    /**
     * 照片存储方式配置
     */
    String PHOTO_STORAGE_URL= "/LAPI/V1.0/Media/Storage/PhotoStorage";
}
