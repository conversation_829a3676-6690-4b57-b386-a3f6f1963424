package com.joinus.terminaladaptor.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisUtil {

    @Resource(name = "redisConfig")
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return false;
        }

    }


    public Long addValueToList(String key, String values) {
        return  redisTemplate.opsForList().rightPush(key, values);
    }
    public List<Object> getAllListItems(String key) {
        // range() 方法获取列表中从索引 0 到 -1 的元素，即获取整个列表
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    public Object getItems(String key) {
        // range() 方法获取列表中从索引 0 到 -1 的元素，即获取整个列表
        return redisTemplate.opsForList().range(key, 0, 0);
    }

    public void removeItems(String key) {
        redisTemplate.opsForList().trim(key, 1, -1);
    }


    /**
     * 对指定的键执行自增操作
     *
     * @param key 键
     * @return 自增后的值，如果操作失败则返回null。
     */
    public Long increment(String key) {
        try {
            // 使用redisTemplate的opsForValue().increment方法来增加键的值
            return redisTemplate.opsForValue().increment(key);
        } catch (Exception e) {
            log.error("Increment operation failed", e);
            return null;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return false;
        }
    }
    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {

                redisTemplate.delete(Arrays.asList(key));
            }
        }
    }


    public void delete(Set<String> keys) {
        redisTemplate.delete(keys);
    }
    /**
     * 设置List缓存
     * @param key 键
     * @param value 值
     * @param cacheSeconds 超时时间，0为不超时
     * @return
     */
    public long setList(String key, List<String> value, int cacheSeconds) {
        long result = 0;
        try {
            result = redisTemplate.opsForSet().add(key, value.toArray());
            if (cacheSeconds != 0) {
                this.expire(key, cacheSeconds);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return result;
    }

    public void putHash(String key, long id, Date lastTime) {
        try {
            JSONArray array = new JSONArray();
            array.add("java.util.Date");
            array.add(lastTime);
            redisTemplate.opsForHash().put(key, String.valueOf(id),array.toString());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void putHash(String key, String field, String value) {
        try {
            redisTemplate.opsForHash().put(key, field, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public String getHash(String key, String field) {
        try {
            return ObjectUtil.isNotNull(redisTemplate.opsForHash().get(key, field)) ? Objects.requireNonNull(redisTemplate.opsForHash().get(key, field)).toString() : null;
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * set的remove操作
     * @params: [key, list] 
     * @return: long
     * @Author: jxt
     * @Date: 2021/3/11 5:06 下午
     */
    public long setRemove(String key, List list) {
        long result = 0;
        try {
            result = redisTemplate.opsForSet().remove(key, list.toArray());
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * set的smembers
     * @params: [key]
     * @return: java.util.Set
     * @Author: jxt
     * @Date: 2021/3/11 5:08 下午
     */
    public Set smembers(String key){
        Set result = new HashSet();
        try {
            result = redisTemplate.opsForSet().members(key);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 判断是否是set的member
     * @params: [key, value]
     * @return: boolean
     * @Author: jxt
     * @Date: 2021/3/11 5:10 下午
     */
    public boolean isMember(String key, Object value) {
        boolean result = false;
        try {
            result = redisTemplate.opsForSet().isMember(key, value);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * set的元素个数
     * @params: [key] 
     * @return: long
     * @Author: jxt
     * @Date: 2021/3/11 5:17 下午
     */
    public long setCount(String key){
        long result = 0;
        try {
            result = redisTemplate.opsForSet().size(key);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 模糊匹配key
     * @params: [keyPrefix]
     * @return: java.util.Set<java.lang.String>
     * @Author: jxt
     * @Date: 2021/3/17 10:20 上午
     */
    public Set<String> keys(String keyPrefix){
        Set<String> result = new HashSet<>();
        try {
            result = redisTemplate.keys(keyPrefix);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    public boolean tryLock(String key, String value, long timeout) {
        try {
            Boolean result = redisTemplate.opsForValue().setIfAbsent(key, value,timeout,TimeUnit.SECONDS);
            return result != null && result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

}
