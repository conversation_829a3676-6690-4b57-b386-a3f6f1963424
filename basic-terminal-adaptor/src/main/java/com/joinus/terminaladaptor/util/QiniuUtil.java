package com.joinus.terminaladaptor.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.joinus.dao.StudentEntity;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.BatchStatus;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 七牛云工具类
 * @date 2020/11/11
 */
@Slf4j
@Component
public class QiniuUtil {
    public static String qiniuDomainName;
    public static  String basicApiDomainName;

    static {
        Config appConfig = ConfigService.getAppConfig();
        qiniuDomainName = appConfig.getProperty("qiniu.domain.name", "https://upload-z1.qiniup.com/");
        basicApiDomainName = appConfig.getProperty("basic.api.domain.name", "https://basic-api.uat.fangxiao.top");
    }

    public static void deleteAfterDays(String qiniuKey, int deleteAfterDays) {
        HttpUtil.createGet( basicApiDomainName+"/api/qiniu/files/delete-schedule")
                .form("key",qiniuKey)
                .form("fileExpireDays",deleteAfterDays)
                .timeout(5000)
                .execute();
    }

    public static String getQiniuToken(){
        HttpResponse response = HttpUtil.createGet( basicApiDomainName+"/api/qiniu/upload/token")
                .timeout(5000)
                .execute();
        if( 200 == response.getStatus()){
            return response.body();
        }
        return null;
    }


    /**
     * 使用获取到的上传token将文件上传到七牛云
     *
     * @param
     * @param uploadToken 上传token
     */
    public static String uploadFile(ByteArrayInputStream inputStream, String uploadToken, String fileUrl)  {
        try {
            String boundary = "----WebKitFormBoundary7MA4YWxkTrZu0gW";
            URL url = new URL(qiniuDomainName);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setDoOutput(true);
            httpConn.setRequestMethod("POST");
            httpConn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            OutputStream outputStream = httpConn.getOutputStream();
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true);
            // 添加文件部分
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + fileUrl + "\"").append("\r\n");
            writer.append("Content-Transfer-Encoding: binary").append("\r\n");
            writer.append("\r\n").flush();
            IOUtils.copy(inputStream, outputStream); // Copy file into output stream
            outputStream.flush();
            // 添加key字段，用于指定上传后的文件名
            writer.append("\r\n").flush();
            writer.append("--").append(boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"key\"").append("\r\n");
            writer.append("\r\n").append(fileUrl).flush();
            // 添加token字段部分
            writer.append("\r\n").flush();
            writer.append("--" + boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"token\"").append("\r\n");
            writer.append("\r\n").append(uploadToken).append("\r\n").flush();
            // 结束标记
            writer.append("--").append(boundary).append("--").append("\r\n").flush();

            // 请求完成, 获取响应
            int status = httpConn.getResponseCode();
            if (status == HttpURLConnection.HTTP_OK) {
                InputStream responseStream = new BufferedInputStream(httpConn.getInputStream());
                String jsonResponse = new BufferedReader(new InputStreamReader(responseStream))
                        .lines().collect(Collectors.joining("\n"));
                // 解析jsonResponse获取key
                JSONObject jsonObject = new JSONObject(jsonResponse);
                String fileKey = (String) jsonObject.get("key");
                httpConn.disconnect();
                return fileKey;  // 返回响应字符串
            } else {
                String error = "Server returned non-OK status: " + status;
                httpConn.disconnect();
                throw new IOException(error);
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return  null;
    }


}
