package com.joinus.terminaladaptor.util;


import cn.hutool.core.util.StrUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023-11-30 11:27:20
 */
public class LocalDateTimeUtils {
    public static final String YYYY_MM_DD_EN = "yyyy-MM-dd";

    public static final String YYYY_MM_DD_HH_MM_EN = "yyyy-MM-dd HH:mm";
    public static final String HH_MM_EN = "HH:mm";

    public static final String YYYY_MM_DD_HH_MM_SS_EN = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYY_MM_DD_HH_MM_SS_SSS_EN = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String YYYY_MM_DD_CN = "yyyy年MM月dd日";

    public static final String YYYY_MM_DD_HH_MM_SS_CN = "yyyy年MM月dd日HH时mm分ss秒";

    public static final String YYYY_MM_DD_HH_MM_CN = "yyyy年MM月dd日HH时mm分";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyyMMddHHmmss";

    public static final long MINUTES_PER_HOUR = 60;

    public static final long HOURS_PER_DAY = 24;

    public static final long MINUTES_PER_DAY = HOURS_PER_DAY * MINUTES_PER_HOUR;

    private static Map<String, DateTimeFormatter> dateTimeFormatterMap = new HashMap<>();

    public static DateTimeFormatter getDateTimeFormatter(String pattern) {
        DateTimeFormatter formatter = dateTimeFormatterMap.get(pattern);
        if (formatter == null) {
            formatter = DateTimeFormatter.ofPattern(pattern);
            dateTimeFormatterMap.put(pattern, formatter);
        }
        return formatter;
    }

    /**
     * 获取当天最晚时间，即当天晚上的23:59:59
     *
     * @return
     */
    public static LocalDateTime getTodayMaxTime() {
        return LocalDateTime.of(LocalDate.now(), LocalTime.MAX.truncatedTo(ChronoUnit.SECONDS));
    }

    /**
     * 日期比较
     *
     * @param date1
     * @param date2
     * @return 如果date1早于date2，返回负数；如果date1晚于date2，返回正数
     */
    public static int compare(Date date1, Date date2) {
        LocalDateTime localDateTime1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime localDateTime2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime1.compareTo(localDateTime2);
    }

    /**
     * 判断日期是否是今天
     *
     * @param date
     * @return
     */
    public static boolean isToday(Date date) {
        LocalDate today = LocalDate.now();
        LocalDate someDay = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return today.compareTo(someDay) == 0;
    }

    /**
     * Date转LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime getInstance(Date date) {

        if (date == null) {
            throw new IllegalArgumentException("参数不能为空！");
        }

        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * Date日期+String时间，转LocalDateTime
     *
     * @param date 日期，格式：yyyy-mm-dd
     * @param time 时间，格式：hh:mi
     * @return
     */
    public static LocalDateTime getInstance(Date date, String time) {

        if (date == null || StrUtil.isBlank(time)) {
            throw new IllegalArgumentException("参数不能为空！");
        }

        return getInstance(date).with(LocalTime.parse(time + ":00"));
    }

    /**
     * 格式化日期为指定的格式
     *
     * @param dateTime
     * @param pattern
     * @return
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        return getDateTimeFormatter(pattern).format(dateTime);
    }

    public static LocalTime getLocalTime(String localTime, String pattern) {
        return LocalTime.parse(localTime, getDateTimeFormatter(pattern));
    }

    // 比较两个时间段是否重叠：start1<=end2 and end1>=start2
    public static boolean isRepeat(LocalDateTime start1, LocalDateTime end1, LocalDateTime start2, LocalDateTime end2) {
        return (start1.isEqual(end2) || start1.isBefore(end2)) && (end1.isEqual(start2) || end1.isAfter(start2));
    }

    public static LocalDateTime getInstance(String localDateTime, String pattern) {
        return LocalDateTime.parse(localDateTime, LocalDateTimeUtils.getDateTimeFormatter(pattern));
    }

    public static LocalDateTime getInstance(String localDateTime) {
        return LocalDateTime.parse(localDateTime, LocalDateTimeUtils.getDateTimeFormatter(LocalDateTimeUtils.YYYY_MM_DD_HH_MM_SS_EN));
    }

    public static boolean isBetween(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        return time.isAfter(start) && time.isBefore(end);
    }

    public static boolean isTheSameYear(LocalDateTime time1, LocalDateTime time2) {
        int year1 = time1.getYear();
        int year2 = time2.getYear();
        return year1 == year2;
    }

    public static LocalDateTime getInstanceReturnNullIfWithInvalidFormat(String dateTimeStr, String dateFormatStr) {
        try {
            DateTimeFormatter format = LocalDateTimeUtils.getDateTimeFormatter(dateFormatStr);
            return LocalDateTime.parse(dateTimeStr, format);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

}
