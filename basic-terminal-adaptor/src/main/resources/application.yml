app:
  id: basic-terminal-adaptor
server:
  port: 9101
  servlet:
    context-path: /api/terminal-adaptor
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher #集成swagger后报错
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ${jdbc.url:*************************************}
          username: ${ORACLE_USERNAME:ijx}
          password: ${ORACLE_PWD:ijxuat9671-hwy}
  application:
    name: basic-terminal-adaptor
  kafka:
    listener:
      ack-mode: manual_immediate
    consumer:
      enable-auto-commit: false
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        group-id: basic-terminal-adaptor
    bootstrap-servers: ***************:9092
  redis:
    host: ***************  # Redis服务器的主机名或IP地址
    port: 6379            # Redis服务器的端口
    password: ijxuat-huaweiyun  # 如果设置了密码验证，则需要提供密码
    database: 0           # 使用的数据库索引，默认通常是0
    timeout: 200           # 命令超时时间（秒）
apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true
springfox:
  documentation:
    swagger:
      use-model-v3: false
qiniu:
  oss:
    access:
      key: UEAq301iSGQxYFtbTH0WaVm2zGKCqxg80Qy88hTs
      secret: GPC8DZuXOi7Y2fOPJr5blm5L1vQwychRXQvU1dr2
      bucket: ijx-capture
    domain:
      url: https://capture.fangxiao.top/